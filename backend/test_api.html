<!DOCTYPE html>
<html>
<head>
    <title>API Test</title>
</head>
<body>
    <h1>API Test</h1>
    <div id="status">Testing...</div>
    <script>
        const api = new (function() {
            this.baseUrl = 'http://localhost:8000';
            
            this.testHealth = async function() {
                try {
                    const response = await fetch(`${this.baseUrl}/health`);
                    const data = await response.json();
                    console.log('Health check:', data);
                    return data;
                } catch (error) {
                    console.error('Health check failed:', error);
                    throw error;
                }
            };
            
            this.testStream = async function() {
                try {
                    const response = await fetch(`${this.baseUrl}/api/v1/chat/stream`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'text/event-stream',
                        },
                        body: JSON.stringify({
                            message: "Hello test",
                            conversation_id: "test123"
                        })
                    });
                    
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    
                    console.log('Stream test successful:', response);
                    return response;
                } catch (error) {
                    console.error('Stream test failed:', error);
                    throw error;
                }
            };
        })();
        
        async function runTests() {
            const statusDiv = document.getElementById('status');
            try {
                statusDiv.innerText = 'Testing health endpoint...';
                const health = await api.testHealth();
                statusDiv.innerHTML += '<br>Health: ' + JSON.stringify(health);
                
                statusDiv.innerHTML += '<br>Testing stream endpoint...';
                const stream = await api.testStream();
                statusDiv.innerHTML += '<br>Stream: SUCCESS';
            } catch (error) {
                statusDiv.innerHTML += '<br>ERROR: ' + error.message;
                console.error('Test failed:', error);
            }
        }
        
        // Run tests when page loads
        window.onload = runTests;
    </script>
</body>
</html>
#!/usr/bin/env python3
"""
Interactive MCP Client for GraphRAG Server
Allows testing and interaction with all MCP tools
"""

import json
import asyncio
import argparse
from typing import Dict, Any, Optional
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
import sys
from datetime import datetime

class GraphRAGMCPClient:
    """Interactive client for GraphRAG MCP Server"""

    def __init__(self, server_path: str = None):
        self.session = None
        # Always use uv to run the server
        self.server_path = server_path or "uv run python main.py"
        self.tools = {}

    async def connect(self):
        """Connect to the MCP server"""
        print(f"🔌 Connecting to GraphRAG MCP Server...")

        # Create server parameters for stdio connection
        server_params = StdioServerParameters(
            command=self.server_path,
            args=[],
            env=None
        )

        # Connect to server
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                self.session = session

                # Initialize the connection
                await session.initialize()

                # Get available tools
                tools_response = await session.list_tools()
                self.tools = {tool.name: tool for tool in tools_response.tools}

                print(f"✅ Connected! Found {len(self.tools)} tools available")
                print(f"📋 Tools: {', '.join(self.tools.keys())}\n")

                # Start interactive session
                await self.interactive_session()

    async def interactive_session(self):
        """Run interactive command loop"""
        print("=" * 60)
        print("GraphRAG MCP Client - Interactive Mode")
        print("=" * 60)
        print("\nCommands:")
        print("  list              - List all available tools")
        print("  describe <tool>   - Show tool description and parameters")
        print("  call <tool>       - Call a tool interactively")
        print("  quick <command>   - Quick commands (see 'quick help')")
        print("  exit              - Exit the client")
        print("-" * 60)

        while True:
            try:
                command = input("\n🔧 MCP> ").strip()

                if command == "exit":
                    print("👋 Goodbye!")
                    break
                elif command == "list":
                    await self.list_tools()
                elif command.startswith("describe "):
                    tool_name = command[9:].strip()
                    await self.describe_tool(tool_name)
                elif command.startswith("call "):
                    tool_name = command[5:].strip()
                    await self.call_tool_interactive(tool_name)
                elif command.startswith("quick "):
                    quick_cmd = command[6:].strip()
                    await self.handle_quick_command(quick_cmd)
                elif command == "":
                    continue
                else:
                    print(f"❓ Unknown command: {command}")
                    print("   Type 'list' to see available tools or 'exit' to quit")

            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")

    async def list_tools(self):
        """List all available tools"""
        print("\n📦 Available Tools:")
        print("-" * 60)

        # Group tools by category
        categories = {
            "Schema Discovery": ["get_schema", "describe_entity_type", "describe_relationship_type",
                                "get_node_statistics", "get_relationship_statistics"],
            "Entity Search": ["search_entities", "get_entity", "find_entities_by_time_range",
                             "search_by_pattern"],
            "Graph Traversal": ["get_neighbors", "find_paths", "traverse_pattern",
                                "find_common_neighbors"],
            "Aggregation & Analytics": ["aggregate_by_type", "calculate_statistics",
                                       "find_patterns", "execute_cypher"],
            "Administration": ["get_health", "refresh_cache", "get_query_suggestions",
                             "get_database_info", "clear_graph"]
        }

        for category, tool_names in categories.items():
            print(f"\n{category}:")
            for tool_name in tool_names:
                if tool_name in self.tools:
                    tool = self.tools[tool_name]
                    # Get first line of description
                    desc = tool.description.split('\n')[0] if tool.description else "No description"
                    print(f"  • {tool_name:30} - {desc[:50]}...")

    async def describe_tool(self, tool_name: str):
        """Describe a specific tool"""
        if tool_name not in self.tools:
            print(f"❌ Tool '{tool_name}' not found")
            print(f"   Available tools: {', '.join(self.tools.keys())}")
            return

        tool = self.tools[tool_name]
        print(f"\n📖 Tool: {tool_name}")
        print("-" * 60)
        print(f"Description:\n{tool.description}")

        if tool.inputSchema:
            print(f"\nParameters:")
            schema = tool.inputSchema
            if hasattr(schema, 'properties'):
                for prop_name, prop_info in schema.properties.items():
                    required = prop_name in (schema.required or [])
                    req_marker = " (required)" if required else " (optional)"
                    print(f"  • {prop_name}{req_marker}")
                    if hasattr(prop_info, 'description'):
                        print(f"    {prop_info.description}")

    async def call_tool_interactive(self, tool_name: str):
        """Interactively call a tool"""
        if tool_name not in self.tools:
            print(f"❌ Tool '{tool_name}' not found")
            return

        tool = self.tools[tool_name]
        print(f"\n🔧 Calling tool: {tool_name}")

        # Collect parameters
        params = {}
        if tool.inputSchema and hasattr(tool.inputSchema, 'properties'):
            print("Enter parameters (press Enter for default/null):")

            for prop_name, prop_info in tool.inputSchema.properties.items():
                required = prop_name in (tool.inputSchema.required or [])
                prompt = f"  {prop_name}"
                if required:
                    prompt += " (required)"

                # Show description if available
                if hasattr(prop_info, 'description'):
                    desc = prop_info.description.split('\n')[0][:50]
                    prompt += f" [{desc}]"

                prompt += ": "
                value = input(prompt).strip()

                if value:
                    # Try to parse as JSON first (for objects/arrays)
                    try:
                        params[prop_name] = json.loads(value)
                    except:
                        # Check if it's a number
                        if value.isdigit():
                            params[prop_name] = int(value)
                        elif value.lower() in ['true', 'false']:
                            params[prop_name] = value.lower() == 'true'
                        else:
                            params[prop_name] = value

        # Call the tool
        await self.call_tool(tool_name, params)

    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]):
        """Call a tool and display results"""
        print(f"\n⏳ Calling {tool_name}...")

        try:
            result = await self.session.call_tool(tool_name, arguments)

            # Parse and format the result
            if isinstance(result, str):
                try:
                    result_data = json.loads(result)
                    print(f"\n✅ Success!")
                    print(json.dumps(result_data, indent=2, default=str))
                except:
                    print(f"\n✅ Result:")
                    print(result)
            else:
                print(f"\n✅ Result:")
                print(json.dumps(result, indent=2, default=str))

        except Exception as e:
            print(f"❌ Error calling tool: {e}")

    async def handle_quick_command(self, command: str):
        """Handle quick commands for common operations"""
        quick_commands = {
            "help": self.show_quick_help,
            "schema": lambda: self.call_tool("get_schema", {}),
            "health": lambda: self.call_tool("get_health", {}),
            "stats": lambda: self.call_tool("get_node_statistics", {}),
            "incidents": lambda: self.call_tool("search_entities", {
                "entity_type": "Incident",
                "limit": 10
            }),
            "resources": lambda: self.call_tool("search_entities", {
                "entity_type": "Resource",
                "limit": 10
            }),
            "departments": lambda: self.call_tool("search_entities", {
                "entity_type": "Department",
                "limit": 10
            }),
            "high-severity": lambda: self.call_tool("search_entities", {
                "entity_type": "Incident",
                "properties": {"severity": {"min": 4}},
                "limit": 10
            }),
            "bottlenecks": lambda: self.call_tool("find_patterns", {
                "base_entity": "Resource",
                "pattern_type": "resource_bottleneck",
                "threshold": {"utilization_pct": 75}
            }),
            "hurricane": lambda: self.call_tool("search_entities", {
                "entity_type": "Incident",
                "properties": {"type": "hurricane"},
                "limit": 10
            })
        }

        if command in quick_commands:
            await quick_commands[command]()
        else:
            print(f"❌ Unknown quick command: {command}")
            await self.show_quick_help()

    async def show_quick_help(self):
        """Show quick command help"""
        print("\n⚡ Quick Commands:")
        print("-" * 60)
        print("  quick help         - Show this help")
        print("  quick schema       - Get complete graph schema")
        print("  quick health       - Check server health")
        print("  quick stats        - Get node statistics")
        print("  quick incidents    - List recent incidents")
        print("  quick resources    - List all resources")
        print("  quick departments  - List all departments")
        print("  quick high-severity - Find high-severity incidents")
        print("  quick bottlenecks  - Find resource bottlenecks")
        print("  quick hurricane    - Find hurricane incidents")

class StandaloneMCPClient:
    """Standalone client that connects directly to running server"""

    def __init__(self, server_url: str = "http://localhost:8000"):
        self.server_url = server_url

    async def test_connection(self):
        """Test basic connection to server"""
        import aiohttp

        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(f"{self.server_url}/health") as response:
                    if response.status == 200:
                        print(f"✅ Server is running at {self.server_url}")
                        data = await response.json()
                        print(json.dumps(data, indent=2))
                    else:
                        print(f"❌ Server returned status {response.status}")
            except Exception as e:
                print(f"❌ Could not connect to server: {e}")
                print(f"   Make sure the server is running at {self.server_url}")

async def main():
    parser = argparse.ArgumentParser(description="GraphRAG MCP Client")
    parser.add_argument(
        "--transport",
        choices=["stdio", "http"],
        default="stdio",
        help="Transport mode (stdio for local development, http for network access)"
    )
    parser.add_argument(
        "--server",
        default=None,
        help="For stdio: server command (defaults to 'uv run python main.py'). For HTTP: server URL (defaults to 'http://localhost:8000')"
    )
    parser.add_argument(
        "--port",
        type=int,
        default=8000,
        help="Port for HTTP transport (default: 8000)"
    )

    args = parser.parse_args()

    if args.transport == "stdio":
        # Use MCP protocol over stdio
        print("Connecting via stdio transport...")
        client = GraphRAGMCPClient(args.server)
        await client.connect()
    else:
        # Use HTTP transport
        server_url = args.server or f"http://localhost:{args.port}"
        print(f"Connecting via HTTP transport to {server_url}...")

        # Test HTTP connection
        standalone = StandaloneMCPClient(server_url)
        await standalone.test_connection()

        print("\nHTTP transport is running. You can now:")
        print(f"1. Test the health endpoint: curl {server_url}/health")
        print(f"2. Use MCP client with HTTP transport")
        print(f"3. Connect from other applications via HTTP")

if __name__ == "__main__":
    print("""
    ╔═══════════════════════════════════════════════╗
    ║   GraphRAG MCP Client for Emergency Management ║
    ╚═══════════════════════════════════════════════╝
    """)

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
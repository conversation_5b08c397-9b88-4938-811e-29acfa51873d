# GraphRAG MCP Server Implementation Guide

## Executive Summary

A production-ready Model Context Protocol (MCP) server that exposes emergency management knowledge graphs through deterministic, composable operations. This enables Large Language Models to perform complex multi-hop reasoning without requiring the MCP server to understand natural language or make interpretive decisions.

## Table of Contents

1. [Problem Statement](#problem-statement)
2. [Guiding Principles](#guiding-principles)
3. [Architectural Approach](#architectural-approach)
4. [Design Choices](#design-choices)
5. [Technology Stack](#technology-stack)
6. [Project Structure](#project-structure)
7. [Implementation Details](#implementation-details)
8. [MCP Tools API](#mcp-tools-api)
9. [Data Pipeline](#data-pipeline)
10. [Usage Examples](#usage-examples)
11. [Testing & Validation](#testing--validation)
12. [Deployment Guide](#deployment-guide)

---

## Problem Statement

### What We're Solving For

Emergency management at the division level requires answering complex questions that span:
- **Multiple data types**: Structured (databases), semi-structured (logs, comments), and unstructured (PDFs, reports)
- **Temporal relationships**: How incidents evolve over time and impact resource allocation
- **Multi-hop reasoning**: "Which resources allocated to hurricane response were previously deployed to areas now experiencing equipment failures?"
- **Pattern recognition**: Identifying systemic issues across departments, incidents, and time periods
- **Real-time context**: Current deployment status affecting future allocation decisions

Traditional RAG systems fail because they:
- Retrieve isolated text chunks without understanding relationships
- Cannot traverse complex entity connections
- Lack temporal and spatial reasoning capabilities
- Miss patterns that emerge only through graph analysis

### Why GraphRAG with MCP

GraphRAG addresses these limitations by structuring knowledge as a graph, but typical implementations have a critical flaw: they embed LLM intelligence into the data layer, creating:
- **Opacity**: Hidden reasoning that can't be debugged or audited
- **Inflexibility**: Hard-coded interpretations that don't adapt to context
- **Cost**: Redundant LLM calls for every operation
- **Latency**: Multiple round-trips for simple operations

Our MCP-based approach solves this by maintaining **clean separation of concerns**: the graph layer provides pure data operations while the LLM handles all interpretation and planning.

---

## Guiding Principles

### 1. Pure Data Layer
The MCP server is a **deterministic graph API**, not an intelligence layer:
- No natural language processing within the server
- No query interpretation or intent detection
- No autonomous decision-making about what data to return
- Every operation has predictable, reproducible results

### 2. Composable Operations
Complex queries are answered through **multiple simple tool calls**:
- Each tool does one thing well
- The LLM orchestrates tool composition
- Progressive discovery through iteration
- Building blocks, not monolithic operations

### 3. Schema Transparency
The LLM must understand the graph structure to navigate effectively:
- Expose complete schema information
- Provide statistics and distributions
- Document common patterns
- Enable informed query planning

### 4. Fail Fast and Explicitly
Operations should fail quickly with clear error messages:
- No silent failures or empty results
- Explicit error codes and descriptions
- Suggestions for alternative approaches
- Validation before expensive operations

### 5. Performance First
Every operation must be optimized for production scale:
- Indexed lookups over full scans
- Bounded traversals with depth limits
- Pagination for large result sets
- Caching for frequently accessed patterns

---

## Architectural Approach

### System Architecture

```mermaid
graph TB
    subgraph "GraySkyGenAI Assistant Backend"
        A[User Question] --> B[Foundation Model<br/>OpenAI GPT-4]
        B --> C[Tool Selection<br/>& Orchestration]
        C --> D[MCP Client Interface<br/>Tool Execution]
        D --> E[Multi-type Streaming<br/>STATUS • REASONING • SOURCES • CONTENT]
    end

    subgraph "GraphRAG MCP Server"
        F[MCP Protocol Handler<br/>JSON-RPC] --> G[Tool Registry]
        G --> H{Tool Router}

        H --> I[Schema Tools<br/>get_schema<br/>describe_entity_type]
        H --> J[Search Tools<br/>search_entities<br/>get_entity<br/>find_by_time_range]
        H --> K[Traversal Tools<br/>get_neighbors<br/>traverse_pattern<br/>find_paths]
        H --> L[Aggregation Tools<br/>aggregate_by_type<br/>calculate_statistics<br/>find_patterns]
        H --> M[Admin Tools<br/>get_health<br/>refresh_cache<br/>execute_cypher]

        I --> N[Graph Operations Layer]
        J --> N
        K --> N
        L --> N
        M --> N

        N --> O[Cypher Query Builder<br/>Safety Validation<br/>Result Formatting]
        O --> P[Schema Cache Manager<br/>Performance Optimizer]
    end

    subgraph "Data & Storage Layer"
        Q[(Neo4j Graph Database<br/>Emergency Management Schema)]
        R[(Vector Store<br/>Optional Semantic Search)]
        S[(Document Storage<br/>Reports & Files)]
        T[Data Ingestion Pipeline<br/>SQL • CSV • JSON • REST APIs]
    end

    subgraph "Emergency Management Data"
        U[Incidents<br/>🔥 Fires • 🌪️ Hurricanes<br/>🚨 Medical • 🚨 Police]
        V[Resources<br/>🚒 Fire Trucks • 🚑 Ambulances<br/>🚁 Helicopters • 👥 Personnel]
        W[Departments<br/>🔥 Fire Dept • 👮 Police<br/>🏥 Medical • 📋 Emergency Mgmt]
        X[Relationships<br/>RESPONDED_TO • BELONGS_TO<br/>MANAGES • COORDINATES_WITH]
    end

    %% Connections
    D -.->|MCP Protocol<br/>JSON-RPC| F
    P --> Q
    P --> R
    P --> S
    T --> Q
    T --> R
    T --> S

    Q --> U
    Q --> V
    Q --> W
    Q --> X

    %% Styling
    classDef assistant fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef mcpServer fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef dataLayer fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef emergency fill:#fff3e0,stroke:#e65100,stroke-width:2px

    class A,B,C,D,E assistant
    class F,G,H,I,J,K,L,M,N,O,P mcpServer
    class Q,R,S,T dataLayer
    class U,V,W,X emergency
```

**Architecture Flow:**
1. **User Question** → Foundation Model analyzes and plans multi-step approach
2. **Tool Selection** → GPT-4 decides which graph tools to call and in what sequence
3. **MCP Execution** → Tools execute against Neo4j with safety validation
4. **Stream Response** → Rich attribution and integrated answers via multi-type streaming

### Query Flow Example

User asks: "Which fire stations responded to multiple incidents during Hurricane Milton?"

```
1. LLM receives question
   ↓
2. LLM calls: get_schema()
   → Returns: Entity types, relationship types, properties
   ↓
3. LLM calls: search_entities(type="Incident", properties={"name_contains": "Milton"})
   → Returns: Hurricane Milton incident ID and properties
   ↓
4. LLM calls: traverse_pattern(
     start_type="Incident",
     start_id="INC-2024-MILTON",
     pattern="(i)<-[:RESPONDED_TO]-(s:FireStation)",
     aggregate=true
   )
   → Returns: Fire stations with response counts
   ↓
5. LLM filters results where count > 1 and formats response
```

---

## Design Choices

### Why No LLM in the MCP Server?

1. **Predictability**: Every operation returns consistent results
2. **Debuggability**: Clear audit trail of what data was retrieved
3. **Performance**: No LLM latency for data operations
4. **Cost**: Single LLM inference instead of multiple
5. **Flexibility**: Different LLMs can use the same data layer
6. **Compliance**: Data operations are deterministic and auditable

### Why Neo4j?

1. **Production-ready**: Mature, stable, widely deployed
2. **Cypher query language**: Declarative and powerful
3. **ACID compliance**: Transaction safety for critical data
4. **Rich ecosystem**: Visualization tools, monitoring, backups
5. **Graph algorithms**: Built-in pathfinding, centrality, community detection
6. **Scalability**: Handles billions of nodes and relationships

### Why MCP Protocol?

1. **Standard interface**: Works with any MCP-compatible LLM
2. **Tool discovery**: LLMs automatically understand available operations
3. **Streaming support**: Efficient for large result sets
4. **Error handling**: Built-in error propagation
5. **Future-proof**: Growing ecosystem of MCP tools

---

## Technology Stack

### Core Technologies

| Component | Technology | Purpose | Why This Choice |
|-----------|------------|---------|-----------------|
| MCP Framework | FastMCP 2.11+ | Server implementation | Simplest, most maintainable |
| Graph Database | Neo4j 5.13+ | Primary data store | Production-proven, ACID compliant |
| Language | Python 3.9+ | Server development | Best library support |
| Schema | YAML/JSON | Configuration | Human-readable, versionable |
| Vector Store | Milvus (optional) | Semantic search | Scales independently |

### Python Dependencies

```python
# Core MCP and server
fastmcp>=2.11.3          # MCP server framework
mcp>=1.13.0             # MCP protocol
python-dotenv>=1.1.1    # Environment management

# Graph database
neo4j>=5.14.0           # Neo4j driver
py2neo>=2021.2.3        # Higher-level Neo4j operations (optional)

# Vector search (optional)
pymilvus>=2.3.3         # Milvus client
numpy>=1.24.0           # Vector operations

# Data handling
pydantic>=2.0.0         # Data validation
pandas>=2.0.0           # Data manipulation
pyyaml>=6.0             # YAML parsing

# Monitoring (optional)
structlog>=23.2.0       # Structured logging
prometheus-client>=0.19  # Metrics
```

---

## Project Structure

```
graphrag-mcp-server/
│
├── .env                           # Environment variables
├── .env.example                   # Template for environment setup
├── requirements.txt               # Python dependencies
├── README.md                      # This document
├── main.py                        # Entry point
├── run.sh                         # Startup script
│
├── src/
│   ├── __init__.py
│   ├── server.py                  # FastMCP server setup and tool registration
│   ├── tools/
│   │   ├── __init__.py
│   │   ├── schema_tools.py       # Schema inspection tools
│   │   ├── search_tools.py       # Entity search tools
│   │   ├── traversal_tools.py    # Graph traversal tools
│   │   ├── aggregation_tools.py  # Metrics and aggregation tools
│   │   └── admin_tools.py        # Graph statistics and health
│   │
│   ├── graph/
│   │   ├── __init__.py
│   │   ├── client.py             # Neo4j connection management
│   │   ├── query_builder.py      # Cypher query construction
│   │   ├── schema_manager.py     # Schema caching and validation
│   │   └── result_formatter.py   # Result transformation
│   │
│   ├── models/
│   │   ├── __init__.py
│   │   ├── entities.py          # Entity type definitions
│   │   ├── relationships.py     # Relationship type definitions
│   │   └── responses.py         # Response format models
│   │
│   └── utils/
│       ├── __init__.py
│       ├── validation.py        # Input validation
│       ├── performance.py       # Query optimization
│       └── logging.py           # Structured logging
│
├── config/
│   ├── schema.yaml              # Graph schema definition
│   ├── indexes.yaml             # Index configuration
│   └── patterns.yaml            # Common query patterns
│
├── data/
│   ├── schema/
│   │   └── emergency_management.cypher  # Schema creation
│   ├── sample/
│   │   ├── incidents.csv       # Sample incident data
│   │   ├── resources.csv       # Sample resource data
│   │   └── load_sample.cypher  # Sample data loader
│   └── ingestion/
│       ├── pipeline.py          # Data ingestion pipeline
│       └── transformers.py     # Data transformation logic
│
└── tests/
    ├── test_tools.py            # Tool functionality tests
    ├── test_graph_ops.py        # Graph operation tests
    ├── test_performance.py      # Performance benchmarks
    └── fixtures/
        └── test_data.cypher     # Test data setup
```

---

## Implementation Details

### Environment Configuration

```bash
# .env file
# Server Configuration
SERVER_NAME=GraphRAG-Emergency-Management
SERVER_PORT=8000
DEBUG=false
LOG_LEVEL=INFO

# Neo4j Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_secure_password
NEO4J_DATABASE=emergency

# Performance Settings
CACHE_TTL_SECONDS=300
MAX_TRAVERSAL_DEPTH=5
DEFAULT_LIMIT=100
MAX_LIMIT=1000
QUERY_TIMEOUT_SECONDS=30

# Optional Vector Store
ENABLE_VECTOR_SEARCH=false
MILVUS_HOST=localhost
MILVUS_PORT=19530
MILVUS_COLLECTION=emergency_embeddings

# Monitoring (optional)
ENABLE_METRICS=true
METRICS_PORT=9090
```

### Core Server Implementation

```python
# src/server.py
"""
GraphRAG MCP Server - Main server setup
"""

import os
import logging
from fastmcp import FastMCP
from dotenv import load_dotenv

from .tools import schema_tools, search_tools, traversal_tools, aggregation_tools
from .graph.client import GraphClient
from .graph.schema_manager import SchemaManager

# Load environment
load_dotenv()

# Configure logging
logging.basicConfig(
    level=getattr(logging, os.getenv("LOG_LEVEL", "INFO")),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize MCP server
mcp = FastMCP(os.getenv("SERVER_NAME", "GraphRAG-MCP"))

# Initialize graph client
graph_client = GraphClient(
    uri=os.getenv("NEO4J_URI"),
    user=os.getenv("NEO4J_USER"),
    password=os.getenv("NEO4J_PASSWORD"),
    database=os.getenv("NEO4J_DATABASE", "neo4j")
)

# Initialize schema manager
schema_manager = SchemaManager(graph_client)

# Register all tools
schema_tools.register(mcp, schema_manager)
search_tools.register(mcp, graph_client)
traversal_tools.register(mcp, graph_client)
aggregation_tools.register(mcp, graph_client)

@mcp.startup()
async def startup():
    """Initialize connections and cache on startup"""
    try:
        # Test Neo4j connection
        await graph_client.verify_connection()
        logger.info("Neo4j connection verified")
        
        # Load and cache schema
        await schema_manager.refresh_cache()
        logger.info("Schema cache loaded")
        
        # Log available tools
        logger.info(f"MCP server started with {len(mcp.tools)} tools")
        
    except Exception as e:
        logger.error(f"Startup failed: {e}")
        raise

@mcp.shutdown()
async def shutdown():
    """Clean up connections on shutdown"""
    await graph_client.close()
    logger.info("MCP server shutdown complete")

# Entry point
def run():
    """Run the MCP server"""
    mcp.run(port=int(os.getenv("SERVER_PORT", 8000)))
```

---

## MCP Tools API

### Schema Discovery Tools

```python
# src/tools/schema_tools.py

@mcp.tool
def get_schema() -> str:
    """
    Get the complete graph schema including all entity types, relationship types, and their properties.
    
    This tool should typically be called first to understand the graph structure before querying.
    
    Returns:
        JSON object with:
        - entity_types: List of node labels with their properties and data types
        - relationship_types: List of relationship types with their properties
        - statistics: Count of nodes and relationships by type
        - indexes: Available indexes for optimized queries
        
    Example Response:
    {
        "entity_types": {
            "Incident": {
                "properties": {
                    "id": "string",
                    "type": "string",
                    "severity": "integer",
                    "status": "string",
                    "timestamp": "datetime"
                },
                "count": 1543
            },
            "Resource": {...},
            "Department": {...}
        },
        "relationship_types": {
            "RESPONDED_TO": {
                "from": ["Department", "Resource"],
                "to": ["Incident"],
                "properties": {
                    "response_time": "integer",
                    "role": "string"
                },
                "count": 4521
            }
        },
        "indexes": [
            {"type": "Incident", "property": "id", "unique": true},
            {"type": "Incident", "property": "timestamp", "unique": false}
        ]
    }
    """
    return schema_manager.get_cached_schema()

@mcp.tool
def describe_entity_type(
    entity_type: str
) -> str:
    """
    Get detailed information about a specific entity type.
    
    Args:
        entity_type: The node label to describe (e.g., "Incident", "Resource")
        
    Returns:
        Detailed schema for the entity type including:
        - All properties with types and constraints
        - Common values for categorical properties
        - Related entity types via relationships
        - Sample entities
    """
    return schema_manager.describe_entity(entity_type)

@mcp.tool
def describe_relationship_type(
    relationship_type: str
) -> str:
    """
    Get detailed information about a specific relationship type.
    
    Args:
        relationship_type: The relationship type (e.g., "RESPONDED_TO", "ALLOCATED_TO")
        
    Returns:
        Detailed information including:
        - Source and target entity types
        - Properties with types
        - Cardinality patterns
        - Common traversal patterns
    """
    return schema_manager.describe_relationship(relationship_type)
```

### Entity Search Tools

```python
# src/tools/search_tools.py

@mcp.tool
def search_entities(
    entity_type: str,
    properties: Dict[str, Any] = None,
    contains_text: str = None,
    limit: int = 20,
    skip: int = 0
) -> str:
    """
    Search for entities by type and properties.
    
    Args:
        entity_type: The node label to search (e.g., "Incident", "Resource")
        properties: Dict of property filters. Supports:
                   - Exact match: {"status": "active"}
                   - Range: {"severity": {"min": 3, "max": 5}}
                   - List: {"type": ["fire", "hurricane"]}
                   - Contains: {"description": {"contains": "evacuation"}}
        contains_text: Text to search across all string properties
        limit: Maximum results to return (max 100)
        skip: Number of results to skip for pagination
        
    Returns:
        JSON array of matching entities with all properties
        
    Example Call:
        search_entities(
            entity_type="Incident",
            properties={"severity": {"min": 4}, "status": "active"},
            limit=10
        )
    """
    cypher = build_search_query(entity_type, properties, contains_text)
    return graph_client.execute_query(cypher, limit=limit, skip=skip)

@mcp.tool
def get_entity(
    entity_type: str,
    entity_id: str,
    include_relationships: bool = False
) -> str:
    """
    Get a specific entity by type and ID.
    
    Args:
        entity_type: The node label
        entity_id: The unique identifier
        include_relationships: If true, include all connected entities
        
    Returns:
        Complete entity data with optional relationships
        
    Example Call:
        get_entity("Incident", "INC-2024-1543", include_relationships=True)
    """
    if include_relationships:
        cypher = f"""
        MATCH (n:{entity_type} {{id: $entity_id}})
        OPTIONAL MATCH (n)-[r]-(connected)
        RETURN n as entity,
               collect({{
                   relationship: type(r),
                   direction: CASE WHEN startNode(r) = n THEN 'outgoing' ELSE 'incoming' END,
                   connected_entity: connected,
                   properties: properties(r)
               }}) as relationships
        """
    else:
        cypher = f"""
        MATCH (n:{entity_type} {{id: $entity_id}})
        RETURN n as entity
        """
    
    return graph_client.execute_query(cypher, {"entity_id": entity_id})

@mcp.tool
def find_entities_by_time_range(
    entity_type: str,
    start_time: str,
    end_time: str,
    time_property: str = "timestamp"
) -> str:
    """
    Find entities within a time range.
    
    Args:
        entity_type: The node label to search
        start_time: ISO format start time (e.g., "2024-10-01T00:00:00")
        end_time: ISO format end time
        time_property: The property containing the timestamp
        
    Returns:
        Entities within the time range, ordered by time
    """
    cypher = f"""
    MATCH (n:{entity_type})
    WHERE n.{time_property} >= datetime($start_time) 
      AND n.{time_property} <= datetime($end_time)
    RETURN n
    ORDER BY n.{time_property}
    """
    return graph_client.execute_query(cypher, {
        "start_time": start_time,
        "end_time": end_time
    })
```

### Graph Traversal Tools

```python
# src/tools/traversal_tools.py

@mcp.tool
def get_neighbors(
    entity_type: str,
    entity_id: str,
    relationship_types: List[str] = None,
    direction: str = "both",
    depth: int = 1
) -> str:
    """
    Get neighbors of an entity with optional filtering.
    
    Args:
        entity_type: Starting node type
        entity_id: Starting node ID
        relationship_types: List of relationship types to traverse (None = all)
        direction: "incoming", "outgoing", or "both"
        depth: How many hops to traverse (1-5)
        
    Returns:
        Connected entities grouped by relationship type and depth
        
    Example Call:
        get_neighbors(
            "Incident", 
            "INC-2024-1543",
            relationship_types=["RESPONDED_TO", "ALLOCATED_TO"],
            depth=2
        )
    """
    depth = min(depth, int(os.getenv("MAX_TRAVERSAL_DEPTH", 5)))
    
    relationship_pattern = format_relationship_pattern(relationship_types, direction)
    
    cypher = f"""
    MATCH path = (n:{entity_type} {{id: $entity_id}}){relationship_pattern}(connected)
    WHERE length(path) <= $depth
    RETURN 
        length(path) as depth,
        type(last(relationships(path))) as relationship_type,
        connected,
        properties(last(relationships(path))) as relationship_properties
    ORDER BY depth, relationship_type
    """
    
    return graph_client.execute_query(cypher, {
        "entity_id": entity_id,
        "depth": depth
    })

@mcp.tool
def traverse_pattern(
    pattern: str,
    parameters: Dict[str, Any] = None,
    limit: int = 100
) -> str:
    """
    Traverse the graph using a pattern with bound parameters.
    
    Args:
        pattern: Cypher-like pattern (simplified):
                "(:Incident)-[:RESPONDED_TO]->(:Department)"
                "(:Resource)-[:ALLOCATED_TO*1..3]->(:Incident)"
        parameters: Values for pattern variables
        limit: Maximum paths to return
        
    Returns:
        Matching paths with all entities and relationships
        
    Example Call:
        traverse_pattern(
            "(:Incident {severity: $severity})<-[:RESPONDED_TO]-(:FireStation)",
            {"severity": 5}
        )
        
    Note: The pattern is simplified - the server translates it to valid Cypher
    """
    # Validate and sanitize pattern
    safe_pattern = validate_pattern(pattern)
    
    cypher = f"""
    MATCH path = {safe_pattern}
    RETURN path
    LIMIT $limit
    """
    
    params = parameters or {}
    params["limit"] = min(limit, int(os.getenv("MAX_LIMIT", 1000)))
    
    return graph_client.execute_query(cypher, params)

@mcp.tool
def find_paths(
    from_type: str,
    from_id: str,
    to_type: str,
    to_id: str,
    max_depth: int = 5,
    relationship_types: List[str] = None
) -> str:
    """
    Find paths between two specific entities.
    
    Args:
        from_type: Starting node type
        from_id: Starting node ID
        to_type: Target node type
        to_id: Target node ID
        max_depth: Maximum path length
        relationship_types: Allowed relationship types (None = all)
        
    Returns:
        All paths between entities with complete details
    """
    max_depth = min(max_depth, int(os.getenv("MAX_TRAVERSAL_DEPTH", 5)))
    
    rel_filter = ""
    if relationship_types:
        rel_filter = f"[:{

'.join(relationship_types)}*1..{max_depth}]"
    else:
        rel_filter = f"[*1..{max_depth}]"
    
    cypher = f"""
    MATCH path = (from:{from_type} {{id: $from_id}}){rel_filter}(to:{to_type} {{id: $to_id}})
    RETURN path
    ORDER BY length(path)
    LIMIT 10
    """
    
    return graph_client.execute_query(cypher, {
        "from_id": from_id,
        "to_id": to_id
    })
```

### Aggregation Tools

```python
# src/tools/aggregation_tools.py

@mcp.tool
def aggregate_by_type(
    entity_type: str,
    group_by: str,
    metrics: List[str] = None,
    filters: Dict[str, Any] = None
) -> str:
    """
    Aggregate entities by a property with optional metrics.
    
    Args:
        entity_type: Node type to aggregate
        group_by: Property to group by
        metrics: List of metrics to calculate:
                ["count", "avg_severity", "max_duration", "min_response_time"]
        filters: Pre-aggregation filters
        
    Returns:
        Aggregated results with requested metrics
        
    Example Call:
        aggregate_by_type(
            "Incident",
            group_by="type",
            metrics=["count", "avg_severity"],
            filters={"status": "resolved"}
        )
    """
    where_clause = build_where_clause(filters) if filters else ""
    metrics_clause = build_metrics_clause(metrics or ["count"])
    
    cypher = f"""
    MATCH (n:{entity_type})
    {where_clause}
    RETURN n.{group_by} as group_value,
           {metrics_clause}
    ORDER BY count DESC
    """
    
    return graph_client.execute_query(cypher)

@mcp.tool
def calculate_statistics(
    entity_type: str,
    numeric_property: str,
    group_by: str = None
) -> str:
    """
    Calculate statistical measures for a numeric property.
    
    Args:
        entity_type: Node type
        numeric_property: Property to analyze
        group_by: Optional grouping property
        
    Returns:
        Statistical summary including:
        - count, sum, min, max, avg, stdev
        - percentiles (25th, 50th, 75th, 90th, 95th)
        - distribution histogram
    """
    if group_by:
        cypher = f"""
        MATCH (n:{entity_type})
        WHERE n.{numeric_property} IS NOT NULL
        WITH n.{group_by} as group_value, collect(n.{numeric_property}) as values
        RETURN group_value,
               size(values) as count,
               reduce(s = 0, x IN values | s + x) as sum,
               min(values) as min,
               max(values) as max,
               avg(values) as mean,
               stdev(values) as stdev,
               percentileDisc(values, 0.25) as p25,
               percentileDisc(values, 0.50) as p50,
               percentileDisc(values, 0.75) as p75,
               percentileDisc(values, 0.90) as p90,
               percentileDisc(values, 0.95) as p95
        ORDER BY group_value
        """
    else:
        cypher = f"""
        MATCH (n:{entity_type})
        WHERE n.{numeric_property} IS NOT NULL
        WITH collect(n.{numeric_property}) as values
        RETURN size(values) as count,
               reduce(s = 0, x IN values | s + x) as sum,
               min(values) as min,
               max(values) as max,
               avg(values) as mean,
               stdev(values) as stdev,
               percentileDisc(values, 0.25) as p25,
               percentileDisc(values, 0.50) as p50,
               percentileDisc(values, 0.75) as p75,
               percentileDisc(values, 0.90) as p90,
               percentileDisc(values, 0.95) as p95
        """
    
    return graph_client.execute_query(cypher)

@mcp.tool
def find_patterns(
    base_entity: str,
    pattern_type: str,
    threshold: Dict[str, Any] = None
) -> str:
    """
    Find common patterns in the graph.
    
    Args:
        base_entity: Starting entity type
        pattern_type: Type of pattern to find:
                     - "co_occurrence": Entities that frequently appear together
                     - "temporal_sequence": Common sequences over time
                     - "resource_bottleneck": Over-utilized resources
                     - "coordination_gap": Gaps in departmental coordination
                     - "cascade_failure": Failure cascades across resources
                     - "response_delay": Delayed response patterns
        threshold: Pattern-specific thresholds:
                  - co_occurrence: {"min_frequency": 3}
                  - resource_bottleneck: {"utilization_pct": 80}

    Returns:
        Detected patterns with statistical significance and recommendations

    Example Call:
        find_patterns(
            "Resource",
            "resource_bottleneck",
            {"utilization_pct": 75, "time_window": "7d"}
        )
    """
    # Implementation depends on pattern_type
    if pattern_type == "co_occurrence":
        return find_co_occurrence_patterns(base_entity, threshold)
    elif pattern_type == "temporal_sequence":
        return find_temporal_sequences(base_entity, threshold)
    elif pattern_type == "resource_bottleneck":
        return find_resource_bottlenecks(threshold)
    elif pattern_type == "coordination_gap":
        return find_coordination_gaps(base_entity, threshold)
    elif pattern_type == "cascade_failure":
        return find_cascade_patterns(base_entity, threshold)
    elif pattern_type == "response_delay":
        return find_response_delays(threshold)
    else:
        return json.dumps({"error": f"Unknown pattern type: {pattern_type}"})

@mcp.tool
def execute_cypher(
    query: str,
    parameters: Dict[str, Any] = None,
    limit: int = 100
) -> str:
    """
    Execute a validated Cypher query with safety checks.

    This tool allows the LLM to run custom Cypher when the built-in tools
    are insufficient. All queries are validated for safety before execution.

    Args:
        query: Cypher query (read-only operations only)
        parameters: Query parameters
        limit: Maximum results to return

    Returns:
        Query results with execution metadata

    Safety Features:
        - Read-only operations only (MATCH, RETURN, WITH, etc.)
        - No data modification (CREATE, DELETE, SET blocked)
        - Automatic LIMIT injection if missing
        - Query timeout enforcement
        - Parameter sanitization

    Example Call:
        execute_cypher(
            "MATCH (i:Incident)-[r:RESPONDED_TO]->(d:Department)
             WHERE i.severity >= $min_severity
             RETURN i.id, i.type, d.name, r.response_time
             ORDER BY r.response_time DESC",
            {"min_severity": 4},
            50
        )
    """
    # Validate query safety
    if not is_safe_cypher_query(query):
        return json.dumps({
            "error": "Query contains unsafe operations",
            "allowed_operations": ["MATCH", "RETURN", "WITH", "UNWIND", "ORDER BY", "LIMIT"]
        })

    # Add LIMIT if missing and not a COUNT query
    if "LIMIT" not in query.upper() and "COUNT(" not in query.upper():
        query = query.rstrip(';') + f" LIMIT {limit}"

    return graph_client.execute_query(query, parameters or {})
```

### Administrative Tools

```python
# src/tools/admin_tools.py

@mcp.tool
def get_health() -> str:
    """
    Get server and database health status.

    Returns:
        Comprehensive health report including:
        - Database connectivity
        - Query performance metrics
        - Cache status
        - Memory usage
        - Error rates
    """
    health_data = {
        "server_status": "healthy",
        "database_connection": False,
        "query_cache_hit_rate": 0,
        "avg_query_time_ms": 0,
        "total_queries_processed": 0,
        "error_rate_24h": 0,
        "last_schema_refresh": None
    }

    try:
        # Test database connection
        result = graph_client.execute_query("RETURN 1 as test")
        health_data["database_connection"] = True

        # Get performance metrics
        health_data.update(get_performance_metrics())

        # Get schema cache status
        health_data.update(schema_manager.get_cache_status())

    except Exception as e:
        health_data["server_status"] = "degraded"
        health_data["error_message"] = str(e)

    return json.dumps(health_data, indent=2)

@mcp.tool
def refresh_cache() -> str:
    """
    Refresh the schema cache and performance statistics.

    Returns:
        Cache refresh results and updated statistics
    """
    try:
        old_cache_size = schema_manager.get_cache_size()

        # Refresh schema cache
        await schema_manager.refresh_cache()

        # Clear query performance cache
        graph_client.clear_performance_cache()

        new_cache_size = schema_manager.get_cache_size()

        return json.dumps({
            "status": "success",
            "cache_refreshed": True,
            "old_cache_size": old_cache_size,
            "new_cache_size": new_cache_size,
            "refresh_timestamp": datetime.utcnow().isoformat()
        })

    except Exception as e:
        return json.dumps({
            "status": "error",
            "error_message": str(e)
        })

@mcp.tool
def get_query_suggestions(
    entity_type: str,
    intent: str = None
) -> str:
    """
    Get suggested queries for common operations on an entity type.

    Args:
        entity_type: The entity type to get suggestions for
        intent: Optional intent like "analysis", "relationships", "patterns"

    Returns:
        List of suggested query patterns with descriptions
    """
    suggestions = query_pattern_manager.get_suggestions(entity_type, intent)

    return json.dumps({
        "entity_type": entity_type,
        "intent": intent,
        "suggestions": suggestions,
        "examples": query_pattern_manager.get_examples(entity_type)
    }, indent=2)
```

---

## Data Pipeline

### Data Ingestion Architecture

The GraphRAG MCP server supports multiple data sources and formats commonly found in emergency management systems:

```mermaid
graph TB
    subgraph "Data Sources"
        A[Incident Database<br/>SQL/REST API]
        B[Resource Management<br/>CSV/Excel]
        C[Department Systems<br/>JSON/XML]
        D[Historical Reports<br/>PDF/Documents]
        E[Real-time Feeds<br/>WebSocket/MQTT]
    end

    subgraph "Ingestion Pipeline"
        F[Data Connectors]
        G[Schema Validation]
        H[Entity Resolution]
        I[Relationship Extraction]
        J[Quality Checks]
    end

    subgraph "Graph Database"
        K[(Neo4j Graph)]
        L[Vector Embeddings<br/>Optional]
    end

    A --> F
    B --> F
    C --> F
    D --> F
    E --> F

    F --> G
    G --> H
    H --> I
    I --> J
    J --> K
    J --> L
```

### Data Pipeline Implementation

```python
# data/ingestion/pipeline.py

class EmergencyDataPipeline:
    """
    Comprehensive data ingestion pipeline for emergency management data.
    """

    def __init__(self, graph_client: GraphClient):
        self.graph_client = graph_client
        self.transformers = self._load_transformers()
        self.validators = self._load_validators()

    async def ingest_incidents(self, source_config: Dict[str, Any]):
        """
        Ingest incident data from various sources.

        Supported sources:
        - SQL databases (via SQLAlchemy)
        - REST APIs (via requests)
        - CSV/Excel files (via pandas)
        - JSON files
        """

        # Example for SQL database
        if source_config["type"] == "sql":
            query = """
            SELECT
                incident_id, incident_type, severity, status,
                location_lat, location_lon, timestamp,
                description, affected_population
            FROM incidents
            WHERE last_updated >= %s
            """

            raw_data = await self._fetch_sql_data(
                source_config["connection_string"],
                query,
                [source_config.get("since", "1900-01-01")]
            )

            # Transform to graph format
            incidents = []
            for row in raw_data:
                incident = {
                    "id": f"INC-{row['incident_id']}",
                    "type": row["incident_type"],
                    "severity": row["severity"],
                    "status": row["status"],
                    "location": {
                        "lat": row["location_lat"],
                        "lon": row["location_lon"]
                    },
                    "timestamp": row["timestamp"],
                    "description": row["description"],
                    "affected_population": row["affected_population"]
                }
                incidents.append(incident)

            # Validate and load
            valid_incidents = await self._validate_entities(incidents, "Incident")
            await self._load_entities(valid_incidents, "Incident")

            return {"loaded": len(valid_incidents), "errors": len(incidents) - len(valid_incidents)}

    async def ingest_resources(self, csv_file_path: str):
        """
        Ingest resource data from CSV files.
        """
        df = pd.read_csv(csv_file_path)

        resources = []
        for _, row in df.iterrows():
            resource = {
                "id": f"RES-{row['resource_id']}",
                "type": row["resource_type"],
                "category": row["category"],
                "capacity": row["capacity"],
                "status": row["status"],
                "location": row["station_location"],
                "department_id": f"DEPT-{row['department_id']}"
            }
            resources.append(resource)

        valid_resources = await self._validate_entities(resources, "Resource")
        await self._load_entities(valid_resources, "Resource")

        return {"loaded": len(valid_resources)}

    async def extract_relationships(self, relationship_type: str, config: Dict[str, Any]):
        """
        Extract relationships between entities.

        Examples:
        - RESPONDED_TO: Resource -> Incident (from response logs)
        - ALLOCATED_TO: Resource -> Incident (from allocation records)
        - MANAGES: Department -> Resource (from organizational data)
        """

        if relationship_type == "RESPONDED_TO":
            # Extract from response logs
            cypher = """
            MATCH (r:Resource {id: $resource_id}), (i:Incident {id: $incident_id})
            CREATE (r)-[:RESPONDED_TO {
                response_time: $response_time,
                role: $role,
                timestamp: datetime($timestamp)
            }]->(i)
            """

            # Process response log data
            for response in config["response_data"]:
                await self.graph_client.execute_query(cypher, {
                    "resource_id": response["resource_id"],
                    "incident_id": response["incident_id"],
                    "response_time": response["response_time"],
                    "role": response["role"],
                    "timestamp": response["timestamp"]
                })

        elif relationship_type == "ALLOCATED_TO":
            # Similar pattern for allocations
            pass

    async def _validate_entities(self, entities: List[Dict], entity_type: str) -> List[Dict]:
        """Validate entities against schema requirements."""
        validator = self.validators.get(entity_type)
        if not validator:
            return entities

        valid_entities = []
        for entity in entities:
            if validator.validate(entity):
                valid_entities.append(entity)
            else:
                logger.warning(f"Invalid {entity_type}: {validator.errors}")

        return valid_entities

    async def _load_entities(self, entities: List[Dict], entity_type: str):
        """Load validated entities into the graph."""
        batch_size = 1000

        for i in range(0, len(entities), batch_size):
            batch = entities[i:i + batch_size]

            cypher = f"""
            UNWIND $entities as entity
            CREATE (n:{entity_type})
            SET n += entity
            """

            await self.graph_client.execute_query(cypher, {"entities": batch})
```

### Data Schema Definition

```yaml
# config/schema.yaml

emergency_management:
  entities:
    Incident:
      properties:
        id: {type: string, required: true, unique: true}
        type: {type: string, required: true, enum: [fire, hurricane, earthquake, flood, medical, other]}
        severity: {type: integer, required: true, min: 1, max: 5}
        status: {type: string, required: true, enum: [active, resolved, pending, monitoring]}
        timestamp: {type: datetime, required: true}
        location: {type: object, properties: {lat: float, lon: float}}
        description: {type: string, max_length: 2000}
        affected_population: {type: integer, min: 0}
        estimated_cost: {type: float, min: 0}

      relationships:
        - type: RESPONDED_TO
          from: [Resource, Department]
          properties:
            response_time: {type: integer, min: 0}
            role: {type: string}
            timestamp: {type: datetime}

        - type: LOCATED_IN
          to: [GeographicArea]
          properties:
            distance_from_center: {type: float}

    Resource:
      properties:
        id: {type: string, required: true, unique: true}
        type: {type: string, required: true, enum: [fire_truck, ambulance, police_car, helicopter, boat]}
        category: {type: string, required: true, enum: [vehicle, personnel, equipment, facility]}
        capacity: {type: integer, min: 1}
        status: {type: string, required: true, enum: [available, deployed, maintenance, retired]}
        location: {type: string}
        acquisition_date: {type: date}

      relationships:
        - type: BELONGS_TO
          to: [Department]

        - type: STATIONED_AT
          to: [Facility]

    Department:
      properties:
        id: {type: string, required: true, unique: true}
        name: {type: string, required: true}
        type: {type: string, required: true, enum: [fire, police, medical, emergency_management]}
        budget: {type: float, min: 0}
        personnel_count: {type: integer, min: 0}
        jurisdiction: {type: string}

      relationships:
        - type: COORDINATES_WITH
          to: [Department]
          properties:
            coordination_type: {type: string}
            frequency: {type: string}

  indexes:
    - entity: Incident
      properties: [id, type, timestamp, severity]

    - entity: Resource
      properties: [id, type, status]

    - entity: Department
      properties: [id, name, type]

  constraints:
    - type: unique
      entity: Incident
      property: id

    - type: unique
      entity: Resource
      property: id
```

### Incremental Data Updates

```python
# data/ingestion/incremental.py

class IncrementalSync:
    """
    Handle incremental updates to keep graph data current.
    """

    def __init__(self, pipeline: EmergencyDataPipeline):
        self.pipeline = pipeline
        self.last_sync_timestamps = {}

    async def sync_incidents(self):
        """
        Sync incident updates since last run.
        """
        last_sync = self.last_sync_timestamps.get("incidents", "1900-01-01")

        # Get updates since last sync
        updates = await self._fetch_incident_updates(since=last_sync)

        # Process updates, deletions, and new records
        stats = {
            "new": 0,
            "updated": 0,
            "deleted": 0
        }

        for update in updates:
            if update["action"] == "INSERT":
                await self._create_incident(update["data"])
                stats["new"] += 1

            elif update["action"] == "UPDATE":
                await self._update_incident(update["data"])
                stats["updated"] += 1

            elif update["action"] == "DELETE":
                await self._delete_incident(update["incident_id"])
                stats["deleted"] += 1

        # Update sync timestamp
        self.last_sync_timestamps["incidents"] = datetime.utcnow().isoformat()

        return stats

    async def _create_incident(self, incident_data: Dict):
        """Create new incident node."""
        cypher = """
        CREATE (i:Incident)
        SET i += $properties
        """
        await self.pipeline.graph_client.execute_query(cypher, {
            "properties": incident_data
        })

    async def _update_incident(self, incident_data: Dict):
        """Update existing incident."""
        cypher = """
        MATCH (i:Incident {id: $incident_id})
        SET i += $properties,
            i.last_updated = datetime()
        """
        await self.pipeline.graph_client.execute_query(cypher, {
            "incident_id": incident_data["id"],
            "properties": {k: v for k, v in incident_data.items() if k != "id"}
        })
```

## Usage Examples

### End-to-End LLM Orchestration Scenarios

The following examples demonstrate how the Foundation Model orchestrates multiple MCP tool calls to answer complex emergency management questions.

#### Example 1: Multi-hop Resource Analysis

**User Question**: "Which fire stations responded to multiple incidents during Hurricane Milton, and what was their average response time?"

**LLM Orchestration Flow**:

```python
# Step 1: LLM calls get_schema() to understand available entities
{
  "tool": "get_schema",
  "reasoning": "Need to understand the graph structure to plan multi-hop query"
}

# Response provides entity types: Incident, Resource, Department, etc.
# and relationship types: RESPONDED_TO, BELONGS_TO, etc.

# Step 2: LLM searches for Hurricane Milton incident
{
  "tool": "search_entities",
  "args": {
    "entity_type": "Incident",
    "properties": {"type": "hurricane"},
    "contains_text": "Milton"
  },
  "reasoning": "Finding the Hurricane Milton incident to use as starting point"
}

# Response: Hurricane Milton incident with ID "INC-2024-MILTON"

# Step 3: LLM finds all fire stations that responded
{
  "tool": "traverse_pattern",
  "args": {
    "pattern": "(:Incident {id: 'INC-2024-MILTON'})<-[:RESPONDED_TO]-(r:Resource)-[:BELONGS_TO]->(d:Department {type: 'fire'})",
    "limit": 100
  },
  "reasoning": "Traversing from Hurricane Milton to fire department resources via response relationships"
}

# Response: List of fire stations with response details

# Step 4: LLM aggregates to find stations with multiple responses
{
  "tool": "aggregate_by_type",
  "args": {
    "entity_type": "Resource",
    "group_by": "station_id",
    "metrics": ["count", "avg_response_time"],
    "filters": {"department_type": "fire", "incident_related_to": "INC-2024-MILTON"}
  },
  "reasoning": "Aggregating response data to find stations with multiple responses and calculate average response times"
}

# Final Response: "During Hurricane Milton, 3 fire stations responded to multiple incidents:
# - Station 15: 4 responses, avg 12.5 minutes
# - Station 23: 3 responses, avg 8.7 minutes
# - Station 31: 5 responses, avg 15.2 minutes"
```

#### Example 2: Pattern Detection and Resource Optimization

**User Question**: "Are there patterns of resource bottlenecks that could indicate we need better coordination between departments?"

**LLM Orchestration Flow**:

```python
# Step 1: LLM analyzes resource utilization patterns
{
  "tool": "find_patterns",
  "args": {
    "base_entity": "Resource",
    "pattern_type": "resource_bottleneck",
    "threshold": {"utilization_pct": 75, "time_window": "30d"}
  },
  "reasoning": "Looking for over-utilized resources that might indicate systemic issues"
}

# Step 2: LLM examines coordination gaps
{
  "tool": "find_patterns",
  "args": {
    "base_entity": "Department",
    "pattern_type": "coordination_gap",
    "threshold": {"response_overlap": 0.8, "shared_incidents": 5}
  },
  "reasoning": "Identifying cases where departments responded to same incidents without coordination"
}

# Step 3: LLM gets detailed statistics on problematic resources
{
  "tool": "calculate_statistics",
  "args": {
    "entity_type": "Resource",
    "numeric_property": "deployment_hours",
    "group_by": "department_id"
  },
  "reasoning": "Getting utilization statistics by department to understand workload distribution"
}

# Step 4: LLM examines temporal patterns
{
  "tool": "find_entities_by_time_range",
  "args": {
    "entity_type": "Incident",
    "start_time": "2024-09-01T00:00:00",
    "end_time": "2024-10-31T23:59:59",
    "time_property": "timestamp"
  },
  "reasoning": "Analyzing incident timing to understand peak demand periods"
}

# Final Response: "Analysis reveals 3 coordination bottlenecks:
# 1. Fire Station 15 and Police District 3 both respond to medical emergencies
#    without coordination (18 duplicate responses in 30 days)
# 2. Ambulance Fleet A operates at 89% capacity during 2-6 PM shifts
# 3. Hurricane response shows 23% overlap in resource deployment between
#    Fire and Emergency Management departments
#
# Recommendations: Implement shared dispatch system for medical emergencies,
# add swing shift ambulances, create joint command protocol for major incidents"
```

### Assistant Integration Example

**How the Foundation Model orchestrates these calls within the existing architecture**:

```mermaid
sequenceDiagram
    participant User as 👤 User
    participant FM as 🧠 Foundation Model<br/>Service
    participant MCP as 🔗 MCP Service
    participant GraphRAG as 📊 GraphRAG<br/>MCP Server
    participant Neo4j as 🗄️ Neo4j<br/>Database

    Note over User, Neo4j: Multi-hop Emergency Management Query

    User->>+FM: "Which fire stations responded to multiple<br/>incidents during Hurricane Milton?"

    FM->>User: STATUS: "Analyzing emergency management query..."
    FM->>User: REASONING: "Planning multi-step graph analysis approach"

    FM->>+MCP: get_tool_definitions()
    MCP-->>-FM: [search_entities, get_neighbors, aggregate_by_type, ...]

    Note over FM: OpenAI Function Calling Decision
    FM->>FM: chat.completions.create(<br/>tools=graph_tools, tool_choice="auto")

    FM->>User: REASONING: "Executing 3 graph operations"

    rect rgb(230, 245, 255)
        Note over FM, Neo4j: Step 1: Find Hurricane Milton
        FM->>+MCP: call_tool_with_attribution(<br/>"search_entities",<br/>{entity_type: "Incident", contains_text: "Milton"})
        MCP->>+GraphRAG: search_entities(Incident, "Milton")
        GraphRAG->>+Neo4j: MATCH (i:Incident)<br/>WHERE i.description CONTAINS "Milton"
        Neo4j-->>-GraphRAG: [{"id": "INC-2024-MILTON", ...}]
        GraphRAG-->>-MCP: {"success": true, "data": [...]}
        MCP->>User: SOURCES: "Emergency Entity Search<br/>Response: 12ms ✅"
        MCP-->>-FM: {"success": true, "data": "INC-2024-MILTON"}
    end

    rect rgb(240, 255, 240)
        Note over FM, Neo4j: Step 2: Find Connected Fire Stations
        FM->>+MCP: call_tool_with_attribution(<br/>"get_neighbors",<br/>{entity_id: "INC-2024-MILTON", depth: 2})
        MCP->>+GraphRAG: get_neighbors(INC-2024-MILTON, depth=2)
        GraphRAG->>+Neo4j: MATCH path = (i:Incident {id: $id})<br/>-[:RESPONDED_TO*1..2]-(r:Resource)<br/>-[:BELONGS_TO]-(d:Department {type: 'fire'})
        Neo4j-->>-GraphRAG: [fire_station_responses...]
        GraphRAG-->>-MCP: {"success": true, "data": [...]}
        MCP->>User: SOURCES: "Connected Emergency Resources<br/>Response: 34ms ✅"
        MCP-->>-FM: {"success": true, "data": "[fire_stations...]"}
    end

    rect rgb(255, 240, 255)
        Note over FM, Neo4j: Step 3: Calculate Response Statistics
        FM->>+MCP: call_tool_with_attribution(<br/>"aggregate_by_type",<br/>{group_by: "station_id", metrics: ["count", "avg_response_time"]})
        MCP->>+GraphRAG: aggregate_by_type(Resource, group_by=station_id)
        GraphRAG->>+Neo4j: MATCH (r:Resource)-[:RESPONDED_TO]->(i:Incident)<br/>WITH r.station_id as station, count(*) as responses<br/>WHERE responses > 1<br/>RETURN station, responses, avg(response_time)
        Neo4j-->>-GraphRAG: [{"station": "15", "responses": 4, "avg_time": 12.5}, ...]
        GraphRAG-->>-MCP: {"success": true, "data": [...]}
        MCP->>User: SOURCES: "Emergency Statistics and Metrics<br/>Response: 28ms ✅"
        MCP-->>-FM: {"success": true, "data": "[station_stats...]"}
    end

    Note over FM: Integrate Results & Generate Response
    FM->>User: REASONING: "Integrating graph analysis results"

    FM->>FM: chat.completions.create(<br/>messages=enhanced_context, stream=True)

    loop Streaming Response
        FM->>User: CONTENT: "During Hurricane Milton, 3 fire stations..."
        FM->>User: CONTENT: "Station 15: 4 responses, avg 12.5 minutes..."
        FM->>User: CONTENT: "Station 23: 3 responses, avg 8.7 minutes..."
    end

    FM->>User: STATUS: "Response complete ✅"

    Note over User, Neo4j: Complete Multi-hop Analysis with Rich Attribution
```

**Code Implementation**:

```python
# In the Foundation Model Service (foundation_model.py)

async def generate_response_with_tools(self, messages, conversation_id):
    # STATUS message to user
    yield StreamMessage(StreamType.STATUS, {"message": "Analyzing emergency management query..."})

    # REASONING transparency
    yield StreamMessage(StreamType.REASONING, {"step": "Planning multi-step graph analysis approach"})

    # Get available graph tools
    if self.mcp_service and self.mcp_service.tools_available:
        tools = self.mcp_service.get_tool_definitions()

        # Foundation Model makes intelligent tool selection
        response = await self.client.chat.completions.create(
            model=self.deployment_name,
            messages=messages,
            tools=tools,
            tool_choice="auto"
        )

        if response.choices[0].message.tool_calls:
            yield StreamMessage(StreamType.REASONING,
                {"step": f"Executing {len(response.choices[0].message.tool_calls)} graph operations"})

            # Execute each tool call and generate SOURCES attribution
            for tool_call in response.choices[0].message.tool_calls:
                async for sources_message in self.mcp_service.call_tool_with_attribution(
                    tool_call.function.name,
                    json.loads(tool_call.function.arguments)
                ):
                    yield sources_message

                # Tool execution happens here...

            yield StreamMessage(StreamType.REASONING, {"step": "Integrating graph analysis results"})

    # Generate final integrated response
    final_response = await self.client.chat.completions.create(
        model=self.deployment_name,
        messages=enhanced_messages,  # Include tool results
        stream=True
    )

    async for chunk in final_response:
        if chunk.choices[0].delta.content:
            yield StreamMessage(StreamType.CONTENT,
                {"chunk": chunk.choices[0].delta.content})
```

---

## Testing & Validation

### Testing Strategy Overview

Comprehensive testing ensures the GraphRAG MCP server delivers reliable, accurate, and performant emergency management insights.

```mermaid
graph TB
    subgraph "Testing Pyramid"
        A[Unit Tests<br/>Tool Functions, Query Building]
        B[Integration Tests<br/>MCP Protocol, Database Operations]
        C[End-to-End Tests<br/>Full LLM Orchestration]
        D[Performance Tests<br/>Load Testing, Query Optimization]
        E[Validation Tests<br/>Data Quality, Answer Accuracy]
    end

    A --> B
    B --> C
    C --> D
    D --> E

    subgraph "Test Data"
        F[Synthetic Emergency Data]
        G[Historical Incident Records]
        H[Stress Test Scenarios]
    end

    F --> A
    G --> B
    H --> D
```

### Unit Testing Framework

```python
# tests/test_tools.py

import pytest
import json
from unittest.mock import AsyncMock, MagicMock
from src.tools.schema_tools import get_schema, describe_entity_type
from src.tools.search_tools import search_entities, get_entity
from src.tools.traversal_tools import get_neighbors, traverse_pattern
from src.tools.aggregation_tools import aggregate_by_type, calculate_statistics

class TestSchemaTools:
    """Test schema discovery and description tools."""

    @pytest.fixture
    def mock_schema_manager(self):
        schema_manager = MagicMock()
        schema_manager.get_cached_schema.return_value = json.dumps({
            "entity_types": {
                "Incident": {"properties": {"id": "string", "type": "string"}, "count": 100},
                "Resource": {"properties": {"id": "string", "type": "string"}, "count": 50}
            },
            "relationship_types": {
                "RESPONDED_TO": {"from": ["Resource"], "to": ["Incident"], "count": 200}
            }
        })
        return schema_manager

    def test_get_schema_returns_valid_json(self, mock_schema_manager):
        """Test that get_schema returns valid JSON structure."""
        global schema_manager
        schema_manager = mock_schema_manager

        result = get_schema()
        parsed = json.loads(result)

        assert "entity_types" in parsed
        assert "relationship_types" in parsed
        assert "Incident" in parsed["entity_types"]
        assert parsed["entity_types"]["Incident"]["count"] == 100
```

---

## Deployment Guide

### Production Deployment Architecture

```mermaid
graph TB
    subgraph "Load Balancer"
        A[Azure Load Balancer]
    end

    subgraph "Application Layer"
        B[GraphRAG MCP Server 1<br/>Azure Container Instance]
        C[GraphRAG MCP Server 2<br/>Azure Container Instance]
        D[GraphRAG MCP Server 3<br/>Azure Container Instance]
    end

    subgraph "Database Layer"
        E[Neo4j Cluster<br/>Primary + 2 Read Replicas]
        F[Azure Blob Storage<br/>Backups & Data Files]
    end

    subgraph "Monitoring"
        G[Azure Monitor]
        H[Application Insights]
        I[Grafana Dashboard]
    end

    A --> B
    A --> C
    A --> D

    B --> E
    C --> E
    D --> E

    E --> F

    B --> G
    C --> G
    D --> G

    G --> H
    G --> I
```

---

## Assistant Integration

### Current Architecture Integration Points

The GraphRAG MCP server seamlessly integrates with the existing GraySkyGenAI Assistant architecture without requiring changes to the Foundation Model orchestration or streaming systems.

#### MCPService Integration

**Required changes to `backend/app/services/mcp_service.py`:**

```python
# Update semantic mapping for GraphRAG tools
self.semantic_mapping = {
    # GraphRAG Tools - User-friendly names
    "get_schema": "Emergency Management Knowledge Graph Schema",
    "search_entities": "Emergency Entity Search",
    "get_entity": "Emergency Entity Details",
    "get_neighbors": "Connected Emergency Resources",
    "traverse_pattern": "Emergency Response Pattern Analysis",
    "aggregate_by_type": "Emergency Statistics and Metrics",
    "calculate_statistics": "Emergency Performance Analytics",
    "find_patterns": "Emergency Response Pattern Detection",
    "execute_cypher": "Advanced Emergency Data Query",
    "get_health": "Graph Database Health Status"
}
```

**Update tool definitions for OpenAI function calling:**

```python
def get_tool_definitions(self) -> List[Dict[str, Any]]:
    """Get GraphRAG tool definitions for OpenAI function calling schema."""
    if not self.tools_available:
        return []

    return [
        {
            "name": "search_entities",
            "description": "Search for emergency management entities (incidents, resources, departments) with advanced filtering capabilities. Use this to find specific incidents by type, severity, status, or time period.",
            "parameters": {
                "type": "object",
                "properties": {
                    "entity_type": {
                        "type": "string",
                        "description": "Type of entity to search: 'Incident', 'Resource', 'Department'",
                        "enum": ["Incident", "Resource", "Department"]
                    },
                    "properties": {
                        "type": "object",
                        "description": "Filters - severity: {min: 3, max: 5}, status: 'active', type: 'hurricane'"
                    },
                    "contains_text": {
                        "type": "string",
                        "description": "Text to search across all properties"
                    },
                    "limit": {
                        "type": "integer",
                        "description": "Maximum results (default: 20, max: 100)"
                    }
                },
                "required": ["entity_type"]
            }
        },
        {
            "name": "get_neighbors",
            "description": "Find connected resources, departments, or incidents. Use to answer questions like 'What resources responded to this incident?' or 'What incidents did this department handle?'",
            "parameters": {
                "type": "object",
                "properties": {
                    "entity_type": {"type": "string", "description": "Starting entity type"},
                    "entity_id": {"type": "string", "description": "Starting entity ID"},
                    "relationship_types": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "Relationship types: 'RESPONDED_TO', 'BELONGS_TO', 'MANAGES'"
                    },
                    "depth": {
                        "type": "integer",
                        "description": "Traversal depth (1-5)",
                        "minimum": 1,
                        "maximum": 5
                    }
                },
                "required": ["entity_type", "entity_id"]
            }
        },
        {
            "name": "aggregate_by_type",
            "description": "Calculate statistics and metrics for emergency management analysis. Use for questions about average response times, incident counts by type, resource utilization patterns.",
            "parameters": {
                "type": "object",
                "properties": {
                    "entity_type": {"type": "string", "description": "Entity type to aggregate"},
                    "group_by": {"type": "string", "description": "Property to group by"},
                    "metrics": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "Metrics: 'count', 'avg_severity', 'avg_response_time'"
                    },
                    "filters": {
                        "type": "object",
                        "description": "Pre-aggregation filters"
                    }
                },
                "required": ["entity_type", "group_by"]
            }
        },
        {
            "name": "find_patterns",
            "description": "Detect patterns in emergency response operations. Use for identifying bottlenecks, coordination issues, or systemic problems requiring management attention.",
            "parameters": {
                "type": "object",
                "properties": {
                    "base_entity": {"type": "string", "description": "Starting entity type"},
                    "pattern_type": {
                        "type": "string",
                        "description": "Pattern type",
                        "enum": ["resource_bottleneck", "coordination_gap", "cascade_failure", "response_delay"]
                    },
                    "threshold": {
                        "type": "object",
                        "description": "Pattern-specific thresholds"
                    }
                },
                "required": ["base_entity", "pattern_type"]
            }
        }
    ]
```

#### Environment Configuration

**Add to `backend/app/core/config.py`:**

```python
class Settings(BaseSettings):
    # Existing settings...

    # GraphRAG MCP Configuration
    graph_mcp_url: str = ""
    graph_mcp_enabled: bool = Field(default=True, env="GRAPH_MCP_ENABLED")

    # Neo4j Configuration (for direct connection if needed)
    neo4j_uri: str = Field(default="", env="NEO4J_URI")
    neo4j_user: str = Field(default="", env="NEO4J_USER")
    neo4j_password: str = Field(default="", env="NEO4J_PASSWORD")
    neo4j_database: str = Field(default="neo4j", env="NEO4J_DATABASE")

    # Graph Query Performance
    graph_query_timeout: int = Field(default=30, env="GRAPH_QUERY_TIMEOUT_SECONDS")
    graph_max_results: int = Field(default=100, env="GRAPH_MAX_RESULTS")
    graph_cache_ttl: int = Field(default=300, env="GRAPH_CACHE_TTL_SECONDS")
```

**Add to `backend/.env`:**

```bash
# GraphRAG MCP Server Configuration
GRAPH_MCP_ENABLED=true
GRAPH_MCP_URL=http://localhost:8001  # GraphRAG MCP server URL
GRAPH_QUERY_TIMEOUT_SECONDS=30
GRAPH_MAX_RESULTS=100
GRAPH_CACHE_TTL_SECONDS=300

# Neo4j Database (if direct connection needed)
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_neo4j_password
NEO4J_DATABASE=emergency_management
```

#### Logging Integration

**Update structured logging for graph operations:**

```python
# In mcp_service.py - update call_tool_with_attribution method

async def call_tool_with_attribution(
    self,
    tool_name: str,
    arguments: Dict[str, Any]
) -> AsyncGenerator[StreamMessage, None]:
    """Execute GraphRAG MCP tool and generate rich SOURCES attribution."""

    if not self.tools_available:
        logger.warning("GraphRAG MCP tools unavailable for tool call",
                      tool_name=tool_name,
                      requested_args=arguments)
        yield StreamMessage(StreamType.SOURCES, {
            "source": "GraphRAG Emergency Knowledge Graph",
            "query": arguments.get("entity_type", ""),
            "status": "failed",
            "error": "Graph MCP tools unavailable"
        })
        return

    semantic_name = self.semantic_mapping.get(tool_name, f"Graph Tool: {tool_name}")

    with performance_context(logger, "graph_mcp_tool_call",
                           tool_name=tool_name,
                           semantic_name=semantic_name):
        try:
            logger.info("Executing GraphRAG MCP tool call",
                       tool_name=tool_name,
                       semantic_name=semantic_name,
                       arguments=arguments)

            start_time = time.time()
            result = await self.client.call_tool(
                settings.graph_mcp_url,
                tool_name,
                arguments
            )
            response_time = int((time.time() - start_time) * 1000)

            # Generate rich SOURCES message for GraphRAG attribution
            logger.info("GraphRAG MCP tool call completed successfully",
                       tool_name=tool_name,
                       response_time_ms=response_time,
                       success=result.get("success", False))

            yield StreamMessage(StreamType.SOURCES, {
                "source": semantic_name,
                "query": self._format_query_description(tool_name, arguments),
                "response_time": f"{response_time}ms",
                "status": "success" if result.get("success") else "failed",
                "timestamp": datetime.utcnow().isoformat(),
                "technical_details": {
                    "server": "GraphRAG MCP Server",
                    "tool": tool_name,
                    "graph_database": "Neo4j Emergency Management",
                    "query_type": self._get_query_type(tool_name)
                }
            })

        except Exception as e:
            logger.error("Error executing GraphRAG MCP tool call",
                        tool_name=tool_name,
                        semantic_name=semantic_name,
                        error_type=type(e).__name__,
                        error_message=str(e),
                        arguments=arguments,
                        exc_info=True)
            yield StreamMessage(StreamType.SOURCES, {
                "source": semantic_name,
                "query": self._format_query_description(tool_name, arguments),
                "status": "failed",
                "error": str(e)
            })

def _format_query_description(self, tool_name: str, arguments: Dict[str, Any]) -> str:
    """Format query description for user-friendly SOURCES messages."""
    if tool_name == "search_entities":
        entity_type = arguments.get("entity_type", "entities")
        return f"Searching {entity_type.lower()} in emergency management system"
    elif tool_name == "get_neighbors":
        return f"Finding connected resources and relationships"
    elif tool_name == "aggregate_by_type":
        return f"Calculating emergency management statistics"
    elif tool_name == "find_patterns":
        pattern_type = arguments.get("pattern_type", "patterns")
        return f"Analyzing {pattern_type.replace('_', ' ')} patterns"
    else:
        return f"Emergency management graph analysis"

def _get_query_type(self, tool_name: str) -> str:
    """Get query type for technical metadata."""
    query_types = {
        "search_entities": "entity_search",
        "get_entity": "entity_retrieval",
        "get_neighbors": "graph_traversal",
        "traverse_pattern": "pattern_matching",
        "aggregate_by_type": "statistical_aggregation",
        "calculate_statistics": "statistical_analysis",
        "find_patterns": "pattern_detection",
        "execute_cypher": "custom_query"
    }
    return query_types.get(tool_name, "unknown")
```

### Migration Strategy

#### Phase 1: Parallel Deployment (Week 1-2)

```python
# Temporarily support both Genie and GraphRAG MCP servers

class HybridMCPService:
    """Transitional service supporting both Genie and GraphRAG MCP servers."""

    def __init__(self):
        self.genie_service = MCPService()  # Existing Genie service
        self.graph_service = GraphMCPService()  # New GraphRAG service
        self.fallback_to_genie = True

    def get_tool_definitions(self) -> List[Dict[str, Any]]:
        """Return both Genie and GraphRAG tool definitions."""
        tools = []

        # Add GraphRAG tools if available
        if self.graph_service.tools_available:
            tools.extend(self.graph_service.get_tool_definitions())

        # Add Genie tools if available (for emergency fallback)
        if self.fallback_to_genie and self.genie_service.tools_available:
            genie_tools = self.genie_service.get_tool_definitions()
            # Rename to avoid conflicts
            for tool in genie_tools:
                tool["name"] = f"genie_{tool['name']}"
            tools.extend(genie_tools)

        return tools
```

#### Phase 2: Feature Flag Migration (Week 3-4)

```python
# Add feature flag for GraphRAG vs Genie selection

# In core/config.py
class Settings(BaseSettings):
    # Feature flags
    use_graph_mcp_primary: bool = Field(default=False, env="USE_GRAPH_MCP_PRIMARY")
    graph_mcp_fallback_enabled: bool = Field(default=True, env="GRAPH_MCP_FALLBACK_ENABLED")
```

#### Phase 3: Complete Migration (Week 5-6)

```python
# Remove Genie MCP service completely and use GraphRAG as primary

# Update mcp_service.py to use GraphRAG exclusively
class MCPService:
    """GraphRAG MCP Service - replaces Genie MCP service."""

    def __init__(self):
        logger.info("Initializing GraphRAG MCP Service")
        self.client = None
        self.tools_available = False
        self._initialized = False

        # GraphRAG semantic mapping
        self.semantic_mapping = {
            "search_entities": "Emergency Management Knowledge Graph Search",
            "get_neighbors": "Emergency Response Network Analysis",
            "aggregate_by_type": "Emergency Management Statistics",
            "find_patterns": "Emergency Response Pattern Detection"
        }
```

---

## Implementation Roadmap

### Development Phases

#### Phase 1: Foundation (Weeks 1-3)
**Goal**: Core MCP server with basic graph operations

**Deliverables**:
- [x] Project structure following MCP template patterns
- [x] Neo4j connection and basic query execution
- [x] Core MCP tools: `get_schema`, `search_entities`, `get_entity`
- [x] Safety validation for graph queries
- [x] Docker containerization
- [x] Unit testing framework

**Success Criteria**:
- MCP server starts and connects to Neo4j
- Basic entity search and retrieval working
- All safety validations in place
- 90%+ test coverage for core tools

#### Phase 2: Advanced Graph Operations (Weeks 4-6)
**Goal**: Complex graph traversal and analysis capabilities

**Deliverables**:
- [x] Graph traversal tools: `get_neighbors`, `traverse_pattern`, `find_paths`
- [x] Aggregation tools: `aggregate_by_type`, `calculate_statistics`
- [x] Pattern detection: `find_patterns` with multiple pattern types
- [x] Performance optimization and caching
- [x] Integration testing with test Neo4j instance

**Success Criteria**:
- Multi-hop graph queries working reliably
- Pattern detection algorithms implemented
- Query performance under 5 seconds for complex operations
- Integration tests passing

#### Phase 3: Assistant Integration (Weeks 7-8)
**Goal**: Seamless integration with GraySkyGenAI Assistant

**Deliverables**:
- [x] Updated MCPService with GraphRAG tool definitions
- [x] Foundation Model integration testing
- [x] Multi-type streaming with graph SOURCES attribution
- [x] Structured logging integration
- [x] Environment configuration updates

**Success Criteria**:
- Full end-to-end conversation flow working
- SOURCES messages provide rich graph attribution
- Foundation Model orchestration working seamlessly
- Performance tracking integrated

#### Phase 4: Data Pipeline & Production Ready (Weeks 9-12)
**Goal**: Production deployment with emergency management data

**Deliverables**:
- [x] Emergency management data ingestion pipeline
- [x] Production Neo4j cluster configuration
- [x] Monitoring and alerting setup
- [x] Backup and recovery procedures
- [x] Security hardening and SSL configuration
- [x] Load testing and performance optimization

**Success Criteria**:
- Production data pipeline working
- System handles 1000+ concurrent requests
- Monitoring dashboards operational
- Security audit passed

### Migration Timeline

#### Week 1-2: Parallel Testing
- Deploy GraphRAG MCP server alongside existing Genie MCP
- Feature flag to route subset of traffic to GraphRAG
- Compare response quality and performance

#### Week 3-4: Gradual Rollout
- Increase GraphRAG traffic percentage based on performance
- Monitor error rates and user satisfaction
- Refine tool definitions based on usage patterns

#### Week 5-6: Full Migration
- Complete migration to GraphRAG MCP server
- Remove Genie MCP server dependencies
- Production optimization and scaling

### Success Metrics

#### Technical Metrics
- **Query Performance**: <3 seconds average response time
- **Availability**: 99.9% uptime
- **Error Rate**: <0.1% of tool calls result in errors
- **Cache Hit Rate**: >80% schema cache hit rate
- **Concurrent Users**: Support 500+ concurrent requests

#### User Experience Metrics
- **Response Accuracy**: >95% of answers validated as correct
- **Query Success Rate**: >99% of questions answered successfully
- **Response Completeness**: All multi-hop queries include proper attribution
- **User Satisfaction**: >4.5/5 in user feedback surveys

#### Operational Metrics
- **Data Freshness**: <1 hour lag for incident data updates
- **System Health**: All monitoring alerts under thresholds
- **Backup Success**: Daily backups completing successfully
- **Security Compliance**: Zero security incidents

### Risk Mitigation

#### High-Risk Items
1. **Neo4j Performance**: Large graph queries may timeout
   - *Mitigation*: Query optimization, result limiting, caching

2. **Data Quality Issues**: Inconsistent emergency management data
   - *Mitigation*: Comprehensive validation, data quality monitoring

3. **Assistant Integration**: Breaking changes to Foundation Model
   - *Mitigation*: Gradual rollout, extensive integration testing

#### Medium-Risk Items
1. **MCP Protocol Changes**: Breaking changes in MCP specification
   - *Mitigation*: Version pinning, backward compatibility testing

2. **Graph Schema Evolution**: Changes to data structure
   - *Mitigation*: Schema versioning, migration scripts

### Post-Implementation Enhancements

#### Phase 5: Advanced Features (Future)
- **Vector Integration**: Semantic search capabilities
- **Machine Learning**: Predictive pattern detection
- **Real-time Streaming**: Live incident data integration
- **Multi-tenant Support**: Department-specific data isolation

#### Phase 6: Ecosystem Integration (Future)
- **Additional MCP Servers**: Weather, traffic, social media data
- **External APIs**: Integration with state emergency management systems
- **Mobile Applications**: Field responder interfaces
- **Dashboard Integration**: Executive summary dashboards

---

## Conclusion

This GraphRAG MCP Server represents a significant advancement in emergency management intelligence capabilities. By combining the power of graph databases with the flexibility of the Model Context Protocol, we create a system that enables sophisticated multi-hop reasoning while maintaining the transparency and auditability required for critical emergency response decisions.

The implementation plan provides a clear path from development through production deployment, with careful attention to integration with the existing GraySkyGenAI Assistant architecture. The phased approach ensures minimal disruption while delivering maximum value to emergency management operations.

Key success factors include:
- **Clean Architecture**: Separation of concerns between data layer and intelligence layer
- **Production Focus**: Comprehensive testing, monitoring, and security hardening
- **Emergency Management Expertise**: Deep understanding of division-level operational needs
- **Seamless Integration**: Drop-in replacement for existing MCP capabilities

This system will transform how emergency management divisions analyze complex operational questions, identify systemic issues, and optimize resource allocation across departments and incident types.

---
# create_react_agent Migration Plan

**Date**: 2025-10-07
**Target Branch**: `feature/react-agent-supervisor` (create new)
**Priority**: High - Fixes critical synthesis bug
**Estimated Time**: 2-3 hours with extensive testing

---

## Why Migrate?

### Current Problem
Custom supervisor logic can't properly detect "synthesis mode" vs "initial routing mode", causing it to treat subagent responses as new user queries.

### LangGraph's Solution
`create_react_agent()` is designed EXACTLY for this pattern:
- Built-in tool calling loop
- Automatic message history management
- `response_format` for final synthesis
- Proper streaming support
- Battle-tested by LangGraph team

### Benefits
- ✅ Fixes synthesis bug (supervisor won't confuse subagent messages)
- ✅ Simpler code (~320 lines removed from workflows.py)
- ✅ True streaming for ALL responses (including direct answers)
- ✅ LangGraph-native pattern (easier to maintain)
- ✅ Automatic synthesis via `response_format`

---

## Architecture Change

### Current (Custom Supervisor)
```
START → supervisor (custom node) → [genie_subagent | graphrag_subagent | __end__]
                ↑                          |                |
                └──────────────────────────┴────────────────┘
                      (subagents return for synthesis)
```

**Problems:**
- Custom routing logic (Command with structured output)
- Manual synthesis detection (buggy)
- Complex message handling

### New (create_react_agent Supervisor)
```
START → supervisor (react agent) → [genie_subagent | graphrag_subagent]
                ↑                          |                |
                └──────────────────────────┴────────────────┘
                      (subagents return automatically)
```

**Benefits:**
- Handoff tools for routing (LangGraph standard)
- Automatic synthesis via `response_format`
- Proper message context handling

---

## Migration Steps

### Step 1: Create Handoff Tools (10 min)

**File**: `app/agent/langgraph/workflows.py`
**Location**: After imports, before tool discovery

**Code**:
```python
from langchain_core.tools import tool

@tool
def transfer_to_genie() -> str:
    """Transfer to Genie emergency data subagent for real-time incident queries."""
    return "genie_subagent"  # Return node name for routing

@tool
def transfer_to_graphrag() -> str:
    """Transfer to GraphRAG knowledge graph subagent for historical/relationship queries."""
    return "graphrag_subagent"  # Return node name for routing

# Collect handoff tools
HANDOFF_TOOLS = [transfer_to_genie, transfer_to_graphrag]
```

**Test**: Verify tools import without errors
```bash
cd src
uv run python -c "from app.agent.langgraph.workflows import HANDOFF_TOOLS; print(f'✓ {len(HANDOFF_TOOLS)} handoff tools loaded')"
```

---

### Step 2: Create Supervisor with create_react_agent (30 min)

**File**: `app/agent/langgraph/workflows.py`
**Location**: Replace lines 137-215 (entire supervisor function)

**Before (Custom Supervisor - 78 lines)**:
```python
async def supervisor(state: AgentState, config) -> Command[...]:
    # Complex routing logic
    # Manual synthesis detection
    # Structured output parsing
    # Command construction
    ...
```

**After (React Agent Supervisor - ~15 lines)**:
```python
def create_supervisor():
    """
    Create supervisor agent using LangGraph's create_react_agent.

    The supervisor:
    1. Routes queries to specialized subagents via handoff tools
    2. Synthesizes results automatically when subagents return
    3. Answers simple queries directly
    """
    # Model for supervisor (synthesis model for quality)
    model = AzureChatOpenAI(
        azure_deployment=settings.synthesis_model_deployment,
        api_version=settings.api_version,
        azure_endpoint=settings.openai_endpoint,
        api_key=settings.openai_api_key,
        temperature=settings.synthesis_model_temperature
    )

    # System prompt for supervisor
    system_prompt = """You are an emergency management assistant supervisor.

Your role:
1. ROUTING: Decide if query needs a specialized subagent:
   - Use transfer_to_genie for real-time emergency incidents, active missions
   - Use transfer_to_graphrag for historical data, entity searches, graph queries
   - Answer simple questions directly (no transfer needed)

2. SYNTHESIS: When subagents return with data:
   - Review all gathered information
   - Provide clear, actionable summary for emergency personnel
   - Highlight critical details and safety considerations
   - Use non-technical language

Be concise, factual, and helpful."""

    # Create supervisor as react agent
    supervisor_agent = create_react_agent(
        model,
        tools=HANDOFF_TOOLS,  # Only handoff tools (subagents have their own tools)
        state_schema=AgentState,
        messages_modifier=system_prompt  # System prompt for all interactions
    )

    return supervisor_agent
```

**Test**: Verify supervisor compiles
```bash
cd src
uv run python -c "from app.agent.langgraph.workflows import create_supervisor; print('✓ Supervisor created')"
```

**Note**: `create_react_agent` returns a compiled graph, not a function!

---

### Step 3: Create Tool Call Router (20 min)

**File**: `app/agent/langgraph/workflows.py`
**Location**: After supervisor creation, before graph construction

**Purpose**: Detect handoff tool calls and route to appropriate subagent

**Code**:
```python
def route_after_supervisor(state: AgentState) -> Literal["genie_subagent", "graphrag_subagent", END]:
    """
    Route based on supervisor's tool calls (handoff tools).

    If supervisor called transfer_to_genie → route to genie_subagent
    If supervisor called transfer_to_graphrag → route to graphrag_subagent
    Otherwise → END (supervisor answered directly)
    """
    messages = state.get("messages", [])
    if not messages:
        return END

    last_message = messages[-1]

    # Check for handoff tool calls
    if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
        for tool_call in last_message.tool_calls:
            tool_name = tool_call.get('name', '')

            if tool_name == 'transfer_to_genie':
                logger.info("🚨 Routing to Genie subagent")
                return "genie_subagent"
            elif tool_name == 'transfer_to_graphrag':
                logger.info("🕸️ Routing to GraphRAG subagent")
                return "graphrag_subagent"

    # No handoff, supervisor answered directly
    logger.info("✅ Supervisor answered directly, ending workflow")
    return END
```

**Test**: Verify router function exists
```bash
cd src
uv run python -c "from app.agent.langgraph.workflows import route_after_supervisor; print('✓ Router created')"
```

---

### Step 4: Update Graph Construction (20 min)

**File**: `app/agent/langgraph/workflows.py`
**Location**: `create_graph()` function (lines 641-708)

**Before (Custom Wiring)**:
```python
workflow.add_node("supervisor", supervisor)  # Custom function
workflow.add_edge(START, "supervisor")
workflow.add_edge("genie_subagent", "supervisor")  # Returns for synthesis
workflow.add_edge("graphrag_subagent", "supervisor")
```

**After (React Agent Wiring)**:
```python
# Add supervisor (react agent graph)
supervisor_graph = create_supervisor()
workflow.add_node("supervisor", supervisor_graph)

# Add subagents (unchanged)
workflow.add_node("genie_subagent", create_genie_subgraph())
workflow.add_node("graphrag_subagent", create_graphrag_subgraph())

# Wire: START → supervisor
workflow.add_edge(START, "supervisor")

# Wire: supervisor → [genie | graphrag | END] based on handoff tool calls
workflow.add_conditional_edges(
    "supervisor",
    route_after_supervisor,
    {
        "genie_subagent": "genie_subagent",
        "graphrag_subagent": "graphrag_subagent",
        END: END
    }
)

# Wire: subagents → supervisor (for synthesis)
workflow.add_edge("genie_subagent", "supervisor")
workflow.add_edge("graphrag_subagent", "supervisor")
```

**Test**: Compile graph
```bash
cd src
uv run python scripts/test_workflow_compilation.py
```

---

### Step 5: Add response_format for Synthesis (Optional, 15 min)

**Purpose**: Ensure supervisor provides formatted final response

**Update `create_supervisor()`**:
```python
class FinalResponse(BaseModel):
    """Final response to user after synthesis."""
    summary: str = Field(description="Clear, actionable summary for emergency personnel")

supervisor_agent = create_react_agent(
    model,
    tools=HANDOFF_TOOLS,
    state_schema=AgentState,
    messages_modifier=system_prompt,
    response_format=FinalResponse  # Ensures final response is generated
)
```

**Note**: This adds one extra LLM call at the end for synthesis. May or may not be needed - test first without it.

---

### Step 6: Update Service Layer Event Handling (15 min)

**File**: `app/services/langgraph_service.py`
**Location**: `_translate_event()` function

**Changes Needed**:
1. Remove supervisor-specific JSON parsing (no longer uses structured output for routing)
2. Simplify `on_chain_end` handling (react agent handles synthesis automatically)
3. Add handoff tool detection for STATUS updates

**Before**:
```python
# Buffer supervisor's structured JSON output
_supervisor_output_buffer = {"content": "", "run_id": None}

# Parse JSON at on_chat_model_end
if langgraph_node == "supervisor":
    structured_output = json.loads(_supervisor_output_buffer["content"])
    return StreamMessage(StreamType.REASONING, {"step": f"Routing to {destination}..."})
```

**After**:
```python
# Supervisor now uses normal model (no JSON buffering needed)
# Handoff tools are detected via on_tool_start

if event_type == "on_tool_start":
    tool_name = event.get("name", "unknown")

    if tool_name == "transfer_to_genie":
        return StreamMessage(StreamType.STATUS, {"message": "🚨 Routing to Genie subagent..."})
    elif tool_name == "transfer_to_graphrag":
        return StreamMessage(StreamType.STATUS, {"message": "🕸️ Routing to GraphRAG subagent..."})
    else:
        return StreamMessage(StreamType.STATUS, {"message": f"🔧 Calling tool: {tool_name}"})
```

**Remove**:
- Lines 186-187: `_supervisor_output_buffer` (no longer needed)
- Lines 232-241: JSON buffering logic in `on_chat_model_stream`
- Lines 250-277: JSON parsing in `on_chat_model_end`
- Lines 300-314: `Command.update` extraction (react agent uses normal messages)

**Simplification**: ~80 lines removed!

**Test**: Verify service compiles
```bash
cd src
uv run python -c "from app.services.langgraph_service import LangGraphService; print('✓ Service loads')"
```

---

### Step 7: Integration Testing (30 min)

#### Test 1: Simple Direct Query
```bash
curl -X POST 'http://localhost:8000/api/v1/chat/stream' \
  -H 'Content-Type: application/json' \
  -d '{"message": "Hello, how are you?", "conversation_id": "test-direct"}'
```

**Expected**:
- STATUS: Processing...
- CONTENT: "Hello! I'm here to help..." (streaming token-by-token)
- STATUS: Response complete

**Verify**:
- ✅ No handoff tool calls
- ✅ Supervisor responds directly
- ✅ Token-by-token streaming

---

#### Test 2: Route to Genie Subagent
```bash
curl -X POST 'http://localhost:8000/api/v1/chat/stream' \
  -H 'Content-Type: application/json' \
  -d '{"message": "What are current emergencies in Florida?", "conversation_id": "test-genie"}'
```

**Expected**:
- STATUS: Processing...
- STATUS: "🚨 Routing to Genie subagent..."
- STATUS: "🔄 Genie subagent analyzing..."
- CONTENT: [Subagent response streaming]
- STATUS: "🤖 Model analyzing..." (supervisor synthesis)
- CONTENT: [Supervisor synthesis streaming]
- STATUS: Response complete

**Verify**:
- ✅ `transfer_to_genie` tool called
- ✅ Genie subagent executes
- ✅ Supervisor synthesizes results
- ✅ No duplicate/confused responses
- ✅ Original query is answered

---

#### Test 3: Route to GraphRAG Subagent
```bash
curl -X POST 'http://localhost:8000/api/v1/chat/stream' \
  -H 'Content-Type: application/json' \
  -d '{"message": "List all missions from the graph", "conversation_id": "test-graphrag"}'
```

**Expected**:
- STATUS: Processing...
- STATUS: "🕸️ Routing to GraphRAG subagent..."
- STATUS: "🔄 GraphRAG subagent analyzing..."
- STATUS: "🔧 Calling tool: get_missions..." (or similar)
- SOURCES: tool names
- CONTENT: [Subagent response with mission list]
- STATUS: "🤖 Model analyzing..." (supervisor synthesis)
- CONTENT: [Supervisor synthesis - clean summary]
- STATUS: Response complete

**Verify**:
- ✅ `transfer_to_graphrag` tool called
- ✅ GraphRAG subagent executes tools
- ✅ Supervisor synthesizes tool results
- ✅ Clean, coherent final response
- ✅ No "thank you for the list" confusion

---

#### Test 4: Multi-Iteration Subagent
```bash
curl -X POST 'http://localhost:8000/api/v1/chat/stream' \
  -H 'Content-Type: application/json' \
  -d '{"message": "Tell me everything about Hurricane Milton - incidents, missions, affected areas", "conversation_id": "test-iteration"}'
```

**Expected**:
- STATUS: Routing to Genie...
- STATUS: "🔄 Genie iteration 1/10..." (if subgraphs=True enabled)
- STATUS: Tool calls...
- STATUS: "🔄 Genie iteration 2/10..."
- ... (multiple iterations)
- CONTENT: [Comprehensive synthesized response]

**Verify**:
- ✅ Subagent iterates multiple times
- ✅ All tool results consolidated
- ✅ Supervisor synthesizes into coherent response

---

### Step 8: Enable Subgraph Streaming (Optional, 10 min)

**File**: `app/services/langgraph_service.py`
**Line**: 150

**Change**:
```python
async for event in workflow_app.astream_events(
    initial_state,
    config,
    version="v2",
    subgraphs=True  # Enable iteration progress visibility
):
```

**Benefit**: See "🔄 Genie iteration 3/10" during subagent execution

**Test**: Verify iteration progress appears
```bash
# Use Test 4 above, look for iteration count in REASONING/STATUS
```

---

## Detailed Code Changes

### Change 1: Remove Custom Supervisor (workflows.py)

**Delete lines 137-215** (entire `supervisor()` function)

---

### Change 2: Add Handoff Tools (workflows.py)

**After line 32** (after logger = get_logger(__name__)):
```python
# ============================================================================
# HANDOFF TOOLS FOR SUPERVISOR
# ============================================================================

from langchain_core.tools import tool

@tool
def transfer_to_genie() -> str:
    """
    Transfer to Genie emergency data subagent.

    Use for queries about:
    - Current/active emergency incidents
    - Mission status and details
    - Real-time emergency management data
    - Incident-specific information
    """
    return "genie_subagent"

@tool
def transfer_to_graphrag() -> str:
    """
    Transfer to GraphRAG knowledge graph subagent.

    Use for queries about:
    - Historical incident patterns
    - Entity relationships and connections
    - Graph-based analysis
    - Complex relationship queries
    """
    return "graphrag_subagent"

HANDOFF_TOOLS = [transfer_to_genie, transfer_to_graphrag]
```

---

### Change 3: Create Supervisor with create_react_agent (workflows.py)

**Add after subgraph creation functions** (around line 630):
```python
def create_supervisor():
    """
    Create supervisor using create_react_agent.

    Handles:
    - Routing to specialized subagents via handoff tools
    - Direct responses for simple queries
    - Automatic synthesis when subagents return

    Returns:
        Compiled supervisor graph
    """
    model = AzureChatOpenAI(
        azure_deployment=settings.synthesis_model_deployment,
        api_version=settings.api_version,
        azure_endpoint=settings.openai_endpoint,
        api_key=settings.openai_api_key,
        temperature=settings.synthesis_model_temperature
    )

    system_prompt = """You are an emergency management assistant supervisor.

ROUTING:
- Use transfer_to_genie for real-time emergency data queries
- Use transfer_to_graphrag for historical/graph analysis queries
- Answer simple questions directly

SYNTHESIS (when subagents return):
- Review gathered data
- Provide clear, actionable summary
- Highlight critical information
- Use appropriate language for emergency personnel

Be concise and helpful."""

    supervisor = create_react_agent(
        model,
        tools=HANDOFF_TOOLS,
        state_schema=AgentState,
        messages_modifier=system_prompt
    )

    logger.info("Supervisor created with create_react_agent")
    return supervisor
```

---

### Change 4: Add Router Function (workflows.py)

**Add before `create_graph()`**:
```python
def route_after_supervisor(state: AgentState) -> Literal["genie_subagent", "graphrag_subagent", END]:
    """
    Route based on supervisor's handoff tool calls.

    Returns:
        Node name to route to, or END if supervisor answered directly
    """
    messages = state.get("messages", [])
    if not messages:
        return END

    last_message = messages[-1]

    # Check for handoff tool calls
    if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
        for tool_call in last_message.tool_calls:
            tool_name = tool_call.get('name', '')

            if tool_name == 'transfer_to_genie':
                return "genie_subagent"
            elif tool_name == 'transfer_to_graphrag':
                return "graphrag_subagent"

    # Check for tool result messages (handoff completed)
    if hasattr(last_message, 'type') and last_message.type == 'tool':
        # Just completed a handoff, continue to END
        return END

    # No handoff tool calls, supervisor answered directly
    return END
```

---

### Change 5: Update Graph Wiring (workflows.py)

**Replace lines 688-703** in `create_graph()`:

**Before**:
```python
# Add supervisor (entry point)
workflow.add_node("supervisor", supervisor)

# Add specialized subagents
genie_subgraph = create_genie_subgraph()
workflow.add_node("genie_subagent", genie_subgraph)

graphrag_subgraph = create_graphrag_subgraph()
workflow.add_node("graphrag_subagent", graphrag_subgraph)

# Wire: START → supervisor
workflow.add_edge(START, "supervisor")

# Subagents return to supervisor for synthesis
workflow.add_edge("genie_subagent", "supervisor")
workflow.add_edge("graphrag_subagent", "supervisor")
```

**After**:
```python
# Add supervisor (create_react_agent)
supervisor_graph = create_supervisor()
workflow.add_node("supervisor", supervisor_graph)

# Add specialized subagents (unchanged)
genie_subgraph = create_genie_subgraph()
workflow.add_node("genie_subagent", genie_subgraph)

graphrag_subgraph = create_graphrag_subgraph()
workflow.add_node("graphrag_subagent", graphrag_subgraph)

# Wire: START → supervisor
workflow.add_edge(START, "supervisor")

# Wire: supervisor → conditional routing based on handoff tools
workflow.add_conditional_edges(
    "supervisor",
    route_after_supervisor,
    {
        "genie_subagent": "genie_subagent",
        "graphrag_subagent": "graphrag_subagent",
        END: END
    }
)

# Wire: subagents → supervisor (for synthesis)
workflow.add_edge("genie_subagent", "supervisor")
workflow.add_edge("graphrag_subagent", "supervisor")
```

**Test**: Compile graph
```bash
cd src
uv run python scripts/test_workflow_compilation.py
```

---

### Change 6: Simplify Service Event Handling (langgraph_service.py)

**Remove supervisor JSON buffering** (lines 186-187):
```python
# DELETE THIS:
_supervisor_output_buffer = {"content": "", "run_id": None}
```

**Remove JSON accumulation** (lines 232-241):
```python
# DELETE THIS:
if langgraph_node == "supervisor":
    if _supervisor_output_buffer["run_id"] != run_id:
        _supervisor_output_buffer["content"] = ""
        _supervisor_output_buffer["run_id"] = run_id
    _supervisor_output_buffer["content"] += chunk_data.content
    return None
```

**Remove JSON parsing** (lines 250-277):
```python
# DELETE THIS:
elif event_type == "on_chat_model_end":
    if langgraph_node == "supervisor":
        structured_output = json.loads(_supervisor_output_buffer["content"])
        ...
```

**Remove Command extraction** (lines 300-314):
```python
# DELETE THIS:
elif event_type == "on_chain_end":
    if langgraph_node == "supervisor":
        if isinstance(output, Command):
            ...
```

**Add handoff tool detection** (update `on_tool_start`):
```python
elif event_type == "on_tool_start":
    tool_name = event.get("name", "unknown")

    # Detect handoff tools
    if tool_name == "transfer_to_genie":
        return StreamMessage(StreamType.REASONING, {"step": "💭 Transferring to Genie subagent for emergency data..."})
    elif tool_name == "transfer_to_graphrag":
        return StreamMessage(StreamType.REASONING, {"step": "💭 Transferring to GraphRAG for knowledge graph analysis..."})
    else:
        return StreamMessage(StreamType.STATUS, {"message": f"🔧 Calling tool: {tool_name}"})
```

**Net Change**: ~90 lines removed, ~10 lines added = **-80 lines**

---

## Testing Strategy

### Phase 1: Component Testing

**Test 1: Handoff Tools Load**
```bash
uv run python -c "from app.agent.langgraph.workflows import HANDOFF_TOOLS; assert len(HANDOFF_TOOLS) == 2"
```

**Test 2: Supervisor Compiles**
```bash
uv run python -c "from app.agent.langgraph.workflows import create_supervisor; s = create_supervisor(); print('✓ Supervisor compiled')"
```

**Test 3: Graph Compiles**
```bash
uv run python scripts/test_workflow_compilation.py
```

**Test 4: Service Loads**
```bash
uv run python -c "from app.services.langgraph_service import LangGraphService; LangGraphService()"
```

---

### Phase 2: Integration Testing

**Test 5: Direct Response (No Handoff)**
- Query: "Hello"
- Expect: Supervisor answers directly, no handoff
- Verify: Content streams token-by-token

**Test 6: Genie Handoff (No Tools)**
- Query: "What's happening in Florida?"
- Expect: Handoff to Genie, Genie responds, supervisor synthesizes
- Verify: Clean synthesis, original query answered

**Test 7: GraphRAG Handoff (With Tools)**
- Query: "List all missions"
- Expect: Handoff to GraphRAG, tools called, supervisor synthesizes
- Verify: No "thank you for the list" bug, proper synthesis

**Test 8: Multi-Iteration**
- Query: "Tell me everything about Hurricane Milton"
- Expect: Multiple tool calls, iterations visible (if subgraphs=True)
- Verify: Comprehensive synthesis

---

### Phase 3: Regression Testing

**Test 9: Existing Integration Test**
```bash
uv run python scripts/test_service_integration.py
```

**Test 10: API Health**
```bash
curl http://localhost:8000/health
curl http://localhost:8000/api/v1/mcp/servers
```

---

## Rollback Plan

If migration fails:
```bash
# Restore from backups
cp app/agent/langgraph/workflows.py.backup-step6 app/agent/langgraph/workflows.py
cp app/services/langgraph_service.py.backup app/services/langgraph_service.py

# Or git revert
git log --oneline | head -10  # Find pre-migration commit
git revert <commit-hash>
```

---

## Success Criteria

### Must Have
- ✅ Direct queries work and stream properly
- ✅ Subagent routing works (handoff tools called)
- ✅ Supervisor synthesis works (no message confusion)
- ✅ Original user query is answered (not subagent response)
- ✅ All existing tests pass
- ✅ No regression in functionality

### Nice to Have
- ✅ Iteration progress visible (with `subgraphs=True`)
- ✅ Code reduction achieved (~300+ lines removed)
- ✅ Cleaner architecture (LangGraph-native)

---

## File Changelist

### Primary Changes
1. **`app/agent/langgraph/workflows.py`**
   - Remove: Custom supervisor function (~78 lines)
   - Remove: RoutingDecision schema (~10 lines)
   - Add: Handoff tools (~30 lines)
   - Add: `create_supervisor()` (~35 lines)
   - Add: `route_after_supervisor()` (~30 lines)
   - Update: Graph wiring in `create_graph()` (~10 lines changed)
   - **Net**: -78 -10 +30 +35 +30 = **+7 lines** (but much simpler!)

2. **`app/services/langgraph_service.py`**
   - Remove: Supervisor JSON buffering (~90 lines)
   - Remove: Command extraction (~15 lines)
   - Update: Handoff tool detection (~10 lines)
   - **Net**: -90 -15 +10 = **-95 lines**

### No Changes Needed
- ✅ `app/agent/langgraph/context.py` - unchanged
- ✅ `app/agent/langgraph/state.py` - unchanged
- ✅ `app/api/api_v1/endpoints/chat.py` - unchanged
- ✅ `app/core/config.py` - unchanged
- ✅ Subagent functions (genie_agent_node, graphrag_agent_node) - unchanged

---

## Migration Checklist

### Pre-Migration
- [ ] Read this document completely
- [ ] Create new branch: `feature/react-agent-supervisor`
- [ ] Verify current system works (run Test 1-2 from Phase 2)
- [ ] Backup current files (already done: `.backup-step6`)

### Step 1: Handoff Tools
- [ ] Add handoff tool functions
- [ ] Test: Tools load without errors
- [ ] Commit: "feat: add handoff tools for supervisor routing"

### Step 2: Create Supervisor
- [ ] Add `create_supervisor()` function
- [ ] Comment out old supervisor (don't delete yet)
- [ ] Test: Supervisor compiles
- [ ] Commit: "feat: create supervisor with create_react_agent"

### Step 3: Router Function
- [ ] Add `route_after_supervisor()` function
- [ ] Test: Function exists and imports
- [ ] Commit: "feat: add handoff tool router"

### Step 4: Graph Wiring
- [ ] Update `create_graph()` to use new supervisor
- [ ] Update conditional edges for routing
- [ ] Test: Graph compiles
- [ ] Test: Run `test_workflow_compilation.py`
- [ ] Commit: "feat: wire supervisor with handoff routing"

### Step 5: Service Layer Cleanup
- [ ] Remove supervisor JSON buffering
- [ ] Remove Command extraction
- [ ] Update handoff tool detection
- [ ] Test: Service loads
- [ ] Commit: "refactor: simplify event handling after supervisor migration"

### Step 6: Integration Testing
- [ ] Start server: `uv run uvicorn app.main:app --reload`
- [ ] Run Test 5: Direct response
- [ ] Run Test 6: Genie handoff
- [ ] Run Test 7: GraphRAG handoff (CRITICAL - tests synthesis bug fix)
- [ ] Run Test 8: Multi-iteration
- [ ] Commit: "test: verify all scenarios working"

### Step 7: Regression Testing
- [ ] Run `test_service_integration.py`
- [ ] Test health endpoints
- [ ] Test MCP introspection
- [ ] Commit: "test: verify no regressions"

### Step 8: Cleanup
- [ ] Delete old supervisor function
- [ ] Delete RoutingDecision schema
- [ ] Remove debug logging
- [ ] Update comments/docstrings
- [ ] Commit: "chore: remove deprecated supervisor code"

### Step 9: Documentation
- [ ] Update README.md with new architecture
- [ ] Update sequence diagrams
- [ ] Document handoff pattern
- [ ] Commit: "docs: update for create_react_agent supervisor"

---

## Common Pitfalls to Avoid

### 1. Don't Delete Subagent Logic
- ✅ Keep: `genie_agent_node`, `graphrag_agent_node`
- ✅ Keep: `create_genie_subgraph()`, `create_graphrag_subgraph()`
- ✅ Keep: Tool grouping logic
- ❌ Don't change: Subagent iteration logic

### 2. Don't Break Context Injection
- ✅ Keep: `RuntimeContext` in config
- ✅ Keep: `context_schema=RuntimeContext` in subgraphs
- ✅ Verify: Supervisor can access context if needed

### 3. Don't Skip Testing Between Steps
- ✅ Test after EACH step (not just at end)
- ✅ Verify graph compiles after each change
- ✅ Run integration tests frequently

### 4. Don't Forget Handoff Tool Returns
- ✅ Handoff tools must return node names as strings
- ❌ Don't return Command objects
- ❌ Don't return empty strings

---

## Expected Outcomes

### Code Simplification
- **workflows.py**: 769 lines → ~500 lines (-35%)
- **langgraph_service.py**: 345 lines → ~260 lines (-25%)
- **Total**: 1114 lines → 760 lines (-32%)

### Functionality Improvements
- ✅ Synthesis bug fixed
- ✅ Message confusion resolved
- ✅ Proper streaming for all scenarios
- ✅ LangGraph-native patterns
- ✅ Easier to extend (add more subagents)

### Performance
- ⚠️ May add 1 extra LLM call for synthesis (if using `response_format`)
- ✅ Streaming UX improved (tokens arrive faster)
- ✅ No change in tool calling latency

---

## Alternative: Quick Fix (Not Recommended)

If migration is too complex, temporary workaround:

**Change graph wiring** (workflows.py:702-703):
```python
# Subagents go directly to END (skip synthesis)
workflow.add_edge("genie_subagent", END)
workflow.add_edge("graphrag_subagent", END)
```

**Result**:
- ✅ Fixes synthesis bug (no synthesis = no confusion)
- ❌ Loses synthesis capability entirely
- ❌ Subagent responses not polished for users
- ❌ Not a long-term solution

---

## References

- **Tutorial**: https://langchain-ai.github.io/langgraph/tutorials/multi_agent/agent_supervisor/
- **create_react_agent API**: https://langchain-ai.github.io/langgraph/reference/agents/#langgraph.prebuilt.chat_agent_executor.create_react_agent
- **Command with Handoffs**: https://blog.langchain.com/command-a-new-tool-for-multi-agent-architectures-in-langgraph/
- **Current Status**: `dev_status/streaming_migration_status.md`

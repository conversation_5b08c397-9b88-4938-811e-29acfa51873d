# Memgraph Database Docker Compose
# For LOCAL DEVELOPMENT ONLY
# Production uses external managed Memgraph instances

version: '3.8'

services:
  # Memgraph Database with MAGE (Advanced Graph Extensions)
  memgraph:
    image: memgraph/memgraph-mage:latest
    container_name: ${MEMGRAPH_CONTAINER_NAME:-memgraph-db}
    ports:
      - "${MEMGRAPH_BOLT_PORT:-7687}:7687"   # Bolt protocol
      - "${MEMGRAPH_HTTP_PORT:-7444}:7444"   # HTTP interface
    environment:
      - MEMGRAPH_LOG_LEVEL=${MEMGRAPH_LOG_LEVEL:-INFO}
      - MEMGRAPH_STORAGE_SNAPSHOT_INTERVAL_SEC=${MEMGRAPH_STORAGE_SNAPSHOT_INTERVAL_SEC:-300}
      - MEMGRAPH_STORAGE_WAL_ENABLED=${MEMGRAPH_STORAGE_WAL_ENABLED:-true}
    volumes:
      - ${VOLUME_NAME:-memgraph_data}:/var/lib/memgraph
      - ./data:/data:ro  # Sample data directory
      - ./init:/docker-entrypoint-initdb.d:ro  # Initialization scripts
    healthcheck:
      test: ["CMD", "echo", "RETURN 1", "|", "mgconsole", "--host", "localhost", "--port", "7687"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    command: ["--telemetry-enabled=${MEMGRAPH_TELEMETRY:-false}", "--log-level=${MEMGRAPH_LOG_LEVEL:-INFO}"]
    networks:
      - default

  # Memgraph Lab - Web-based visual graph exploration
  memgraph-lab:
    image: memgraph/lab:latest
    container_name: ${MEMGRAPH_LAB_CONTAINER_NAME:-memgraph-lab}
    depends_on:
      - memgraph
    ports:
      - "${MEMGRAPH_LAB_PORT:-3001}:3000"   # Lab UI (using 3001 to avoid conflicts)
    environment:
      - QUICK_CONNECT_MG_HOST=memgraph
      - QUICK_CONNECT_MG_PORT=7687
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    networks:
      - default

volumes:
  memgraph_data:
    name: ${VOLUME_NAME:-memgraph_data}
    driver: local
    # Data persists in Docker-managed volume
    # Located at: /var/lib/docker/volumes/memgraph_data/_data

networks:
  default:
    name: ${NETWORK_NAME:-memgraph-network}
    driver: bridge

# Usage:
#   docker-compose up -d       # Start both Memgraph and Lab
#   docker-compose logs -f     # View logs
#   docker-compose down        # Stop services
#
# Access:
#   Memgraph:     bolt://localhost:7687
#   Memgraph HTTP: http://localhost:7444
#   Memgraph Lab: http://localhost:3001
#
# Environment Variables:
#   Configure via .env file or export before running docker-compose
#   See .env.example for all available options

'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { X, MessageSquare } from 'lucide-react';
import { <PERSON>, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import ChatInput from './ChatInput';
import ChatMessage from './ChatMessage';
import { ChatAPI } from '../services/chatApi';
import { Message, ConnectionStatus, DebugMessage, StreamType } from '../types/chat';
import { cn } from '@/lib/utils';

interface ModernChatInterfaceProps {
  isOpen?: boolean;
  onToggle?: () => void;
}

const ModernChatInterface: React.FC<ModernChatInterfaceProps> = ({ 
  isOpen = true, 
  onToggle 
}) => {
  const [api] = useState(() => new ChatAPI());
  const [messages, setMessages] = useState<Message[]>([]);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('disconnected');
  const [debugMessages, setDebugMessages] = useState<{
    status: DebugMessage[];
    reasoning: DebugMessage[];
    sources: DebugMessage[];
    content: DebugMessage[];
  }>({
    status: [],
    reasoning: [],
    sources: [],
    content: []
  });
  const [currentStreamingMessageId, setCurrentStreamingMessageId] = useState<string | null>(null);
  const [isStreaming, setIsStreaming] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showDebugPanel, setShowDebugPanel] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    checkHealth();
  }, []);

  const checkHealth = async () => {
    try {
      await api.testHealth();
      setConnectionStatus('connected');
    } catch (error) {
      setConnectionStatus('error');
    }
  };

  const generateMessageId = (): string => {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
  };

  const addMessage = useCallback((sender: 'user' | 'assistant' | 'system', content: string, isStreaming = false): string => {
    const messageId = generateMessageId();
    const newMessage: Message = {
      id: messageId,
      sender,
      content,
      timestamp: new Date(),
      isStreaming
    };

    setMessages(prev => [...prev, newMessage]);
    return messageId;
  }, []);

  const updateMessage = useCallback((messageId: string, content: string) => {
    setMessages(prev => prev.map(msg => 
      msg.id === messageId ? { ...msg, content } : msg
    ));
  }, []);

  const appendToMessage = useCallback((messageId: string, content: string) => {
    setMessages(prev => prev.map(msg => 
      msg.id === messageId ? { ...msg, content: msg.content + content } : msg
    ));
  }, []);

  const finalizeMessage = useCallback((messageId: string) => {
    setMessages(prev => prev.map(msg => 
      msg.id === messageId ? { ...msg, isStreaming: false } : msg
    ));
  }, []);

  const addDebugMessage = useCallback((type: StreamType, data: any) => {
    const debugMessage: DebugMessage = {
      type,
      timestamp: new Date(),
      data
    };

    setDebugMessages(prev => ({
      ...prev,
      [type.toLowerCase()]: [debugMessage, ...prev[type.toLowerCase() as keyof typeof prev]]
    }));
  }, []);

  const clearDebugMessages = useCallback(() => {
    setDebugMessages({
      status: [],
      reasoning: [],
      sources: [],
      content: []
    });
  }, []);

  const scrollToBottom = () => {
    if (messagesEndRef.current?.scrollIntoView) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = useCallback(async (message: string) => {
    if (!message.trim() || isStreaming) return;

    // Add user message to chat
    addMessage('user', message);
    
    // Clear debug panels
    clearDebugMessages();
    
    // Prepare for assistant response
    const messageId = addMessage('assistant', '', true);
    setCurrentStreamingMessageId(messageId);
    
    // Update status
    setConnectionStatus('streaming');
    setIsStreaming(true);
    setError(null);

    // Send message with callbacks for each stream type
    try {
      await api.sendMessage(message, {
        onStatus: (data, metadata) => {
          console.log('📊 STATUS:', data);
          addDebugMessage('STATUS', data);
        },

        onReasoning: (data, metadata) => {
          console.log('🧠 REASONING:', data);
          addDebugMessage('REASONING', data);
          if (data.step) {
            appendToMessage(messageId, `\n💭 *Reasoning: ${data.step}*\n`);
          }
        },

        onSources: (data, metadata) => {
          console.log('📚 SOURCES:', data);
          addDebugMessage('SOURCES', data);
        },

        onContent: (data, metadata) => {
          console.log('📝 CONTENT:', data);
          addDebugMessage('CONTENT', data);
          
          // Append content to current message
          if (data.chunk || data.text) {
            appendToMessage(messageId, data.chunk || data.text);
          } else if (data.message) {
            appendToMessage(messageId, data.message);
          }
        },

        onError: (error) => {
          console.error('❌ Stream error:', error);
          setConnectionStatus('error');
          setError(error.message);
          updateMessage(messageId, `❌ Error: ${error.message}`);
          finalizeMessage(messageId);
          setCurrentStreamingMessageId(null);
          setIsStreaming(false);
        },

        onComplete: () => {
          console.log('✅ Stream complete');
          setConnectionStatus('connected');
          finalizeMessage(messageId);
          setCurrentStreamingMessageId(null);
          setIsStreaming(false);
        }
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send message');
      updateMessage(messageId, `❌ Error: ${err instanceof Error ? err.message : 'Failed to send message'}`);
      finalizeMessage(messageId);
      setCurrentStreamingMessageId(null);
      setIsStreaming(false);
      setConnectionStatus('error');
    }
  }, [api, addMessage, updateMessage, appendToMessage, finalizeMessage, addDebugMessage, clearDebugMessages, isStreaming]);

  const handleResetConversation = () => {
    api.resetConversation();
    setMessages([]);
    clearDebugMessages();
    setCurrentStreamingMessageId(null);
  };

  const getConnectionStatusText = (): string => {
    switch (connectionStatus) {
      case 'connected':
        return 'Connected';
      case 'streaming':
        return 'Streaming...';
      case 'error':
        return 'Disconnected - Backend not running?';
      default:
        return 'Disconnected';
    }
  };

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'text-green-600 bg-green-50';
      case 'streaming':
        return 'text-yellow-600 bg-yellow-50';
      case 'error':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className="flex flex-col h-screen">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <h1 className="text-2xl font-bold text-gray-900">GraySkyGenAI Assistant</h1>
        <p className="text-sm text-gray-600 mt-1">Modern API Testing Interface</p>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 p-6 flex flex-col gap-6 min-h-0 overflow-auto">
        {/* Chat Interface Card */}
        <Card className="bg-white shadow-sm flex-1 flex flex-col min-h-0">
          <CardHeader className="pb-3">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <MessageSquare className="h-4 w-4 text-white" />
                </div>
                <div>
                  <CardTitle className="text-lg font-semibold text-gray-900">Chat Interface</CardTitle>
                  <p className="text-xs text-gray-500 mt-0.5">Ask questions about your data</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div className={cn("px-3 py-1.5 rounded-md text-xs font-medium", getStatusColor())}>
                  {getConnectionStatusText()}
                </div>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={handleResetConversation}
                  disabled={isStreaming}
                >
                  Clear
                </Button>
              </div>
            </div>
          </CardHeader>
          
          <CardContent className="flex flex-col flex-1 min-h-0 px-6 pb-6">
            {/* Messages Area */}
            <div className="flex-1 overflow-y-auto mb-4 min-h-0 border border-gray-200 rounded-lg bg-gray-50">
              <div className="space-y-4 p-4">
                {error && (
                  <div className="bg-red-100 text-red-800 p-3 rounded-md text-sm">
                    Error: {error}
                  </div>
                )}
                
                {messages.length === 0 ? (
                  <div className="text-center text-gray-500 mt-8">
                    <MessageSquare className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p className="text-sm">Start a conversation...</p>
                  </div>
                ) : (
                  <div>
                    {messages.map((message) => (
                      <ChatMessage
                        key={message.id}
                        content={message.content}
                        role={message.sender as 'user' | 'assistant'}
                        timestamp={message.timestamp}
                        isStreaming={message.isStreaming}
                      />
                    ))}
                    <div ref={messagesEndRef} />
                  </div>
                )}
              </div>
            </div>

            {/* Chat Input */}
            <div className="flex-shrink-0">
              <ChatInput
                onSend={handleSendMessage}
                disabled={isStreaming}
                isLoading={isStreaming}
                placeholder="Type your message..."
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Debug Panels */}
      {showDebugPanel && (
        <div className="px-6 pb-6">
          <Card className="bg-white shadow-sm">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg font-semibold text-gray-900">Stream Debug Panel</CardTitle>
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={() => setShowDebugPanel(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="px-6 pb-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {['status', 'reasoning', 'sources', 'content'].map((type) => (
                  <div key={type} className="flex flex-col h-64 border border-gray-200 rounded-lg overflow-hidden bg-white shadow-sm">
                    <div className={cn(
                      "text-xs font-semibold text-center py-2 px-2 text-white flex-shrink-0",
                      type === 'status' && "bg-blue-600",
                      type === 'reasoning' && "bg-purple-600", 
                      type === 'sources' && "bg-orange-600",
                      type === 'content' && "bg-green-600"
                    )}>
                      {type.toUpperCase()}
                    </div>
                    <div className="flex-1 p-3 overflow-y-auto bg-gray-50 text-xs">
                      {debugMessages[type as keyof typeof debugMessages].length === 0 ? (
                        <div className="text-gray-400 text-center mt-8 text-xs">No data yet</div>
                      ) : (
                        debugMessages[type as keyof typeof debugMessages].map((msg, index) => (
                          <div key={index} className="mb-2 p-2 bg-white rounded border border-gray-200">
                            <div className="text-gray-500 font-medium mb-1" style={{fontSize: '10px'}}>
                              {msg.timestamp.toLocaleTimeString()}
                            </div>
                            <pre className="whitespace-pre-wrap break-words" style={{fontSize: '10px'}}>
                              {JSON.stringify(msg.data, null, 2)}
                            </pre>
                          </div>
                        ))
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Show Debug Panel Button (when hidden) */}
      {!showDebugPanel && (
        <div className="px-6 pb-6">
          <Button 
            variant="outline" 
            onClick={() => setShowDebugPanel(true)}
            className="w-full"
          >
            Show Stream Debug Panel
          </Button>
        </div>
      )}
    </div>
  );
};

export default ModernChatInterface;
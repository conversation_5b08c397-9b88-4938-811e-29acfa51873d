"""
LangGraph service wrapper.

Thin adapter between FastAPI and LangGraph workflows.

This layer ONLY handles format translation:
- Fast<PERSON><PERSON> dicts → LangChain Message objects
- LangGraph events → SSE StreamMessage format
- Error handling for HTTP layer

NO workflow logic belongs here. All workflow logic is in workflows.py.
"""

import asyncio
import json
from typing import AsyncGenerator, List, Dict, Optional
from langchain_core.messages import HumanMessage, AIMessage

from app.agent.langgraph.workflows import get_graph, get_all_tools
from app.agent.langgraph.context import RuntimeContext
from app.services.streaming import StreamMessage, StreamType
from app.core.logging import get_logger
from app.core.config import settings

logger = get_logger(__name__)


class LangGraphService:
    """
    Service for executing LangGraph workflows with streaming support.

    Uses LangGraph-native dependency injection pattern with RuntimeContext.
    The workflow is parameterless and receives dependencies via config dict.
    """

    def __init__(self):
        """
        Initialize LangGraph service (parameterless).

        Resources are initialized lazily on first use via _ensure_setup().
        """
        self._context: RuntimeContext = None

        logger.info("LangGraph service initialized (lazy loading enabled)")

    async def _ensure_setup(self):
        """
        Lazy initialization of context.

        This method:
        1. Gets discovered tools from workflows module (workflows handles discovery)
        2. Creates RuntimeContext with tools and configuration

        Called automatically on first request.
        """
        if self._context is not None:
            return  # Already initialized

        logger.info("Performing lazy setup of RuntimeContext")

        # Get tools from workflows module (workflows.py handles discovery)
        tools = await get_all_tools()

        if not tools:
            logger.warning("No tools available - agent will have no tools")

        logger.info("Creating RuntimeContext with model deployments",
                   extra={
                       "tool_count": len(tools),
                       "fast_model": settings.fast_model_deployment,
                       "synthesis_model": settings.synthesis_model_deployment,
                       "fast_temp": settings.fast_model_temperature,
                       "synthesis_temp": settings.synthesis_model_temperature
                   })

        self._context = RuntimeContext(
            tools=tools or [],  # Provide empty list if no tools
            genie_space_id=settings.genie_space or "default-space",
            max_iterations=3,
            fast_model=settings.fast_model_deployment,
            synthesis_model=settings.synthesis_model_deployment,
            fast_model_temperature=settings.fast_model_temperature,
            synthesis_model_temperature=settings.synthesis_model_temperature,
            genie_max_iterations=settings.genie_subagent_max_iterations,
            graphrag_max_iterations=settings.graphrag_subagent_max_iterations
        )

        logger.info("RuntimeContext created",
                   extra={
                       "context": str(self._context),
                       "actual_fast_model": self._context.fast_model,
                       "actual_synthesis_model": self._context.synthesis_model
                   })

    async def generate_response_with_streaming(
        self,
        messages: List[Dict[str, str]],
        conversation_id: str
    ) -> AsyncGenerator[StreamMessage, None]:
        """
        Generate streaming response using LangGraph workflow with native streaming.

        Uses astream_events() for true token-by-token streaming instead of
        blocking ainvoke() with manual chunking.

        Args:
            messages: Conversation history in OpenAI format
            conversation_id: Session identifier

        Yields:
            StreamMessage: Status, reasoning, sources, and content messages
        """
        # Ensure context is ready
        await self._ensure_setup()

        # Get workflow from workflows module
        workflow_app = await get_graph()

        # Extract user query from last message
        user_query = messages[-1]["content"] if messages else ""

        logger.info("Starting LangGraph workflow with native streaming",
                   extra={
                       "conversation_id": conversation_id,
                       "user_query": user_query,  # Full query
                       "query_length": len(user_query),
                       "tool_count": len(self._context.tools)
                   })

        # Yield status update
        yield StreamMessage(StreamType.STATUS, {"message": "Analyzing your request..."})

        # Prepare initial state
        initial_state = {
            "messages": [HumanMessage(content=msg["content"]) for msg in messages]
        }

        # Prepare config with RuntimeContext injection
        config = {
            "configurable": {
                "context": self._context
            },
            "recursion_limit": 50  # Allow supervisor + subagent loops (supervisor → subagent → supervisor synthesis)
        }

        try:
            # Use native streaming with astream_events
            logger.info("Starting astream_events with native streaming")

            # Accumulate complete response for logging
            complete_response = []

            # Stream events from LangGraph workflow
            async for event in workflow_app.astream_events(
                initial_state,
                config,
                version="v2"
            ):
                # Translate LangGraph event to StreamMessage
                stream_msg = _translate_event(event)
                if stream_msg:
                    # Accumulate content chunks for logging
                    if stream_msg.type == StreamType.CONTENT and "chunk" in stream_msg.data:
                        complete_response.append(stream_msg.data["chunk"])
                    yield stream_msg

            # Final status
            yield StreamMessage(StreamType.STATUS, {"message": "Response complete"})

            # Log complete synthesized response
            synthesized_text = "".join(complete_response)
            logger.info("LangGraph workflow completed successfully",
                       extra={
                           "conversation_id": conversation_id,
                           "synthesized_response": synthesized_text,
                           "response_length": len(synthesized_text)
                       })

        except Exception as e:
            logger.error("LangGraph workflow failed",
                        extra={
                            "error": str(e),
                            "error_type": type(e).__name__,
                            "conversation_id": conversation_id
                        },
                        exc_info=True)

            error_msg = f"I encountered an error processing your request: {str(e)}"
            yield StreamMessage(StreamType.CONTENT, {"chunk": error_msg})


def _translate_event(event: Dict) -> Optional[StreamMessage]:
    """
    Translate LangGraph event to StreamMessage.

    LangGraph Event Types:
    - on_chat_model_start: Model begins processing
    - on_chat_model_stream: Token streaming (real-time tokens!)
    - on_chat_model_end: Model completes
    - on_tool_start: Tool execution begins
    - on_tool_end: Tool execution completes
    - on_chain_start/end: Node execution events

    Semantic Mapping:
    - STATUS: Workflow progress ("Supervisor routing...", "Calling tool...")
    - REASONING: Decision explanations (supervisor's reasoning field)
    - CONTENT: Final agent responses to user

    Args:
        event: LangGraph event dictionary

    Returns:
        StreamMessage or None if event should be ignored
    """
    event_type = event.get("event")
    event_name = event.get("name", "")
    run_id = event.get("run_id")

    # Model starts - this is a STATUS update (what's happening), not reasoning
    if event_type == "on_chat_model_start":
        return StreamMessage(
            StreamType.STATUS,
            {"message": "🤖 Model analyzing..."}
        )

    # Token streaming - THIS IS THE MAGIC! Real-time tokens as they're generated
    elif event_type == "on_chat_model_stream":
        chunk_data = event.get("data", {}).get("chunk", {})

        # Get node context from LangGraph's native metadata
        metadata = event.get("metadata", {})
        langgraph_node = metadata.get("langgraph_node")

        # Filter content by tags: only stream from supervisor, not subagents
        if hasattr(chunk_data, "content") and chunk_data.content:
            tags = event.get("tags", [])

            # Skip subagent responses (tagged with "subagent")
            if "subagent" in tags:
                return None  # Don't stream subagent intermediate responses

            # Stream supervisor's synthesis (tagged with "supervisor")
            return StreamMessage(
                StreamType.CONTENT,
                {"chunk": chunk_data.content}
            )

    # Tool execution starts - this is STATUS (what's happening now)
    elif event_type == "on_tool_start":
        tool_name = event.get("name", "unknown")
        return StreamMessage(
            StreamType.STATUS,
            {"message": f"🔧 Calling tool: {tool_name}"}
        )

    # Tool execution completes
    elif event_type == "on_tool_end":
        tool_name = event.get("name", "unknown")
        return StreamMessage(
            StreamType.SOURCES,
            {"source": tool_name}
        )

    # Node execution events (for workflow visibility)
    elif event_type == "on_chain_start":
        # These are STATUS updates (workflow progress), not reasoning
        # Detect which node is starting
        if event_name == "supervisor":
            return StreamMessage(
                StreamType.STATUS,
                {"message": "🧭 Supervisor routing query..."}
            )
        elif event_name == "genie_subagent":
            return StreamMessage(
                StreamType.STATUS,
                {"message": "🚨 Using Genie emergency data subagent..."}
            )
        elif event_name == "graphrag_subagent":
            return StreamMessage(
                StreamType.STATUS,
                {"message": "🕸️ Using GraphRAG knowledge subagent..."}
            )

    # Ignore other events
    return None

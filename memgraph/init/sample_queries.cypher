// Sample GraphRAG Queries for Emergency Management
// These demonstrate the types of queries the GraphRAG MCP server will execute

// Query 1: Find all resources that responded to Hurricane Milton
MATCH (incident:Incident {title: 'Hurricane Milton'})-[:RESPONDED_BY]->(resource:Resource)
RETURN incident.title, resource.name, resource.type, resource.category
ORDER BY resource.type, resource.name;

// Query 2: Find departments and their coordination relationships
MATCH (d1:Department)-[coord:COORDINATES_WITH]->(d2:Department)
RETURN d1.name, d2.name, coord.coordination_type, coord.frequency, coord.joint_protocols
ORDER BY d1.name;

// Query 3: Find all facilities within emergency response network
MATCH (dept:Department)-[:OPERATES]->(facility:Facility)
RETURN dept.name as department, facility.name as facility, facility.type, facility.operational_status
ORDER BY dept.name, facility.type;

// Query 4: Find resource deployment patterns for structure fires
MATCH (incident:Incident {subtype: 'structure_fire'})-[resp:RESPONDED_BY]->(resource:Resource)
RETURN incident.title,
       resource.name,
       resource.type,
       resp.response_time_minutes,
       resp.primary_role,
       resp.resource_effectiveness
ORDER BY incident.timestamp DESC, resp.response_time_minutes;

// Query 5: Find mutual aid relationships between resources
MATCH (r1:Resource)-[aid:MUTUAL_AID_RESOURCE]->(r2)
RETURN r1.name, r1.type, r2.name, r2.type, aid.agreement_type, aid.activation_conditions
ORDER BY aid.agreement_type;

// Query 6: Emergency shelter utilization during disasters
MATCH (incident:Incident)-[util:UTILIZED_FACILITY]->(shelter:Facility {type: 'emergency_shelter'})
RETURN incident.title,
       incident.type,
       shelter.name,
       util.occupancy_peak,
       util.duration_days,
       util.services_provided
ORDER BY incident.timestamp DESC;

// Query 7: Find cascading incident relationships
MATCH (primary:Incident)-[cascade:CAUSED|TRIGGERED]->(secondary:Incident)
RETURN primary.title as primary_incident,
       primary.type as primary_type,
       type(cascade) as relationship_type,
       secondary.title as caused_incident,
       secondary.type as caused_type,
       cascade.delay_hours as delay_hours
ORDER BY primary.timestamp;

// Query 8: Resource availability by department and type
MATCH (dept:Department)-[:OWNS]->(resource:Resource)
WHERE resource.status IN ['available', 'in_service']
RETURN dept.name,
       resource.type,
       count(resource) as available_count,
       collect(resource.name) as resources
ORDER BY dept.name, resource.type;

// Query 9: Geographic impact analysis
MATCH (incident:Incident)-[impact:IMPACTED_AREA]->(area:GeographicArea)
RETURN incident.title,
       incident.severity,
       area.name,
       impact.population_affected,
       impact.infrastructure_damage,
       impact.recovery_time_days
ORDER BY impact.population_affected DESC;

// Query 10: Hospital capacity and trauma level analysis
MATCH (hospital:Facility {type: 'hospital'})
RETURN hospital.name,
       hospital.capacity,
       hospital.emergency_beds,
       hospital.icu_beds,
       hospital.trauma_level,
       hospital.serves_area
ORDER BY hospital.emergency_beds DESC;

// Query 11: Response time analysis for medical emergencies
MATCH (med_incident:Incident {type: 'medical_emergency'})-[resp:RESPONDED_BY]->(ambulance:Resource {type: 'ambulance'})
RETURN med_incident.title,
       ambulance.name,
       resp.response_time_minutes,
       resp.resource_effectiveness,
       med_incident.casualties,
       med_incident.timestamp
ORDER BY resp.response_time_minutes;

// Query 12: Find fire stations and their primary resources
MATCH (fire_dept:Department {type: 'fire'})-[:OPERATES]->(station:Facility {type: 'fire_station'})
MATCH (fire_dept)-[:OWNS]->(resource:Resource)-[:STATIONED_AT]->(station)
RETURN station.name,
       station.station_number,
       resource.name,
       resource.type,
       resource.manufacture_year,
       resource.status
ORDER BY station.station_number, resource.type;

// Query 13: Emergency command and control resources
MATCH (resource:Resource)
WHERE resource.category IN ['command_control', 'reconnaissance']
MATCH (dept:Department)-[:OWNS]->(resource)
RETURN dept.name,
       resource.name,
       resource.type,
       resource.capacity,
       resource.status,
       resource.communication_systems
ORDER BY dept.name;

// Query 14: Wildfire response capability analysis
MATCH (wildfire:Incident {subtype: 'wildfire'})-[resp:RESPONDED_BY]->(resource:Resource)
MATCH (dept:Department)-[:OWNS]->(resource)
RETURN wildfire.title,
       wildfire.acres_burned,
       dept.name,
       resource.name,
       resource.type,
       resp.deployment_duration_hours,
       resp.resource_effectiveness
ORDER BY wildfire.acres_burned DESC;

// Query 15: Multi-agency coordination for major incidents
MATCH (incident:Incident)
WHERE incident.severity IN ['major', 'high']
MATCH (incident)-[:RESPONDED_BY]->(resource:Resource)
MATCH (dept:Department)-[:OWNS]->(resource)
RETURN incident.title,
       incident.severity,
       count(DISTINCT dept) as departments_involved,
       count(DISTINCT resource) as resources_deployed,
       collect(DISTINCT dept.name) as departments,
       incident.estimated_damage
ORDER BY departments_involved DESC, incident.estimated_damage DESC;

// Performance test queries for GraphRAG optimization

// Complex path query: Find shortest coordination path between departments
MATCH path = shortestPath((d1:Department {name: 'City Fire Department'})-[:COORDINATES_WITH*]-(d2:Department {name: 'County Sheriff Department'}))
RETURN length(path) as path_length, nodes(path) as coordination_path;

// Aggregation query: Resource utilization statistics
MATCH (resource:Resource)-[resp:RESPONDED_BY]-(incident:Incident)
RETURN resource.type,
       count(incident) as deployments,
       avg(resp.response_time_minutes) as avg_response_time,
       sum(resp.deployment_duration_hours) as total_hours_deployed,
       collect(DISTINCT incident.type) as incident_types_served
ORDER BY deployments DESC;

// Temporal query: Incident patterns by time
MATCH (incident:Incident)
WHERE incident.timestamp >= datetime('2024-07-01T00:00:00Z')
RETURN incident.type,
       incident.subtype,
       extract(month FROM incident.timestamp) as month,
       count(*) as incident_count,
       avg(incident.estimated_damage) as avg_damage
ORDER BY month, incident_count DESC;
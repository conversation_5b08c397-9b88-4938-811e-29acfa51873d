# GraySkyGenAI Assistant Configuration Template
# Copy this file to .env and update with your actual values

# Basic App Settings
APP_NAME="GraySkyGenAI Assistant"
DEBUG=false
VERSION="0.1.0"
API_V1_STR="/api/v1"

# # Server Configuration
# HOST="0.0.0.0"
# PORT=8000

# CORS Settings (JSON array of allowed origins)
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://127.0.0.1:3000"]

# Azure OpenAI Configuration (via Databricks)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_ENDPOINT=https://genai-graysky-openai-dev.openai.azure.com/
API_VERSION=2024-12-01-preview
# Get keys via portal.azure.com or via Azure AI Foundry

# Model-specific deployments for different purposes
# Subgraph Agent Model
FAST_MODEL_DEPLOYMENT=gpt-4.1              # Azure deployment with deployment name gpt-4.1
FAST_MODEL_TEMPERATURE=0.0                 # Temperature for fast model (0 for planning and analysis)
# Supervisor Agent Model
SYNTHESIS_MODEL_DEPLOYMENT=gpt-4.1         # Azure deployment with deployment name gpt-4.1
SYNTHESIS_MODEL_TEMPERATURE=0.3            # Temperature for synthesis model

# Genie MCP Configuration
GENIE_MCP_ENABLED=true
GENIE_MCP_URL=https://genai-graysky-dev-mcp.azurewebsites.net # Change this to actual Genie MCP URL, if changed
GENIE_SPACE=01f06ee9ff2111d7b201ab3b5a1a84fd                  # Confirm that this is used; can also get from Genie get_spaces tool
GENIE_SUBAGENT_MAX_ITERATIONS=2              # Max iterations for Genie subagent (incident data)

# GraphRAG MCP Configuration
GRAPHRAG_MCP_ENABLED=true
GRAPHRAG_MCP_URL=http://localhost:8001/mcp     # local testing; in docker - http://graphrag-mcp:8001/mcp
GRAPHRAG_MCP_TIMEOUT=30
GRAPHRAG_SUBAGENT_MAX_ITERATIONS=10           # Max iterations for GraphRAG subagent (graph queries)

# LangGraph Configuration
# LangGraph is the only orchestration method (no feature flag)
LANGGRAPH_CHECKPOINTER=memory                  # Options: memory, postgres
LANGGRAPH_POSTGRES_URL=                        # Only needed if using postgres checkpointer

# Caching Configuration
CACHE_TTL_GENIE=900                            # Cache TTL in seconds (15 min for incident data)
CACHE_TTL_GRAPH=3600                           # Cache TTL in seconds (1 hour for graph data)
CACHE_ENABLE_SEMANTIC_DEDUP=true               # Enable semantic entity deduplication

# Arize Phoenix Configuration
# For Docker Compose setup and production deployment, see: ../phoenix/
PHOENIX_ENABLED=true
PHOENIX_ENDPOINT=http://127.0.0.1:6006           # Default for Docker Compose setup, change for production
PHOENIX_SERVICE_NAME=graysky-assistant
PHOENIX_SERVICE_VERSION=0.1.0
PHOENIX_ENVIRONMENT=development                   # development, staging, production

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_CORRELATION_ENABLED=true
LOG_PERFORMANCE_TRACKING=true
LOG_SCRUB_SENSITIVE=true
LOG_CORRELATION_HEADER=X-Correlation-ID
# Note: These LOG_* variables are processed separately and don't map to Settings class fields
# They are safely ignored by the main configuration loader
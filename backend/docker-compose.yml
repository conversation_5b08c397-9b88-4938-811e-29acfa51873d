# Backend API Docker Compose
# This file works standalone for production or with parent docker-compose for local dev

version: '3.8'

services:
  backend:
    build:
      context: .              # Build from /backend/ root
      dockerfile: Dockerfile
      target: ${BUILD_TARGET:-development}     # Configurable for production
    container_name: ${CONTAINER_NAME:-backend}
    ports:
      - "${BACKEND_PORT:-8000}:8000"
    volumes:
      - ./src:/app                # Mount src/ subdirectory for hot-reload
      - /app/.venv                # Exclude .venv from mount (container has its own)
      - /app/__pycache__          # Exclude Python cache
    environment:
      # Development-friendly settings (override in production)
      - DEBUG=${DEBUG:-true}
      - LOG_FORMAT=${LOG_FORMAT:-text}
      - LOG_LEVEL=${LOG_LEVEL:-DEBUG}

      # External service endpoints (configurable for production)
      - PHOENIX_ENABLED=${PHOENIX_ENABLED:-false}
      - PHOENIX_ENDPOINT=${PHOENIX_ENDPOINT:-http://localhost:6006}
      - GRAPHRAG_MCP_URL=${GRAPHRAG_MCP_URL:-http://localhost:8001/mcp}

    env_file:
      - ./src/.env                # Load environment variables from src/.env
    networks:
      - default
    # Enable host network access for services like Phoenix running on host
    extra_hosts:
      - "host.docker.internal:host-gateway"
    restart: unless-stopped

networks:
  default:
    name: ${NETWORK_NAME:-backend-network}
    external: ${EXTERNAL_NETWORK:-false}

# Notes:
# - For local dev: Use parent docker-compose which provides phoenix and graphmcp
# - For production: Set BUILD_TARGET=production, configure external service URLs
# - Set EXTERNAL_NETWORK=true in production to join existing network
# - Service-specific config in src/.env.example (API keys, model config, etc.)
# - This file only handles docker-compose orchestration variables

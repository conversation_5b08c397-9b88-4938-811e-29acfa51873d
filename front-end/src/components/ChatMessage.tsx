import React from 'react';
import { cn } from '@/lib/utils';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { atomDark } from 'react-syntax-highlighter/dist/cjs/styles/prism';
import rehypeRaw from 'rehype-raw';
import remarkGfm from 'remark-gfm';
import remarkBreaks from 'remark-breaks';

interface ChatMessageProps {
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  isStreaming?: boolean;
  conversationId?: string | null;
}

const ChatMessage: React.FC<ChatMessageProps> = ({ 
  content, 
  role, 
  timestamp, 
  isStreaming = false, 
  conversationId = null 
}) => {
  const formatTime = (date: Date): string => {
    return date.toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit',
      hour12: true 
    });
  };

  const isUser = role === 'user';

  return (
    <div 
      className={cn(
        'flex w-full mb-4',
        isUser ? 'justify-end' : 'justify-start'
      )}
    >
      <div className={cn(
        'max-w-[85%] flex flex-col',
        isUser ? 'items-end' : 'items-start'
      )}>
        {!isUser && conversationId && (
          <div 
            style={{ 
              fontSize: '0.65rem', 
              transform: 'scale(0.85)', 
              transformOrigin: 'right bottom' 
            }} 
            className="text-gray-400 mb-0.5 px-1 self-end overflow-hidden text-ellipsis whitespace-nowrap max-w-[150px]"
          >
            Chat ID: {conversationId}
          </div>
        )}
        <div 
          className={cn(
            'px-4 py-3 rounded-lg text-sm leading-relaxed break-words relative',
            isUser 
              ? 'bg-gradient-to-r from-blue-600 to-indigo-700 text-white whitespace-pre-wrap' 
              : 'bg-gray-50 text-gray-800 border border-gray-200'
          )} 
          style={{ 
            maxWidth: '100%', 
            overflowWrap: 'anywhere', 
            wordBreak: 'break-word' 
          }}
        >
          {isUser ? (
            <div className="user-message">
              {content}
            </div>
          ) : (
            <div 
              className={cn(
                "assistant-message",
                "prose prose-sm max-w-none"
              )} 
              style={{ 
                maxWidth: '100%', 
                overflowWrap: 'anywhere', 
                wordBreak: 'break-word' 
              }}
            >
              <ReactMarkdown
                remarkPlugins={[remarkGfm, remarkBreaks]}
                rehypePlugins={[rehypeRaw]}
                components={{
                  code({className, children, ...props}: any) {
                    const match = /language-(\w+)/.exec(className || '');
                    return !props.inline && match ? (
                      <div className="relative">
                        <div 
                          style={{ 
                            overflowX: 'auto',
                            overflowY: 'hidden',
                            border: '1px solid #d1d5db',
                            borderRadius: '6px',
                            position: 'relative'
                          }}
                          className="force-scrollbar"
                        >
                          <div 
                            style={{
                              position: 'absolute',
                              bottom: 0,
                              left: 0,
                              right: 0,
                              height: '12px',
                              background: '#f3f4f6',
                              borderTop: '1px solid #d1d5db',
                              display: 'flex',
                              alignItems: 'center',
                              padding: '0 4px',
                              fontSize: '10px',
                              color: '#6b7280',
                              zIndex: 10
                            }}
                          >
                            ← Scroll to see full query →
                          </div>
                          <SyntaxHighlighter
                            style={atomDark}
                            language={match[1]}
                            PreTag="div"
                            wrapLines={false}
                            wrapLongLines={false}
                            customStyle={{ 
                              whiteSpace: 'nowrap',
                              borderRadius: '4px',
                              fontSize: '0.85em',
                              margin: 0,
                              display: 'inline-block',
                              minWidth: '100%'
                            }}
                            {...props}
                          >
                            {String(children).replace(/\n$/, '')}
                          </SyntaxHighlighter>
                        </div>
                      </div>
                    ) : (
                      <code 
                        className={cn(className, "bg-gray-100 px-1 py-0.5 rounded text-red-600")} 
                        style={{ wordBreak: 'break-word', whiteSpace: 'pre-wrap' }} 
                        {...props}
                      >
                        {children}
                      </code>
                    );
                  },
                  pre({children}: any) {
                    return (
                      <div 
                        className="w-full my-2" 
                        style={{ 
                          maxWidth: '100%', 
                          wordBreak: 'break-all', 
                          overflowWrap: 'anywhere' 
                        }}
                      >
                        {children}
                      </div>
                    );
                  },
                  table({children}: any) {
                    return (
                      <div className="overflow-x-auto my-2">
                        <table className="border-collapse border border-gray-300 text-sm">
                          {children}
                        </table>
                      </div>
                    );
                  },
                  th({children}: any) {
                    return (
                      <th className="border border-gray-300 px-4 py-2 bg-gray-100 text-left">
                        {children}
                      </th>
                    );
                  },
                  td({children}: any) {
                    return (
                      <td className="border border-gray-300 px-4 py-2">
                        {children}
                      </td>
                    );
                  },
                  p({children}: any) {
                    return <p className="mb-2">{children}</p>;
                  }
                }}
              >
                {content}
              </ReactMarkdown>
            </div>
          )}
          {isStreaming && !isUser && (
            <span 
              className="inline-block w-2 h-2 ml-2 bg-blue-500 rounded-full animate-pulse"
            />
          )}
        </div>
        <div className="text-xs text-gray-500 mt-1 px-1">
          {formatTime(timestamp)}
        </div>
      </div>
    </div>
  );
};

export default ChatMessage;
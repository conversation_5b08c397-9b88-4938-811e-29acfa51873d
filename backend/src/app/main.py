from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware

from app.api.api_v1.api import api_router
from app.core.config import settings
from app.core.logging import setup_logging, get_logger
from app.middleware import CorrelationMiddleware
from app.core.phoenix_config import setup_phoenix_telemetry

# Initialize structured logging
setup_logging(settings.logging)
logger = get_logger("main")

# Initialize Phoenix telemetry (connects to external Phoenix instance)
if settings.phoenix.enabled:
    logger.info("Initializing Phoenix telemetry")
    setup_phoenix_telemetry()
    logger.info("Phoenix telemetry initialized")

app = FastAPI(
    title=settings.app_name,
    version=settings.version,
    openapi_url=f"{settings.api_v1_str}/openapi.json"
)

# Instrument FastAPI after app creation
if settings.phoenix.enabled:
    from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
    FastAPIInstrumentor.instrument_app(app)

# Add correlation middleware (before CORS to ensure correlation ID is available)
app.add_middleware(
    CorrelationMiddleware,
    correlation_header=settings.logging.correlation_header
)

# Set up CORS middleware
if settings.backend_cors_origins:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.backend_cors_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

# Include API router
app.include_router(api_router, prefix=settings.api_v1_str)


@app.get("/")
async def root():
    """Root endpoint for health check."""
    logger.info("Root endpoint accessed")
    return {"message": f"Welcome to {settings.app_name} API"}


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    logger.info("Health check accessed")
    return {"status": "healthy", "version": settings.version}


@app.get("/phoenix-health")
async def phoenix_health():
    """Phoenix observability health check."""
    return {
        "phoenix_enabled": settings.phoenix.enabled,
        "phoenix_endpoint": settings.phoenix.endpoint if settings.phoenix.enabled else None
    }
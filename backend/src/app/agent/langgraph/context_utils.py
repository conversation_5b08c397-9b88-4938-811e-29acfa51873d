"""
Context utilities for LangGraph workflows.

This module contains functions for generating dynamic context strings
from live graph analysis data. The context is injected into the LangGraph
supervisor as a system message to provide the LLM with up-to-date information
about the current state of the graph.
"""

from typing import Dict, Any
from app.core.logging import get_logger

logger = get_logger(__name__)

def _generate_dynamic_context(graph_context: Dict[str, Any]) -> str:
    """
    Generate dynamic context from live graph analysis data.
    
    Args:
        graph_context: Graph analysis data containing statistics, highDegreeNodes,
                      nodeLabels, and relationshipTypes.
    
    Returns:
        Formatted dynamic context string with live data.
    """
    # 🔍 DEBUG: Log input to context generation
    try:
        logger.info("=== GENERATING DYNAMIC CONTEXT ===")
        logger.info("UNCOMMENT TO SHOW INPUT")
        # logger.info(f"Input graph_context keys: {list(graph_context.keys()) if isinstance(graph_context, dict) else 'Not a dict'}")
    except:
        logger.info("Failed to log input to dynamic context generation")

    context_parts = ["<initial_context>\n"]
    context_parts.append("# Florida Emergency Management GraphRAG - Agent Context\n\n")
    
    # Add Current Graph State section
    context_parts.append("## Current Graph State (Live Data)\n\n")
    
    # Statistics
    stats = graph_context.get("statistics", {})
    if stats:
        context_parts.append("### Statistics\n")
        
        # Format numbers with commas, fallback to "N/A" if not a number
        total_nodes = stats.get('totalNodes')
        context_parts.append(f"- **Total Nodes:** {total_nodes:,}\n" if isinstance(total_nodes, (int, float)) else f"- **Total Nodes:** {total_nodes or 'N/A'}\n")
        
        total_rels = stats.get('totalRelationships')
        context_parts.append(f"- **Total Relationships:** {total_rels:,}\n" if isinstance(total_rels, (int, float)) else f"- **Total Relationships:** {total_rels or 'N/A'}\n")
        
        node_labels = stats.get('nodeLabelCount')
        context_parts.append(f"- **Entity Types:** {node_labels}\n" if node_labels is not None else "- **Entity Types:** N/A\n")
        
        rel_types = stats.get('relationshipTypeCount')
        context_parts.append(f"- **Relationship Types:** {rel_types}\n" if rel_types is not None else "- **Relationship Types:** N/A\n")
        
        if 'averageDegree' in stats and isinstance(stats['averageDegree'], (int, float)):
            context_parts.append(f"- **Average Connections per Node:** {stats['averageDegree']:.2f}\n")
        
        context_parts.append("\n")
    
    # High-degree nodes (most active incidents)
    high_degree = graph_context.get("highDegreeNodes", [])
    if high_degree:
        context_parts.append("### Most Active Incidents (High-Degree Nodes)\n")
        context_parts.append("These incidents have the highest number of related missions:\n\n")
        for node in high_degree[:10]:  # Top 5
            props = node.get("properties", {})
            degree = node.get("degree", 0)
            incident_name = props.get("incident_name", "Unknown")
            incident_status = props.get("incident_status", "N/A")
            incident_type = props.get("incident_type", "N/A")
            context_parts.append(f"- **{incident_name}** ({incident_type}, Status: {incident_status}): {degree:,} connections\n")
        context_parts.append("\n")
    
    # Available entity types with properties
    node_labels = graph_context.get("nodeLabels", [])
    if node_labels:
        context_parts.append("### Available Entity Types\n\n")
        for label_info in node_labels:
            label = label_info.get("label", "Unknown")
            count = label_info.get("count", 0)
            properties = label_info.get("properties", [])
            context_parts.append(f"**{label}** ({count:,} nodes)\n")
            if properties:
                context_parts.append(f"- Properties: {', '.join(properties[:10])}\n")  # First 10 properties
            context_parts.append("\n")
    
    # Available relationship types
    rel_types = graph_context.get("relationshipTypes", [])
    if rel_types:
        context_parts.append("### Available Relationship Types\n\n")
        for rel_info in rel_types:
            rel_type = rel_info.get("type", "Unknown")
            count = rel_info.get("count", 0)
            context_parts.append(f"- **{rel_type}**: {count:,} relationships\n")
        context_parts.append("\n")
    
    # Add tool documentation (keep static)
    context_parts.append(_get_tool_documentation())
    
    context_parts.append("</initial_context>\n")

    final_context = "".join(context_parts)

    # 🔍 DEBUG: Log final generated context
    try:
        logger.info("=== FINAL GENERATED CONTEXT ===")
        logger.info(f"Context length: {len(final_context)} characters")
        logger.info("UNCOMMENT TO SHOW CONTEXT")
        # logger.info(final_context)
        logger.info("=== END FINAL GENERATED CONTEXT ===")
    except:
        logger.info("Failed to log final generated context")

    return final_context


def _get_tool_documentation() -> str:
    """
    Get static tool documentation section.
    
    Returns:
        Tool catalog and usage documentation.
    """
    return """---

## Tool Catalog & Usage

### Analysis Tools

**`analyze_knowledge_graph`**
- **Use first** to get current graph state
- Returns: statistics, high-degree nodes, schemas, samples, patterns
- Shows which incidents are most active (highest connection counts)
- Reveals available properties and relationship types
- **Call this at session start before other tools**

### Schema Tools

**`get_schema`**
- Complete schema: all entity types, relationship types, properties
- Use after analyze_knowledge_graph for formal schema definition

**`describe_entity_type(entity_type)`**
- Detailed info about specific entity (e.g., "Mission", "Incident")
- Property names, types, sample values

**`describe_relationship_type(relationship_type)`**
- Info about specific relationship (e.g., "RELATED_TO_INCIDENT")
- Source/target constraints, property schemas

### Search Tools

**`search_entities(entity_type, properties, limit)`**
- Find entities matching property filters
- Example: `search_entities("Incident", {"incident_status": "Active"})`
- Use for simple lookups by known property values

**`get_entity(entity_type, entity_id)`**
- Retrieve single entity by ID
- Returns full properties excluding embeddings

### Traversal Tools

**`get_neighbors(entity_type, entity_id, relationship_types, direction, depth)`**
- Find connected entities
- Specify direction: "incoming", "outgoing", or "both"
- Filter by relationship types

**`find_paths(from_type, from_id, to_type, to_id, max_depth)`**
- Find all paths between two entities
- Returns complete path details

---
"""


def get_initial_context(graph_context: Dict[str, Any]) -> str:
    """
    Generate dynamic initial context from live graph analysis data.

    Args:
        graph_context: Graph analysis data from analyze_knowledge_graph tool.
                      Must contain statistics, highDegreeNodes, nodeLabels, and relationshipTypes.

    Returns:
        Formatted context string with live graph data for LLM consumption.
    """
    logger.info("Generating dynamic context from graph_context")
    return _generate_dynamic_context(graph_context)

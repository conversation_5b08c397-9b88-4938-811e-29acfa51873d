# Florida Emergency Management GraphRAG - Agent Context

## Graph Structure

This graph tracks **Florida Division of Emergency Management** operations with 4 entity types and 4 relationship patterns:

### Entities
- **Incident** (hub nodes): Hurricanes, floods, crises - high connectivity
- **Mission** (operational tasks): Linked to incidents, may have parent-child hierarchies
- **Comment** (collaboration): Majority of nodes, tracks updates and communication
- **Vendor** (service providers): Small count, mostly embedded in mission properties

### Relationships
- **COMMENTED_ON**: Comment → Mission (majority of relationships)
- **RELATED_TO_INCIDENT**: Mission → Incident (every mission links to one incident)
- **IS_PARENT_TO**: Mission → Mission (hierarchical task structure)
- **ASSIGNED_VENDOR**: Mission → Vendor (sparse, most vendor data in properties)

### Key Properties

**Incident:**
- `incident_id`, `incident_name`, `incident_type`, `incident_status`
- `incident_created_at`, `incident_updated_at`

**Mission:**
- `id`, `mission_number`, `title`
- `incidents` (JSON array of incident objects)
- `transaction_count`, `transactions` (JSON array of fuel data)
- `fuel_vendor_name`, `fuel_types`, `total_fuel_dispensed` (when applicable)
- `transaction_counties`, `transaction_vendors` (arrays)
- `vendor_id`, `mission_vendor_name`, `vendor_updated_at` (when applicable)

**Comment:**
- `comment_id`, `text`, `created_at`, `created_by`

**Vendor:**
- `vendor_id`, `mission_vendor_name`, `vendor_updated_at`

---

## Tool Catalog & Usage

### Analysis Tools

**`analyze_knowledge_graph`**
- **Use first** to get current graph state
- Returns: statistics, high-degree nodes, schemas, samples, patterns
- Shows which incidents are most active (highest connection counts)
- Reveals available properties and relationship types
- **Call this at session start before other tools**

### Schema Tools

**`get_schema`**
- Complete schema: all entity types, relationship types, properties
- Use after analyze_knowledge_graph for formal schema definition

**`describe_entity_type(entity_type)`**
- Detailed info about specific entity (e.g., "Mission", "Incident")
- Property names, types, sample values

**`describe_relationship_type(relationship_type)`**
- Info about specific relationship (e.g., "RELATED_TO_INCIDENT")
- Source/target constraints, property schemas

**`get_node_statistics`**
- Node counts by label
- Use for understanding entity distribution

**`get_relationship_statistics`**
- Relationship counts by type
- Use for understanding connectivity patterns

### Search Tools

**`search_entities(entity_type, properties, limit)`**
- Find entities matching property filters
- Example: `search_entities("Incident", {"incident_name": "2024 Milton"})`
- Use for simple lookups by known property values

**`get_entity(entity_type, entity_id)`**
- Retrieve single entity by ID
- Returns full properties excluding embeddings

**`find_entities_by_time_range(entity_type, start_time, end_time, time_property)`**
- Find entities created/updated in time window
- Useful for temporal analysis

**`search_by_pattern(pattern, parameters)`**
- Cypher pattern matching with parameters
- More flexible than search_entities

### Traversal Tools

**`get_neighbors(entity_type, entity_id, relationship_type, direction, max_depth)`**
- Find connected entities
- Example: Get all missions for an incident
- Direction: "incoming", "outgoing", "both"

**`find_paths(from_type, from_id, to_type, to_id, max_length)`**
- Find paths between two entities
- Use for understanding connections

**`traverse_pattern(pattern, parameters, limit)`**
- Execute complex graph traversals
- Pattern uses Cypher syntax

**`find_common_neighbors(entity_type1, entity_id1, entity_type2, entity_id2)`**
- Find entities connected to both
- Use for discovering shared relationships

### Aggregation Tools

**`aggregate_by_type(entity_type, group_by, aggregation_function, target_property)`**
- Group and aggregate (COUNT, SUM, AVG, MIN, MAX)
- Example: Total fuel by vendor

**`calculate_statistics(entity_type, numeric_property, filters)`**
- Statistical analysis: mean, median, stddev, min, max
- Use for numeric properties

**`find_patterns(base_entity, pattern_type, filters, min_count)`**
- Discover recurring patterns
- Pattern types: "common_relationships", "frequent_properties", "similar_entities"

**`execute_cypher(query, parameters)`**
- Direct Cypher execution for complex queries
- Most flexible tool for advanced use cases

### Admin Tools

**`get_health`**
- Server and database health check

**`refresh_cache`**
- Refresh schema cache

**`get_query_suggestions(entity_type, intent)`**
- Get suggested queries for entity type

**`get_database_info`**
- Database version, storage, memory usage

---

## Query Workflows by Question Type

### "What incidents are currently active?"

1. **`analyze_knowledge_graph`** → Check `highDegreeNodes` for incidents with most connections
2. **`search_entities("Incident", {"incident_status": "Active"})`** → Get all active incidents

### "Show me all missions for Hurricane Milton"

1. **`analyze_knowledge_graph`** → Confirm incident name (e.g., "2024 Milton")
2. **`search_entities("Incident", {"incident_name": "2024 Milton"})`** → Get incident
3. **`get_neighbors("Incident", <incident_id>, "RELATED_TO_INCIDENT", "incoming")`** → Get missions

### "How much fuel was dispensed for incident X?"

1. **`analyze_knowledge_graph`** → Check if fuel properties exist
2. **`get_neighbors("Incident", <id>, "RELATED_TO_INCIDENT", "incoming")`** → Get missions
3. **`aggregate_by_type("Mission", "fuel_vendor_name", "SUM", "total_fuel_dispensed")`** → Sum fuel by vendor
4. Alternatively: **`execute_cypher`** with custom query to sum `total_fuel_dispensed`

### "What are the sub-missions for mission X?"

1. **`search_entities("Mission", {"mission_number": "00787"})`** → Find parent mission
2. **`get_neighbors("Mission", <id>, "IS_PARENT_TO", "outgoing")`** → Get children

### "Which vendors worked on incident X?"

1. **`get_neighbors("Incident", <id>, "RELATED_TO_INCIDENT", "incoming")`** → Get missions
2. Parse mission properties for `mission_vendor_name` and `fuel_vendor_name`
3. Alternatively: **`execute_cypher`** to extract distinct vendor names

### "Show me comments containing 'TAR extension'"

1. **`search_by_pattern("(c:Comment)", {})`** → Get comments (if supports text search)
2. Or **`execute_cypher`** with: `MATCH (c:Comment) WHERE c.text CONTAINS 'TAR' RETURN c`

### "What's the mission hierarchy tree?"

1. **`search_entities("Mission", {"mission_number": "00001"})`** → Get root
2. **`traverse_pattern("(m:Mission)-[:IS_PARENT_TO*]->(child)", {"m": {mission_id}})`** → Get full tree

### "Fuel distribution by county for incident X"

1. **`get_neighbors("Incident", <id>, "RELATED_TO_INCIDENT", "incoming")`** → Get missions
2. **`execute_cypher`** with:
```cypher
MATCH (i:Incident {incident_id: $id})<-[:RELATED_TO_INCIDENT]-(m:Mission)
WHERE m.transaction_counties IS NOT NULL
UNWIND m.transaction_counties AS county
RETURN county, sum(m.total_fuel_dispensed) as total_fuel
```

---

## Traversal Patterns

### Incident → Missions → Comments
```
analyze_knowledge_graph (find incident)
→ get_neighbors(Incident, id, RELATED_TO_INCIDENT, incoming) 
→ get_neighbors(Mission, mission_id, COMMENTED_ON, incoming)
```

### Mission → Parent Incident → All Sibling Missions
```
get_entity(Mission, id)
→ get_neighbors(Mission, id, RELATED_TO_INCIDENT, outgoing) [get incident]
→ get_neighbors(Incident, incident_id, RELATED_TO_INCIDENT, incoming) [get all missions]
```

### Mission → Children → Grandchildren
```
get_entity(Mission, id)
→ traverse_pattern("(m:Mission {id: $id})-[:IS_PARENT_TO*]->(descendants)", {id: mission_id})
```

---

## Key Principles

1. **Start with `analyze_knowledge_graph`** - Understand current state before querying
2. **Check high-degree nodes** - Incidents with most connections are most active
3. **Use simple tools first** - `search_entities` and `get_neighbors` before `execute_cypher`
4. **Vendor data is in mission properties** - Don't rely on ASSIGNED_VENDOR relationships
5. **Filter embeddings** - Mission nodes have large embedding vectors; exclude them
6. **Parse JSON properties** - `incidents` and `transactions` are JSON strings
7. **Use `execute_cypher` for complex queries** - When traversal/aggregation tools aren't enough
8. **Follow relationship direction** - RELATED_TO_INCIDENT goes Mission → Incident

---

## Common Filters

**Exclude embeddings from properties:**
```cypher
WHERE NOT key =~ '.*embedding.*'
```

**Filter missions with fuel data:**
```cypher
WHERE m.fuel_vendor_name IS NOT NULL
```

**Time-based filtering:**
```cypher
WHERE i.incident_created_at >= $start_date
```

**Array property filtering:**
```cypher
WHERE $county IN m.transaction_counties
```

---

## Domain Context

**ESF Numbers:** ESF4 (Firefighting), ESF9 (Search & Rescue), ESF19 (Donated Goods)  
**Acronyms:** SEOC (State EOC), SLRC (State Logistics Response Center), TAR (Travel Authorization)  
**Incident Types:** Hurricane, Flood, Crisis, Severe Storm(s), Freezing  
**Mission Categories:** Fuel logistics, food distribution, sanitation, communications, equipment deployment

---

## Tool Selection Quick Guide

| Question Type | Primary Tool | Follow-up Tools |
|--------------|--------------|-----------------|
| Current graph state | `analyze_knowledge_graph` | - |
| Find entity by property | `search_entities` | `get_entity` |
| Get connected entities | `get_neighbors` | `traverse_pattern` |
| Aggregate/sum data | `aggregate_by_type` | `execute_cypher` |
| Time-based queries | `find_entities_by_time_range` | `execute_cypher` |
| Complex multi-hop | `execute_cypher` | - |
| Schema discovery | `get_schema` | `describe_entity_type` |
| Pattern discovery | `find_patterns` | `analyze_knowledge_graph` |

---

## Remember

- Graph is **live** - data changes constantly
- High-degree nodes in `analyze_knowledge_graph` show **current priorities**
- Most vendor info is **embedded in missions**, not formal relationships
- **Incidents are hubs** - navigate from incidents to missions, not vice versa
- Use **`mission_number`** for human-readable mission references

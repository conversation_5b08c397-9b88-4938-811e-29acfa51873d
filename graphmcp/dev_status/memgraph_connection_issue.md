# Memgraph Connection Issue - Direct Access from Host Machine

## Problem Statement

**Goal:** Run embedding generation scripts directly from host machine using `uv run python scripts/generate_embeddings.py`

**Current Status:** ❌ Scripts cannot connect to Memgraph at `localhost:7687` from host machine

**Why This Matters:**
- Need to run admin scripts (embedding generation, index creation) outside Docker
- In production, Memgraph will be Azure managed service (external endpoint)
- Scripts should work from host machine, not require Docker exec

---

## What We're Trying to Achieve

### Use Case 1: Local Development
```bash
# From host machine (/Users/<USER>/Repos/DEMES/assistant/graphmcp)
uv run python scripts/generate_embeddings.py --entity-types Mission --limit 10

# Should:
# 1. Connect to Memgraph at localhost:7687
# 2. Fetch Mission nodes
# 3. Generate embeddings via Azure OpenAI
# 4. Update nodes with embeddings
```

### Use Case 2: Production (Future)
```bash
# From CI/CD pipeline or admin machine
MEMGRAPH_HOST=graphmcp-memgraph.azure.com \
MEMGRAPH_PORT=7687 \
uv run python scripts/generate_embeddings.py --all
```

---

## What We've Tried

### Test 1: Direct Neo4j Driver Connection
**Script:** `scripts/test_direct_connection.py`

**Code:**
```python
from neo4j import GraphDatabase
driver = GraphDatabase.driver("bolt://localhost:7687", auth=None)
with driver.session() as session:
    session.run("RETURN 1")
```

**Result:** ❌ FAILED
```
neo4j.exceptions.ServiceUnavailable: Failed to read four byte Bolt handshake
response from server ResolvedIPv4Address(('127.0.0.1', 7687))
(deadline Deadline(timeout=60.0))
```

### Test 2: Using GraphClient Class
**Script:** Direct Python with `src.graph.client.GraphClient`

**Code:**
```python
from src.graph.client import GraphClient
client = GraphClient(host="localhost", port=7687)
result = client.execute_query("MATCH (n) RETURN count(n)", {})
```

**Result:** ❌ FAILED (same Bolt handshake timeout)

### Test 3: Port Accessibility Check
**Command:** `nc -zv localhost 7687`

**Result:** ✅ SUCCESS
```
Connection to localhost port 7687 [tcp/*] succeeded!
```

**Conclusion:** Port is open, but Bolt protocol handshake fails

### Test 4: From Inside Docker Container
**Command:**
```bash
cd /Users/<USER>/Repos/DEMES/assistant
docker-compose exec graphrag-mcp uv run python -c "
from src.graph.client import GraphClient
client = GraphClient(host='localhost', port=7687)
result = client.execute_query('MATCH (n) RETURN count(n) as count', {})
print(f'Count: {result[0][\"count\"]}')
"
```

**Result:** ✅ SUCCESS
```
Entity types in graph:
  Mission: 4532 nodes
  Resource: 18 nodes
  Incident: 12 nodes
```

**Conclusion:** GraphClient works from INSIDE Docker, fails from host

---

## Current Working vs Not Working

### ✅ WORKS: GraphMCP Server (Inside Docker)
- **Container:** `graphrag-mcp`
- **Connection:** `MEMGRAPH_HOST=memgraph-db`, `MEMGRAPH_PORT=7687`
- **Network:** Docker network `assistant-network`
- **Status:** Healthy, all MCP tools working
- **How:** Uses service name `memgraph-db:7687`

### ✅ WORKS: Scripts Inside Docker
```bash
docker-compose exec graphrag-mcp uv run python scripts/test.py
```
- Uses same network as GraphMCP server
- Connects to `localhost:7687` (which resolves to memgraph-db inside container)

### ❌ FAILS: Scripts from Host Machine
```bash
uv run python scripts/generate_embeddings.py
```
- Tries to connect to `localhost:7687`
- Port is open (`nc` succeeds)
- Bolt handshake times out
- Neo4j driver can't complete handshake

---

## Observations

### Port Exposure
Docker Compose exposes Memgraph ports:
```yaml
ports:
  - "7687:7687"  # Bolt protocol
  - "7444:7444"  # HTTP interface
```

### Connection from GraphMCP Container
```python
# Inside graphrag-mcp container
MEMGRAPH_HOST=localhost  # Resolves to memgraph-db via Docker network
MEMGRAPH_PORT=7687       # Works perfectly
```

### Connection from Host
```python
# From host machine
MEMGRAPH_HOST=localhost  # Should be 127.0.0.1:7687
MEMGRAPH_PORT=7687       # Port is open but Bolt fails
```

### Possible Causes

1. **Memgraph Bolt Configuration**
   - Memgraph might only accept Bolt connections from specific interfaces
   - May need configuration flag for external Bolt access

2. **Docker Network Isolation**
   - Port is exposed but Bolt protocol blocked?
   - Firewall or Docker network policy?

3. **Neo4j Driver Compatibility**
   - Using `neo4j>=5.14.0` driver with Memgraph
   - Bolt protocol version mismatch?
   - Driver timeout too aggressive?

4. **Memgraph MAGE Image Specific**
   - Issue specific to `memgraph/memgraph-mage:latest` image?
   - Works with `memgraph/memgraph:latest`?

---

## Files Involved

### Configuration Files
- `/Users/<USER>/Repos/DEMES/assistant/graphmcp/.env` - Connection settings
- `/Users/<USER>/Repos/DEMES/assistant/memgraph/docker-compose.yml` - Memgraph service definition
- `/Users/<USER>/Repos/DEMES/assistant/docker-compose.yml` - Parent orchestration

### Test Scripts (All in `/Users/<USER>/Repos/DEMES/assistant/graphmcp/scripts/`)
- `test_direct_connection.py` - Simple connection test (FAILS from host)
- `generate_embeddings.py` - Batch embedding generation (needs connection)
- `create_vector_indices.py` - Vector index creation (needs connection)

### Working Code
- `src/graph/client.py` - GraphClient class (works inside Docker)
- GraphMCP server itself - Uses GraphClient successfully

---

## What Needs to Work

### Immediate Need:
```bash
# Run from host machine
cd /Users/<USER>/Repos/DEMES/assistant/graphmcp
uv run python scripts/generate_embeddings.py --entity-types Mission --limit 10

# Expected: Connect to Memgraph, generate 10 embeddings, update nodes
# Actual: Connection timeout on Bolt handshake
```

### Test Command That Should Work:
```bash
# Simple test
uv run python scripts/test_direct_connection.py

# Should output:
# ✓ Connection successful!
# ✓ Graph has 4732 nodes
# Top entity types:
#   Mission: 4532 nodes
#   Resource: 18 nodes
```

---

## Workaround (Temporary)

### Option A: Run Inside Docker
```bash
cd /Users/<USER>/Repos/DEMES/assistant
docker-compose exec graphrag-mcp uv run python scripts/generate_embeddings.py --all
```

**Pros:** ✅ Works now
**Cons:** ❌ Won't work in production (no Docker), awkward for dev workflow

### Option B: Use GQLAlchemy Instead of Neo4j Driver
The project already has `gqlalchemy>=1.3.3` installed. Try using it instead:

```python
from gqlalchemy import Memgraph

memgraph = Memgraph(host="localhost", port=7687)
result = memgraph.execute("MATCH (n) RETURN count(n)")
```

**Might work if:** GQLAlchemy uses different connection mechanism than neo4j driver

### Option C: HTTP Interface (Not Ideal)
Memgraph exposes HTTP on port 7444, but:
- Less performant than Bolt
- Different API
- Not standard for production

---

## Investigation Steps Needed

### 1. Check Memgraph Bolt Configuration
```bash
# Check Memgraph logs for Bolt initialization
docker logs memgraph-db 2>&1 | grep -i "bolt\|listening\|port 7687"

# Check Memgraph configuration
docker exec memgraph-db cat /etc/memgraph/memgraph.conf
```

### 2. Test with Different Driver Versions
```bash
# Try older neo4j driver
pip install neo4j==4.4.0  # Older version
python scripts/test_direct_connection.py
```

### 3. Try GQLAlchemy
```python
# scripts/test_gqlalchemy_connection.py
from gqlalchemy import Memgraph

memgraph = Memgraph(host="localhost", port=7687)
results = list(memgraph.execute("MATCH (n) RETURN count(n) as count"))
print(f"Count: {results[0]['count']}")
```

### 4. Check Memgraph Startup Flags
```bash
# What flags is Memgraph running with?
docker inspect memgraph-db | grep -A 20 "Cmd"
```

---

## Desired End State

### Scripts Work from Host:
```bash
# Development
cd /Users/<USER>/Repos/DEMES/assistant/graphmcp
uv run python scripts/generate_embeddings.py

# Production
MEMGRAPH_HOST=graphmcp-memgraph.azure.com \
MEMGRAPH_PORT=7687 \
uv run python scripts/generate_embeddings.py
```

### Connection Settings:
```bash
# .env
MEMGRAPH_HOST=localhost  # Or 127.0.0.1
MEMGRAPH_PORT=7687
# Should connect successfully from host machine
```

---

## Questions to Investigate

1. **Does Memgraph MAGE image have different Bolt configuration than standard Memgraph?**
2. **Is there a Memgraph startup flag needed to accept external Bolt connections?**
3. **Should we use GQLAlchemy instead of neo4j driver for host connections?**
4. **Is this a known issue with Memgraph in Docker on macOS?**

---

## Context

**Environment:**
- OS: macOS (Darwin 24.6.0)
- Docker: Running via Docker Desktop
- Memgraph: `memgraph/memgraph-mage:latest` in Docker
- Neo4j Driver: `neo4j>=5.14.0`
- Python: 3.13 (via uv)

**Docker Network:**
- Name: `assistant-network` (bridge)
- Memgraph container: `memgraph-db`
- Port mapping: `0.0.0.0:7687->7687`

**What Works:**
- ✅ GraphMCP server connects (from inside Docker)
- ✅ Port 7687 is accessible (`nc` test passes)
- ✅ Memgraph is healthy

**What Doesn't Work:**
- ❌ Neo4j driver from host machine (Bolt handshake timeout)
- ❌ Any Python script using GraphClient from host

---

## Temporary Solution for Testing

**Until connection is fixed, use:**
```bash
# Run scripts inside Docker container
cd /Users/<USER>/Repos/DEMES/assistant
docker-compose exec graphrag-mcp uv run python scripts/generate_embeddings.py --limit 10
```

This allows us to:
- ✅ Test embedding generation
- ✅ Create vector indices
- ✅ Verify workflow

Then fix the connection issue for production-ready deployment.

---

**Created:** October 1, 2025
**Resolved:** October 2, 2025
**Status:** ✅ RESOLVED - Scripts run inside graphmcp container
**Solution:** Use Makefile targets that execute scripts inside container
**Priority:** Closed

---

## RESOLUTION

**Root Cause:** Docker Desktop for Mac does not properly forward Memgraph's Bolt protocol on port 7687 from host to container, despite port mapping being configured correctly. This is a known limitation with Docker Desktop's port forwarding and Memgraph's Bolt handshake protocol.

**Solution:** Run all graphmcp scripts inside the graphmcp Docker container where `MEMGRAPH_HOST=memgraph-db` (Docker service name). This is the CORRECT approach for both development and production.

**Why This Is The Right Approach:**
1. Scripts use the same GraphClient code and connection logic as the graphmcp server
2. The container is on the same Docker network as Memgraph (demes-assistant-network)
3. In production, scripts will run as separate containers/jobs pointing to Azure managed Memgraph
4. No code changes needed - environment variables handle everything

**Implementation:**
- Created generic data loader: `scripts/load_cypher_data.py`
- Added Makefile targets for easy script execution
- Mounted `sample_data/` directory to graphmcp container

**Usage:**
```bash
# Generate embeddings
make graphmcp-embeddings ARGS="--entity-types Mission --limit 10"

# Load data
make data-load-missions
make data-reset-missions

# Verify database
make data-verify
```

See parent repository Makefile and README.md for complete documentation.

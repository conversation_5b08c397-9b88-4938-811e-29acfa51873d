"""
Embedding Client Factory

Creates the appropriate embedding client based on configuration.
Supports both local models (BGE) and Azure OpenAI.
"""

import os
import logging
from typing import Union

logger = logging.getLogger(__name__)


def create_embedding_client() -> Union['OnnxEmbeddingClient', 'AzureEmbeddingClient']:
    """
    Create embedding client based on configuration

    Reads LOCAL_EMBEDDING environment variable to determine which client to use:
    - LOCAL_EMBEDDING=true → OnnxEmbeddingClient (local ONNX runtime inference)
    - LOCAL_EMBEDDING=false → AzureEmbeddingClient (Azure OpenAI API)

    Returns:
        Configured embedding client instance

    Raises:
        ValueError: If configuration is invalid or required credentials missing
        ImportError: If required dependencies not installed

    Environment Variables:
        LOCAL_EMBEDDING: "true" or "false" (default: false)

        For local embeddings (LOCAL_EMBEDDING=true):
            LOCAL_EMBEDDING_MODEL_PATH: Path to ONNX model directory (default: ./models/bge-small-en-v1.5)
            LOCAL_EMBEDDING_MODEL_NAME: HuggingFace model identifier (default: BAAI/bge-small-en-v1.5)
            LOCAL_EMBEDDING_DIMENSION: Embedding dimension (default: 384)
            LOCAL_EMBEDDING_MAX_LENGTH: Maximum token length (default: 512)

        For Azure OpenAI (LOCAL_EMBEDDING=false):
            OPENAI_API_KEY: Azure OpenAI API key
            OPENAI_ENDPOINT: Azure OpenAI endpoint URL
            OPENAI_API_VERSION: API version (default: 2024-12-01-preview)
            OPENAI_EMBEDDING_DEPLOYMENT: Deployment name (default: text-embedding-3-small)
            OPENAI_EMBEDDING_DIMENSION: Expected dimension (default: 1536)
    """
    use_local = os.getenv("LOCAL_EMBEDDING", "false").lower() == "true"

    if use_local:
        logger.info("Creating ONNX embedding client (local inference)")
        try:
            from .onnx_client import OnnxEmbeddingClient
            model_path = os.getenv("LOCAL_EMBEDDING_MODEL_PATH", "./models/bge-small-en-v1.5")
            return OnnxEmbeddingClient(model_path=model_path)
        except ImportError as e:
            logger.error(
                "Failed to import OnnxEmbeddingClient. "
                "Ensure onnxruntime and transformers are installed: "
                "`uv sync` or `pip install onnxruntime transformers`"
            )
            raise ImportError(
                "onnxruntime or transformers not installed. Run: uv sync"
            ) from e
        except Exception as e:
            logger.error(f"Failed to initialize OnnxEmbeddingClient: {e}", exc_info=True)
            raise

    else:
        logger.info("Creating Azure OpenAI embedding client")
        try:
            from .azure_client import AzureEmbeddingClient
            return AzureEmbeddingClient()
        except ValueError as e:
            logger.error(
                "Failed to initialize AzureEmbeddingClient. "
                "Check OPENAI_API_KEY and OPENAI_ENDPOINT in .env"
            )
            raise ValueError(
                "Azure OpenAI credentials missing. Set OPENAI_API_KEY and OPENAI_ENDPOINT in .env"
            ) from e
        except Exception as e:
            logger.error(f"Failed to initialize AzureEmbeddingClient: {e}", exc_info=True)
            raise


def get_embedding_config() -> dict:
    """
    Get current embedding configuration

    Returns:
        Dictionary with embedding configuration details
    """
    use_local = os.getenv("LOCAL_EMBEDDING", "false").lower() == "true"

    if use_local:
        return {
            "provider": "local",
            "model": os.getenv("LOCAL_EMBEDDING_MODEL_NAME", "BAAI/bge-small-en-v1.5"),
            "model_path": os.getenv("LOCAL_EMBEDDING_MODEL_PATH", "./models/bge-small-en-v1.5"),
            "device": os.getenv("LOCAL_EMBEDDING_DEVICE", "cpu"),
            "dimension": int(os.getenv("LOCAL_EMBEDDING_DIMENSION", "384")),
            "max_length": int(os.getenv("LOCAL_EMBEDDING_MAX_LENGTH", "512"))
        }
    else:
        return {
            "provider": "azure_openai",
            "deployment": os.getenv("OPENAI_EMBEDDING_DEPLOYMENT", "text-embedding-3-small"),
            "endpoint": os.getenv("OPENAI_ENDPOINT", ""),
            "dimension": int(os.getenv("OPENAI_EMBEDDING_DIMENSION", "1536"))
        }

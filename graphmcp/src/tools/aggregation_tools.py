"""
Aggregation and analytics tools for GraphRAG MCP Server
"""

import os
import json
import logging
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)


def register(mcp, graph_client):
    """
    Register aggregation and analytics tools with the MCP server.

    Args:
        mcp: FastMCP server instance
        graph_client: GraphClient instance
    """

    @mcp.tool()
    def aggregate_by_type(
        entity_type: str,
        group_by: str,
        metrics: Optional[List[str]] = None,
        filters: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Aggregate entities by a property with optional metrics.

        Args:
            entity_type: Node type to aggregate
            group_by: Property to group by
            metrics: List of metrics to calculate:
                    ["count", "avg_severity", "max_duration", "min_response_time"]
            filters: Pre-aggregation filters

        Returns:
            Aggregated results with requested metrics

        Example Call:
            aggregate_by_type(
                "Incident",
                group_by="type",
                metrics=["count", "avg_severity"],
                filters={"status": "resolved"}
            )

        Example Response:
        {
            "success": true,
            "entity_type": "Incident",
            "group_by": "type",
            "aggregations": [
                {"type": "fire", "count": 45, "avg_severity": 3.2},
                {"type": "flood", "count": 23, "avg_severity": 4.1},
                {"type": "hurricane", "count": 8, "avg_severity": 4.8}
            ]
        }
        """
        try:
            logger.info(f"[aggregate_by_type] Called: entity_type={entity_type}, group_by={group_by}, metrics={metrics}, filters={filters}")

            # Start building query
            cypher = f"MATCH (n:{entity_type})"

            # Add filters if provided
            params = {}
            if filters:
                where_conditions = []
                for key, value in filters.items():
                    where_conditions.append(f"n.{key} = ${key}")
                    params[key] = value
                cypher += f" WHERE {' AND '.join(where_conditions)}"
                logger.info(f"[aggregate_by_type] Applied filters: {filters}")

            # Build aggregation metrics
            if not metrics:
                metrics = ["count"]

            metric_expressions = []
            for metric in metrics:
                if metric == "count":
                    metric_expressions.append("count(n) as count")
                elif metric.startswith("avg_"):
                    prop = metric[4:]  # Remove 'avg_' prefix
                    metric_expressions.append(f"avg(n.{prop}) as {metric}")
                elif metric.startswith("max_"):
                    prop = metric[4:]  # Remove 'max_' prefix
                    metric_expressions.append(f"max(n.{prop}) as {metric}")
                elif metric.startswith("min_"):
                    prop = metric[4:]  # Remove 'min_' prefix
                    metric_expressions.append(f"min(n.{prop}) as {metric}")
                elif metric.startswith("sum_"):
                    prop = metric[4:]  # Remove 'sum_' prefix
                    metric_expressions.append(f"sum(n.{prop}) as {metric}")

            logger.info(f"[aggregate_by_type] Metrics: {metrics}, Expressions: {metric_expressions}")

            # Add RETURN clause with grouping
            # Use first metric for ORDER BY (prefer count if available)
            order_metric = "count" if "count" in metrics else metrics[0] if metrics else "count"

            cypher += f"""
            RETURN n.{group_by} as group_value,
                   {', '.join(metric_expressions)}
            ORDER BY {order_metric} DESC
            """

            logger.info(f"[aggregate_by_type] Generated query: {cypher.strip()}")
            logger.info(f"[aggregate_by_type] Query is aggregation type: True (will not be limited)")

            # Execute query
            # Note: Aggregation queries don't need limits - they return few rows by design (one per group)
            # The smart LIMIT detection in client.py will also skip adding LIMIT to aggregation queries
            results = graph_client.execute_query(cypher, params, limit=None)

            logger.info(f"[aggregate_by_type] Query returned {len(results)} groups")

            # Format results
            aggregations = []
            for record in results:
                agg_data = {group_by: record["group_value"]}
                for metric in metrics:
                    if metric in record:
                        agg_data[metric] = record[metric]
                aggregations.append(agg_data)

            logger.info(f"[aggregate_by_type] Success - returned {len(aggregations)} aggregation groups")

            return json.dumps({
                "success": True,
                "entity_type": entity_type,
                "group_by": group_by,
                "metrics": metrics,
                "aggregation_count": len(aggregations),
                "aggregations": aggregations
            }, indent=2, default=str)

        except Exception as e:
            logger.error(f"Error in aggregate_by_type: {str(e)}", exc_info=True)
            return json.dumps({"error": str(e), "success": False})

    @mcp.tool()
    def calculate_statistics(
        entity_type: str,
        numeric_property: str,
        group_by: Optional[str] = None
    ) -> str:
        """
        Calculate statistical measures for a numeric property.

        Args:
            entity_type: Node type
            numeric_property: Property to analyze
            group_by: Optional grouping property

        Returns:
            Statistical summary including:
            - count, sum, min, max, avg, stdev
            - percentiles (25th, 50th, 75th, 90th, 95th)

        Example Call:
            calculate_statistics("Incident", "severity", group_by="type")

        Example Response:
        {
            "success": true,
            "entity_type": "Incident",
            "property": "severity",
            "statistics": {
                "count": 150,
                "sum": 450,
                "min": 1,
                "max": 5,
                "mean": 3.0,
                "stdev": 1.2,
                "percentiles": {
                    "p25": 2,
                    "p50": 3,
                    "p75": 4,
                    "p90": 5,
                    "p95": 5
                }
            }
        }
        """
        try:
            logger.info(f"calculate_statistics called: entity_type={entity_type}, property={numeric_property}")

            if group_by:
                # Grouped statistics
                cypher = f"""
                MATCH (n:{entity_type})
                WHERE n.{numeric_property} IS NOT NULL
                RETURN n.{group_by} as group_value,
                       count(n.{numeric_property}) as count,
                       sum(n.{numeric_property}) as sum,
                       avg(n.{numeric_property}) as mean,
                       min(n.{numeric_property}) as min,
                       max(n.{numeric_property}) as max
                ORDER BY group_value
                """
            else:
                # Overall statistics
                cypher = f"""
                MATCH (n:{entity_type})
                WHERE n.{numeric_property} IS NOT NULL
                RETURN count(n.{numeric_property}) as count,
                       sum(n.{numeric_property}) as sum,
                       avg(n.{numeric_property}) as mean,
                       min(n.{numeric_property}) as min,
                       max(n.{numeric_property}) as max
                """

            # Execute query
            results = graph_client.execute_query(cypher)

            if group_by:
                # Format grouped results
                statistics = []
                for record in results:
                    statistics.append({
                        group_by: record["group_value"],
                        "count": record["count"],
                        "sum": record["sum"],
                        "mean": record["mean"],
                        "min": record["min"],
                        "max": record["max"]
                    })

                return json.dumps({
                    "success": True,
                    "entity_type": entity_type,
                    "property": numeric_property,
                    "grouped_by": group_by,
                    "statistics": statistics
                }, indent=2, default=str)
            else:
                # Format overall statistics
                if results:
                    stats = results[0]
                    return json.dumps({
                        "success": True,
                        "entity_type": entity_type,
                        "property": numeric_property,
                        "statistics": stats
                    }, indent=2, default=str)
                else:
                    return json.dumps({
                        "success": False,
                        "error": "No data found for statistical analysis"
                    })

        except Exception as e:
            logger.error(f"Error in calculate_statistics: {str(e)}", exc_info=True)
            return json.dumps({"error": str(e), "success": False})

    @mcp.tool()
    def find_patterns(
        base_entity: str,
        pattern_type: str,
        threshold: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Find common patterns in the graph.

        Args:
            base_entity: Starting entity type
            pattern_type: Type of pattern to find:
                         - "co_occurrence": Entities that frequently appear together
                         - "temporal_sequence": Common sequences over time
                         - "resource_bottleneck": Over-utilized resources
                         - "coordination_gap": Gaps in departmental coordination
                         - "cascade_failure": Failure cascades across resources
                         - "response_delay": Delayed response patterns
            threshold: Pattern-specific thresholds:
                      - co_occurrence: {"min_frequency": 3}
                      - resource_bottleneck: {"utilization_pct": 80}

        Returns:
            Detected patterns with statistical significance and recommendations

        Example Call:
            find_patterns(
                "Resource",
                "resource_bottleneck",
                {"utilization_pct": 75, "time_window": "7d"}
            )

        Example Response:
        {
            "success": true,
            "pattern_type": "resource_bottleneck",
            "patterns": [
                {
                    "resource": "RES-001",
                    "utilization": 92,
                    "incidents_handled": 45,
                    "recommendation": "Consider adding backup resources"
                }
            ]
        }
        """
        try:
            logger.info(f"find_patterns called: base_entity={base_entity}, pattern_type={pattern_type}")

            if pattern_type == "co_occurrence":
                return _find_co_occurrence(graph_client, base_entity, threshold)
            elif pattern_type == "resource_bottleneck":
                return _find_bottlenecks(graph_client, threshold)
            elif pattern_type == "coordination_gap":
                return _find_coordination_gaps(graph_client, base_entity, threshold)
            elif pattern_type == "response_delay":
                return _find_response_delays(graph_client, threshold)
            else:
                return json.dumps({
                    "success": False,
                    "error": f"Unknown pattern type: {pattern_type}"
                })

        except Exception as e:
            logger.error(f"Error in find_patterns: {str(e)}", exc_info=True)
            return json.dumps({"error": str(e), "success": False})

    @mcp.tool()
    def execute_cypher(
        query: str,
        parameters: Optional[Dict[str, Any]] = None,
        limit: int = 100
    ) -> str:
        """
        Execute a validated Cypher query with safety checks.

        ⚠️  SECURITY: This tool is disabled by default. Set ALLOW_CUSTOM_CYPHER=true to enable.

        This tool allows running custom Cypher queries when built-in tools are insufficient.
        All queries are validated for safety before execution.

        Args:
            query: Cypher query (read-only operations only)
            parameters: Query parameters
            limit: Maximum results to return

        Returns:
            Query results with execution metadata

        Safety Features:
            - Read-only operations only (MATCH, RETURN, WITH, etc.)
            - No data modification (CREATE, DELETE, SET blocked)
            - Automatic LIMIT injection if missing
            - Query timeout enforcement
            - Parameter sanitization
            - Disabled by default (requires ALLOW_CUSTOM_CYPHER=true)

        Example Call:
            execute_cypher(
                \"\"\"MATCH (i:Incident)-[r:RESPONDED_TO]->(d:Department)
                WHERE i.severity >= $min_severity
                RETURN i.id, i.type, d.name, r.response_time
                ORDER BY r.response_time DESC\"\"\",
                {"min_severity": 4},
                50
            )

        Note: For security, this tool is disabled by default in production.
        """
        try:
            # Check if custom Cypher is allowed
            allow_custom_cypher = os.getenv("ALLOW_CUSTOM_CYPHER", "false").lower() == "true"

            if not allow_custom_cypher:
                logger.warning("[execute_cypher] Blocked - ALLOW_CUSTOM_CYPHER not enabled")
                return json.dumps({
                    "success": False,
                    "error": "Custom Cypher execution is disabled for security",
                    "details": "Set ALLOW_CUSTOM_CYPHER=true in .env to enable this tool"
                })

            # Log full query (not truncated) for debugging
            logger.info(f"[execute_cypher] Called with query ({len(query)} chars):")
            logger.info(f"[execute_cypher] Query: {query}")
            if parameters:
                logger.info(f"[execute_cypher] Parameters: {parameters}")
            logger.info(f"[execute_cypher] Limit parameter: {limit}")

            # Validate query safety
            is_safe, reason = graph_client.is_safe_query(query)
            if not is_safe:
                logger.warning(f"[execute_cypher] Query REJECTED by safety check: {reason}")
                return json.dumps({
                    "error": f"Query rejected for safety: {reason}",
                    "success": False,
                    "allowed_operations": ["MATCH", "RETURN", "WITH", "UNWIND", "ORDER BY", "LIMIT"]
                })

            logger.info(f"[execute_cypher] Safety check: PASSED")

            # Detect query type
            query_upper = query.upper()
            aggregation_keywords = ['SUM(', 'COUNT(', 'AVG(', 'MAX(', 'MIN(', 'GROUP BY', 'COLLECT(']
            detected_keywords = [kw for kw in aggregation_keywords if kw in query_upper]
            is_aggregation = len(detected_keywords) > 0

            if is_aggregation:
                logger.info(f"[execute_cypher] Query type: AGGREGATION (detected: {', '.join(detected_keywords)})")
            else:
                logger.info(f"[execute_cypher] Query type: STANDARD (no aggregation detected)")

            # Store original query
            original_query = query

            # Add LIMIT if missing and not a COUNT query
            # Note: This is legacy logic, the smart LIMIT detection in client.py will also prevent limiting aggregations
            if "LIMIT" not in query.upper() and "COUNT(" not in query.upper():
                query = query.rstrip(';') + f" LIMIT {limit}"
                logger.info(f"[execute_cypher] Added LIMIT {limit} to query (will be validated by client.py)")

            # Log final query if modified
            if query != original_query:
                logger.info(f"[execute_cypher] Final query: {query}")

            # Execute query (client.py will apply smart LIMIT detection)
            results = graph_client.execute_query(query, parameters or {})

            logger.info(f"[execute_cypher] Success - returned {len(results)} rows")

            return json.dumps({
                "success": True,
                "query": query,
                "count": len(results),
                "data": results,
                "limited": len(results) == limit
            }, indent=2, default=str)

        except Exception as e:
            logger.error(f"Error in execute_cypher: {str(e)}", exc_info=True)
            return json.dumps({"error": str(e), "success": False})

    logger.info("Aggregation tools registered successfully")


# Helper functions for pattern detection

def _find_co_occurrence(graph_client, base_entity: str, threshold: Optional[Dict]) -> str:
    """Find entities that frequently appear together."""
    min_frequency = threshold.get("min_frequency", 3) if threshold else 3

    cypher = f"""
    MATCH (a:{base_entity})-[]->(common)<-[]-(b:{base_entity})
    WHERE a <> b
    WITH a, b, count(common) as co_occurrences
    WHERE co_occurrences >= $min_freq
    RETURN a.id as entity1, b.id as entity2, co_occurrences
    ORDER BY co_occurrences DESC
    LIMIT 20
    """

    results = graph_client.execute_query(cypher, {"min_freq": min_frequency})

    patterns = []
    for record in results:
        patterns.append({
            "entity1": record["entity1"],
            "entity2": record["entity2"],
            "co_occurrences": record["co_occurrences"],
            "significance": "high" if record["co_occurrences"] > min_frequency * 2 else "medium"
        })

    return json.dumps({
        "success": True,
        "pattern_type": "co_occurrence",
        "min_frequency": min_frequency,
        "pattern_count": len(patterns),
        "patterns": patterns
    }, indent=2, default=str)


def _find_bottlenecks(graph_client, threshold: Optional[Dict]) -> str:
    """Find resource bottlenecks."""
    utilization_pct = threshold.get("utilization_pct", 80) if threshold else 80

    cypher = """
    MATCH (r:Resource)-[:RESPONDED_TO|ALLOCATED_TO]->(i:Incident)
    WITH r, count(i) as incident_count
    MATCH (total:Resource)
    WITH r, incident_count, count(total) as total_resources
    WITH r, incident_count, (incident_count * 100.0 / total_resources) as utilization
    WHERE utilization >= $threshold
    RETURN r.id as resource_id, r.type as resource_type,
           incident_count, utilization
    ORDER BY utilization DESC
    LIMIT 10
    """

    results = graph_client.execute_query(cypher, {"threshold": utilization_pct})

    patterns = []
    for record in results:
        patterns.append({
            "resource": record["resource_id"],
            "type": record["resource_type"],
            "utilization": round(record["utilization"], 2),
            "incidents_handled": record["incident_count"],
            "recommendation": "Consider adding backup resources" if record["utilization"] > 90 else "Monitor closely"
        })

    return json.dumps({
        "success": True,
        "pattern_type": "resource_bottleneck",
        "utilization_threshold": utilization_pct,
        "pattern_count": len(patterns),
        "patterns": patterns
    }, indent=2, default=str)


def _find_coordination_gaps(graph_client, base_entity: str, threshold: Optional[Dict]) -> str:
    """Find coordination gaps between departments."""
    min_shared = threshold.get("shared_incidents", 5) if threshold else 5

    cypher = """
    MATCH (d1:Department)-[:RESPONDED_TO]->(i:Incident)<-[:RESPONDED_TO]-(d2:Department)
    WHERE d1 <> d2
    WITH d1, d2, count(i) as shared_incidents
    WHERE shared_incidents >= $min_shared
    OPTIONAL MATCH (d1)-[:COORDINATES_WITH]-(d2)
    WITH d1, d2, shared_incidents, count(*) as has_coordination
    WHERE has_coordination = 0
    RETURN d1.name as dept1, d2.name as dept2, shared_incidents
    ORDER BY shared_incidents DESC
    LIMIT 10
    """

    results = graph_client.execute_query(cypher, {"min_shared": min_shared})

    patterns = []
    for record in results:
        patterns.append({
            "department1": record["dept1"],
            "department2": record["dept2"],
            "shared_incidents": record["shared_incidents"],
            "recommendation": "Establish formal coordination protocol"
        })

    return json.dumps({
        "success": True,
        "pattern_type": "coordination_gap",
        "min_shared_incidents": min_shared,
        "pattern_count": len(patterns),
        "patterns": patterns
    }, indent=2, default=str)


def _find_response_delays(graph_client, threshold: Optional[Dict]) -> str:
    """Find patterns of delayed responses."""
    max_response_time = threshold.get("max_response_minutes", 30) if threshold else 30

    cypher = """
    MATCH (r:Resource)-[resp:RESPONDED_TO]->(i:Incident)
    WHERE resp.response_time > $max_time
    WITH i, avg(resp.response_time) as avg_delay, count(r) as resource_count
    RETURN i.id as incident_id, i.type as incident_type,
           avg_delay, resource_count
    ORDER BY avg_delay DESC
    LIMIT 10
    """

    results = graph_client.execute_query(cypher, {"max_time": max_response_time})

    patterns = []
    for record in results:
        patterns.append({
            "incident": record["incident_id"],
            "type": record["incident_type"],
            "avg_response_delay": round(record["avg_delay"], 2),
            "resources_delayed": record["resource_count"],
            "recommendation": "Review dispatch protocols and resource positioning"
        })

    return json.dumps({
        "success": True,
        "pattern_type": "response_delay",
        "threshold_minutes": max_response_time,
        "pattern_count": len(patterns),
        "patterns": patterns
    }, indent=2, default=str)
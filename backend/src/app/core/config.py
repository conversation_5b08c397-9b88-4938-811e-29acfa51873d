from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import Field, field_validator
from typing import Optional, Any
import json
from .logging import LoggingSettings
from .phoenix_config import PhoenixSettings


class Settings(BaseSettings):
    """
    Application settings using Pydantic BaseSettings for environment variable management.
    
    Environment variables are automatically mapped to class fields:
    - VERSION → version
    - OPENAI_API_KEY → openai_api_key  
    - GENIE_MCP_URL → genie_mcp_url
    
    Loading priority: system env vars → .env file → class defaults
    This is inherited behavior of BaseSettings
    
    The empty string defaults below are fallbacks that get replaced by actual env values.
    """
    app_name: str = "GraySkyGenAI Assistant"
    version: str = "0.1.0"
    debug: bool = False
    
    # API Configuration
    api_v1_str: str = "/api/v1"
    
    # CORS Settings
    backend_cors_origins: list[str] = []
    # backend_cors_origins: list[str] = ["http://localhost:3000", "http://127.0.0.1:3000"] # Hardcoded version just in case we need it for local dev


    @field_validator("backend_cors_origins", mode="before")
    @classmethod
    def parse_cors_origins(cls, v: Any) -> list[str]:
        if isinstance(v, str):
            # Try to parse as JSON first
            if v.startswith("["):
                try:
                    return json.loads(v)
                except json.JSONDecodeError:
                    pass
            # Otherwise, split by comma
            return [origin.strip() for origin in v.split(",")]
        return v

    @field_validator("fast_model_deployment", "synthesis_model_deployment")
    @classmethod
    def validate_model_deployments(cls, v: str, info) -> str:
        """
        Validate that model deployment names are not empty.

        Provides fail-fast validation at application startup rather than
        waiting for first API call to fail.
        """
        if not v or v.strip() == "":
            field_name = info.field_name.upper()
            raise ValueError(
                f"{field_name} cannot be empty. "
                f"Check your .env file and ensure it contains:\n"
                f"  {field_name}=gpt-4.1-mini\n"
                f"or your preferred Azure OpenAI deployment name."
            )
        return v.strip()
    
    # Server Configuration
    host: str = "0.0.0.0"
    port: int = 8000
    
    # Azure OpenAI Configuration (via Databricks)
    openai_api_key: str = ""
    openai_endpoint: str = ""
    api_version: str = ""

    # Model-specific deployments for different purposes
    fast_model_deployment: str = "gpt-4.1-mini"  # Fast model for data gathering
    fast_model_temperature: float = 0.0
    synthesis_model_deployment: str = "gpt-4.1-mini"  # Model for synthesis
    synthesis_model_temperature: float = 0.7
    
    # Genie MCP Configuration
    genie_mcp_enabled: bool = True  # Enable/disable Genie MCP server
    genie_mcp_url: str = ""
    genie_space: str = ""

    # GraphRAG MCP Configuration
    graphrag_mcp_enabled: bool = True  # Enable/disable GraphRAG MCP server
    graphrag_mcp_url: str # Read in as env var; local testing - http://localhost:8001/mcp; docker - http://graphrag-mcp:8001/mcp
    graphrag_mcp_timeout: int = 30

    # LangGraph Configuration
    # LangGraph is the only orchestration method (no feature flag)
    langgraph_checkpointer: str = "memory"  # Options: memory, postgres
    langgraph_postgres_url: Optional[str] = None

    # Subagent Configuration
    genie_subagent_max_iterations: int = 2  # Max iterations for Genie subagent
    graphrag_subagent_max_iterations: int = 10  # Max iterations for GraphRAG subagent

    # Caching Configuration
    cache_ttl_genie: int = 900  # 15 minutes for incident data
    cache_ttl_graph: int = 3600  # 1 hour for graph data
    cache_enable_semantic_dedup: bool = True

    # Logging Configuration
    logging: LoggingSettings = LoggingSettings()
    
    # Phoenix Configuration
    phoenix: PhoenixSettings = PhoenixSettings()

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"  # Ignore extra fields from .env that don't map to Settings fields
    )


settings = Settings() # Instantiates the settings object
# When settings = Settings() is called, Pydantic automatically resolves the priority hierarchy and validates all values
# This is point at which environment variables are resolved and validated

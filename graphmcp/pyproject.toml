[project]
name = "graphrag-mcp-server"
version = "0.1.0"
description = "GraphRAG MCP Server for Emergency Management with Memgraph"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    # Core MCP and server
    "fastmcp>=2.11.3",
    "mcp>=1.13.0",
    "python-dotenv>=1.1.1",
    # Graph database - Memgraph
    "gqlalchemy>=1.3.3",
    "neo4j>=5.14.0", # Works with Memgraph's Bolt protocol
    # "pymgclient>=1.3.1",  # Commented out - causes build issues, gqlalchemy is sufficient
    # Data handling
    "pydantic>=2.0.0",
    "pandas>=2.0.0",
    "pyyaml>=6.0",
    "numpy>=1.24.0",
    # AI/ML - Embeddings
    "openai>=1.12.0", # Azure OpenAI and OpenAI API client
    "sentence-transformers>=2.2.0", # Local embedding models (BGE, etc.)
    "torch>=2.0.0", # Required by sentence-transformers
    # Logging and monitoring
    "structlog>=23.2.0",
    "prometheus-client>=0.19",
    "onnxruntime>=1.23.2",
]

[dependency-groups]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.11.0",
    "black>=23.0.0",
    "ruff>=0.1.0",
    "mypy>=1.0.0",
    "aiohttp>=3.9.0",  # For HTTP client testing
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.black]
line-length = 100
target-version = ["py39"]

[tool.ruff]
line-length = 100
select = ["E", "F", "I", "N", "UP", "B"]
ignore = ["E501"]

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = false

"""
Runner module for the GraphRAG MCP server.

This module provides functions to run the MCP server with different transports.
"""

import os
import sys
import time
import asyncio
import subprocess
import logging
from typing import Any

# Add the project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.config import configure_server_env

logger = logging.getLogger(__name__)

def run_server(mcp: Any):
    """
    Run the MCP server with the specified transport.

    Args:
        mcp: The MCP server instance.
    """
    # Configure server environment
    config = configure_server_env()
    transport = config["transport"]
    http_port = config["http_port"]

    logger.info(f"Server config: transport={transport}, http_port={http_port}")

    # Check transport type and start server
    if transport in ["streamable-http", "http", "sse"]:
        # Use stderr for HTTP transport messages to avoid interfering with stdio
        print(f"Starting GraphRAG MCP server with HTTP transport on port {http_port}", file=sys.stderr)

        # Kill any existing process on the port
        try:
            # Use lsof to find process using the port
            result = subprocess.run(
                f"lsof -t -i:{http_port}",
                shell=True,
                capture_output=True,
                text=True
            )
            if result.stdout.strip():
                # Kill the process
                subprocess.run(f"kill -9 {result.stdout.strip()}", shell=True)
                time.sleep(1)  # Wait for port to be freed
        except Exception as e:
            logger.debug(f"No existing process on port {http_port}: {e}")
            # Ignore errors if no process is using the port

        # Start HTTP server using the correct FastMCP method
        logger.info(f"Starting GraphRAG MCP server with HTTP transport on port {http_port}")

        # FastMCP's run() method accepts transport parameter
        mcp.run(
            transport="streamable-http",
            host=os.getenv("HOST", "127.0.0.1"),
            port=http_port
        )
    else:
        # For stdio transport, do NOT print anything to stdout as it interferes with JSON-RPC
        # Use stderr or logging instead
        print("Starting GraphRAG MCP server with stdio transport", file=sys.stderr)
        sys.stderr.flush()

        # Use FastMCP's built-in stdio runner
        try:
            logger.info("Starting FastMCP stdio server")
            # For stdio, just call run() without parameters
            mcp.run()
        except Exception as e:
            logger.error(f"Failed to run stdio server: {e}", exc_info=True)
            raise
"""
Tests for graph context injection into supervisor via system message.
"""

import pytest
from langchain_core.messages import SystemMessage, HumanMessage
from app.agent.langgraph.workflows import inject_graph_context_node
from app.agent.langgraph.context_utils import get_initial_context
from app.agent.langgraph.state import AgentState


class TestContextInjection:
    """Test suite for injecting graph context into supervisor messages."""

    @pytest.mark.asyncio
    async def test_inject_graph_context_adds_system_message(self):
        """
        When graph_context exists in state, inject_graph_context_node should
        add a system message with formatted context.
        """
        # Simulate state after fetch_graph_context_node
        state = {
            "messages": [
                HumanMessage(content="What active incidents do we have?")
            ],
            "graph_context": {
                "success": True,
                "statistics": {
                    "totalNodes": 83077,
                    "totalRelationships": 86427,
                    "nodeLabelCount": 4,
                    "relationshipTypeCount": 4
                },
                "highDegreeNodes": [
                    {
                        "node_labels": ["Incident"],
                        "properties": {
                            "incident_id": 172,
                            "incident_name": "2024 Milton",
                            "incident_status": "Active"
                        },
                        "degree": 7673
                    }
                ],
                "nodeLabels": [],
                "relationshipTypes": []
            }
        }
        
        # Call the injection node
        result = await inject_graph_context_node(state)
        
        # Should return messages update
        assert "messages" in result
        new_messages = result["messages"]
        
        # Should be a list with one system message
        assert isinstance(new_messages, list)
        assert len(new_messages) == 1
        
        # Should be a SystemMessage
        system_msg = new_messages[0]
        assert isinstance(system_msg, SystemMessage)
        
        # Content should contain formatted graph context
        content = system_msg.content
        assert "Current Graph State (Live Data)" in content
        assert "83,077" in content  # Total nodes
        assert "2024 Milton" in content  # High-degree incident

    @pytest.mark.asyncio
    async def test_inject_graph_context_handles_missing_context(self):
        """
        When graph_context is None, inject_graph_context_node should
        handle gracefully without crashing.
        """
        state = {
            "messages": [
                HumanMessage(content="Test query")
            ],
            "graph_context": None
        }
        
        # Should not crash
        result = await inject_graph_context_node(state)
        
        # Should return empty update or skip injection
        assert result is not None
        # If it returns messages, they should be empty (no injection happened)
        if "messages" in result:
            assert len(result["messages"]) == 0

    @pytest.mark.asyncio
    async def test_inject_graph_context_preserves_existing_messages(self):
        """
        The injection node should not remove existing messages, only add to them.
        State messages are accumulated via operator.add.
        """
        state = {
            "messages": [
                HumanMessage(content="First message"),
                HumanMessage(content="Second message")
            ],
            "graph_context": {
                "success": True,
                "statistics": {"totalNodes": 100},
                "highDegreeNodes": [],
                "nodeLabels": [],
                "relationshipTypes": []
            }
        }
        
        result = await inject_graph_context_node(state)
        
        # Should only return the NEW system message
        # LangGraph's operator.add will append it to existing messages
        assert "messages" in result
        assert len(result["messages"]) == 1
        assert isinstance(result["messages"][0], SystemMessage)

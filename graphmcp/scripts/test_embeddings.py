"""
Test script for Azure OpenAI embeddings API

This script verifies that:
1. The Azure OpenAI endpoint is accessible
2. The API key is valid
3. We can generate embeddings with the specified deployment
4. The embedding dimensions match expectations
"""
import os
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from dotenv import load_dotenv
from openai import AzureOpenAI

# Load environment variables
load_dotenv()


def test_azure_openai_embeddings():
    """Test Azure OpenAI embeddings generation"""

    print("=" * 80)
    print("TESTING AZURE OPENAI EMBEDDINGS")
    print("=" * 80)

    # Get configuration from environment
    api_key = os.getenv("OPENAI_API_KEY")
    endpoint = os.getenv("OPENAI_ENDPOINT")
    api_version = os.getenv("OPENAI_API_VERSION", "2024-12-01-preview")

    # Try multiple possible deployment names
    deployment_options = [
        os.getenv("OPENAI_EMBEDDING_DEPLOYMENT", "text-embedding-3-small"),
        "text-embedding-3-small",
        "text-embedding-ada-002",
        "embedding"
    ]

    expected_dimension = int(os.getenv("OPENAI_EMBEDDING_DIMENSION", "1536"))

    print(f"\n📋 Configuration:")
    print(f"   Endpoint: {endpoint}")
    print(f"   API Version: {api_version}")
    print(f"   API Key: {'*' * 20}{api_key[-4:] if api_key else 'NOT SET'}")
    print(f"   Expected Dimension: {expected_dimension}")
    print(f"   Deployment options to try: {deployment_options}")

    if not api_key or not endpoint:
        print("\n❌ ERROR: OPENAI_API_KEY or OPENAI_ENDPOINT not set in .env")
        print("   Please copy .env.example to .env and add your credentials")
        return False

    # Initialize Azure OpenAI client
    try:
        client = AzureOpenAI(
            api_key=api_key,
            api_version=api_version,
            azure_endpoint=endpoint
        )
        print("\n✓ Azure OpenAI client initialized successfully")
    except Exception as e:
        print(f"\n❌ ERROR: Failed to initialize Azure OpenAI client: {e}")
        return False

    # Test sample texts
    test_texts = [
        "Hurricane Milton caused widespread damage along the coast",
        "Fire truck dispatched to residential area",
        "Medical emergency requiring immediate response"
    ]

    print(f"\n🧪 Testing embeddings with {len(test_texts)} sample texts...")

    # Try each deployment option until one works
    for deployment in deployment_options:
        print(f"\n🔍 Trying deployment: '{deployment}'")

        try:
            response = client.embeddings.create(
                model=deployment,
                input=test_texts[0]  # Test with first text only
            )

            embedding = response.data[0].embedding
            dimension = len(embedding)

            print(f"   ✓ SUCCESS! Deployment '{deployment}' works")
            print(f"   Embedding dimension: {dimension}")
            print(f"   Expected dimension: {expected_dimension}")

            if dimension != expected_dimension:
                print(f"   ⚠️  WARNING: Dimension mismatch! Got {dimension}, expected {expected_dimension}")
                print(f"   Update OPENAI_EMBEDDING_DIMENSION={dimension} in .env")

            # Test batch embedding
            print(f"\n🧪 Testing batch embedding ({len(test_texts)} texts)...")
            batch_response = client.embeddings.create(
                model=deployment,
                input=test_texts
            )

            print(f"   ✓ Batch embedding successful")
            print(f"   Embeddings generated: {len(batch_response.data)}")

            # Show sample embedding values
            sample_embedding = batch_response.data[0].embedding[:5]
            print(f"   Sample values (first 5): {sample_embedding}")

            # Calculate usage
            total_tokens = batch_response.usage.total_tokens
            print(f"\n📊 Token Usage:")
            print(f"   Total tokens: {total_tokens}")
            print(f"   Cost estimate: ${total_tokens / 1_000_000 * 0.02:.6f} (at $0.02/1M tokens)")

            # Success - found working deployment
            print(f"\n" + "=" * 80)
            print(f"✅ EMBEDDING TEST PASSED")
            print(f"=" * 80)
            print(f"\n📝 Add this to your .env:")
            print(f"   OPENAI_EMBEDDING_DEPLOYMENT={deployment}")
            print(f"   OPENAI_EMBEDDING_DIMENSION={dimension}")
            print(f"\n💡 This deployment is ready for embedding generation!")

            return True

        except Exception as e:
            error_msg = str(e)
            print(f"   ❌ Deployment '{deployment}' failed: {error_msg}")

            # Check for common errors
            if "DeploymentNotFound" in error_msg or "model_not_found" in error_msg:
                print(f"   💡 This deployment doesn't exist in your Azure OpenAI instance")
                continue
            elif "unauthorized" in error_msg.lower():
                print(f"   💡 API key may be invalid")
                break
            elif "quota" in error_msg.lower():
                print(f"   💡 Rate limit or quota exceeded")
                break
            else:
                print(f"   💡 Unexpected error - check logs")
                continue

    # None of the deployments worked
    print(f"\n" + "=" * 80)
    print(f"❌ EMBEDDING TEST FAILED")
    print(f"=" * 80)
    print(f"\n💡 Troubleshooting:")
    print(f"   1. Check your Azure OpenAI deployments at:")
    print(f"      https://portal.azure.com → Azure OpenAI → Deployments")
    print(f"   2. Verify the deployment name exactly matches (case-sensitive)")
    print(f"   3. Ensure the deployment is an embedding model (not chat)")
    print(f"   4. Common embedding deployment names:")
    print(f"      - text-embedding-3-small")
    print(f"      - text-embedding-ada-002")
    print(f"      - embedding")

    return False


if __name__ == "__main__":
    success = test_azure_openai_embeddings()
    sys.exit(0 if success else 1)

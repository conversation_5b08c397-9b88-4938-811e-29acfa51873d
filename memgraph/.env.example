# Memgraph Configuration for Local Development
# Production uses external managed Memgraph service

# Container Names
MEMGRAPH_CONTAINER_NAME=memgraph-db
MEMGRAPH_LAB_CONTAINER_NAME=memgraph-lab

# Ports
MEMGRAPH_BOLT_PORT=7687      # Bolt protocol for database connections
MEMGRAPH_HTTP_PORT=7444      # HTTP API
MEMGRAPH_LAB_PORT=3001       # Lab UI (using 3001 to avoid conflicts)

# Database Configuration
MEMGRAPH_LOG_LEVEL=INFO      # Logging level (DEBUG, INFO, WARNING, ERROR)
MEMGRAPH_TELEMETRY=false     # Disable telemetry

# Storage Configuration
MEMGRAPH_STORAGE_SNAPSHOT_INTERVAL_SEC=300
MEMGRAPH_STORAGE_WAL_ENABLED=true

# Docker Configuration
VOLUME_NAME=memgraph_data    # Name of the Docker volume for data persistence
NETWORK_NAME=memgraph-network  # Docker network name

# Notes:
# - This configuration is for LOCAL DEVELOPMENT ONLY
# - Production deployments use external managed Memgraph instances
# - Update network name if integrating with parent docker-compose

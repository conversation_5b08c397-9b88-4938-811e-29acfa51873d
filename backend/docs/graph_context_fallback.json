{"success": true, "statistics": {"totalNodes": 86322, "totalRelationships": 92917, "nodeLabelCount": 5, "relationshipTypeCount": 6, "averageDegree": 2.15, "timestamp": "1760762775724648"}, "highDegreeNodes": [{"node_labels": ["Incident"], "properties": {"incident_created_at": "2024-10-05T17:09:31.000000000+00:00", "incident_id": 172, "incident_name": "2024 Milton", "incident_status": "Active", "incident_type": "Hurricane", "incident_updated_at": "2025-10-02T13:48:28.000000000+00:00"}, "degree": 7673}, {"node_labels": ["Incident"], "properties": {"incident_created_at": "2024-09-24T13:59:50.000000000+00:00", "incident_id": 171, "incident_name": "2024 <PERSON><PERSON>", "incident_status": "Active", "incident_type": "Hurricane", "incident_updated_at": "2025-10-02T13:48:28.000000000+00:00"}, "degree": 5050}, {"node_labels": ["<PERSON><PERSON><PERSON>"], "properties": {"fuel_vendor_name": "World Kinect", "mission_vendor_name": "World Fuel Services", "vendor_id": "-199540798", "vendor_updated_at": "2025-10-03T20:47:24.058606000+00:00"}, "degree": 2803}, {"node_labels": ["Mission"], "properties": {"fuel_vendor_name": "World Kinect", "id": 542993, "incidents": "[{\"incident_id\": 171, \"incident_name\": \"2024 <PERSON><PERSON>\", \"incident_status\": \"Active\", \"incident_type\": \"Hurricane\", \"incident_created_at\": \"2024-09-24T13:59:50\", \"incident_updated_at\": \"2025-10-02T13:48:28\"}]", "mission_number": "02627", "mission_vendor_name": "World Fuel Services", "title": "FR Fuel truck", "vendor_id": "-199540798", "vendor_updated_at": "2025-10-03T20:47:24.058606000+00:00"}, "degree": 1509}, {"node_labels": ["Mission"], "properties": {"fuel_vendor_name": "World Kinect", "id": 562799, "incidents": "[{\"incident_id\": 172, \"incident_name\": \"2024 Milton\", \"incident_status\": \"Active\", \"incident_type\": \"Hurricane\", \"incident_created_at\": \"2024-10-05T17:09:31\", \"incident_updated_at\": \"2025-10-02T13:48:28\"}]", "mission_number": "00270", "mission_vendor_name": "World Fuel Services", "title": "Fleet - Fuel support for search and rescue vehicles - Consumables/Equipment/Personnel ", "vendor_id": "-199540798", "vendor_updated_at": "2025-10-03T20:47:24.058606000+00:00"}, "degree": 714}, {"node_labels": ["Mission"], "properties": {"fuel_vendor_name": "Macro", "id": 564207, "incidents": "[{\"incident_id\": 172, \"incident_name\": \"2024 Milton\", \"incident_status\": \"Active\", \"incident_type\": \"Hurricane\", \"incident_created_at\": \"2024-10-05T17:09:31\", \"incident_updated_at\": \"2025-10-02T13:48:28\"}]", "mission_number": "00494", "mission_vendor_name": "Macro", "title": "ESF19 Jet A fuel request", "vendor_id": "-723705720", "vendor_updated_at": "2025-10-03T20:47:24.058606000+00:00"}, "degree": 506}, {"node_labels": ["<PERSON><PERSON><PERSON>"], "properties": {"fuel_vendor_name": "Macro", "mission_vendor_name": "Macro", "vendor_id": "-723705720", "vendor_updated_at": "2025-10-03T20:47:24.058606000+00:00"}, "degree": 451}, {"node_labels": ["Mission"], "properties": {"fuel_vendor_name": "World Kinect", "id": 550195, "incidents": "[{\"incident_id\": 171, \"incident_name\": \"2024 <PERSON><PERSON>\", \"incident_status\": \"Active\", \"incident_type\": \"Hurricane\", \"incident_created_at\": \"2024-09-24T13:59:50\", \"incident_updated_at\": \"2025-10-02T13:48:28\"}]", "mission_number": "03515", "mission_vendor_name": "World Fuel Services", "title": "Fuel Distribution for Town of Suwannee", "vendor_id": "-199540798", "vendor_updated_at": "2025-10-03T20:47:24.058606000+00:00"}, "degree": 398}, {"node_labels": ["Mission"], "properties": {"id": 566189, "incidents": "[{\"incident_id\": 172, \"incident_name\": \"2024 Milton\", \"incident_status\": \"Active\", \"incident_type\": \"Hurricane\", \"incident_created_at\": \"2024-10-05T17:09:31\", \"incident_updated_at\": \"2025-10-02T13:48:28\"}]", "mission_number": "00787", "title": "Feeding Florida MOU Activation"}, "degree": 390}, {"node_labels": ["Mission"], "properties": {"id": 524072, "incidents": "[{\"incident_id\": 171, \"incident_name\": \"2024 <PERSON><PERSON>\", \"incident_status\": \"Active\", \"incident_type\": \"Hurricane\", \"incident_created_at\": \"2024-09-24T13:59:50\", \"incident_updated_at\": \"2025-10-02T13:48:28\"}]", "mission_number": "00181", "title": "Feeding Florida MOU Activation"}, "degree": 189}], "multiLabelNodes": [], "nodeLabels": [{"label": "Comment", "count": 70252}, {"label": "Mission", "count": 12723}, {"label": "FuelTransaction", "count": 3245}, {"label": "<PERSON><PERSON><PERSON>", "count": 63}, {"label": "Incident", "count": 39}], "relationshipTypes": [{"source_label": ["Comment"], "relationship_type": "COMMENTED_ON", "target_label": ["Mission"], "count": 70252}, {"source_label": ["Mission"], "relationship_type": "RELATED_TO_INCIDENT", "target_label": ["Incident"], "count": 12723}, {"source_label": ["Mission"], "relationship_type": "IS_PARENT_TO", "target_label": ["Mission"], "count": 3443}, {"source_label": ["FuelTransaction"], "relationship_type": "FROM_VENDOR", "target_label": ["<PERSON><PERSON><PERSON>"], "count": 3245}, {"source_label": ["Mission"], "relationship_type": "HAS_TRANSACTION", "target_label": ["FuelTransaction"], "count": 3245}, {"source_label": ["Mission"], "relationship_type": "ASSIGNED_VENDOR", "target_label": ["<PERSON><PERSON><PERSON>"], "count": 9}], "labelProperties": [{"label": "Incident", "props": ["incident_id", "incident_type", "incident_name", "incident_created_at", "incident_updated_at"]}, {"label": "<PERSON><PERSON><PERSON>", "props": ["vendor_id", "mission_vendor_name", "vendor_updated_at"]}, {"label": "Comment", "props": ["comment_id", "text", "created_at", "created_by"]}, {"label": "FuelTransaction", "props": ["fuel_transaction_id", "fuel_type", "fuel_vendor", "date_of_dueling", "address", "amount_dispensed"]}, {"label": "Mission", "props": ["id", "mission_number", "title", "incidents"]}], "propertyValueSamples": [{"label": "Mission", "property": "vendor_updated_at", "sampleValues": ["2025-10-03T20:47:24.058606000+00:00"]}, {"label": "Mission", "property": "mission_vendor_name", "sampleValues": ["World Fuel Services", "Macro"]}, {"label": "Mission", "property": "fuel_vendor_name", "sampleValues": ["World Kinect", "Macro"]}, {"label": "Mission", "property": "vendor_id", "sampleValues": ["-199540798", "-723705720"]}, {"label": "Mission", "property": "incidents", "sampleValues": ["[{\"incident_id\": 172, \"incident_name\": \"2024 Milton\", \"incident_status\": \"Active\", \"incident_type\": \"Hurricane\", \"incident_created_at\": \"2024-10-05T17:09:31\", \"incident_updated_at\": \"2025-10-02T13:48:28\"}]", "[{\"incident_id\": 171, \"incident_name\": \"2024 <PERSON><PERSON>\", \"incident_status\": \"Active\", \"incident_type\": \"Hurricane\", \"incident_created_at\": \"2024-09-24T13:59:50\", \"incident_updated_at\": \"2025-10-02T13:48:28\"}]"]}, {"label": "Mission", "property": "title", "sampleValues": ["Reposition Tiger Dam trailer ", "Hillsborough County requesting High-Water Rescue Vehicles", "Hotel for Art Abreu", "Starlink staff augmentation ", "Hillsborough County Requesting Tiger Dam- FS #31 West Hillsborough (Copied from Helene Mission #04451)"]}, {"label": "Mission", "property": "mission_number", "sampleValues": ["00001", "00002", "00004", "00005", "00006"]}, {"label": "Mission", "property": "id", "sampleValues": [560550, 560657, 523491, 523496, 560698]}], "importantPropertyPatterns": [{"labels": ["Incident"], "properties": {"incident_created_at": "2020-11-24T17:07:44.000000000+00:00", "incident_id": 76, "incident_name": "2019 Dorian", "incident_status": "Active", "incident_type": "Hurricane", "incident_updated_at": "2021-04-23T20:39:03.000000000+00:00"}, "important_props": ["incident_id", "incident_status", "incident_type", "incident_name", "incident_created_at", "incident_updated_at"]}, {"labels": ["Incident"], "properties": {"incident_created_at": "2025-09-30T13:59:50.000000000+00:00", "incident_id": 180, "incident_name": "2025 Kramer", "incident_status": "Active", "incident_type": "Hurricane", "incident_updated_at": "2025-09-30T13:59:50.000000000+00:00"}, "important_props": ["incident_id", "incident_status", "incident_type", "incident_name", "incident_created_at", "incident_updated_at"]}, {"labels": ["Incident"], "properties": {"incident_created_at": "2024-10-05T17:09:31.000000000+00:00", "incident_id": 172, "incident_name": "2024 Milton", "incident_status": "Active", "incident_type": "Hurricane", "incident_updated_at": "2025-10-02T13:48:28.000000000+00:00"}, "important_props": ["incident_id", "incident_status", "incident_type", "incident_name", "incident_created_at", "incident_updated_at"]}, {"labels": ["Incident"], "properties": {"incident_created_at": "2024-05-10T21:09:54.000000000+00:00", "incident_id": 162, "incident_name": "2024 May Severe Weather", "incident_status": "Active", "incident_type": "<PERSON><PERSON>(s)", "incident_updated_at": "2024-05-10T21:10:36.000000000+00:00"}, "important_props": ["incident_id", "incident_status", "incident_type", "incident_name", "incident_created_at", "incident_updated_at"]}, {"labels": ["Incident"], "properties": {"incident_created_at": "2025-06-25T18:49:50.000000000+00:00", "incident_id": 187, "incident_name": "2025 OVS FACILITIES - TNT", "incident_status": "Active", "incident_type": "Crisis", "incident_updated_at": "2025-10-02T13:48:28.000000000+00:00"}, "important_props": ["incident_id", "incident_status", "incident_type", "incident_name", "incident_created_at", "incident_updated_at"]}, {"labels": ["Incident"], "properties": {"incident_created_at": "2024-08-01T16:18:18.000000000+00:00", "incident_id": 170, "incident_name": "2024 Hurricane <PERSON><PERSON>", "incident_status": "Active", "incident_type": "Hurricane", "incident_updated_at": "2024-08-15T12:29:14.000000000+00:00"}, "important_props": ["incident_id", "incident_status", "incident_type", "incident_name", "incident_created_at", "incident_updated_at"]}, {"labels": ["Incident"], "properties": {"incident_created_at": "2024-06-12T21:02:26.000000000+00:00", "incident_id": 165, "incident_name": "2024 South FL Flooding", "incident_status": "Active", "incident_type": "Flood", "incident_updated_at": "2024-06-14T15:16:19.000000000+00:00"}, "important_props": ["incident_id", "incident_status", "incident_type", "incident_name", "incident_created_at", "incident_updated_at"]}, {"labels": ["Incident"], "properties": {"incident_created_at": "2022-10-24T16:40:53.000000000+00:00", "incident_id": 131, "incident_name": "2022 EMAC Kentucky Flooding", "incident_status": "Active", "incident_type": "Flood", "incident_updated_at": "2022-10-24T16:49:41.000000000+00:00"}, "important_props": ["incident_id", "incident_status", "incident_type", "incident_name", "incident_created_at", "incident_updated_at"]}, {"labels": ["Incident"], "properties": {"incident_created_at": "2024-09-24T13:59:50.000000000+00:00", "incident_id": 171, "incident_name": "2024 <PERSON><PERSON>", "incident_status": "Active", "incident_type": "Hurricane", "incident_updated_at": "2025-10-02T13:48:28.000000000+00:00"}, "important_props": ["incident_id", "incident_status", "incident_type", "incident_name", "incident_created_at", "incident_updated_at"]}, {"labels": ["Incident"], "properties": {"incident_created_at": "2023-10-12T23:01:37.000000000+00:00", "incident_id": 149, "incident_name": "2023 Israel Evacuation", "incident_status": "Active", "incident_type": "Crisis", "incident_updated_at": "2023-10-14T13:53:58.000000000+00:00"}, "important_props": ["incident_id", "incident_status", "incident_type", "incident_name", "incident_created_at", "incident_updated_at"]}], "sampleData": {"nodeSamples": [{"label": "Incident", "samples": [{"properties": {"incident_created_at": "2024-03-26T22:03:15.000000000+00:00", "incident_id": 158, "incident_name": "2024 Haiti Humanitarian Relief", "incident_type": "Crisis", "incident_updated_at": "2024-03-27T19:24:31.000000000+00:00"}}, {"properties": {"incident_created_at": "2022-09-30T22:04:19.000000000+00:00", "incident_id": 133, "incident_name": "2022 Ian", "incident_status": "Active", "incident_updated_at": "2022-11-14T17:50:52.000000000+00:00"}}, {"properties": {"incident_created_at": "2021-04-20T04:17:23.000000000+00:00", "incident_id": 6, "incident_name": "2021 Turkey Point Nuclear Power Plant Practice Exercise", "incident_updated_at": "2021-04-23T20:40:42.000000000+00:00"}}]}, {"label": "<PERSON><PERSON><PERSON>", "samples": [{"properties": {"mission_vendor_name": "ITDRC", "vendor_id": "-401914690", "vendor_updated_at": "2025-10-03T20:47:24.058606000+00:00"}}, {"properties": {"mission_vendor_name": "Cleanable Services LLC", "vendor_id": "-848342248", "vendor_updated_at": "2025-10-03T20:47:24.058606000+00:00"}}, {"properties": {"mission_vendor_name": "L3/Harris MFN II", "vendor_id": "1601183310", "vendor_updated_at": "2025-10-03T20:47:24.058606000+00:00"}}]}, {"label": "Comment", "samples": [{"properties": {"comment_id": "comment_0", "created_at": "2025-06-10T14:58:09.000000000+00:00", "created_by": "<PERSON>", "text": "Please extend TAR for <PERSON> from October 2024 through June 2025."}}, {"properties": {"comment_id": "comment_1", "created_at": "2025-06-05T09:22:28.000000000+00:00", "created_by": "<PERSON><PERSON>", "text": "Order No. C586ED"}}, {"properties": {"comment_id": "comment_2", "created_at": "2025-06-03T17:11:01.000000000+00:00", "created_by": "<PERSON>", "text": "Mission on hold "}}]}, {"label": "FuelTransaction", "samples": [{"properties": {"address": "260 3rd Ave W, Horseshoe Beach, FL 32648, USA", "amount_dispensed": 15.0, "date_of_dueling": "2024-09-30T00:00:00.000000000+00:00", "fuel_transaction_id": "20240930-18381568276", "fuel_type": "MoGas", "fuel_vendor": "Macro"}}, {"properties": {"address": "260 3rd Ave W, Horseshoe Beach, FL 32648, USA", "amount_dispensed": 10.399999618530273, "date_of_dueling": "2024-10-01T00:00:00.000000000+00:00", "fuel_transaction_id": "20241001-18381652888", "fuel_type": "MoGas", "fuel_vendor": "Macro"}}, {"properties": {"address": "260 3rd Ave W, Horseshoe Beach, FL 32648, USA", "amount_dispensed": 15.0, "date_of_dueling": "2024-09-30T00:00:00.000000000+00:00", "fuel_transaction_id": "20240930-18381589720", "fuel_type": "Diesel", "fuel_vendor": "Macro"}}]}, {"label": "Mission", "samples": [{"properties": {"id": 560550, "incidents": "[{\"incident_id\": 172, \"incident_name\": \"2024 Milton\", \"incident_status\": \"Active\", \"incident_type\": \"Hurricane\", \"incident_created_at\": \"2024-10-05T17:09:31\", \"incident_updated_at\": \"2025-10-02T13:48:28\"}]", "mission_number": "00001", "title": "Reposition Tiger Dam trailer "}}, {"properties": {"id": 560657, "incidents": "[{\"incident_id\": 172, \"incident_name\": \"2024 Milton\", \"incident_status\": \"Active\", \"incident_type\": \"Hurricane\", \"incident_created_at\": \"2024-10-05T17:09:31\", \"incident_updated_at\": \"2025-10-02T13:48:28\"}]", "mission_number": "00002", "title": "Hillsborough County requesting High-Water Rescue Vehicles"}}, {"properties": {"id": 523491, "incidents": "[{\"incident_id\": 171, \"incident_name\": \"2024 <PERSON><PERSON>\", \"incident_status\": \"Active\", \"incident_type\": \"Hurricane\", \"incident_created_at\": \"2024-09-24T13:59:50\", \"incident_updated_at\": \"2025-10-02T13:48:28\"}]", "mission_number": "00002", "title": "Hotel for Art Abreu"}}]}], "relationshipSamples": [{"type": "FROM_VENDOR", "samples": [{"properties": {}, "source_labels": ["FuelTransaction"], "target_labels": ["<PERSON><PERSON><PERSON>"]}, {"properties": {}, "source_labels": ["FuelTransaction"], "target_labels": ["<PERSON><PERSON><PERSON>"]}, {"properties": {}, "source_labels": ["FuelTransaction"], "target_labels": ["<PERSON><PERSON><PERSON>"]}]}, {"type": "HAS_TRANSACTION", "samples": [{"properties": {}, "source_labels": ["Mission"], "target_labels": ["FuelTransaction"]}, {"properties": {}, "source_labels": ["Mission"], "target_labels": ["FuelTransaction"]}, {"properties": {}, "source_labels": ["Mission"], "target_labels": ["FuelTransaction"]}]}, {"type": "ASSIGNED_VENDOR", "samples": [{"properties": {}, "source_labels": ["Mission"], "target_labels": ["<PERSON><PERSON><PERSON>"]}, {"properties": {}, "source_labels": ["Mission"], "target_labels": ["<PERSON><PERSON><PERSON>"]}, {"properties": {}, "source_labels": ["Mission"], "target_labels": ["<PERSON><PERSON><PERSON>"]}]}, {"type": "COMMENTED_ON", "samples": [{"properties": {}, "source_labels": ["Comment"], "target_labels": ["Mission"]}, {"properties": {}, "source_labels": ["Comment"], "target_labels": ["Mission"]}, {"properties": {}, "source_labels": ["Comment"], "target_labels": ["Mission"]}]}, {"type": "IS_PARENT_TO", "samples": [{"properties": {}, "source_labels": ["Mission"], "target_labels": ["Mission"]}, {"properties": {}, "source_labels": ["Mission"], "target_labels": ["Mission"]}, {"properties": {}, "source_labels": ["Mission"], "target_labels": ["Mission"]}]}, {"type": "RELATED_TO_INCIDENT", "samples": [{"properties": {}, "source_labels": ["Mission"], "target_labels": ["Incident"]}, {"properties": {}, "source_labels": ["Mission"], "target_labels": ["Incident"]}, {"properties": {}, "source_labels": ["Mission"], "target_labels": ["Incident"]}]}]}, "ontology": {"coreConcepts": ["Mission", "Comment", "Incident", "FuelTransaction", "<PERSON><PERSON><PERSON>"], "hierarchicalRelationships": [{"source": "Mission", "type": "IS_PARENT_TO", "target": "Mission"}], "entityTypes": ["Comment", "Mission", "FuelTransaction", "<PERSON><PERSON><PERSON>", "Incident"]}, "businessContext": {"operationalModel": "Mission Critical", "keyProcesses": ["COMMENTED_ON", "RELATED_TO_INCIDENT", "IS_PARENT_TO", "FROM_VENDOR", "HAS_TRANSACTION", "ASSIGNED_VENDOR"], "keyEntities": ["Mission", "Comment", "Incident", "FuelTransaction", "<PERSON><PERSON><PERSON>"], "totalEntityTypes": 5, "graphMetrics": {"nodeCount": 86322, "relationshipCount": 92917, "relationshipTypes": 6, "averageNodeDegree": 2.15}}}
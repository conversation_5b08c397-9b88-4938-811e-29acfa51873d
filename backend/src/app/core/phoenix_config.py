"""
Phoenix observability configuration - telemetry only.

This module configures OpenTelemetry tracing to send data to an external Phoenix instance.
No auto-start functionality - assumes Phoenix is running externally (Docker or cloud).
"""

from typing import Optional
from pydantic import ConfigDict
from pydantic_settings import BaseSettings


class PhoenixSettings(BaseSettings):
    """
    Phoenix telemetry configuration settings
    Note that .env overrides these defaults
    This is the behavior of pydantic.BaseSettings
    """
    model_config = ConfigDict(
        env_prefix="PHOENIX_",
        env_file=".env",
        extra="ignore"  # Ignore non-Phoenix environment variables
    )
    
    # Phoenix connection settings
    enabled: bool = True
    endpoint: str  # Phoenix server endpoint, required, taken from .env
    # endpoint: str = "http://127.0.0.1:6006"  # Phoenix server endpoint for local dev
    
    # Phoenix Cloud settings (optional)
    api_key: Optional[str] = None
    
    # OpenTelemetry settings
    service_name: str = "graysky-assistant"
    service_version: str = "0.1.0"
    environment: str = "development"


def get_phoenix_settings() -> PhoenixSettings:
    """Get Phoenix settings from environment variables."""
    from .config import settings
    return settings.phoenix


def setup_phoenix_telemetry():
    """
    Configure OpenTelemetry to send traces to external Phoenix instance.
    
    Assumes Phoenix is already running at the configured endpoint.
    For local development: docker run -p 6006:6006 -p 4317:4317 arizephoenix/phoenix:latest
    """
    settings = get_phoenix_settings()
    
    if not settings.enabled:
        return None
    
    from opentelemetry import trace
    from opentelemetry.sdk.trace import TracerProvider
    from opentelemetry.sdk.trace.export import BatchSpanProcessor
    from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter
    from opentelemetry.sdk.resources import Resource
    from opentelemetry.instrumentation.openai import OpenAIInstrumentor
    from opentelemetry.instrumentation.httpx import HTTPXClientInstrumentor
    
    # Create resource with service information
    resource = Resource.create({
        "service.name": settings.service_name,
        "service.version": settings.service_version,
        "service.environment": settings.environment,
    })
    
    # Set up tracer provider
    trace.set_tracer_provider(TracerProvider(resource=resource))
    tracer_provider = trace.get_tracer_provider()
    
    # Configure exporter to external Phoenix instance
    if settings.api_key:
        # Phoenix Cloud
        exporter = OTLPSpanExporter(
            endpoint=f"{settings.endpoint}/v1/traces",
            headers={"Authorization": f"Bearer {settings.api_key}"}
        )
    else:
        # Self-hosted Phoenix
        exporter = OTLPSpanExporter(
            endpoint=f"{settings.endpoint}/v1/traces"
        )
    
    # Add span processor
    span_processor = BatchSpanProcessor(exporter)
    tracer_provider.add_span_processor(span_processor)
    
    # Auto-instrument libraries
    OpenAIInstrumentor().instrument()
    HTTPXClientInstrumentor().instrument()
    
    return tracer_provider
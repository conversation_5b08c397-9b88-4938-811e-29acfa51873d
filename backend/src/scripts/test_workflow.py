#!/usr/bin/env python3
"""
Test the refactored LangGraph workflow with context injection.

Verifies that the parameterless create_tool_calling_workflow() function works
correctly with RuntimeContext dependency injection.

Run from backend/src directory:
    uv run python test_workflow.py
"""

import asyncio
from langchain_core.messages import HumanMessage

from app.agent.langgraph.workflows import create_tool_calling_workflow
from app.agent.langgraph.context import RuntimeContext
from app.mcp.adapter import MCPToolAdapter
from app.core.config import settings


async def test_workflow_creation():
    """Test that workflow can be created without parameters."""
    print("=" * 80)
    print("Test 1: Workflow Creation (No Parameters)")
    print("=" * 80)

    try:
        # Create workflow - NO PARAMETERS!
        workflow = create_tool_calling_workflow()

        print("✅ Workflow created successfully (parameterless)")
        print(f"   Type: {type(workflow)}")
        print(f"   Compiled: {hasattr(workflow, 'invoke')}")

        return workflow, 0

    except Exception as e:
        print(f"❌ Workflow creation failed: {e}")
        import traceback
        traceback.print_exc()
        return None, 1


async def test_context_injection():
    """Test that RuntimeContext can be created and tools discovered."""
    print("\n" + "=" * 80)
    print("Test 2: RuntimeContext Creation")
    print("=" * 80)

    try:
        # Discover tools from enabled servers
        server_configs = {}

        # Add Genie MCP if enabled
        if settings.genie_mcp_enabled:
            server_configs["genie"] = settings.genie_mcp_url

        # Add GraphRAG MCP if enabled
        if settings.graphrag_mcp_enabled:
            server_configs["graphrag"] = settings.graphrag_mcp_url

        adapter = await MCPToolAdapter.discover_all_servers(server_configs)
        tools = await adapter.discover_tools()

        print(f"✅ Tools discovered: {len(tools)}")

        # Create RuntimeContext
        context = RuntimeContext(
            tools=tools,
            genie_space_id=settings.genie_space or "test-space",
            max_iterations=3,
            fast_model=settings.fast_model_deployment,
            synthesis_model=settings.synthesis_model_deployment
        )

        print(f"✅ RuntimeContext created")
        print(f"   {context}")

        return context, 0

    except Exception as e:
        print(f"❌ Context creation failed: {e}")
        import traceback
        traceback.print_exc()
        return None, 1


async def test_workflow_invocation(workflow, context):
    """Test that workflow can be invoked with context injection."""
    print("\n" + "=" * 80)
    print("Test 3: Workflow Invocation with Context Injection")
    print("=" * 80)

    try:
        # Prepare initial state
        initial_state = {
            "messages": [
                HumanMessage(content="What is 2+2?")
            ]
        }

        print(f"   Input: {initial_state['messages'][0].content}")
        print(f"   Invoking workflow with context injection...")

        # Invoke workflow with context injection
        # Context is passed through config dict
        config = {
            "configurable": {
                "context": context
            }
        }

        result = await workflow.ainvoke(
            initial_state,
            config
        )

        print(f"✅ Workflow executed successfully")
        print(f"   Messages in result: {len(result.get('messages', []))}")

        if result.get("messages"):
            last_message = result["messages"][-1]
            print(f"   Last message type: {type(last_message).__name__}")
            if hasattr(last_message, 'content'):
                content = last_message.content[:200]
                print(f"   Response preview: {content}...")

        return 0

    except Exception as e:
        print(f"❌ Workflow invocation failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


async def test_tool_calling(workflow, context):
    """Test that workflow can invoke tools via LLM."""
    print("\n" + "=" * 80)
    print("Test 4: Tool Calling via LLM")
    print("=" * 80)

    try:
        # Prepare query that should trigger tool usage
        initial_state = {
            "messages": [
                HumanMessage(content="List the available Genie spaces")
            ]
        }

        print(f"   Input: {initial_state['messages'][0].content}")
        print(f"   Expected: LLM should call get_genie_spaces tool")
        print(f"   Invoking workflow...")

        # Invoke workflow with context through config
        config = {
            "configurable": {
                "context": context
            }
        }

        result = await workflow.ainvoke(
            initial_state,
            config
        )

        print(f"✅ Workflow executed")
        print(f"   Messages in result: {len(result.get('messages', []))}")

        # Check if tools were called
        messages = result.get("messages", [])
        tool_calls_found = False
        tool_results_found = False

        for msg in messages:
            if hasattr(msg, 'tool_calls') and msg.tool_calls:
                tool_calls_found = True
                print(f"   ✅ Found tool calls: {len(msg.tool_calls)}")
                for tc in msg.tool_calls[:3]:  # First 3
                    print(f"      - {tc.get('name', 'unknown')}")

            if hasattr(msg, 'type') and msg.type == 'tool':
                tool_results_found = True
                print(f"   ✅ Found tool result message")

        if tool_calls_found or tool_results_found:
            print("   ✅ Tool calling pattern verified")
        else:
            print("   ⚠️  No tool calls detected (LLM may have answered directly)")

        return 0

    except Exception as e:
        print(f"❌ Tool calling test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


async def main():
    """Run all workflow tests."""
    print("\n🧪 Testing Refactored LangGraph Workflow\n")

    # Test 1: Workflow creation
    workflow, result1 = await test_workflow_creation()
    if result1 != 0:
        return 1

    # Test 2: Context creation
    context, result2 = await test_context_injection()
    if result2 != 0:
        return 1

    # Test 3: Simple invocation
    result3 = await test_workflow_invocation(workflow, context)
    if result3 != 0:
        return 1

    # Test 4: Tool calling
    result4 = await test_tool_calling(workflow, context)
    if result4 != 0:
        return 1

    # Final summary
    print("\n" + "=" * 80)
    print("✅ ALL WORKFLOW TESTS PASSED")
    print("=" * 80)
    print("\nKey Achievements:")
    print("  ✅ Parameterless workflow creation (Studio compatible)")
    print("  ✅ RuntimeContext dependency injection")
    print("  ✅ LLM-driven tool selection (no hardcoded routing)")
    print("  ✅ Clean node signatures with context")
    print("\nReady for Phase 3: Service Layer Integration")
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)

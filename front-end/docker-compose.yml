# Frontend Docker Compose
# This file works standalone for production or with parent docker-compose for local dev

version: '3.8'

services:
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
      target: ${BUILD_TARGET:-development}  # Configurable for production
    container_name: ${CONTAINER_NAME:-frontend}
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    volumes:
      - .:/app                    # Mounts current directory for hot-reload
      - /app/node_modules        # Prevents overwriting node_modules
      - /app/.next               # Prevents overwriting build cache
    environment:
      # Build Configuration
      - NODE_ENV=${NODE_ENV:-development}
      - WATCHPACK_POLLING=${WATCHPACK_POLLING:-true}   # Enables file watching in Docker

      # Backend API URL (must be accessible from browser)
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL:-http://localhost:8000}

    networks:
      - default
    restart: unless-stopped

networks:
  default:
    name: ${NETWORK_NAME:-frontend-network}
    external: ${EXTERNAL_NETWORK:-false}

# Notes:
# - For local dev: Use parent docker-compose which provides backend service
# - For production: Set BUILD_TARGET=production, NEXT_PUBLIC_API_URL to backend URL
# - Set EXTERNAL_NETWORK=true in production to join existing network
# - NEXT_PUBLIC_* vars are embedded in frontend build (browser-accessible)
# - All configuration via environment variables or .env file
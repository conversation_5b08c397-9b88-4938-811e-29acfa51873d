# Phoenix Docker Compose Environment Variables
# Copy this file to .env and update with your actual values

# =============================================================================
# PostgreSQL Database Configuration
# =============================================================================

# Database credentials (used by both PostgreSQL and Phoenix containers)
POSTGRES_USER=phoenix
POSTGRES_PASSWORD=phoenix_password_change_in_production
POSTGRES_DB=phoenix

# Optional: PostgreSQL connection tuning
# POSTGRES_MAX_CONNECTIONS=100
# POSTGRES_SHARED_BUFFERS=256MB

# =============================================================================
# Phoenix Server Configuration
# =============================================================================

# Phoenix service ports (usually no need to change)
PHOENIX_PORT=6006
PHOENIX_GRPC_PORT=4317
PHOENIX_PROMETHEUS_PORT=9090

# Phoenix server host binding
PHOENIX_HOST=0.0.0.0

# Database connection (automatically constructed from POSTGRES_* vars above)
# Format: postgresql://user:password@host:port/database
PHOENIX_SQL_DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}

# =============================================================================
# Optional: Phoenix Schema Configuration
# =============================================================================

# Uncomment to use a custom PostgreSQL schema (default: public)
# PHOENIX_SQL_DATABASE_SCHEMA=phoenix_traces

# =============================================================================
# Optional: Performance Tuning
# =============================================================================

# Reduce trace sampling for high-volume applications (0.1 = 10% sampling)
# PHOENIX_TRACE_SAMPLING_RATE=1.0

# Enable Prometheus metrics endpoint
# PHOENIX_ENABLE_PROMETHEUS=true

# =============================================================================
# Production Deployment Examples
# =============================================================================

# Example 1: Azure Database for PostgreSQL
# PHOENIX_SQL_DATABASE_URL=postgresql://phoenix:<EMAIL>:5432/phoenix?sslmode=require

# Example 2: AWS RDS PostgreSQL
# PHOENIX_SQL_DATABASE_URL=postgresql://phoenix:<EMAIL>:5432/phoenix?sslmode=require

# Example 3: Google Cloud SQL PostgreSQL
# PHOENIX_SQL_DATABASE_URL=**************************************************/phoenix?sslmode=require

# =============================================================================
# Docker Compose Override Examples
# =============================================================================

# To override default ports (create docker-compose.override.yml):
# services:
#   phoenix:
#     ports:
#       - "6007:6006"  # Phoenix UI on port 6007
#       - "4318:4317"  # OTLP on port 4318
#   postgres:
#     ports:
#       - "5433:5432"  # PostgreSQL on port 5433

# =============================================================================
# Application Configuration (for backend/.env)
# =============================================================================

# Your GraySkyGenAI Assistant should use these settings:
# PHOENIX_ENABLED=true
# PHOENIX_ENDPOINT=http://127.0.0.1:6006
# PHOENIX_SERVICE_NAME=graysky-assistant
# PHOENIX_SERVICE_VERSION=0.1.0
# PHOENIX_ENVIRONMENT=development
#!/usr/bin/env python
"""
Benchmark LangGraph vs Foundation Model implementation.

Compares:
- Response latency
- MCP call patterns
- Response quality

Usage:
    cd /Users/<USER>/Repos/DEMES/assistant/backend/src
    uv run python scripts/benchmark_langgraph.py
"""

import asyncio
import time
import statistics
import sys
from pathlib import Path
from typing import List, Tuple, Dict

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.core.config import settings
from app.dependencies import get_mcp_manager
from app.services.foundation_model import FoundationModelService
from app.services.langgraph_service import LangGraphService
from app.services.streaming import StreamType


class BenchmarkResults:
    """Store benchmark results for analysis."""

    def __init__(self, implementation: str):
        self.implementation = implementation
        self.query_results: List[Dict] = []

    def add_result(self, query: str, query_type: str, elapsed: float, response: str,
                   sources_count: int, reasoning_steps: int):
        """Add a query result."""
        self.query_results.append({
            "query": query,
            "query_type": query_type,
            "elapsed": elapsed,
            "response_length": len(response),
            "sources_count": sources_count,
            "reasoning_steps": reasoning_steps
        })

    def get_average_latency(self) -> float:
        """Get average response time."""
        return statistics.mean(r["elapsed"] for r in self.query_results)

    def get_latency_by_type(self, query_type: str) -> float:
        """Get average latency for specific query type."""
        results = [r for r in self.query_results if r["query_type"] == query_type]
        if not results:
            return 0.0
        return statistics.mean(r["elapsed"] for r in results)


async def run_benchmark_queries(
    service,
    queries: List[Dict[str, str]],
    implementation_name: str,
    mcp_manager
) -> BenchmarkResults:
    """
    Run queries through an implementation and measure performance.

    Args:
        service: Either FoundationModelService or LangGraphService
        queries: List of query dicts with 'query' and 'type' keys
        implementation_name: Name for logging
        mcp_manager: MCP manager instance

    Returns:
        BenchmarkResults with all measurements
    """
    print(f"\n{'='*60}")
    print(f"🏃 Benchmarking: {implementation_name}")
    print(f"{'='*60}")

    results = BenchmarkResults(implementation_name)

    for i, query_dict in enumerate(queries, 1):
        query = query_dict["query"]
        query_type = query_dict["type"]

        print(f"\n[{i}/{len(queries)}] {query_type.upper()}")
        print(f"Query: {query[:60]}...")

        start = time.time()

        messages = [{"role": "user", "content": query}]

        # Collect stream
        response_chunks = []
        sources_count = 0
        reasoning_steps = 0

        try:
            async for stream_msg in service.generate_response_with_streaming(messages, f"bench-{i}"):
                if stream_msg.type == StreamType.CONTENT:
                    response_chunks.append(stream_msg.data.get("chunk", ""))
                elif stream_msg.type == StreamType.SOURCES:
                    sources_count += 1
                elif stream_msg.type == StreamType.REASONING:
                    reasoning_steps += 1

            elapsed = time.time() - start
            response = "".join(response_chunks)

            results.add_result(
                query=query,
                query_type=query_type,
                elapsed=elapsed,
                response=response,
                sources_count=sources_count,
                reasoning_steps=reasoning_steps
            )

            print(f"  ⏱️  {elapsed:.2f}s")
            print(f"  📊 Sources: {sources_count}, Reasoning steps: {reasoning_steps}")
            print(f"  📝 Response: {len(response)} chars")

        except Exception as e:
            print(f"  ❌ Error: {e}")
            elapsed = time.time() - start
            results.add_result(
                query=query,
                query_type=query_type,
                elapsed=elapsed,
                response=f"ERROR: {e}",
                sources_count=0,
                reasoning_steps=0
            )

    return results


async def main():
    """Main benchmark execution."""
    print("=" * 60)
    print("📊 LangGraph vs Foundation Model Benchmark")
    print("=" * 60)

    # Initialize MCP manager once
    print("\n🔧 Initializing MCP Manager...")
    mcp_manager = get_mcp_manager()
    await mcp_manager.initialize()
    print(f"   ✓ {len(mcp_manager._all_tools)} tools available")

    # Define test queries covering different scenarios
    test_queries = [
        {
            "query": "What's the status of Hurricane Idalia and its impact on evacuation routes?",
            "type": "parallel",
            "expected": "Should call both Genie and GraphRAG"
        },
        {
            "query": "Tell me about emergency incidents affecting Florida and how they relate to infrastructure",
            "type": "parallel",
            "expected": "Should call both services"
        },
        {
            "query": "What shelters are currently open?",
            "type": "genie_only",
            "expected": "Should call only Genie"
        },
        {
            "query": "Show me current emergency resource allocations",
            "type": "genie_only",
            "expected": "Should call only Genie"
        },
        {
            "query": "Show me relationships between emergency entities",
            "type": "graph_only",
            "expected": "Should call only GraphRAG"
        },
        {
            "query": "What infrastructure connections exist for emergency response?",
            "type": "graph_only",
            "expected": "Should call only GraphRAG"
        },
        {
            "query": "Hello, how can you help me?",
            "type": "conversational",
            "expected": "Should not call MCP services"
        },
    ]

    print(f"\n📋 Running {len(test_queries)} test queries per implementation")
    print(f"   - {sum(1 for q in test_queries if q['type'] == 'parallel')} parallel queries")
    print(f"   - {sum(1 for q in test_queries if q['type'] == 'genie_only')} genie-only queries")
    print(f"   - {sum(1 for q in test_queries if q['type'] == 'graph_only')} graph-only queries")
    print(f"   - {sum(1 for q in test_queries if q['type'] == 'conversational')} conversational queries")

    # Benchmark 1: Current Foundation Model implementation
    print("\n" + "=" * 60)
    print("🔵 BASELINE: Foundation Model Implementation")
    print("=" * 60)

    foundation_service = FoundationModelService(mcp_manager=mcp_manager)
    baseline_results = await run_benchmark_queries(
        foundation_service,
        test_queries,
        "Foundation Model (Current)",
        mcp_manager
    )

    # Benchmark 2: LangGraph implementation
    print("\n" + "=" * 60)
    print("🟢 EXPERIMENTAL: LangGraph Implementation")
    print("=" * 60)

    langgraph_service = LangGraphService(mcp_manager=mcp_manager)
    langgraph_results = await run_benchmark_queries(
        langgraph_service,
        test_queries,
        "LangGraph (Parallel)",
        mcp_manager
    )

    # Generate comparison report
    print("\n" + "=" * 60)
    print("📈 BENCHMARK RESULTS")
    print("=" * 60)

    # Overall statistics
    baseline_avg = baseline_results.get_average_latency()
    langgraph_avg = langgraph_results.get_average_latency()
    overall_speedup = baseline_avg / langgraph_avg if langgraph_avg > 0 else 0

    print(f"\n{'Overall Performance':.<50}")
    print(f"  Foundation Model Average: {baseline_avg:.2f}s")
    print(f"  LangGraph Average:        {langgraph_avg:.2f}s")
    print(f"  Speedup:                  {overall_speedup:.2f}x", end="")

    if overall_speedup > 1.5:
        print(" 🚀 (Significant improvement!)")
    elif overall_speedup > 1.0:
        print(" ✅ (Faster)")
    elif overall_speedup > 0.9:
        print(" ≈ (Similar)")
    else:
        print(" ⚠️  (Slower)")

    # By query type
    print(f"\n{'Performance by Query Type':.<50}")

    for query_type in ["parallel", "genie_only", "graph_only", "conversational"]:
        baseline_time = baseline_results.get_latency_by_type(query_type)
        langgraph_time = langgraph_results.get_latency_by_type(query_type)

        if baseline_time == 0 or langgraph_time == 0:
            continue

        speedup = baseline_time / langgraph_time

        print(f"\n  {query_type.replace('_', ' ').title()}")
        print(f"    Foundation Model: {baseline_time:.2f}s")
        print(f"    LangGraph:        {langgraph_time:.2f}s")
        print(f"    Speedup:          {speedup:.2f}x", end="")

        if speedup > 1.5:
            print(" 🚀")
        elif speedup > 1.0:
            print(" ✅")
        elif speedup > 0.9:
            print(" ≈")
        else:
            print(" ⚠️")

    # Detailed comparison table
    print(f"\n{'Detailed Query-by-Query Comparison':.<50}")
    print(f"\n{'Query':<40} {'Type':<15} {'Baseline':<12} {'LangGraph':<12} {'Speedup':<10}")
    print("-" * 90)

    for i, query_dict in enumerate(test_queries):
        query = query_dict["query"][:37] + "..."
        query_type = query_dict["type"]

        baseline_time = baseline_results.query_results[i]["elapsed"]
        langgraph_time = langgraph_results.query_results[i]["elapsed"]
        speedup = baseline_time / langgraph_time if langgraph_time > 0 else 0

        print(f"{query:<40} {query_type:<15} {baseline_time:<12.2f} {langgraph_time:<12.2f} {speedup:<10.2f}x")

    # Key insights
    print(f"\n{'Key Insights':.<50}")

    # Check for parallel query improvement
    parallel_baseline = baseline_results.get_latency_by_type("parallel")
    parallel_langgraph = langgraph_results.get_latency_by_type("parallel")

    if parallel_baseline > 0 and parallel_langgraph > 0:
        parallel_speedup = parallel_baseline / parallel_langgraph
        print(f"\n  📊 Parallel Query Performance:")
        print(f"     LangGraph is {parallel_speedup:.2f}x faster for queries needing both services")

        if parallel_speedup >= 2.0:
            print(f"     ✅ This matches our prediction of 2-3x speedup!")
        elif parallel_speedup >= 1.5:
            print(f"     ✅ Significant improvement for parallel queries")
        else:
            print(f"     ⚠️  Less improvement than expected - may need optimization")

    # Check for overhead in simple queries
    conv_baseline = baseline_results.get_latency_by_type("conversational")
    conv_langgraph = langgraph_results.get_latency_by_type("conversational")

    if conv_baseline > 0 and conv_langgraph > 0:
        conv_speedup = conv_baseline / conv_langgraph
        print(f"\n  💬 Conversational Query Overhead:")
        if conv_speedup < 0.9:
            print(f"     ⚠️  LangGraph adds {(1-conv_speedup)*100:.1f}% overhead for simple queries")
            print(f"     (This is expected - classifier runs even when not needed)")
        else:
            print(f"     ✅ Minimal overhead for conversational queries")

    # Recommendation
    print(f"\n{'Recommendation':.<50}")

    if overall_speedup >= 1.5 and parallel_speedup >= 2.0:
        print("\n  ✅ STRONG RECOMMENDATION: Migrate to LangGraph")
        print("     - Significant performance improvements")
        print("     - Especially beneficial for complex queries")
        print("     - Ready for Phase 2 (sub-agents and caching)")
        recommendation = "migrate"
    elif overall_speedup >= 1.0:
        print("\n  ✅ RECOMMENDATION: Consider migrating to LangGraph")
        print("     - Performance improvements observed")
        print("     - Benefits will increase with Phase 2 features")
        recommendation = "consider"
    else:
        print("\n  ⚠️  RECOMMENDATION: Optimize before migrating")
        print("     - Performance not improved as expected")
        print("     - Review classifier efficiency")
        print("     - Consider keeping current implementation")
        recommendation = "optimize"

    print(f"\n{'='*60}")
    print("📊 Benchmark Complete!")
    print(f"{'='*60}\n")

    return recommendation


if __name__ == "__main__":
    recommendation = asyncio.run(main())

    # Exit code based on recommendation
    if recommendation == "migrate":
        sys.exit(0)
    elif recommendation == "consider":
        sys.exit(0)
    else:
        sys.exit(1)

#!/bin/bash

# Docker Test Script - Tests Docker setup with auto-reload
set -e

echo "🐳 Docker Setup Test Script"
echo "============================"
echo ""

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

API_BASE="http://localhost:8000"
BACKEND_DIR="/Users/<USER>/Repos/DEMES/assistant/backend"

# Cleanup function
cleanup() {
    echo -e "\n${YELLOW}🧹 Cleaning up...${NC}"
    cd "$BACKEND_DIR"
    docker-compose down 2>/dev/null || true
    docker image rm backend:test 2>/dev/null || true
    echo -e "${GREEN}✅ Cleanup complete${NC}"
}

# Set up cleanup trap
trap cleanup EXIT INT TERM

cd "$BACKEND_DIR"

# Test 1: Docker Build
echo -e "${BLUE}📦 Test 1: Building Docker image...${NC}"
if docker build -t backend:test --target development . ; then
    echo -e "${GREEN}✅ Docker build successful${NC}"
else
    echo -e "${RED}❌ Docker build failed${NC}"
    exit 1
fi
echo ""

# Test 2: Docker Compose Up
echo -e "${BLUE}🚀 Test 2: Starting docker-compose...${NC}"
docker-compose up -d

echo -e "${BLUE}⏳ Waiting for container to start...${NC}"
sleep 3

# Check if container is running
if docker-compose ps | grep -q "Up"; then
    echo -e "${GREEN}✅ Container is running${NC}"
else
    echo -e "${RED}❌ Container failed to start${NC}"
    docker-compose logs
    exit 1
fi
echo ""

# Test 3: Wait for API to be ready
echo -e "${BLUE}⏳ Test 3: Waiting for API to be ready...${NC}"
for i in {1..30}; do
    if curl -s "$API_BASE/health" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ API is ready!${NC}"
        break
    fi
    echo "Waiting for API... ($i/30)"
    sleep 1
done

if ! curl -s "$API_BASE/health" > /dev/null 2>&1; then
    echo -e "${RED}❌ API failed to start properly${NC}"
    echo "Container logs:"
    docker-compose logs backend
    exit 1
fi
echo ""

# Test 4: Health Endpoint
echo -e "${BLUE}🏥 Test 4: Testing health endpoint...${NC}"
health_response=$(curl -s "$API_BASE/health")
echo "Response: $health_response"
if echo "$health_response" | grep -q "healthy"; then
    echo -e "${GREEN}✅ Health check passed${NC}"
else
    echo -e "${RED}❌ Health check failed${NC}"
    exit 1
fi
echo ""

# Test 5: Root Endpoint
echo -e "${BLUE}🏠 Test 5: Testing root endpoint...${NC}"
root_response=$(curl -s "$API_BASE/")
echo "Response: $root_response"
if echo "$root_response" | grep -q "Welcome"; then
    echo -e "${GREEN}✅ Root endpoint passed${NC}"
else
    echo -e "${RED}❌ Root endpoint failed${NC}"
    exit 1
fi
echo ""

# Test 6: Stream Endpoint
echo -e "${BLUE}🌊 Test 6: Testing stream endpoint...${NC}"
echo "Sending message: 'Hello from Docker test!'"
stream_response=$(curl -N -s "$API_BASE/api/v1/chat/stream" \
    -H "Content-Type: application/json" \
    -d '{"message": "Hello from Docker test!", "conversation_id": "docker-test"}' \
    --max-time 10 | head -20)

if echo "$stream_response" | grep -q "data:"; then
    echo -e "${GREEN}✅ Stream endpoint working${NC}"
    echo "Sample response (first 20 lines):"
    echo "$stream_response" | head -5
else
    echo -e "${RED}❌ Stream endpoint failed${NC}"
    echo "Response: $stream_response"
    exit 1
fi
echo ""

# Test 7: Auto-reload Test
echo -e "${BLUE}🔄 Test 7: Testing auto-reload functionality...${NC}"
echo "Creating a test file change..."

# Get the current log output
docker-compose logs backend > /tmp/docker-logs-before.txt

# Make a small change to trigger reload
TEST_FILE="$BACKEND_DIR/src/app/test_reload.py"
echo "# Auto-reload test - $(date)" > "$TEST_FILE"

# Wait for reload
echo "Waiting for uvicorn to detect change..."
sleep 5

# Check new logs
docker-compose logs backend > /tmp/docker-logs-after.txt

# Clean up test file
rm -f "$TEST_FILE"

if diff /tmp/docker-logs-before.txt /tmp/docker-logs-after.txt > /dev/null; then
    echo -e "${YELLOW}⚠️  Warning: Auto-reload may not have triggered (no new logs detected)${NC}"
    echo "This might be due to timing. Manual verification recommended."
else
    echo -e "${GREEN}✅ Auto-reload detected (logs changed)${NC}"
fi
echo ""

# Test 8: Run Python Test Client (if dependencies available)
echo -e "${BLUE}🧪 Test 8: Running Python test client...${NC}"
if command -v python3 &> /dev/null; then
    if python3 -c "import httpx, rich" 2>/dev/null; then
        echo "Running test_client.py..."
        cd "$BACKEND_DIR"
        python3 test_client.py || echo -e "${YELLOW}⚠️  Test client had issues (API might not support all features yet)${NC}"
    else
        echo -e "${YELLOW}⚠️  Skipping: httpx or rich not installed${NC}"
        echo "Install with: pip install httpx rich"
    fi
else
    echo -e "${YELLOW}⚠️  Skipping: python3 not found${NC}"
fi
echo ""

# Test 9: Check Container Logs
echo -e "${BLUE}📋 Test 9: Container logs (last 20 lines):${NC}"
docker-compose logs --tail=20 backend
echo ""

# Summary
echo -e "${GREEN}🎉 Docker Tests Complete!${NC}"
echo "=========================="
echo -e "${BLUE}Summary:${NC}"
echo -e "  ✅ Docker image built successfully"
echo -e "  ✅ docker-compose started container"
echo -e "  ✅ Health endpoint working"
echo -e "  ✅ Root endpoint working"
echo -e "  ✅ Stream endpoint working"
echo -e "  ✅ Auto-reload functionality checked"
echo ""
echo -e "${YELLOW}Docker files are now at /backend/ root:${NC}"
echo -e "  📄 Dockerfile"
echo -e "  📄 docker-compose.yml"
echo -e "  📄 .dockerignore"
echo ""
echo -e "${BLUE}To use Docker:${NC}"
echo -e "  cd $BACKEND_DIR"
echo -e "  docker-compose up      # Start with auto-reload"
echo -e "  docker-compose down    # Stop"
echo -e "  docker-compose logs -f # View logs"
echo ""

# Cleanup will be called automatically by the trap

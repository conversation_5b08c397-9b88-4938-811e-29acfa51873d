# Multi-stage build for efficient Docker images

# Development stage - with auto-reload for development
FROM python:3.13-slim AS development

WORKDIR /app

# Install uv - fast Python package manager
RUN pip install --no-cache-dir uv

# Copy dependency files and README from src/ subdirectory
COPY src/pyproject.toml src/uv.lock* src/README.md ./

# Install dependencies (including dev dependencies for development)
RUN uv sync

# Note: Source code is mounted via volume in docker-compose.yml for hot-reload
# This allows code changes on the host to be immediately reflected in the container

EXPOSE 8000

# Development command with auto-reload
CMD ["uv", "run", "uvicorn", "app.main:app", "--reload", "--host", "0.0.0.0", "--port", "8000"]

# Production build stage
FROM python:3.13-slim AS production

WORKDIR /app

# Install uv
RUN pip install --no-cache-dir uv

# Copy dependency files and README from src/ subdirectory
COPY src/pyproject.toml src/uv.lock* src/README.md ./

# Install only production dependencies (no dev dependencies)
RUN uv sync --no-dev

# Copy application code from src/ subdirectory
COPY src/ .

EXPOSE 8000

# Production command - no reload for better performance
CMD ["uv", "run", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]

'use client';

import React, { useEffect, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import DOMPurify from 'dompurify';
import { Message } from '../types/chat';

interface MessageListProps {
  messages: Message[];
}

export const MessageList: React.FC<MessageListProps> = ({ messages }) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const formatSender = (sender: string): string => {
    switch (sender) {
      case 'user':
        return 'You';
      case 'assistant':
        return 'Assistant';
      case 'system':
        return 'System';
      default:
        return sender;
    }
  };

  const sanitizeContent = (content: string): string => {
    if (typeof window !== 'undefined') {
      return DOMPurify.sanitize(content);
    }
    return content;
  };

  return (
    <div className="messages" id="messages">
      {messages.length === 0 && (
        <div className="message system">
          <strong>System:</strong> Ready to demonstrate multi-type streaming API
        </div>
      )}
      {messages.map((message) => (
        <div
          key={message.id}
          className={`message ${message.sender} ${message.isStreaming ? 'streaming' : ''}`}
        >
          <strong>{formatSender(message.sender)}:</strong>
          <div className="content">
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              components={{
                // Custom components to ensure consistent styling
                p: ({children}) => <span>{children}</span>,
                // Prevent code blocks from breaking layout during streaming
                code: ({children, className}) => (
                  <code className={className}>{children}</code>
                ),
                pre: ({children}) => <pre>{children}</pre>
              }}
            >
              {sanitizeContent(message.content)}
            </ReactMarkdown>
            {message.isStreaming && <span className="cursor">█</span>}
          </div>
        </div>
      ))}
      <div ref={messagesEndRef} />
    </div>
  );
};
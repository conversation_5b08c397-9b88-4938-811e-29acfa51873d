"""
Test iteration subagent with multiple tool calls.

Run: uv run python scripts/test_iteration_subagent.py
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from langchain_core.messages import HumanMessage
from app.agent.langgraph.workflows import get_graph
from app.agent.langgraph.context import RuntimeContext
from app.agent.langgraph.workflows import get_all_tools
from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)


async def test_iteration():
    """Test iteration subagent."""
    print("=" * 60)
    print("STEP 2: Testing iteration subagent...")
    print("=" * 60)
    print()

    print("1. Getting graph and creating context...")
    graph = await get_graph()
    tools = await get_all_tools()

    # Create context for testing
    context = RuntimeContext(
        tools=tools,
        genie_space_id=settings.genie_space or "default-space",
        max_iterations=3,
        fast_model=settings.fast_model_deployment,
        synthesis_model=settings.synthesis_model_deployment,
        fast_model_temperature=settings.fast_model_temperature,
        synthesis_model_temperature=settings.synthesis_model_temperature,
        iteration_max_iterations=5,
        iteration_enabled=True
    )
    print(f"   ✓ Graph loaded")
    print(f"   ✓ Context created with {len(tools)} tools")
    print()

    print("2. Testing main agent with simple query...")
    # Query that should NOT require multiple tool calls
    messages = [
        HumanMessage(content="What is 2+2?")
    ]

    config = {
        "configurable": {
            "context": context
        }
    }

    result = await graph.ainvoke({"messages": messages}, config)
    result_messages = result.get("messages", [])
    print(f"   ✓ Main agent completed")
    print(f"   ✓ Total messages: {len(result_messages)}")
    print()

    print("3. Testing main agent with tool-calling query...")
    # Query that should require tool calls
    messages = [
        HumanMessage(content="List the available Genie spaces")
    ]

    result = await graph.ainvoke({"messages": messages}, config)
    result_messages = result.get("messages", [])

    # Count tool calls
    tool_calls_found = []
    for msg in result_messages:
        if hasattr(msg, 'tool_calls') and msg.tool_calls:
            for tc in msg.tool_calls:
                tool_name = tc.get('name', 'unknown')
                if tool_name not in tool_calls_found:
                    tool_calls_found.append(tool_name)

    print(f"   ✓ Main agent completed")
    print(f"   ✓ Total messages: {len(result_messages)}")
    print(f"   ✓ Tools called: {len(tool_calls_found)}")
    if tool_calls_found:
        print(f"   ✓ Tool names: {', '.join(tool_calls_found)}")
    print()

    print("4. Iteration subagent structure verification...")
    # Check that iteration subagent node exists
    graph_nodes = list(graph.get_graph().nodes.keys())
    print(f"   Available nodes: {', '.join(graph_nodes)}")

    if "iteration_subagent" in graph_nodes:
        print("   ✓ Iteration subagent node found!")
    else:
        print("   ✗ Iteration subagent node NOT found")
        return False

    print()
    print("=" * 60)
    print("✓ Iteration subagent test complete!")
    print("=" * 60)
    print()
    print("Note: Iteration subagent is available but not actively")
    print("routed to yet. Add custom routing logic to use it.")
    return True


if __name__ == "__main__":
    result = asyncio.run(test_iteration())
    sys.exit(0 if result else 1)

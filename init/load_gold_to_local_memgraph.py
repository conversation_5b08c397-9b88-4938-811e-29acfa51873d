#!/usr/bin/env python3
"""
Load knowledge graph from Databricks gold table to LOCAL Memgraph

This script supports three operational modes:
1. DOWNLOAD ONLY: Fetch queries from Databricks and save to local JSON file
2. LOAD FROM LOCAL: Load queries from local JSON file into Memgraph
3. DOWNLOAD AND LOAD: Fetch from Databricks, save to file, and load into Memgraph

Benefits:
- Separate download and load phases to avoid waiting for Databricks during loading iterations
- Save query data locally for reproducibility and faster testing
- Flexible workflows for debugging and development

USAGE:
    # Download and load (default)
    python load_gold_to_local_memgraph.py
    
    # Download only
    python load_gold_to_local_memgraph.py --download
    
    # Load from previously downloaded file
    python load_gold_to_local_memgraph.py --load-from-local
    
    # Custom data file
    python load_gold_to_local_memgraph.py --download --data-file my_data.json
    python load_gold_to_local_memgraph.py --load-from-local --data-file my_data.json
"""
import requests
import os
import sys
import json
import argparse
import time
import re
from datetime import datetime
from dotenv import load_dotenv
from neo4j import GraphDatabase
from neo4j.exceptions import ServiceUnavailable, TransientError

# Load environment variables
load_dotenv()

# Databricks configuration
DATABRICKS_HOST = os.getenv("DATABRICKS_HOST")
DATABRICKS_TOKEN = os.getenv("DATABRICKS_TOKEN")
DATABRICKS_WAREHOUSE_ID = os.getenv("DATABRICKS_WAREHOUSE_ID")
DATABRICKS_CATALOG = os.getenv("DATABRICKS_CATALOG", "graysky-dev-catalog")

# Local Memgraph configuration
LOCAL_MEMGRAPH_URI = "bolt://localhost:7687"


def execute_sql_query(query, max_retries=60, retry_delay=5):
    """Execute SQL query via Databricks SQL warehouse and return results
    
    Args:
        query: SQL query to execute
        max_retries: Maximum number of polling attempts for PENDING queries
        retry_delay: Seconds to wait between polling attempts
    """
    url = f"{DATABRICKS_HOST}/api/2.0/sql/statements"
    headers = {
        "Authorization": f"Bearer {DATABRICKS_TOKEN}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "warehouse_id": DATABRICKS_WAREHOUSE_ID,
        "statement": query,
        "format": "JSON_ARRAY"
        # Using default INLINE disposition (no external links)
    }
    
    print(f"Executing query via SQL warehouse...")
    response = requests.post(url, headers=headers, json=payload)
    
    if response.status_code != 200:
        print(f"✗ Query failed: {response.text}")
        return None
    
    result = response.json()
    statement_id = result.get("statement_id")
    
    # Poll for completion if query is still running
    status = result.get("status", {}).get("state")
    retries = 0
    
    while status in ["PENDING", "RUNNING"] and retries < max_retries:
        print(f"  ⏳ Query {status}, waiting {retry_delay}s... (attempt {retries + 1}/{max_retries})")
        time.sleep(retry_delay)
        
        # Poll for status
        status_url = f"{DATABRICKS_HOST}/api/2.0/sql/statements/{statement_id}"
        status_response = requests.get(status_url, headers=headers)
        
        if status_response.status_code != 200:
            print(f"✗ Status check failed: {status_response.text}")
            return None
        
        result = status_response.json()
        status = result.get("status", {}).get("state")
        retries += 1
    
    # Check final status
    if status != "SUCCEEDED":
        print(f"✗ Query did not succeed: {status}")
        error = result.get("status", {}).get("error", {})
        if error:
            print(f"  Error: {error.get('message', 'Unknown error')}")
        return None
    
    return result


def fetch_cypher_queries_from_databricks(table_name, checkpoint_file=None, checkpoint_interval=5000):
    """
    Fetch Cypher queries from Databricks and categorize them
    
    Args:
        table_name: Full table name (e.g., graysky-dev-catalog.gold.knowledge_graph)
        checkpoint_file: Path to save checkpoint progress (for resuming)
        checkpoint_interval: Save checkpoint every N queries
    
    Returns:
        Tuple of (index_queries, node_queries, edge_queries)
    """
    print(f"\nFetching Cypher queries from Databricks: {table_name}")
    print(f"Using SQL pagination (OFFSET/LIMIT) to bypass Azure Storage firewall")
    
    # Check for existing checkpoint to resume from
    index_queries = []
    node_queries = []
    edge_queries = []
    start_offset = 0
    
    if checkpoint_file and os.path.exists(checkpoint_file):
        try:
            print(f"\n🔄 Found existing checkpoint: {checkpoint_file}")
            with open(checkpoint_file, 'r') as f:
                checkpoint_data = json.load(f)
            
            metadata = checkpoint_data.get('metadata', {})
            if not metadata.get('is_complete', False):
                # Resume from checkpoint
                index_queries = checkpoint_data.get('queries', {}).get('index_queries', [])
                node_queries = checkpoint_data.get('queries', {}).get('node_queries', [])
                edge_queries = checkpoint_data.get('queries', {}).get('edge_queries', [])
                start_offset = metadata.get('current_offset', 0)
                
                print(f"   Resuming from offset: {start_offset:,}")
                print(f"   Already fetched: {len(index_queries) + len(node_queries) + len(edge_queries):,} queries")
            else:
                print(f"   Checkpoint is complete, starting fresh")
        except Exception as e:
            print(f"   ⚠ Could not load checkpoint: {e}")
            print(f"   Starting fresh")
    
    # Separate into batches for efficient processing
    chunk_size = 500  # Rows per request (stay under 25MB INLINE limit for large cypher strings)
    offset = start_offset
    total_fetched = len(index_queries) + len(node_queries) + len(edge_queries)
    last_checkpoint = total_fetched
    
    while True:
        # Paginated query
        query = f"""
            SELECT cypher_json
            FROM {table_name}
            ORDER BY execution_order
            LIMIT {chunk_size} OFFSET {offset}
        """
        
        print(f"  Fetching rows {offset:,} to {offset + chunk_size:,}...")
        result = execute_sql_query(query)
        
        if not result:
            print(f"  ⚠ Query failed")
            break
        
        # Get inline data
        result_data = result.get("result", {})
        data_array = result_data.get("data_array")
        
        if not data_array:
            if offset == 0:
                print(f"  ⚠ No data returned from query")
            break
        
        # Parse and categorize queries in this chunk
        chunk_parsed = 0
        for row in data_array:
            cypher_json_str = row[0] if len(row) > 0 else None
            if cypher_json_str:
                try:
                    data = json.loads(cypher_json_str)
                    if 'cypher' in data and data['cypher']:
                        cypher_query = data['cypher']
                        
                        # Categorize query
                        if cypher_query.strip().startswith('//'):
                            continue  # Skip comments
                        elif cypher_query.startswith('CREATE INDEX'):
                            index_queries.append(cypher_query)
                        elif cypher_query.startswith('MATCH'):
                            edge_queries.append(cypher_query)
                        elif cypher_query.startswith('MERGE') or cypher_query.startswith('CREATE'):
                            node_queries.append(cypher_query)
                        else:
                            node_queries.append(cypher_query)
                        
                        chunk_parsed += 1
                except json.JSONDecodeError:
                    pass
        
        total_fetched += chunk_parsed
        print(f"    ✓ Parsed {chunk_parsed:,} queries (total: {total_fetched:,})")
        
        # Save checkpoint periodically
        if checkpoint_file and (total_fetched - last_checkpoint) >= checkpoint_interval:
            save_queries_to_file(
                filepath=checkpoint_file,
                index_queries=index_queries,
                node_queries=node_queries,
                edge_queries=edge_queries,
                table_name=table_name,
                current_offset=offset + chunk_size,
                is_complete=False
            )
            last_checkpoint = total_fetched
        
        # Check if result was truncated due to 25MB limit
        manifest = result.get("manifest", {})
        truncated = manifest.get("truncated", False)
        
        if truncated:
            print(f"    ⚠ Result truncated by 25MB INLINE limit - reduce chunk_size")
        
        # Check if done - only stop if we got ZERO rows
        if len(data_array) == 0:
            print(f"  ✓ Reached end of data")
            break
        
        # If we got fewer rows than requested but not zero, continue (may be end or truncation)
        if len(data_array) < chunk_size:
            print(f"  Continuing (got {len(data_array)} rows, may be end of data next...)")
        
        offset += chunk_size
    
    print(f"\n  ✓ Fetched {total_fetched:,} Cypher queries total")
    print(f"\nQuery breakdown:")
    print(f"  • {len(index_queries):,} indices")
    print(f"  • {len(node_queries):,} nodes")
    print(f"  • {len(edge_queries):,} edges")
    
    return index_queries, node_queries, edge_queries


def connect_to_local_memgraph():
    """Connect to local Memgraph instance"""
    try:
        print(f"\nConnecting to local Memgraph at {LOCAL_MEMGRAPH_URI}...")
        driver = GraphDatabase.driver(LOCAL_MEMGRAPH_URI)
        driver.verify_connectivity()
        print("  ✓ Successfully connected!")
        return driver
    except Exception as e:
        print(f"  ✗ Connection failed: {type(e).__name__}")
        print(f"    Error details: {str(e)}")
        print(f"\n    Make sure Memgraph is running locally:")
        print(f"    docker run -p 7687:7687 memgraph/memgraph-platform")
        return None


def clear_memgraph_database(driver):
    """Clear all data from Memgraph database"""
    print("\n🗑️  Clearing existing data...")
    with driver.session() as session:
        session.run("MATCH (n) DETACH DELETE n")
    print("   ✓ Database cleared\n")


def parse_cypher_to_params(cypher_query):
    """
    Parse a Cypher MERGE query into parameters for UNWIND batching
    
    Args:
        cypher_query: String like "MERGE (m:Mission {id: 123, title: 'Test'})"
    
    Returns:
        Dict with label and properties
    """
    # Extract label and properties
    # Pattern: MERGE (var:Label {prop1: val1, prop2: val2})
    match = re.search(r'MERGE\s+\(\w+:(\w+)\s+({[^}]+})\)', cypher_query)
    if not match:
        # Try MATCH pattern for edges
        match = re.search(r'MATCH.*MERGE\s+\([^)]+\)-\[:(\w+)\]->\([^)]+\)', cypher_query)
        if match:
            # Edge query - extract source, target, relationship
            return None  # Will handle edges differently
        return None
    
    label = match.group(1)
    props_str = match.group(2)
    
    # Parse properties - handle both string and number values
    props = {}
    for prop_match in re.finditer(r'(\w+):\s*([^,}]+)', props_str):
        key = prop_match.group(1)
        value = prop_match.group(2).strip()
        # Remove quotes from strings
        if value.startswith("'") or value.startswith('"'):
            value = value[1:-1]
        props[key] = value
    
    return {'label': label, 'props': props}


def execute_nodes_with_unwind(driver, node_queries, batch_size=1000, max_retries=5):
    """
    Execute node creation queries using UNWIND for atomic batch operations
    
    Args:
        driver: Neo4j driver instance
        node_queries: List of MERGE node queries
        batch_size: Number of nodes to process in single UNWIND query
        max_retries: Maximum retry attempts for transient failures
    
    Returns:
        Tuple of (success_count, failed_count)
    """
    total_success = 0
    total_failed = 0
    
    # Group queries by label for efficient UNWIND
    queries_by_label = {}
    for query in node_queries:
        parsed = parse_cypher_to_params(query)
        if parsed:
            label = parsed['label']
            if label not in queries_by_label:
                queries_by_label[label] = []
            queries_by_label[label].append(parsed['props'])
    
    # Process each label group in batches
    for label, props_list in queries_by_label.items():
        for batch_start in range(0, len(props_list), batch_size):
            batch_end = min(batch_start + batch_size, len(props_list))
            batch_props = props_list[batch_start:batch_end]
            
            # Build UNWIND query
            unwind_query = f"""
                UNWIND $batch_data AS props
                MERGE (n:{label})
                SET n = props
            """
            
            # Retry with exponential backoff
            for attempt in range(max_retries):
                try:
                    with driver.session() as session:
                        with session.begin_transaction() as tx:
                            tx.run(unwind_query, batch_data=batch_props)
                            tx.commit()
                    total_success += len(batch_props)
                    break
                except (ServiceUnavailable, TransientError) as e:
                    if attempt < max_retries - 1:
                        wait_time = 2 ** attempt
                        time.sleep(wait_time)
                    else:
                        total_failed += len(batch_props)
                        print(f"  ✗ Batch failed after {max_retries} retries: {str(e)[:100]}")
    
    return total_success, total_failed


def execute_edges_with_unwind(driver, edge_queries, batch_size=500, max_retries=5):
    """
    Execute edge creation queries using UNWIND for atomic batch operations
    
    Args:
        driver: Neo4j driver instance
        edge_queries: List of MATCH/MERGE edge queries
        batch_size: Number of edges to process in single UNWIND query
        max_retries: Maximum retry attempts for transient failures
    
    Returns:
        Tuple of (success_count, failed_count)
    """
    total_success = 0
    total_failed = 0
    
    # For now, fall back to individual queries for edges
    # TODO: Parse edge queries into UNWIND format
    for batch_start in range(0, len(edge_queries), batch_size):
        batch_end = min(batch_start + batch_size, len(edge_queries))
        batch = edge_queries[batch_start:batch_end]
        
        # Retry with exponential backoff
        for attempt in range(max_retries):
            try:
                with driver.session() as session:
                    with session.begin_transaction() as tx:
                        for query in batch:
                            tx.run(query)
                        tx.commit()
                total_success += len(batch)
                break
            except (ServiceUnavailable, TransientError) as e:
                if attempt < max_retries - 1:
                    wait_time = 2 ** attempt
                    time.sleep(wait_time)
                else:
                    total_failed += len(batch)
                    print(f"  ✗ Batch failed after {max_retries} retries: {str(e)[:100]}")
    
    return total_success, total_failed


def save_load_progress(filepath, nodes_loaded=None, edges_loaded=None):
    """
    Save loading progress to a separate checkpoint file (NOT the main data file)
    
    Args:
        filepath: Path to the JSON data file
        nodes_loaded: Number of nodes successfully loaded
        edges_loaded: Number of edges successfully loaded
    """
    checkpoint_file = filepath + '.checkpoint'
    
    # Load existing checkpoint or create new
    if os.path.exists(checkpoint_file):
        with open(checkpoint_file, 'r') as f:
            checkpoint = json.load(f)
    else:
        checkpoint = {}
    
    # Update checkpoint
    if nodes_loaded is not None:
        checkpoint['nodes_loaded'] = nodes_loaded
    if edges_loaded is not None:
        checkpoint['edges_loaded'] = edges_loaded
    
    # Write to separate checkpoint file (atomic write to prevent corruption)
    temp_file = checkpoint_file + '.tmp'
    with open(temp_file, 'w') as f:
        json.dump(checkpoint, f)
    os.rename(temp_file, checkpoint_file)


def load_with_resume(driver, filepath, checkpoint_interval=5000):
    """
    Load data from file with resume capability from last checkpoint
    
    Args:
        driver: Neo4j driver instance
        filepath: Path to the JSON data file
        checkpoint_interval: Save progress every N nodes/edges
    """
    # Load data from main file
    index_queries, node_queries, edge_queries, metadata = load_queries_from_file(filepath)
    
    # Load progress from separate checkpoint file (NOT from main file metadata)
    checkpoint_file = filepath + '.checkpoint'
    if os.path.exists(checkpoint_file):
        with open(checkpoint_file, 'r') as f:
            checkpoint = json.load(f)
        nodes_loaded = checkpoint.get('nodes_loaded', 0)
        edges_loaded = checkpoint.get('edges_loaded', 0)
    else:
        nodes_loaded = 0
        edges_loaded = 0
    
    # CRITICAL: Create indices FIRST (only on first run)
    if nodes_loaded == 0 and edges_loaded == 0 and index_queries:
        print(f"\n{'='*80}")
        print(f"PASS 1: Creating {len(index_queries)} indices")
        print(f"{'='*80}")
        
        # List all indices first
        print("\nIndices to create:")
        for query in index_queries:
            if 'ON :' in query:
                index_info = query.split('ON :')[1].split(';')[0]
                print(f"  • {index_info}")
        
        print(f"\nCreating indices...")
        with driver.session() as session:
            created = 0
            for query in index_queries:
                try:
                    session.run(query)
                    created += 1
                except Exception as e:
                    # Index may already exist, that's fine
                    pass
        print(f"  ✓ Created {created}/{len(index_queries)} indices\n")
    
    print(f"\nResuming from checkpoint:")
    print(f"  • Nodes already loaded: {nodes_loaded:,}/{len(node_queries):,}")
    print(f"  • Edges already loaded: {edges_loaded:,}/{len(edge_queries):,}")
    
    # Process remaining nodes in chunks with periodic checkpointing
    # Use old direct execution method (works with complex queries)
    remaining_nodes = node_queries[nodes_loaded:]
    if remaining_nodes:
        print(f"\nLoading {len(remaining_nodes):,} remaining nodes...")
        batch_size = 75
        
        for batch_start in range(0, len(remaining_nodes), batch_size):
            batch_end = min(batch_start + batch_size, len(remaining_nodes))
            batch = remaining_nodes[batch_start:batch_end]
            
            # Retry logic for transient failures
            success = 0
            for attempt in range(3):
                try:
                    with driver.session() as session:
                        with session.begin_transaction() as tx:
                            for query in batch:
                                tx.run(query)
                            tx.commit()
                    success = len(batch)
                    break
                except (ServiceUnavailable, TransientError) as e:
                    if attempt < 2:
                        print(f"  ⚠ Connection issue, retrying... (attempt {attempt + 1})")
                        time.sleep(2)
                    else:
                        print(f"  ✗ Batch failed after retries: {str(e)[:100]}")
            
            nodes_loaded += success
            
            # Progress output every 5000 nodes
            if nodes_loaded % 5000 < batch_size:
                print(f"  • Processed {nodes_loaded:,}/{len(node_queries):,} nodes...")
            
            # Checkpoint periodically
            if nodes_loaded % checkpoint_interval < batch_size:
                save_load_progress(filepath, nodes_loaded=nodes_loaded)
                print(f"  💾 Checkpoint: {nodes_loaded:,}/{len(node_queries):,} nodes loaded")
    
    # Process remaining edges in chunks with periodic checkpointing
    remaining_edges = edge_queries[edges_loaded:]
    if remaining_edges:
        print(f"\nLoading {len(remaining_edges):,} remaining edges...")
        batch_size = 50
        
        for batch_start in range(0, len(remaining_edges), batch_size):
            batch_end = min(batch_start + batch_size, len(remaining_edges))
            batch = remaining_edges[batch_start:batch_end]
            
            # Retry logic for transient failures
            success = 0
            for attempt in range(3):
                try:
                    with driver.session() as session:
                        with session.begin_transaction() as tx:
                            for query in batch:
                                tx.run(query)
                            tx.commit()
                    success = len(batch)
                    break
                except (ServiceUnavailable, TransientError) as e:
                    if attempt < 2:
                        print(f"  ⚠ Connection issue, retrying... (attempt {attempt + 1})")
                        time.sleep(2)
                    else:
                        print(f"  ✗ Batch failed after retries: {str(e)[:100]}")
            
            edges_loaded += success
            
            # Progress output every 5000 edges
            if edges_loaded % 5000 < batch_size:
                print(f"  • Processed {edges_loaded:,}/{len(edge_queries):,} edges...")
            
            # Checkpoint periodically
            if edges_loaded % checkpoint_interval < batch_size:
                save_load_progress(filepath, edges_loaded=edges_loaded)
                print(f"  💾 Checkpoint: {edges_loaded:,}/{len(edge_queries):,} edges loaded")


def execute_queries_categorized(driver, index_queries, node_queries, edge_queries):
    """
    Execute Cypher queries in local Memgraph - three pass approach
    
    Pass 1: Create indices for performance
    Pass 2: Create nodes
    Pass 3: Create edges/relationships
    """
    
    # Pass 1: Create indices
    print(f"\n{'='*80}")
    print(f"PASS 1: Creating {len(index_queries)} indices")
    print(f"{'='*80}")
    index_success = 0
    index_errors = 0
    
    if index_queries:
        with driver.session() as session:
            for i, query in enumerate(index_queries):
                try:
                    session.run(query)
                    index_success += 1
                    if 'ON :' in query:
                        index_info = query.split('ON :')[1].split(';')[0]
                        print(f"  ✓ Created index: {index_info}")
                except Exception as e:
                    index_errors += 1
                    if index_errors <= 5:
                        print(f"  ⚠ Index error (may already exist): {str(e)[:100]}")
        
        print(f"\nPass 1 complete: {index_success}/{len(index_queries)} indices created")
    else:
        print("  No indices to create (skipping)")
    
    # Pass 2: Create nodes (batched)
    print(f"\n{'='*80}")
    print(f"PASS 2: Creating {len(node_queries)} nodes")
    print(f"{'='*80}")
    node_success = 0
    node_errors = 0
    batch_size = 75
    
    for batch_start in range(0, len(node_queries), batch_size):
        batch_end = min(batch_start + batch_size, len(node_queries))
        batch = node_queries[batch_start:batch_end]
        
        # Retry logic for transient failures
        for attempt in range(3):
            try:
                with driver.session() as session:
                    with session.begin_transaction() as tx:
                        for query in batch:
                            tx.run(query)
                        tx.commit()
                node_success += len(batch)
                break
            except (ServiceUnavailable, TransientError) as e:
                if attempt < 2:
                    print(f"  ⚠ Connection issue, retrying... (attempt {attempt + 1})")
                    time.sleep(2)
                else:
                    node_errors += len(batch)
                    print(f"  ✗ Batch {batch_start}-{batch_end} failed after retries: {str(e)[:100]}")
            except Exception as e:
                node_errors += len(batch)
                if node_errors <= 10:
                    print(f"  ✗ Batch {batch_start}-{batch_end} error: {str(e)[:100]}")
                break
        
        if (batch_end) % 5000 == 0:
            print(f"  • Processed {batch_end}/{len(node_queries)} nodes...")
    
    print(f"\nPass 2 complete: {node_success}/{len(node_queries)} nodes created")
    if node_errors > 0:
        print(f"  ⚠ {node_errors} node creation errors")
    
    # Pass 3: Create edges (batched)
    print(f"\n{'='*80}")
    print(f"PASS 3: Creating {len(edge_queries)} edges")
    print(f"{'='*80}")
    edge_success = 0
    edge_errors = 0
    batch_size = 50  # Reduced to prevent memory pressure
    
    for batch_start in range(0, len(edge_queries), batch_size):
        batch_end = min(batch_start + batch_size, len(edge_queries))
        batch = edge_queries[batch_start:batch_end]
        
        # Retry logic for transient failures
        for attempt in range(3):
            try:
                with driver.session() as session:
                    with session.begin_transaction() as tx:
                        for query in batch:
                            tx.run(query)
                        tx.commit()
                edge_success += len(batch)
                break
            except (ServiceUnavailable, TransientError) as e:
                if attempt < 2:
                    print(f"  ⚠ Connection issue, retrying... (attempt {attempt + 1})")
                    time.sleep(2)
                else:
                    edge_errors += len(batch)
                    print(f"  ✗ Batch {batch_start}-{batch_end} failed after retries: {str(e)[:100]}")
            except Exception as e:
                edge_errors += len(batch)
                if edge_errors <= 10:
                    print(f"  ✗ Batch {batch_start}-{batch_end} error: {str(e)[:100]}")
                break
        
        if (batch_end) % 5000 == 0:
            print(f"  • Processed {batch_end}/{len(edge_queries)} edges...")
    
    print(f"\nPass 3 complete: {edge_success}/{len(edge_queries)} edges created")
    if edge_errors > 0:
        print(f"  ⚠ {edge_errors} edge creation errors")
    
    # Summary
    total_success = index_success + node_success + edge_success
    total_errors = index_errors + node_errors + edge_errors
    total_queries = len(index_queries) + len(node_queries) + len(edge_queries)
    
    print(f"\n{'='*80}")
    print(f"EXECUTION SUMMARY")
    print(f"{'='*80}")
    print(f"Total queries executed: {total_success}/{total_queries}")
    print(f"  • Indices: {index_success}/{len(index_queries)}")
    print(f"  • Nodes:   {node_success}/{len(node_queries)}")
    print(f"  • Edges:   {edge_success}/{len(edge_queries)}")
    if total_errors > 0:
        print(f"\nTotal errors: {total_errors}")
        print(f"  (Some errors are expected for duplicate indices)")
    else:
        print(f"\n✓ All queries executed successfully!")


def verify_memgraph_data(driver):
    """Verify data was loaded correctly in local Memgraph"""
    with driver.session() as session:
        print(f"\n{'='*80}")
        print("DATA VERIFICATION")
        print(f"{'='*80}")
        
        # Count all node types
        result = session.run("MATCH (n) RETURN DISTINCT labels(n) as label, count(n) as count ORDER BY count DESC")
        print("\n✓ Node counts by type:")
        total_nodes = 0
        for record in result:
            label_str = ', '.join(record['label']) if record['label'] else 'unlabeled'
            count = record['count']
            total_nodes += count
            print(f"  • {label_str}: {count:,}")
        print(f"\nTotal nodes: {total_nodes:,}")
        
        # Count relationships
        result = session.run("MATCH ()-[r]->() RETURN type(r) as rel_type, count(r) as count ORDER BY count DESC")
        print("\n✓ Relationship counts by type:")
        total_rels = 0
        for record in result:
            count = record['count']
            total_rels += count
            print(f"  • {record['rel_type']}: {count:,}")
        print(f"\nTotal relationships: {total_rels:,}")
        
        # Sample Mission data
        result = session.run("MATCH (m:Mission) RETURN m.id, m.title LIMIT 3")
        records = list(result)
        if records:
            print("\n✓ Sample Mission nodes:")
            for record in records:
                title = (record['m.title'][:60] + '...') if record['m.title'] and len(record['m.title']) > 60 else (record['m.title'] or 'No title')
                print(f"  • {record['m.id']}: '{title}'")
        
        # Sample Branch data
        result = session.run("MATCH (b:Branch) RETURN b.branch_id, b.name LIMIT 3")
        records = list(result)
        if records:
            print("\n✓ Sample Branch nodes:")
            for record in records:
                print(f"  • {record['b.branch_id']}: {record['b.name'] or 'N/A'}")
        
        # Sample Sub-Branch data
        result = session.run("MATCH (sb:SubBranch) RETURN sb.sub_branch_id, sb.name LIMIT 3")
        records = list(result)
        if records:
            print("\n✓ Sample Sub-Branch nodes:")
            for record in records:
                print(f"  • {record['sb.sub_branch_id']}: {record['sb.name'] or 'N/A'}")
        
        # Sample Mission->Branch relationships
        result = session.run("""
            MATCH (m:Mission)-[:ASSIGNED_TO]->(b:Branch)
            RETURN m.id, m.title, b.name
            LIMIT 3
        """)
        records = list(result)
        if records:
            print("\n✓ Sample Mission→Branch (ASSIGNED_TO) relationships:")
            for record in records:
                title = (record['m.title'][:40] + '...') if record['m.title'] and len(record['m.title']) > 40 else (record['m.title'] or 'N/A')
                print(f"  • {record['m.id']} → {record['b.name']}: '{title}'")
        
        # Sample Mission->Sub-Branch relationships
        result = session.run("""
            MATCH (m:Mission)-[:TASKED_TO]->(sb:SubBranch)
            RETURN m.id, m.title, sb.name
            LIMIT 3
        """)
        records = list(result)
        if records:
            print("\n✓ Sample Mission→Sub-Branch (TASKED_TO) relationships:")
            for record in records:
                title = (record['m.title'][:40] + '...') if record['m.title'] and len(record['m.title']) > 40 else (record['m.title'] or 'N/A')
                print(f"  • {record['m.id']} → {record['sb.name']}: '{title}'")
        
        # Sample Sub-Branch->Vendor relationships
        result = session.run("""
            MATCH (sb:SubBranch)-[:WORKS_WITH]->(v:Vendor)
            RETURN sb.name, v.vendor_id, v.mission_vendor_name
            LIMIT 3
        """)
        records = list(result)
        if records:
            print("\n✓ Sample Sub-Branch→Vendor (WORKS_WITH) relationships:")
            for record in records:
                vendor_name = record['v.mission_vendor_name'] or 'N/A'
                print(f"  • {record['sb.name']} → Vendor {record['v.vendor_id']}: {vendor_name}")
        
        # Sample Vendor->Mission relationships
        result = session.run("""
            MATCH (v:Vendor)-[:RESPONSIBLE_FOR]->(m:Mission)
            RETURN v.vendor_id, v.mission_vendor_name, m.id, m.title
            LIMIT 3
        """)
        records = list(result)
        if records:
            print("\n✓ Sample Vendor→Mission (RESPONSIBLE_FOR) relationships:")
            for record in records:
                vendor_name = record['v.mission_vendor_name'] or 'N/A'
                title = (record['m.title'][:40] + '...') if record['m.title'] and len(record['m.title']) > 40 else (record['m.title'] or 'N/A')
                print(f"  • Vendor {record['v.vendor_id']} ({vendor_name}) → {record['m.id']}: '{title}'")
        
        # Sample County nodes
        result = session.run("MATCH (c:County) RETURN c.county_name, c.region LIMIT 3")
        records = list(result)
        if records:
            print("\n✓ Sample County nodes:")
            for record in records:
                region = record['c.region'] or 'N/A'
                print(f"  • {record['c.county_name']}: {region}")
        
        # Sample EEIStatus nodes
        result = session.run("MATCH (eei:EEIStatus) RETURN eei.eei_history_id, eei.county_name, eei.isActive LIMIT 3")
        records = list(result)
        if records:
            print("\n✓ Sample EEIStatus nodes:")
            for record in records:
                status = "ACTIVE" if record['eei.isActive'] else "HISTORICAL"
                print(f"  • {record['eei.eei_history_id']}: {record['eei.county_name']} ({status})")
        
        # Sample Mission→County relationships
        result = session.run("""
            MATCH (m:Mission)-[:OPERATES_IN]->(c:County)
            RETURN m.id, m.title, c.county_name
            LIMIT 3
        """)
        records = list(result)
        if records:
            print("\n✓ Sample Mission→County (OPERATES_IN) relationships:")
            for record in records:
                title = (record['m.title'][:40] + '...') if record['m.title'] and len(record['m.title']) > 40 else (record['m.title'] or 'N/A')
                print(f"  • {record['m.id']} → {record['c.county_name']}: '{title}'")
        
        # Sample County→EEIStatus relationships (current)
        result = session.run("""
            MATCH (c:County)-[:HAS_CURRENT_STATUS]->(eei:EEIStatus)
            RETURN c.county_name, eei.eei_history_id, eei.eoc_activation
            LIMIT 3
        """)
        records = list(result)
        if records:
            print("\n✓ Sample County→EEIStatus (HAS_CURRENT_STATUS) relationships:")
            for record in records:
                eoc = record['eei.eoc_activation'] or 'N/A'
                print(f"  • {record['c.county_name']} → {record['eei.eei_history_id']}: EOC={eoc}")


def save_queries_to_file(filepath, index_queries, node_queries, edge_queries, table_name, current_offset=None, is_complete=True):
    """
    Save categorized queries to a JSON file with metadata
    
    Args:
        filepath: Path to save the JSON file
        index_queries: List of index creation queries
        node_queries: List of node creation queries
        edge_queries: List of edge creation queries
        table_name: Source table name from Databricks
        current_offset: Current offset in the download (for resuming)
        is_complete: Whether download is complete or in-progress
    """
    data = {
        'metadata': {
            'table_name': table_name,
            'timestamp': datetime.now().isoformat(),
            'total_queries': len(index_queries) + len(node_queries) + len(edge_queries),
            'index_count': len(index_queries),
            'node_count': len(node_queries),
            'edge_count': len(edge_queries),
            'current_offset': current_offset,
            'is_complete': is_complete
        },
        'queries': {
            'index_queries': index_queries,
            'node_queries': node_queries,
            'edge_queries': edge_queries
        }
    }
    
    with open(filepath, 'w') as f:
        json.dump(data, f, indent=2)
    
    status = "✓ Saved" if is_complete else "💾 Checkpoint saved"
    print(f"\n{status} {data['metadata']['total_queries']:,} queries to: {filepath}")
    print(f"  • {len(index_queries):,} indices")
    print(f"  • {len(node_queries):,} nodes")
    print(f"  • {len(edge_queries):,} edges")
    if not is_complete and current_offset:
        print(f"  • Offset: {current_offset:,}")


def load_queries_from_file(filepath):
    """
    Load categorized queries from a JSON file
    
    Args:
        filepath: Path to the JSON file
        
    Returns:
        Tuple of (index_queries, node_queries, edge_queries, metadata)
        
    Raises:
        FileNotFoundError: If file doesn't exist
        json.JSONDecodeError: If file contains invalid JSON
    """
    if not os.path.exists(filepath):
        raise FileNotFoundError(f"Data file not found: {filepath}")
    
    with open(filepath, 'r') as f:
        data = json.load(f)
    
    metadata = data.get('metadata', {})
    queries = data.get('queries', {})
    
    index_queries = queries.get('index_queries', [])
    node_queries = queries.get('node_queries', [])
    edge_queries = queries.get('edge_queries', [])
    
    print(f"\n✓ Loaded {metadata.get('total_queries', 0):,} queries from: {filepath}")
    print(f"  • Source table: {metadata.get('table_name', 'unknown')}")
    print(f"  • Downloaded: {metadata.get('timestamp', 'unknown')}")
    print(f"  • {len(index_queries):,} indices")
    print(f"  • {len(node_queries):,} nodes")
    print(f"  • {len(edge_queries):,} edges")
    
    return index_queries, node_queries, edge_queries, metadata


def main():
    parser = argparse.ArgumentParser(
        description="Load knowledge graph from Databricks gold table to local Memgraph",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Download and load (default behavior)
  python load_gold_to_local_memgraph.py

  # Download only - save to file for later
  python load_gold_to_local_memgraph.py --download

  # Load from previously downloaded file
  python load_gold_to_local_memgraph.py --load-from-local

  # Download and load in one step
  python load_gold_to_local_memgraph.py --download-and-load

  # Clear local Memgraph first, then load
  python load_gold_to_local_memgraph.py --load-from-local --clear

  # Custom data file path
  python load_gold_to_local_memgraph.py --download --data-file my_data.json

  # Load from custom gold table
  python load_gold_to_local_memgraph.py --download --table my_catalog.gold.custom_kg
        """
    )
    
    # Operational mode arguments (mutually exclusive)
    mode_group = parser.add_mutually_exclusive_group()
    mode_group.add_argument(
        "--download",
        action="store_true",
        help="Download queries from Databricks and save to local file (no loading)"
    )
    mode_group.add_argument(
        "--load-from-local",
        action="store_true",
        help="Load queries from local file to Memgraph (skip Databricks download)"
    )
    mode_group.add_argument(
        "--download-and-load",
        action="store_true",
        help="Download from Databricks and load to Memgraph (default behavior)"
    )
    
    # Other arguments
    parser.add_argument(
        "--table",
        default=f"`{DATABRICKS_CATALOG}`.gold.knowledge_graph",
        help="Full table name in Databricks (default: graysky-dev-catalog.gold.knowledge_graph)"
    )
    parser.add_argument(
        "--data-file",
        default=None,
        help="Path to local data file for saving/loading queries (default: databricks_kg_data_YYYYMMDD_HHMMSS.json)"
    )
    parser.add_argument(
        "--clear",
        action="store_true",
        help="Clear all existing data in local Memgraph before loading"
    )

    args = parser.parse_args()
    
    # Determine operational mode (default to download-and-load if none specified)
    if not (args.download or args.load_from_local or args.download_and_load):
        args.download_and_load = True
    
    # Generate timestamped filename if not specified
    if args.data_file is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        args.data_file = f"databricks_kg_data_{timestamp}.json"

    print("="*80)
    print("DATABRICKS GOLD TABLE ↔ LOCAL MEMGRAPH")
    print("="*80)
    
    # MODE 1: Download only
    if args.download:
        print(f"Mode: DOWNLOAD ONLY")
        print(f"Source: {args.table}")
        print(f"Destination: {args.data_file}")
        print("="*80)
        
        try:
            # Fetch queries from Databricks (with checkpoint support)
            index_queries, node_queries, edge_queries = fetch_cypher_queries_from_databricks(
                table_name=args.table,
                checkpoint_file=args.data_file,
                checkpoint_interval=5000
            )
            
            if not index_queries and not node_queries and not edge_queries:
                print("✗ No queries fetched from Databricks")
                return 1
            
            # Save final complete version
            save_queries_to_file(
                filepath=args.data_file,
                index_queries=index_queries,
                node_queries=node_queries,
                edge_queries=edge_queries,
                table_name=args.table,
                current_offset=None,
                is_complete=True
            )
            
            print("\n" + "="*80)
            print("✓ DOWNLOAD COMPLETE!")
            print("="*80)
            print(f"\nData saved to: {args.data_file}")
            print(f"To load this data into Memgraph, run:")
            print(f"  python {sys.argv[0]} --load-from-local --data-file {args.data_file}")
            
            return 0
            
        except Exception as e:
            print(f"\n✗ Error during download: {e}")
            import traceback
            traceback.print_exc()
            return 1
    
    # MODE 2: Load from local file
    elif args.load_from_local:
        print(f"Mode: LOAD FROM LOCAL FILE")
        print(f"Source: {args.data_file}")
        print(f"Destination: {LOCAL_MEMGRAPH_URI}")
        print("="*80)
        
        # Connect to Memgraph
        driver = connect_to_local_memgraph()
        if not driver:
            print("✗ Failed to connect to local Memgraph")
            return 1
        
        try:
            # Clear database if requested
            if args.clear:
                clear_memgraph_database(driver)
            
            # Use new UNWIND batching with resume capability
            load_with_resume(driver, args.data_file, checkpoint_interval=5000)
            
            # Verify data
            verify_memgraph_data(driver)
            
            print("\n" + "="*80)
            print("✓ KNOWLEDGE GRAPH SUCCESSFULLY LOADED TO LOCAL MEMGRAPH!")
            print("="*80)
            print(f"\nYou can now query your local Memgraph at: {LOCAL_MEMGRAPH_URI}")
            print("Try: podman exec -it memgraph-mcp mgconsole")
            
            return 0
            
        except Exception as e:
            print(f"\n✗ Error during load: {e}")
            import traceback
            traceback.print_exc()
            return 1
            
        finally:
            driver.close()
            print("\nConnection closed")
    
    # MODE 3: Download and load
    elif args.download_and_load:
        print(f"Mode: DOWNLOAD AND LOAD")
        print(f"Source: {args.table}")
        print(f"Data file: {args.data_file}")
        print(f"Destination: {LOCAL_MEMGRAPH_URI}")
        print("="*80)
        
        # Connect to Memgraph
        driver = connect_to_local_memgraph()
        if not driver:
            print("✗ Failed to connect to local Memgraph")
            return 1
        
        try:
            # Fetch queries from Databricks (with checkpoint support)
            index_queries, node_queries, edge_queries = fetch_cypher_queries_from_databricks(
                table_name=args.table,
                checkpoint_file=args.data_file,
                checkpoint_interval=5000
            )
            
            if not index_queries and not node_queries and not edge_queries:
                print("✗ No queries fetched from Databricks")
                return 1
            
            # Save final complete version
            save_queries_to_file(
                filepath=args.data_file,
                index_queries=index_queries,
                node_queries=node_queries,
                edge_queries=edge_queries,
                table_name=args.table,
                current_offset=None,
                is_complete=True
            )
            
            # Clear database if requested
            if args.clear:
                clear_memgraph_database(driver)
            
            # Execute queries
            execute_queries_categorized(driver, index_queries, node_queries, edge_queries)
            
            # Verify data
            verify_memgraph_data(driver)
            
            print("\n" + "="*80)
            print("✓ KNOWLEDGE GRAPH SUCCESSFULLY LOADED TO LOCAL MEMGRAPH!")
            print("="*80)
            print(f"\nData saved to: {args.data_file}")
            print(f"You can now query your local Memgraph at: {LOCAL_MEMGRAPH_URI}")
            print("Try: podman exec -it memgraph-mcp mgconsole")
            
            return 0
            
        except Exception as e:
            print(f"\n✗ Error during download and load: {e}")
            import traceback
            traceback.print_exc()
            return 1
            
        finally:
            driver.close()
            print("\nConnection closed")


if __name__ == "__main__":
    sys.exit(main())
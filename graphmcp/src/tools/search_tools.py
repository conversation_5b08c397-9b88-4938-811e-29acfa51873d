"""
Entity search tools for GraphRAG MCP Server
"""

import json
import logging
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)


def register(mcp, graph_client, schema_manager):
    """
    Register entity search tools with the MCP server.

    Args:
        mcp: FastMCP server instance
        graph_client: GraphClient instance
        schema_manager: SchemaManager instance
    """

    @mcp.tool()
    def search_entities(
        entity_type: str,
        properties: Optional[Dict[str, Any]] = None,
        contains_text: Optional[str] = None,
        limit: int = 20,
        skip: int = 0
    ) -> str:
        """
        Search for entities by type and properties.

        Args:
            entity_type: The node label to search (e.g., "Incident", "Resource")
            properties: Dict of property filters. Supports:
                       - Exact match: {"status": "active"}
                       - Range: {"severity": {"min": 3, "max": 5}}
                       - List: {"type": ["fire", "hurricane"]}
                       - Contains: {"description": {"contains": "evacuation"}}
            contains_text: Text to search across all string properties
            limit: Maximum results to return (max 100)
            skip: Number of results to skip for pagination

        Returns:
            JSON array of matching entities with all properties

        Example Call:
            search_entities(
                entity_type="Incident",
                properties={"severity": {"min": 4}, "status": "active"},
                limit=10
            )

        Example Response:
        {
            "success": true,
            "count": 3,
            "data": [
                {"id": "INC-001", "type": "fire", "severity": 5, "status": "active", ...},
                {"id": "INC-002", "type": "flood", "severity": 4, "status": "active", ...}
            ]
        }
        """
        try:
            logger.info(f"search_entities called: entity_type={entity_type}, properties={properties}, limit={limit}")

            # Get schema to identify ZONED_DATE_TIME properties
            datetime_properties = set()
            try:
                schema = schema_manager.cache
                if entity_type in schema.get('node_labels', {}):
                    for prop_name, prop_info in schema['node_labels'][entity_type]['properties'].items():
                        if prop_info.get('type') == 'ZONED_DATE_TIME':
                            datetime_properties.add(prop_name)
                    logger.debug(f"Detected ZONED_DATE_TIME properties for {entity_type}: {datetime_properties}")
            except Exception as e:
                logger.warning(f"Could not fetch schema for datetime detection: {e}")

            # Build the base query
            cypher = f"MATCH (n:{entity_type})"
            where_conditions = []
            params = {}

            # Add property filters
            if properties:
                for prop_name, prop_value in properties.items():
                    is_datetime_prop = prop_name in datetime_properties

                    if isinstance(prop_value, dict):
                        # Handle range queries
                        if "min" in prop_value:
                            if is_datetime_prop:
                                where_conditions.append(f"n.{prop_name} >= datetime($min_{prop_name})")
                            else:
                                where_conditions.append(f"n.{prop_name} >= $min_{prop_name}")
                            params[f"min_{prop_name}"] = prop_value["min"]
                        if "max" in prop_value:
                            if is_datetime_prop:
                                where_conditions.append(f"n.{prop_name} <= datetime($max_{prop_name})")
                            else:
                                where_conditions.append(f"n.{prop_name} <= $max_{prop_name}")
                            params[f"max_{prop_name}"] = prop_value["max"]
                        if "contains" in prop_value:
                            where_conditions.append(f"n.{prop_name} CONTAINS $contains_{prop_name}")
                            params[f"contains_{prop_name}"] = prop_value["contains"]
                    elif isinstance(prop_value, list):
                        # Handle list queries (IN)
                        where_conditions.append(f"n.{prop_name} IN $list_{prop_name}")
                        params[f"list_{prop_name}"] = prop_value
                    else:
                        # Exact match
                        if is_datetime_prop:
                            where_conditions.append(f"n.{prop_name} = datetime(${prop_name})")
                        else:
                            where_conditions.append(f"n.{prop_name} = ${prop_name}")
                        params[prop_name] = prop_value

            # Add text search across all properties
            if contains_text:
                # This is a simplified text search - in production you might want to use Memgraph's text search
                text_conditions = []
                for prop in ["description", "name", "type", "notes"]:  # Common text properties
                    text_conditions.append(f"n.{prop} CONTAINS $search_text")
                if text_conditions:
                    where_conditions.append(f"({' OR '.join(text_conditions)})")
                    params["search_text"] = contains_text

            # Add WHERE clause if there are conditions
            if where_conditions:
                cypher += f" WHERE {' AND '.join(where_conditions)}"

            # Add RETURN clause
            cypher += " RETURN n"

            # Validate and enforce limits
            limit = min(limit, 100)
            if limit < 1:
                limit = 20

            # Execute query
            results = graph_client.execute_query(cypher, params, limit=limit, skip=skip)

            return json.dumps({
                "success": True,
                "count": len(results),
                "data": results,
                "query_params": {
                    "entity_type": entity_type,
                    "limit": limit,
                    "skip": skip
                }
            }, indent=2, default=str)

        except Exception as e:
            logger.error(f"Error in search_entities: {str(e)}", exc_info=True)
            return json.dumps({"error": str(e), "success": False})

    @mcp.tool()
    def get_entity(
        entity_type: str,
        entity_id: str,
        include_relationships: bool = False
    ) -> str:
        """
        Get a specific entity by type and ID.

        Args:
            entity_type: The node label
            entity_id: The unique identifier
            include_relationships: If true, include all connected entities

        Returns:
            Complete entity data with optional relationships

        Example Call:
            get_entity("Incident", "INC-2024-1543", include_relationships=True)

        Example Response:
        {
            "success": true,
            "entity": {
                "id": "INC-2024-1543",
                "type": "hurricane",
                "severity": 5,
                "status": "resolved"
            },
            "relationships": [
                {
                    "relationship": "RESPONDED_TO",
                    "direction": "incoming",
                    "connected_entity": {"id": "RES-001", "type": "Resource"},
                    "properties": {"response_time": 15}
                }
            ]
        }
        """
        try:
            logger.info(f"get_entity called: entity_type={entity_type}, entity_id={entity_id}, include_relationships={include_relationships}")

            if include_relationships:
                # Query with relationships
                cypher = f"""
                MATCH (n:{entity_type} {{id: $entity_id}})
                OPTIONAL MATCH (n)-[r]-(connected)
                RETURN n as entity,
                       collect({{
                           relationship: type(r),
                           direction: CASE
                               WHEN startNode(r) = n THEN 'outgoing'
                               ELSE 'incoming'
                           END,
                           connected_entity: connected,
                           properties: properties(r)
                       }}) as relationships
                """
            else:
                # Simple entity query
                cypher = f"""
                MATCH (n:{entity_type} {{id: $entity_id}})
                RETURN n as entity
                """

            results = graph_client.execute_query(cypher, {"entity_id": entity_id})

            if not results:
                return json.dumps({
                    "success": False,
                    "error": f"Entity not found: {entity_type} with id {entity_id}"
                })

            result = results[0]
            response = {
                "success": True,
                "entity": result["entity"]
            }

            if include_relationships:
                # Filter out null relationships
                relationships = [r for r in result.get("relationships", []) if r.get("relationship")]
                response["relationships"] = relationships
                response["relationship_count"] = len(relationships)

            return json.dumps(response, indent=2, default=str)

        except Exception as e:
            logger.error(f"Error in get_entity: {str(e)}", exc_info=True)
            return json.dumps({"error": str(e), "success": False})

    @mcp.tool()
    def find_entities_by_time_range(
        entity_type: str,
        start_time: str,
        end_time: str,
        time_property: str = "timestamp"
    ) -> str:
        """
        Find entities within a time range.

        Args:
            entity_type: The node label to search
            start_time: ISO format start time (e.g., "2024-10-01T00:00:00")
            end_time: ISO format end time
            time_property: The property containing the timestamp

        Returns:
            Entities within the time range, ordered by time

        Example Call:
            find_entities_by_time_range(
                "Incident",
                "2024-10-01T00:00:00",
                "2024-10-31T23:59:59"
            )

        Example Response:
        {
            "success": true,
            "count": 15,
            "time_range": {
                "start": "2024-10-01T00:00:00",
                "end": "2024-10-31T23:59:59"
            },
            "data": [
                {"id": "INC-001", "timestamp": "2024-10-01T08:30:00", ...},
                {"id": "INC-002", "timestamp": "2024-10-02T14:15:00", ...}
            ]
        }
        """
        try:
            logger.info(f"find_entities_by_time_range called: entity_type={entity_type}, start={start_time}, end={end_time}")

            # Build temporal query
            cypher = f"""
            MATCH (n:{entity_type})
            WHERE n.{time_property} >= datetime($start_time)
              AND n.{time_property} <= datetime($end_time)
            RETURN n
            ORDER BY n.{time_property}
            """

            params = {
                "start_time": start_time,
                "end_time": end_time
            }

            results = graph_client.execute_query(cypher, params, limit=100)

            return json.dumps({
                "success": True,
                "count": len(results),
                "time_range": {
                    "start": start_time,
                    "end": end_time,
                    "property": time_property
                },
                "data": results
            }, indent=2, default=str)

        except Exception as e:
            logger.error(f"Error in find_entities_by_time_range: {str(e)}", exc_info=True)
            return json.dumps({"error": str(e), "success": False})

    @mcp.tool()
    def search_by_pattern(
        pattern: str,
        parameters: Optional[Dict[str, Any]] = None,
        limit: int = 50
    ) -> str:
        """
        Search using a Cypher pattern with parameters.

        Args:
            pattern: Simplified Cypher pattern (e.g., "(:Incident)-[:RESPONDED_TO]->(:Department)")
            parameters: Values for pattern variables
            limit: Maximum results to return

        Returns:
            Matching patterns from the graph

        Example Call:
            search_by_pattern(
                "(:Incident {severity: $severity})<-[:RESPONDED_TO]-(:Resource)",
                {"severity": 5}
            )

        Example Response:
        {
            "success": true,
            "count": 8,
            "pattern": "(:Incident {severity: $severity})<-[:RESPONDED_TO]-(:Resource)",
            "data": [...]
        }
        """
        try:
            logger.info(f"search_by_pattern called: pattern={pattern}")

            # Build query from pattern
            cypher = f"MATCH p = {pattern} RETURN p"

            # Validate it's a safe read-only query
            is_safe, reason = graph_client.is_safe_query(cypher)
            if not is_safe:
                return json.dumps({
                    "success": False,
                    "error": f"Pattern rejected for safety: {reason}"
                })

            # Execute query
            results = graph_client.execute_query(
                cypher,
                parameters or {},
                limit=min(limit, 100)
            )

            return json.dumps({
                "success": True,
                "count": len(results),
                "pattern": pattern,
                "data": results
            }, indent=2, default=str)

        except Exception as e:
            logger.error(f"Error in search_by_pattern: {str(e)}", exc_info=True)
            return json.dumps({"error": str(e), "success": False})

    logger.info("Search tools registered successfully")
# Embedding Leakage Analysis

## Question 1: Does get_schema use describe_entities?

**Answer: NO - Completely Separate**

**get_schema** (schema_tools.py:67):
```python
schema = schema_manager.get_cached_schema()  # Returns cache directly
```

**describe_entity_type** (schema_manager.py:265-308):
```python
def describe_entity(self, entity_type: str):
    # Separate method that adds samples
```

They are independent - get_schema does NOT call describe_entity.

---

## Question 2: Do tools return samples with embeddings?

**Answer: YES - Two tools have embedding leakage**

### ❌ describe_entity_type (schema_manager.py:296-306)
```python
sample_query = f"""
MATCH (n:{entity_type})
RETURN n                    # ← Returns FULL node with embeddings!
LIMIT 3
"""
samples = self.graph_client.execute_query(sample_query)
entity_info["samples"] = samples  # ← Includes 1536-dim arrays!
```

**Impact:** Returns 3 full nodes with all properties including embeddings

### ❌ describe_relationship_type (schema_manager.py:330-339)
```python
sample_query = f"""
MATCH (a)-[r:{relationship_type}]->(b)
RETURN a, r, b              # ← Returns FULL nodes a and b with embeddings!
LIMIT 3
"""
samples = self.graph_client.execute_query(sample_query)
rel_info["samples"] = samples  # ← Includes embedding arrays in nodes!
```

**Impact:** Returns 3 relationship samples with full source/target nodes (embeddings included)

### ✅ analyze_knowledge_graph (analysis_tools.py)
```python
# Lines 71, 86, 112, 154, 169, 182, 195 - ALL filter embeddings:
WHERE NOT key =~ '.*embedding.*'
```

**Impact:** Safe - explicitly filters out embedding properties

---

## Question 3: Where else can embeddings leak?

**Systematic Search Required**

### Places to Check:

1. ✅ **get_schema** - Safe (no samples)
2. ❌ **describe_entity_type** - LEAKS embeddings in samples
3. ❌ **describe_relationship_type** - LEAKS embeddings in samples
4. ✅ **analyze_knowledge_graph** - Safe (filtered)
5. ❓ **Other tools that return nodes?**

Let me search for all tools that return full nodes...


# Multi-Agent Architecture with Supervisor Pattern

**Date**: 2025-10-06
**Status**: In Progress
**Priority**: High

---

## Problem Statement

Our current architecture has a single workflow that uses all 34 tools from 2 MCP servers (Genie + GraphRAG). For complex queries requiring deep data gathering, we need:

1. **Specialized subagents** that can iterate multiple times with focused tool sets
2. **Intelligent routing** that decides which subagent to use based on the query
3. **Configurable iteration limits** that operators can tune per environment

**Current State:**
- Single workflow with all 34 tools
- No iteration capability for deep research
- No specialization by data source

**Desired State:**
- Supervisor node that routes queries intelligently
- Genie subagent (4 tools, up to 10 iterations)
- GraphRAG subagent (18 tools, up to 10 iterations)
- LLM-powered routing decisions

---

## Architecture Decision: Supervisor Pattern

### Why Supervisor Over Supervisor-as-Tools?

After researching LangGraph's multi-agent patterns, we chose the **Supervisor** pattern over **Supervisor-as-Tools**:

**Supervisor Pattern:**
- ✅ Explicit control flow via `Command` objects
- ✅ Works with subgraphs (iteration loops)
- ✅ Clear iteration counting and visibility
- ✅ Better for complex workflows with state

**Supervisor-as-Tools:**
- ❌ Treats agents as callable functions
- ❌ Less control over iteration/looping
- ❌ Harder to track subagent state
- ✅ Simpler for basic agent coordination

**Our use case requires:**
- Subagents that loop/iterate (not just single calls)
- Explicit tracking of iteration counts
- Clear visibility into "iteration 3/10" progress
- Tools already grouped by MCP server

**Verdict:** Supervisor pattern is the right choice.

---

## LangGraph Command Pattern Explained

### How Command Routing Works

The `Command` object is LangGraph's native way to handle dynamic routing:

```python
async def supervisor(state: AgentState, config) -> Command[Literal["genie_subagent", "graphrag_subagent", END]]:
    # LLM makes routing decision
    decision = await model.ainvoke(...)

    # Return Command object TO LANGGRAPH
    return Command(goto=decision.destination)
```

**Key Points:**
1. **Command is returned TO LangGraph** (not to another node)
2. **LangGraph reads `Command.goto`** and routes accordingly
3. **No conditional_edges needed** - supervisor handles routing
4. **Type hints show valid destinations** for clarity

**Flow:**
```
supervisor node → returns Command(goto="genie_subagent")
                → LangGraph engine routes to genie_subagent
                → genie_subagent executes
                → returns to END
```

This is the recommended pattern from LangGraph docs for multi-agent systems.

---

## Visual Architecture

### Flow Diagram

```mermaid
graph TB
    START([START]) --> SUPERVISOR[Supervisor Node<br/>LLM-powered routing<br/>synthesis model @ temp 0.7]

    SUPERVISOR -->|Emergency<br/>incident data| GENIE[Genie Subagent<br/>4 tools<br/>max 2 iterations]
    SUPERVISOR -->|Graph database<br/>queries| GRAPHRAG[GraphRAG Subagent<br/>18 tools<br/>max 10 iterations]
    SUPERVISOR -->|Simple query<br/>no tools needed| END_DIRECT([END<br/>Direct answer])

    GENIE --> GENIE_AGENT[Genie Agent Node<br/>LLM + 4 Genie tools]
    GENIE_AGENT --> GENIE_ROUTER{Continue?}
    GENIE_ROUTER -->|Yes, iteration < 2| GENIE_TOOLS[Execute Genie Tools]
    GENIE_ROUTER -->|No, done| END_GENIE([END])
    GENIE_TOOLS --> GENIE_AGENT

    GRAPHRAG --> GRAPHRAG_AGENT[GraphRAG Agent Node<br/>LLM + 18 graph tools]
    GRAPHRAG_AGENT --> GRAPHRAG_ROUTER{Continue?}
    GRAPHRAG_ROUTER -->|Yes, iteration < 10| GRAPHRAG_TOOLS[Execute GraphRAG Tools]
    GRAPHRAG_ROUTER -->|No, done| END_GRAPHRAG([END])
    GRAPHRAG_TOOLS --> GRAPHRAG_AGENT

    style SUPERVISOR fill:#1565c0,color:#fff,stroke:#0d47a1,stroke-width:4px
    style GENIE fill:#00c853,color:#fff,stroke:#00a152,stroke-width:3px
    style GRAPHRAG fill:#f57c00,color:#fff,stroke:#e65100,stroke-width:3px
    style GENIE_AGENT fill:#64b5f6,color:#000
    style GENIE_TOOLS fill:#64b5f6,color:#000
    style GRAPHRAG_AGENT fill:#ff9800,color:#000
    style GRAPHRAG_TOOLS fill:#ff9800,color:#000
```

**ASCII Version:**

```
                    ┌──────────────┐
                    │    START     │
                    └──────┬───────┘
                           │
                    ┌──────▼───────┐
                    │  supervisor  │  ← LLM analyzes query
                    │(synthesis @  │     and decides route
                    │  temp 0.7)   │
                    └──────┬───────┘
                           │
        ┌──────────────────┼──────────────────┐
        │                  │                  │
  ┌─────▼──────┐    ┌──────▼──────┐   ┌──────▼──────┐
  │   genie    │    │     END     │   │  graphrag   │
  │ subagent   │    │   (direct   │   │  subagent   │
  │            │    │   answer)   │   │             │
  │ 4 tools    │    └─────────────┘   │ 18 tools    │
  │ up to 2x   │                      │ up to 10x   │
  └─────┬──────┘                      └──────┬──────┘
        │                                    │
        └────────────────┬───────────────────┘
                         │
                    ┌────▼─────┐
                    │   END    │
                    └──────────┘
```

### Component Breakdown

**Supervisor Node:**
- Uses synthesis model (gpt-4.1-mini) - the "main brain"
- Temperature 0.7 (nuanced routing decisions)
- Structured output (RoutingDecision)
- Returns Command object to LangGraph engine
- Emergency management domain-aware routing

**Genie Subagent:**
- Tools: get_genie_spaces, ask_genie, follow_up, get_genie_space_metadata
- Max iterations: configurable (default 2 in production)
- Fast model for data gathering (temp 0.0)
- Loops until satisfied or max reached
- Returns consolidated result to END

**GraphRAG Subagent:**
- Tools: 18 graph database query tools (search_*, describe_*, get_*, etc.)
- Max iterations: configurable (default 10 for deeper research)
- Fast model for data gathering (temp 0.0)
- Loops until satisfied or max reached
- Returns consolidated result to END

---

## Configuration

### Environment Variables (`.env`)

```bash
# Subagent iteration limits
GENIE_SUBAGENT_MAX_ITERATIONS=2   # Fast iteration for emergency incident data
GRAPHRAG_SUBAGENT_MAX_ITERATIONS=10  # Deeper research for graph queries

# Model configuration
SYNTHESIS_MODEL_DEPLOYMENT=gpt-4.1-mini  # Supervisor routing model
SYNTHESIS_MODEL_TEMPERATURE=0.7  # Higher temp for nuanced routing decisions
FAST_MODEL_DEPLOYMENT=gpt-4.1-mini  # Subagent data gathering model
FAST_MODEL_TEMPERATURE=0.0  # Deterministic for data gathering
```

**Why `.env` level?**
- Operators can tune without code changes
- Different values per environment (dev: 10, staging: 5, prod: 2/10)
- Follows existing pattern (FAST_MODEL_TEMPERATURE, etc.)
- Easy to adjust based on cost/latency requirements

### Settings Class (`config.py`)

```python
class Settings(BaseSettings):
    # Subagent Configuration
    genie_subagent_max_iterations: int = 2  # Fast iteration for incident data
    graphrag_subagent_max_iterations: int = 10  # Deeper research for graph queries
```

### Runtime Context (`context.py`)

Updated to use config values instead of hardcoded:
```python
@dataclass
class RuntimeContext:
    # Use settings values
    genie_max_iterations: int  # from settings
    graphrag_max_iterations: int  # from settings
```

---

## Implementation Details

### Routing Schema

```python
from pydantic import BaseModel, Field
from typing import Literal

class RoutingDecision(BaseModel):
    """LLM decides where to route the query."""
    destination: Literal["genie_subagent", "graphrag_subagent", END]
    reasoning: str = Field(description="Why this route was chosen")
```

### Supervisor Node

```python
async def supervisor(state: AgentState, config) -> Command[Literal["genie_subagent", "graphrag_subagent", END]]:
    """
    LLM-powered router that analyzes query and decides which subagent to use.

    Uses synthesis model (the "main brain") with structured output for nuanced routing.
    Returns Command object that LangGraph uses for routing.
    """
    context = config["configurable"]["context"]

    # Synthesis model with structured output - higher temp for nuanced decisions
    model = AzureChatOpenAI(
        azure_deployment=context.synthesis_model,
        temperature=context.synthesis_model_temperature  # 0.7 for routing
    ).with_structured_output(RoutingDecision)

    system_prompt = """You are an emergency management assistant supervisor.
    Analyze the query and decide which subagent to use:

    - 'genie_subagent': Has 4 tools for querying emergency incident data (Genie)
    - 'graphrag_subagent': Has 18 tools for graph database queries (relationships, entities)
    - '__end__': Simple question that doesn't need external data

    Consider:
    - Emergency management context (incidents, resources, operations)
    - Query complexity and data source requirements
    - Which subagent's specialized tools best match the need"""

    decision = await model.ainvoke([
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": state["messages"][-1].content}
    ])

    logger.info(f"🧭 Supervisor routing to: {decision.destination}",
                extra={"reasoning": decision.reasoning})

    # Return Command TO LANGGRAPH (not to another node)
    return Command(goto=decision.destination)
```

### Subagent Structure

Both subagents follow the same pattern:

```python
def create_genie_subgraph():
    """Genie subagent - iterates with Genie tools."""
    workflow = StateGraph(IterationState, context_schema=RuntimeContext)

    workflow.add_node("genie_agent", genie_agent_node)
    workflow.add_node("genie_tools", genie_tools_node)

    workflow.set_entry_point("genie_agent")
    workflow.add_conditional_edges(
        "genie_agent",
        should_iterate_genie,  # Checks max from config
        {"tools": "genie_tools", "end": END}
    )
    workflow.add_edge("genie_tools", "genie_agent")

    return workflow.compile()
```

### Graph Wiring

```python
def create_graph():
    """Main graph with supervisor and dual subagents."""
    workflow = StateGraph(AgentState, context_schema=RuntimeContext)

    # Add supervisor (entry point)
    workflow.add_node("supervisor", supervisor)

    # Add specialized subagents
    genie_subgraph = create_genie_subgraph()
    workflow.add_node("genie_subagent", genie_subgraph)

    graphrag_subgraph = create_graphrag_subgraph()
    workflow.add_node("graphrag_subagent", graphrag_subgraph)

    # Wire: START → supervisor → (subagents) → END
    workflow.add_edge(START, "supervisor")
    workflow.add_edge("genie_subagent", END)
    workflow.add_edge("graphrag_subagent", END)

    return workflow.compile()
```

---

## Testing Strategy

### What We Test

✅ **Routing Works:**
- Supervisor makes a routing decision
- Workflow successfully routes to a subagent or END
- Subagent completes and returns response

✅ **Configuration:**
- Max iterations read from config
- Different values for genie vs graphrag

✅ **Subagent Execution:**
- Subagent can iterate multiple times
- Iteration count enforced
- Tools executed correctly

❌ **What We DON'T Test Yet:**
- Specific routing logic (incident → genie, entity → graphrag)
- Optimal prompt tuning
- Routing accuracy metrics

**Rationale:** The routing logic needs real-world testing and refinement. Initial implementation focuses on infrastructure working correctly.

### Test Scenarios

```python
# Test 1: Verify routing works
async def test_supervisor_routing():
    """Test that supervisor makes a routing decision."""
    # Query that should route somewhere
    result = await graph.ainvoke({"messages": [HumanMessage("Test query")]})
    # Verify: Got a response, no errors

# Test 2: Verify max iterations from config
async def test_max_iterations_config():
    """Test that subagents use max iterations from settings."""
    # Verify genie uses settings.genie_subagent_max_iterations
    # Verify graphrag uses settings.graphrag_subagent_max_iterations

# Test 3: Verify subagent completion
async def test_subagent_completes():
    """Test that subagent executes and returns result."""
    # Verify subagent runs to completion
    # Verify result reaches END
```

---

## Usage Examples

### Example 1: Query Routes to Genie Subagent

```
User: "What are the current emergency incidents?"

Flow:
1. Supervisor analyzes query
2. Decides: genie_subagent (has incident tools)
3. Genie subagent:
   - Iteration 1: Calls get_genie_spaces
   - Iteration 2: Calls ask_genie with specific space
   - Iteration 3: Calls follow_up for details
   - Returns consolidated response
4. Result returned to user
```

### Example 2: Query Routes to GraphRAG Subagent

```
User: "Show me relationships between entities"

Flow:
1. Supervisor analyzes query
2. Decides: graphrag_subagent (needs graph tools)
3. GraphRAG subagent:
   - Iteration 1: Calls search_entities
   - Iteration 2: Calls describe_relationship_type
   - Iteration 3: Calls query_graph for details
   - Returns consolidated response
4. Result returned to user
```

### Example 3: Simple Query (Direct Answer)

```
User: "What is 2+2?"

Flow:
1. Supervisor analyzes query
2. Decides: END (no tools needed)
3. Direct answer returned
4. No subagent invoked
```

---

## Known Limitations & Future Work

### Current Limitations

1. **Routing Logic is Basic**
   - Current prompt is generic
   - No specific query patterns defined
   - Needs real-world testing and tuning

2. **No Routing Metrics**
   - Can't measure routing accuracy
   - No feedback loop for improvement
   - No A/B testing infrastructure

3. **Fixed Subagent Assignment**
   - Tools statically assigned by server
   - Can't dynamically adjust tool sets
   - No hybrid routing (multiple subagents)

### Future Enhancements

**Phase 2: Routing Refinement**
- Collect routing decision data
- Analyze which queries went where
- Tune prompts based on patterns
- Add query classification examples

**Phase 3: Observability**
- Track routing accuracy
- Measure subagent performance
- Monitor iteration counts
- Add routing decision metrics

**Phase 4: Advanced Routing**
- Multi-subagent queries (both Genie + GraphRAG)
- Conditional subagent chaining
- Dynamic tool set adjustment
- Query complexity scoring

**Phase 5: Optimization**
- Reduce unnecessary iterations
- Cache subagent results
- Parallel subagent execution
- Cost optimization

---

## Implementation Status

### ✅ Completed
- Architecture design
- Documentation created
- Configuration added to .env and config.py
- Supervisor node created with LLM-based routing
- Genie subagent created (4 tools, max 10 iterations from config)
- GraphRAG subagent created (18 tools, max 10 iterations from config)
- Graph wired with supervisor pattern
- Tests created and passing
- Integration tests passing

### 🚧 In Progress
- N/A

### ⏳ Not Started
- Routing prompt refinement (based on real-world usage)
- Production testing
- Performance metrics
- Routing accuracy analysis

## Implementation Results

**Date Completed**: 2025-10-06

### What Was Built

1. **Configuration System**
   - Added `GENIE_SUBAGENT_MAX_ITERATIONS` to `.env` (default: 10)
   - Added `GRAPHRAG_SUBAGENT_MAX_ITERATIONS` to `.env` (default: 10)
   - Added settings fields to `config.py`
   - Updated `RuntimeContext` to use config values

2. **Supervisor Node**
   - Uses fast model (gpt-4.1-mini) with temperature 0.0
   - Structured output via Pydantic `RoutingDecision` model
   - Returns `Command` object for LangGraph-native routing
   - Logs routing decisions with reasoning

3. **Dual Subagents**
   - **Genie Subagent**: 4 tools, iterates up to config max
   - **GraphRAG Subagent**: 18 tools, iterates up to config max
   - Both use fast model for cost-effective iteration
   - Iteration counts enforced from config

4. **Graph Architecture**
   - START → supervisor → (genie_subagent | graphrag_subagent | END)
   - Subagents → END
   - Clean, simple flow

5. **Testing**
   - Created `test_multi_agent.py`
   - Verifies routing works (not specific logic)
   - All integration tests pass
   - Service layer tests pass

### Test Results

```
============================================================
MULTI-AGENT: Testing supervisor pattern...
============================================================

✓ Graph loaded
✓ Context created with 34 tools
✓ Genie max iterations: 10
✓ GraphRAG max iterations: 10

✓ All nodes found: supervisor, genie_subagent, graphrag_subagent

✓ Supervisor routing works
✓ Workflow completed
✓ Config values used correctly

============================================================
✓ All multi-agent tests passed!
============================================================
```

### Files Modified

1. `.env.example` - Added 2 subagent config values
2. `config.py` - Added 2 settings fields
3. `context.py` - Updated to use config values
4. `workflows.py` - Major refactor:
   - Added supervisor node (65 lines)
   - Renamed iteration → genie subagent
   - Created graphrag subagent
   - Updated graph wiring
5. `langgraph_service.py` - Pass config to context
6. `scripts/test_multi_agent.py` - New test file

**Total Lines Changed**: ~200 lines

### Example Routing Decision

```
Query: "Test query for routing"

Supervisor Decision:
- Destination: __end__
- Reasoning: "The query is a simple test query for routing and does not
  require any external data or complex processing. Therefore, it can be
  handled directly without invoking any subagent."

Result: Workflow completes directly without subagent invocation
```

### Known Behavior

1. **Routing is working but generic**
   - Supervisor makes decisions
   - May not always choose optimal subagent
   - Needs real-world testing and tuning

2. **Config values are respected**
   - Max iterations read from .env
   - Different values for genie vs graphrag
   - Can be tuned per environment

3. **All tests pass**
   - Multi-agent test passes
   - Service integration tests pass
   - No regressions detected

---

## References

- [LangGraph Multi-Agent Concepts](https://langchain-ai.github.io/langgraph/concepts/multi_agent/)
- [LangGraph MCP Integration](https://langchain-ai.github.io/langgraph/agents/mcp/)
- [LangGraph Command Pattern](https://langchain-ai.github.io/langgraph/concepts/low_level/)
- Parent Migration Guide: `langgraph_native_migration.md`

---

## Key Learnings

### Technical Insights

1. **Command Pattern is LangGraph-Native**
   - `Command(goto=destination)` is returned TO the LangGraph engine (not to another node)
   - LangGraph reads `Command.goto` and handles routing automatically
   - No `conditional_edges` needed when using Command pattern
   - Type hints (`Command[Literal[...]]`) provide IDE autocomplete and type safety

2. **Supervisor Uses Synthesis Model (Not Fast Model)**
   - Supervisor is the "main brain" that makes routing decisions
   - Higher temperature (0.7) allows nuanced, context-aware routing
   - Fast model (temp 0.0) is for subagent data gathering (deterministic)
   - Synthesis model chosen by user for better reasoning quality

3. **Iteration Limits are Environment-Configurable**
   - `.env` level configuration allows operator tuning without code changes
   - Different values per environment: dev (10), staging (5), prod (2/10)
   - Genie (2 iterations): Fast queries for emergency incident data
   - GraphRAG (10 iterations): Deeper research for complex graph queries
   - Follows existing pattern (FAST_MODEL_TEMPERATURE, etc.)

4. **Tool Grouping by MCP Server**
   - Natural separation: Genie (emergency data) vs GraphRAG (graph queries)
   - Tools auto-discovered via official `langchain-mcp-adapters` library
   - 34 total tools: 4 Genie + 18 GraphRAG + 12 unknown
   - Prefix-based grouping: `genie_` or `graphrag_` prefixes

5. **Routing Logic Evolves Over Time**
   - Initial implementation verifies infrastructure works (not specific routing logic)
   - Test only that routing happens, not which specific route is chosen
   - Routing prompts will be refined based on real-world usage patterns
   - Emphasized emergency management domain context in supervisor prompt

### Architecture Decisions

**Why Supervisor Pattern Over Supervisor-as-Tools?**
- ✅ Explicit control flow via `Command` objects
- ✅ Works with subgraphs (iteration loops)
- ✅ Clear iteration counting and visibility
- ✅ Better for complex workflows with state
- ❌ Supervisor-as-Tools treats agents as callable functions (no loops)

**Why Synthesis Model for Supervisor?**
- Better reasoning quality for routing decisions
- Higher temperature (0.7) for nuanced, context-aware routing
- "Main brain" that orchestrates the system
- Fast model (temp 0.0) reserved for deterministic data gathering

**Why Different Iteration Limits?**
- Genie (2): Emergency incident data requires fast, focused queries
- GraphRAG (10): Graph queries may need deeper exploration of relationships
- Cost optimization: Fewer iterations for well-structured data sources
- Latency optimization: Faster response times for incident queries

### Implementation Insights

1. **Import Location Matters**
   - `Command` must be imported from `langgraph.types` (not `langgraph.graph`)
   - Caused initial import error, fixed by correcting import statement

2. **Test Strategy**
   - Verify routing infrastructure works (graph structure, config values)
   - Don't test specific routing decisions (those need real-world tuning)
   - Tests pass if workflow completes without errors

3. **Lazy Initialization Pattern**
   - Tools discovered on first request, then cached
   - RuntimeContext created per request with config values
   - Graph compiled once at service startup

4. **Emergency Management Context**
   - Supervisor prompt emphasizes EM domain (incidents, resources, operations)
   - User modified prompt to reflect actual use case
   - Routing considers emergency management terminology

### Future Considerations

1. **Routing Refinement**
   - Collect routing decisions over time
   - Analyze which queries went to which subagent
   - Tune prompts based on patterns
   - Add query classification examples

2. **Observability**
   - Track routing accuracy metrics
   - Measure subagent performance (iterations, latency, cost)
   - Monitor which tools are used most frequently
   - Add routing decision logging

3. **Advanced Routing**
   - Multi-subagent queries (both Genie + GraphRAG)
   - Conditional subagent chaining
   - Dynamic tool set adjustment
   - Query complexity scoring

---

**Last Updated**: 2025-10-06
**Next Review**: After implementation and initial testing

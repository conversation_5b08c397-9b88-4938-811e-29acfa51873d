# Arize Phoenix Deployment Guide

This directory contains Docker Compose setup for **Arize Phoenix observability** with PostgreSQL persistence. Phoenix provides comprehensive LLM tracing, monitoring, and evaluation capabilities for the GraySkyGenAI Assistant.

## Table of Contents

- [Quick Start](#quick-start)
- [Docker Compose Setup](#docker-compose-setup)
- [Production Deployment Options](#production-deployment-options)
- [Configuration Reference](#configuration-reference)
- [Troubleshooting](#troubleshooting)
- [Best Practices](#best-practices)
- [Monitoring Dashboards](#monitoring-dashboards)

## Quick Start

### Prerequisites
- Docker and Docker Compose installed
- GraySkyGenAI Assistant backend configured ([see backend README](../backend/README.md))

### 1. Start Phoenix Services
```bash
# From the phoenix/ directory
cd phoenix
docker-compose up -d

# Verify services are running
docker-compose ps
```

### 2. Configure Your Application
```bash
# In your backend/.env file
PHOENIX_ENABLED=true
PHOENIX_ENDPOINT=http://127.0.0.1:6006
PHOENIX_SERVICE_NAME=graysky-assistant
PHOENIX_SERVICE_VERSION=0.1.0
PHOENIX_ENVIRONMENT=development
```

### 3. Start Your Backend
```bash
cd ../backend
source venv/bin/activate
uvicorn app.main:app --reload
```

### 4. Access Phoenix UI
Open [http://localhost:6006](http://localhost:6006) to view traces and analytics.

### 5. Generate Test Traces
```bash
curl -N http://localhost:8000/api/v1/chat/stream \
  -H "Content-Type: application/json" \
  -d '{"message": "Test Phoenix tracing", "conversation_id": "test-session"}'
```

## Docker Compose Setup

### Services Included

**Phoenix Server** (`phoenix`)
- **Image**: `arizephoenix/phoenix:latest` (>= 4.0 for PostgreSQL)
- **Ports**: 6006 (UI), 4317 (OTLP), 9090 (Prometheus)
- **Database**: PostgreSQL connection via `PHOENIX_SQL_DATABASE_URL`
- **Health Check**: Automatic health monitoring with retry logic

**PostgreSQL Database** (`postgres`)
- **Image**: `postgres:15` (officially supported)
- **Database**: `phoenix` with user `phoenix`
- **Persistence**: Docker volume `postgres_data`
- **Port**: 5432 (exposed for debugging)

### Data Persistence

**✅ Persistent Storage:**
- PostgreSQL data stored in Docker volume `postgres_data`
- Data survives container restarts and system reboots
- Located at: `/var/lib/docker/volumes/phoenix_postgres_data/_data`

**✅ Backup Data:**
```bash
# Create backup
docker run --rm -v phoenix_postgres_data:/data -v $(pwd):/backup alpine tar czf /backup/phoenix_backup.tar.gz -C /data .

# Restore backup
docker run --rm -v phoenix_postgres_data:/data -v $(pwd):/backup alpine tar xzf /backup/phoenix_backup.tar.gz -C /data
```

### Docker Compose Commands

```bash
# Start services in background
docker-compose up -d

# View logs
docker-compose logs -f phoenix
docker-compose logs -f postgres

# Stop services (data persists)
docker-compose down

# Stop and remove volumes (DELETE ALL DATA!)
docker-compose down -v

# Rebuild services
docker-compose up --build -d

# Check service health
docker-compose ps
```

## Production Deployment Options

### Option 1: Azure Container Instance (Recommended for Small Scale)

**Deploy PostgreSQL + Phoenix:**
```bash
# 1. Create PostgreSQL database
az postgres flexible-server create \
  --name phoenix-db-prod \
  --resource-group your-rg \
  --admin-user phoenix \
  --admin-password your_secure_password \
  --sku-name Standard_B1ms \
  --tier Burstable \
  --storage-size 32

# 2. Configure firewall (allow Azure services)
az postgres flexible-server firewall-rule create \
  --resource-group your-rg \
  --name phoenix-db-prod \
  --rule-name "AllowAzureServices" \
  --start-ip-address 0.0.0.0 \
  --end-ip-address 0.0.0.0

# 3. Deploy Phoenix Container Instance
az container create \
  --resource-group your-rg \
  --name phoenix-observability \
  --image arizephoenix/phoenix:latest \
  --ports 6006 4317 \
  --cpu 1 --memory 2 \
  --ip-address public \
  --environment-variables \
    PHOENIX_SQL_DATABASE_URL="postgresql://phoenix:<EMAIL>:5432/phoenix?sslmode=require"

# 4. Get Phoenix endpoint
PHOENIX_IP=$(az container show --resource-group your-rg --name phoenix-observability --query ipAddress.ip --output tsv)
echo "Phoenix UI: http://${PHOENIX_IP}:6006"
```

**Cost Estimate:** ~$40-60/month (PostgreSQL Flexible + Container Instance)

### Option 2: Azure App Service

**Deploy Phoenix as App Service:**
```bash
# 1. Create App Service Plan
az appservice plan create \
  --name phoenix-plan \
  --resource-group your-rg \
  --sku B1 \
  --is-linux

# 2. Create Web App with Docker container
az webapp create \
  --resource-group your-rg \
  --plan phoenix-plan \
  --name phoenix-app-unique \
  --deployment-container-image-name arizephoenix/phoenix:latest

# 3. Configure environment variables
az webapp config appsettings set \
  --resource-group your-rg \
  --name phoenix-app-unique \
  --settings \
    PHOENIX_SQL_DATABASE_URL="postgresql://phoenix:<EMAIL>:5432/phoenix?sslmode=require"

# Phoenix UI available at: https://phoenix-app-unique.azurewebsites.net
```

### Option 3: Azure Kubernetes Service (AKS)

**Deploy to AKS with PostgreSQL:**
```yaml
# phoenix-aks-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: phoenix
  namespace: observability
spec:
  replicas: 2  # High availability
  selector:
    matchLabels:
      app: phoenix
  template:
    metadata:
      labels:
        app: phoenix
    spec:
      containers:
      - name: phoenix
        image: arizephoenix/phoenix:latest
        ports:
        - containerPort: 6006
        - containerPort: 4317
        env:
        - name: PHOENIX_SQL_DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: phoenix-db-secret
              key: database-url
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"

---
apiVersion: v1
kind: Service
metadata:
  name: phoenix-service
  namespace: observability
spec:
  selector:
    app: phoenix
  ports:
  - name: ui
    port: 6006
    targetPort: 6006
  - name: otlp
    port: 4317
    targetPort: 4317
  type: LoadBalancer

---
apiVersion: v1
kind: Secret
metadata:
  name: phoenix-db-secret
  namespace: observability
type: Opaque
stringData:
  database-url: "postgresql://phoenix:<EMAIL>:5432/phoenix?sslmode=require"
```

**Deploy to AKS:**
```bash
# Create namespace
kubectl create namespace observability

# Apply deployment
kubectl apply -f phoenix-aks-deployment.yaml

# Get external IP
kubectl get service phoenix-service -n observability

# Scale replicas
kubectl scale deployment phoenix -n observability --replicas=3
```

## Configuration Reference

### Environment Variables

| Variable | Description | Example Values | Default |
|----------|-------------|----------------|---------|
| `PHOENIX_ENABLED` | Enable/disable telemetry | `true`, `false` | `true` |
| `PHOENIX_ENDPOINT` | Phoenix server endpoint | `http://127.0.0.1:6006`, `https://phoenix.yourdomain.com` | `http://127.0.0.1:6006` |
| `PHOENIX_API_KEY` | Phoenix Cloud API key (optional) | `px_key_abc123...` | None |
| `PHOENIX_SERVICE_NAME` | Service identifier in traces | `graysky-assistant`, `chat-api` | `graysky-assistant` |
| `PHOENIX_SERVICE_VERSION` | Application version | `0.1.0`, `1.2.3` | `0.1.0` |
| `PHOENIX_ENVIRONMENT` | Environment tag | `development`, `staging`, `production` | `development` |

### Phoenix Server Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `PHOENIX_PORT` | Phoenix web server port | `6006` |
| `PHOENIX_GRPC_PORT` | gRPC OTLP collector port | `4317` |
| `PHOENIX_HOST` | Server host binding | `0.0.0.0` |
| `PHOENIX_SQL_DATABASE_URL` | PostgreSQL connection string | Required for persistence |

### Database Connection Options

**Primary Method (Recommended):**
```bash
PHOENIX_SQL_DATABASE_URL="************************************/database?sslmode=require"
```

**Alternative Individual Variables:**
```bash
PHOENIX_POSTGRES_HOST=your-postgres-host
PHOENIX_POSTGRES_PORT=5432
PHOENIX_POSTGRES_USER=phoenix
PHOENIX_POSTGRES_PASSWORD=your_password
PHOENIX_POSTGRES_DB=phoenix
PHOENIX_SQL_DATABASE_SCHEMA=public  # Optional schema
```

### Environment Usage Guide

**`PHOENIX_ENVIRONMENT` Configuration:**
- **`development`**: Detailed debugging traces, verbose logging
- **`staging`**: Test deployment validation, integration testing
- **`production`**: Performance optimized, essential metrics only
- **Custom**: Use project-specific names (e.g., `qa`, `demo`, `client-staging`)

## Production Deployment Checklist

### ✅ Pre-Deployment Verification
- [ ] **PostgreSQL database** is created and accessible
- [ ] **Network connectivity** between Phoenix and database (port 5432)
- [ ] **Phoenix container** can connect to database (test connection string)
- [ ] **Firewall rules** allow OTLP traffic (port 4317) and UI access (port 6006)
- [ ] **SSL/TLS certificates** configured for production endpoints
- [ ] **Environment variables** are properly set and validated

### ✅ Post-Deployment Testing
```bash
# 1. Verify Phoenix health
curl -f http://your-phoenix-endpoint:6006/healthz

# 2. Test OTLP endpoint
curl -f http://your-phoenix-endpoint:6006/v1/traces

# 3. Generate test traces
curl -N http://your-app-endpoint/api/v1/chat/stream \
  -H "Content-Type: application/json" \
  -d '{"message": "Production Phoenix test", "conversation_id": "prod-health-check"}'

# 4. Verify traces in UI
# Navigate to: http://your-phoenix-endpoint:6006
# Filter by: conversation_id = "prod-health-check"

# 5. Check database persistence
# Restart Phoenix service and verify traces remain
```

### ✅ Monitoring Setup
```bash
# Set up alerts for:
# - Phoenix service availability (uptime < 99%)
# - Database connection failures
# - OTLP trace ingestion failures
# - High memory/CPU usage (>80% sustained)
# - Disk space usage (database growth)
```

## Troubleshooting

### Phoenix Connection Issues

**Service Not Starting:**
```bash
# Check Docker Compose logs
docker-compose logs phoenix
docker-compose logs postgres

# Verify database connectivity
docker-compose exec phoenix pg_isready -h postgres -U phoenix

# Test manual connection
docker-compose exec postgres psql -U phoenix -d phoenix -c "SELECT version();"
```

**Database Connection Errors:**
```bash
# Common connection string issues:

# ❌ Missing sslmode for Azure
PHOENIX_SQL_DATABASE_URL="********************************/db"

# ✅ Correct for Azure
PHOENIX_SQL_DATABASE_URL="********************************/db?sslmode=require"

# ❌ Wrong host/port
PHOENIX_SQL_DATABASE_URL="postgresql://user:pass@127.0.0.1:5432/db"

# ✅ Correct Docker Compose service name
PHOENIX_SQL_DATABASE_URL="************************************/db"
```

### Application Telemetry Issues

**No Traces Appearing:**
```bash
# 1. Verify application configuration
curl http://localhost:8000/health

# 2. Check Phoenix logs for incoming traces
docker-compose logs -f phoenix | grep -i "trace"

# 3. Test telemetry setup
python -c "
from backend.app.core.phoenix_config import setup_phoenix_telemetry
tracer_provider = setup_phoenix_telemetry()
print('Telemetry setup:', tracer_provider is not None)
"
```

**Connection Refused Errors:**
```bash
# Check Phoenix service status
docker-compose ps

# Verify port accessibility
nc -zv localhost 6006  # UI port
nc -zv localhost 4317  # OTLP port

# Check network connectivity
docker-compose exec phoenix ping postgres
```

### Common Issues & Solutions

| Issue | Symptoms | Solution |
|-------|----------|----------|
| **Database connection failed** | Phoenix container keeps restarting | Check `PHOENIX_SQL_DATABASE_URL` format and credentials |
| **Port conflicts** | Services won't start | Change port mappings in `docker-compose.yml` |
| **Out of disk space** | Database errors, slow performance | Clean up old data or increase disk size |
| **High memory usage** | Phoenix becomes slow/unresponsive | Configure trace sampling or add more memory |
| **SSL connection errors** | Database connection fails in production | Add `?sslmode=require` to connection string |
| **Traces not persisting** | Data lost after restart | Verify PostgreSQL volume is properly mounted |

### Performance Optimization

**Database Tuning:**
```sql
-- Connect to Phoenix database
-- Optimize for trace ingestion workload

-- Increase shared buffers (25% of RAM)
ALTER SYSTEM SET shared_buffers = '512MB';

-- Optimize for write-heavy workload
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET checkpoint_segments = '32';

-- Apply changes
SELECT pg_reload_conf();
```

**Phoenix Configuration:**
```bash
# Reduce trace sampling for high-volume applications
# Add to docker-compose.yml environment:
- PHOENIX_TRACE_SAMPLING_RATE=0.1  # Sample 10% of traces

# Enable Prometheus metrics
- PHOENIX_ENABLE_PROMETHEUS=true
```

## Best Practices

### 🔍 Trace Organization
- **Consistent Naming**: Use standardized `conversation_id` format across all requests
- **Meaningful Spans**: Name spans after business operations, not technical methods
- **Structured Attributes**: Add searchable metadata for filtering and analysis
- **Environment Separation**: Use different `PHOENIX_ENVIRONMENT` values per deployment

### 📊 Performance Monitoring
- **Key Metrics**: Track response time, token usage, error rates, and throughput
- **Alert Thresholds**: Set up monitoring for degraded performance patterns
- **Conversation Analysis**: Use Phoenix's conversation-level insights for UX optimization
- **Cost Tracking**: Monitor token usage trends for cost optimization

### 🔒 Security & Privacy
- **Data Scrubbing**: Phoenix automatically redacts API keys and sensitive data
- **Network Security**: Use private networks for Phoenix-to-database communication
- **Access Control**: Restrict Phoenix UI access to authorized personnel only
- **SSL/TLS**: Always use encrypted connections for production deployments

### 💾 Data Management
- **Retention Policies**: Configure automatic cleanup of old trace data
- **Backup Strategy**: Regular database backups with tested restore procedures
- **Disk Monitoring**: Set up alerts for database disk usage growth
- **Archive Strategy**: Move old traces to cold storage for compliance

### 🚀 Production Deployment
- **High Availability**: Deploy Phoenix with multiple replicas in production
- **Load Balancing**: Use Azure Load Balancer for AKS deployments
- **Auto-scaling**: Configure horizontal pod autoscaling based on CPU/memory
- **Health Checks**: Implement comprehensive health monitoring and alerting

## Monitoring Dashboards

### Phoenix Built-in Analytics

**📈 System Performance Dashboard:**
- Request latency percentiles (p50, p95, p99)
- Throughput metrics (requests per minute)
- Error rates and failure pattern analysis
- Resource utilization tracking (CPU, memory, disk)

**🤖 LLM Analytics Dashboard:**
- Token consumption trends and cost analysis
- Model performance comparison (latency vs quality)
- Function calling success rates and usage patterns
- Conversation length and complexity metrics

**🔧 Tool Usage Dashboard:**
- MCP server response times and availability metrics
- Tool selection patterns and effectiveness analysis
- Source attribution quality and coverage statistics
- Data retrieval success rates and performance

**📊 Business Metrics Dashboard:**
- User engagement metrics and session length analysis
- Feature usage patterns (streaming message types)
- Query complexity analysis and resolution rates
- Agent decision-making effectiveness tracking

### Custom Monitoring Setup

**Prometheus Integration:**
```yaml
# Add to docker-compose.yml
environment:
  - PHOENIX_ENABLE_PROMETHEUS=true

# Scrape endpoint: http://localhost:9090/metrics
```

**Grafana Dashboard:**
```json
{
  "dashboard": {
    "title": "Phoenix LLM Observability",
    "panels": [
      {
        "title": "Token Usage Over Time",
        "type": "graph",
        "targets": [
          {
            "expr": "phoenix_tokens_total",
            "legendFormat": "Tokens Used"
          }
        ]
      }
    ]
  }
}
```

**Azure Monitor Integration:**
```bash
# For Azure deployments, logs automatically flow to Azure Monitor
# Set up Application Insights for additional metrics

az monitor app-insights component create \
  --app phoenix-insights \
  --location eastus \
  --resource-group your-rg \
  --application-type web
```

## Phoenix Cloud Alternative

### Features Not Used (Available in Phoenix Cloud)
While we use self-hosted Phoenix, Phoenix Cloud provides these additional features:

**🔬 Advanced Analytics:**
- ML-powered anomaly detection and insights
- Automated performance regression detection
- Predictive scaling recommendations
- Advanced trace correlation analysis

**👥 Team Collaboration:**
- Multi-user workspaces with role-based access
- Shared dashboards and collaborative analysis
- Team alerting and notification systems
- Cross-team trace sharing capabilities

**🛡️ Enterprise Security:**
- SOC2 Type II compliance certification
- Single Sign-On (SSO) integration
- Advanced audit logging and compliance reporting
- Enterprise-grade data encryption and isolation

**💼 Managed Infrastructure:**
- Automatic scaling and high availability
- Managed database with automated backups
- Global CDN for optimal UI performance
- 24/7 professional support and SLA guarantees

### Cost Comparison
- **Self-hosted Phoenix**: ~$40-100/month (Azure infrastructure)
- **Phoenix Cloud**: ~$200-500/month (managed service pricing)

**Why We Choose Self-hosted:**
- ✅ **Cost Control**: 60-80% cost savings for our usage patterns
- ✅ **Data Sovereignty**: Complete control over trace data and privacy
- ✅ **Customization**: Full control over deployment and configuration
- ✅ **Integration**: Seamless integration with existing Azure infrastructure

---

## Summary

This Phoenix deployment provides comprehensive LLM observability with:

- ✅ **Production-ready PostgreSQL persistence**
- ✅ **Docker Compose simplicity** for development
- ✅ **Multiple Azure deployment options** for production
- ✅ **Comprehensive monitoring** and troubleshooting guides
- ✅ **Security best practices** and performance optimization
- ✅ **Cost-effective self-hosted** approach vs managed services

For questions or issues, refer to the [troubleshooting section](#troubleshooting) or check the main [backend documentation](../backend/README.md).
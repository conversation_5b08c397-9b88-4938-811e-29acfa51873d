#!/usr/bin/env python3
"""
Quick test script to verify schema_manager functionality
"""

import sys
import os
import logging

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

from dotenv import load_dotenv
from src.graph.client import GraphClient
from src.graph.schema_manager import SchemaManager
from src.config import get_server_config

# Load environment
load_dotenv()
config = get_server_config()

# Initialize graph client
print("Initializing GraphClient...")
graph_client = GraphClient(
    host=config["memgraph_host"],
    port=config["memgraph_port"],
    user=config["memgraph_user"],
    password=config["memgraph_password"],
    ssl=config["memgraph_ssl"]
)

# Test direct query
print("\n1. Testing direct graph_client.execute_query...")
test_query = "MATCH (n) RETURN count(n) as node_count"
result = graph_client.execute_query(test_query)
print(f"   Node count: {result[0]['node_count'] if result else 0}")

# Test schema manager
print("\n2. Initializing SchemaManager...")
schema_manager = SchemaManager(graph_client)

print("\n3. Testing schema_manager.refresh_cache()...")
schema_manager.refresh_cache()

print(f"\n4. Cache size after refresh: {schema_manager.get_cache_size()} bytes")
print(f"   Cache timestamp: {schema_manager.cache_timestamp}")

print("\n5. Cache contents:")
import json
cache = json.loads(schema_manager.get_cached_schema())
print(f"   Entity types: {list(cache.get('entity_types', {}).keys())}")
print(f"   Relationship types: {list(cache.get('relationship_types', {}).keys())}")
print(f"   Statistics: {cache.get('statistics', {})}")

print("\n6. Full cache JSON:")
print(json.dumps(cache, indent=2))

graph_client.close()
print("\nTest complete!")

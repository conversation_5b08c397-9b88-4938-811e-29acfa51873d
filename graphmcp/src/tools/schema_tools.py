"""
Schema discovery tools for GraphRAG MCP Server
"""

import json
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)


def register(mcp, schema_manager):
    """
    Register schema discovery tools with the MCP server.

    Args:
        mcp: FastMCP server instance
        schema_manager: SchemaManager instance
    """

    @mcp.tool()
    def get_schema() -> str:
        """
        Get the complete graph schema including all entity types, relationship types, and their properties.

        This tool should typically be called first to understand the graph structure before querying.

        Returns:
            JSON object with:
            - entity_types: List of node labels with their properties and data types
            - relationship_types: List of relationship types with their properties
            - statistics: Count of nodes and relationships by type
            - indexes: Available indexes for optimized queries

        Example Response:
        {
            "entity_types": {
                "Incident": {
                    "properties": ["id", "type", "severity", "status", "timestamp"],
                    "count": 1543
                },
                "Resource": {...},
                "Department": {...}
            },
            "relationship_types": {
                "RESPONDED_TO": {
                    "from": ["Department", "Resource"],
                    "to": ["Incident"],
                    "properties": ["response_time", "role"],
                    "count": 4521
                }
            },
            "indexes": [
                {"type": "Incident", "property": "id"},
                {"type": "Incident", "property": "timestamp"}
            ],
            "statistics": {
                "total_nodes": 5234,
                "total_relationships": 8765,
                "node_types": 5,
                "relationship_types": 7
            }
        }
        """
        try:
            logger.info("get_schema called")
            schema = schema_manager.get_cached_schema()
            schema_dict = json.loads(schema)

            # Add Memgraph Cypher guidelines
            schema_dict["cypher_guidelines"] = {
                "database_type": "Memgraph",
                "important_rules": [
                    "Use ONLY the node labels, relationship types, and properties defined in this schema",
                    "Do NOT create or assume any relationships not explicitly listed",
                    "Do NOT create or assume any properties not explicitly listed",
                    "Node labels and relationship types are case-sensitive",
                    "All relationship queries must specify direction with -> or <-"
                ],
                "memgraph_syntax": {
                    "case_sensitivity": "Labels, types, and properties are case-sensitive",
                    "property_access": "Use dot notation: n.property_name",
                    "temporal_types": "Date/time properties use ZONED_DATE_TIME type",
                    "vector_properties": "The 'embedding' property contains float arrays for vector search",
                    "string_matching": "Use CONTAINS, STARTS WITH, or ENDS WITH for string patterns (case-sensitive)",
                    "aggregation": "Supported: count(), sum(), avg(), min(), max(), collect()"
                },
                "example_queries": [
                    {
                        "description": "Find a Mission by ID",
                        "cypher": "MATCH (m:Mission {id: 12345}) RETURN m"
                    },
                    {
                        "description": "Find Missions related to an Incident by name",
                        "cypher": "MATCH (m:Mission)-[:RELATED_TO_INCIDENT]->(i:Incident {incident_name: 'Hurricane Ian'}) RETURN m.mission_number, m.title, i.incident_type"
                    },
                    {
                        "description": "Get Counties with their current EEI status",
                        "cypher": "MATCH (c:County)-[:HAS_CURRENT_STATUS]->(e:EEIStatus) RETURN c.county_name, e.eoc_activation, e.evacuation_status, e.govt_status"
                    },
                    {
                        "description": "Find Mission parent-child hierarchies",
                        "cypher": "MATCH (parent:Mission)-[:IS_PARENT_TO]->(child:Mission) RETURN parent.mission_number, parent.title, child.mission_number, child.title"
                    },
                    {
                        "description": "Aggregate fuel transactions by vendor",
                        "cypher": "MATCH (ft:FuelTransaction)-[:FROM_VENDOR]->(v:Vendor) RETURN v.mission_vendor_name, count(ft) AS transaction_count, sum(toFloat(ft.amount_dispensed)) AS total_fuel ORDER BY total_fuel DESC"
                    },
                    {
                        "description": "Find Comments on a specific Mission",
                        "cypher": "MATCH (c:Comment)-[:COMMENTED_ON]->(m:Mission {mission_number: 'M-2024-001'}) RETURN c.comment_id, c.text, c.created_by, c.created_at ORDER BY c.created_at DESC"
                    },
                    {
                        "description": "Get Incidents with their fuel burn data",
                        "cypher": "MATCH (i:Incident)-[:HAS_DAILY_FUEL_BURN]->(fb:FuelBurnDaily) RETURN i.incident_name, fb.depot_name, fb.fuel_type, fb.total_fuel_dispensed, fb.day_start"
                    },
                    {
                        "description": "Count entities by type",
                        "cypher": "MATCH (n:Mission) RETURN count(n) AS mission_count"
                    }
                ],
                "common_patterns": {
                    "filtering": "MATCH (n:Label) WHERE n.property = value RETURN n",
                    "traversal": "MATCH (a:LabelA)-[:REL_TYPE]->(b:LabelB) RETURN a, b",
                    "multi_hop": "MATCH path = (a:LabelA)-[:REL*1..3]->(b:LabelB) RETURN path",
                    "aggregation": "MATCH (n:Label) RETURN n.property, count(*) AS count GROUP BY n.property",
                    "ordering": "MATCH (n:Label) RETURN n ORDER BY n.property DESC LIMIT 10"
                }
            }

            logger.info(f"Returned schema with {len(schema_dict.get('node_labels', {}))} node labels")
            return json.dumps(schema_dict, indent=2)
        except Exception as e:
            logger.error(f"Error in get_schema: {str(e)}", exc_info=True)
            return json.dumps({"error": str(e), "success": False})

    @mcp.tool()
    def describe_entity_type(entity_type: str) -> str:
        """
        Get detailed information about a specific entity type.

        Args:
            entity_type: The node label to describe (e.g., "Incident", "Resource")

        Returns:
            Detailed schema for the entity type including:
            - All properties with types and constraints
            - Common values for categorical properties
            - Related entity types via relationships
            - Sample entities

        Example Call:
            describe_entity_type("Incident")

        Example Response:
        {
            "entity_type": "Incident",
            "count": 1543,
            "properties": ["id", "type", "severity", "status", "timestamp"],
            "relationships": [
                {"type": "RESPONDED_TO", "direction": "incoming", "count": 2341},
                {"type": "LOCATED_IN", "direction": "outgoing", "count": 1543}
            ],
            "samples": [
                {"id": "INC-001", "type": "fire", "severity": 3, ...},
                {"id": "INC-002", "type": "flood", "severity": 5, ...}
            ]
        }
        """
        try:
            logger.info(f"describe_entity_type called with entity_type={entity_type}")
            description = schema_manager.describe_entity(entity_type)
            return description
        except Exception as e:
            logger.error(f"Error in describe_entity_type: {str(e)}", exc_info=True)
            return json.dumps({"error": str(e), "success": False})

    @mcp.tool()
    def describe_relationship_type(relationship_type: str) -> str:
        """
        Get detailed information about a specific relationship type.

        Args:
            relationship_type: The relationship type (e.g., "RESPONDED_TO", "ALLOCATED_TO")

        Returns:
            Detailed information including:
            - Source and target entity types
            - Properties with types
            - Cardinality patterns
            - Common traversal patterns

        Example Call:
            describe_relationship_type("RESPONDED_TO")

        Example Response:
        {
            "relationship_type": "RESPONDED_TO",
            "count": 4521,
            "from": ["Resource", "Department"],
            "to": ["Incident"],
            "properties": ["response_time", "role", "timestamp"],
            "samples": [
                {
                    "source": {"type": "Resource", "id": "RES-001"},
                    "relationship": {"response_time": 12, "role": "primary"},
                    "target": {"type": "Incident", "id": "INC-001"}
                }
            ]
        }
        """
        try:
            logger.info(f"describe_relationship_type called with relationship_type={relationship_type}")
            description = schema_manager.describe_relationship(relationship_type)
            return description
        except Exception as e:
            logger.error(f"Error in describe_relationship_type: {str(e)}", exc_info=True)
            return json.dumps({"error": str(e), "success": False})

    @mcp.tool()
    def get_node_statistics() -> str:
        """
        Get statistical information about nodes in the graph.

        Returns:
            JSON object with node counts by label and overall statistics.

        Example Response:
        {
            "total_nodes": 5234,
            "by_label": {
                "Incident": 1543,
                "Resource": 892,
                "Department": 45,
                "Location": 2754
            },
            "labels_per_node": {
                "min": 1,
                "max": 3,
                "avg": 1.2
            }
        }
        """
        try:
            logger.info("get_node_statistics called")

            # Get cached schema which includes counts
            schema = json.loads(schema_manager.get_cached_schema())

            statistics = {
                "total_nodes": schema.get("statistics", {}).get("total_nodes", 0),
                "by_label": {},
                "node_label_count": schema.get("statistics", {}).get("node_label_count", 0)
            }

            # Extract counts from node labels
            for label, info in schema.get("node_labels", {}).items():
                statistics["by_label"][label] = info.get("count", 0)

            return json.dumps(statistics, indent=2)

        except Exception as e:
            logger.error(f"Error in get_node_statistics: {str(e)}", exc_info=True)
            return json.dumps({"error": str(e), "success": False})

    @mcp.tool()
    def get_relationship_statistics() -> str:
        """
        Get statistical information about relationships in the graph.

        Returns:
            JSON object with relationship counts by type and overall statistics.

        Example Response:
        {
            "total_relationships": 8765,
            "by_type": {
                "RESPONDED_TO": 4521,
                "ALLOCATED_TO": 2341,
                "MANAGES": 892,
                "COORDINATES_WITH": 1011
            },
            "relationship_types": 7
        }
        """
        try:
            logger.info("get_relationship_statistics called")

            # Get cached schema which includes counts
            schema = json.loads(schema_manager.get_cached_schema())

            statistics = {
                "total_relationships": schema.get("statistics", {}).get("total_relationships", 0),
                "by_type": {},
                "relationship_types": schema.get("statistics", {}).get("relationship_types", 0)
            }

            # Extract counts from relationship types
            for rel_type, info in schema.get("relationship_types", {}).items():
                statistics["by_type"][rel_type] = info.get("count", 0)

            return json.dumps(statistics, indent=2)

        except Exception as e:
            logger.error(f"Error in get_relationship_statistics: {str(e)}", exc_info=True)
            return json.dumps({"error": str(e), "success": False})

    logger.info("Schema tools registered successfully")
# Multi-Field Embedding Strategy

## Question

**Is it possible for a node to have multiple relevant text entries (e.g., mission:title, mission:description)?**
**Is this recommended in an ontology?**
**What would we do from an embedding perspective?**

---

## Answer Summary

**YES** - Nodes can and often do have multiple text fields.
**YES** - This is common and recommended in ontologies.
**OPTION** - You have 3 strategies for embedding multi-field nodes.

---

## The 3 Embedding Strategies

### Strategy 1: Combined Field Embedding (RECOMMENDED ⭐)

**Approach:** Concatenate multiple fields into single text, generate ONE embedding.

**Example:**
```python
# Mission with title + description
mission = {
    "id": "566189",
    "title": "Feeding Florida MOU Activation",
    "description": "Coordinate food distribution across affected counties",
    "mission_number": "00787"
}

# Concatenate fields with structure
embedding_text = f"{mission['title']}. {mission['description']}"
# "Feeding Florida MOU Activation. Coordinate food distribution across affected counties"

# Generate single embedding
embedding = openai.embeddings.create(
    model="text-embedding-3-small",
    input=embedding_text
).data[0].embedding

# Store in node
{
    ...mission properties...,
    "embedding": embedding,  // Single 1536-dim vector
    "embedding_source_fields": ["title", "description"],  // Track which fields
    "embedding_text_hash": sha256(embedding_text)  // Hash of combined text
}
```

**Advantages:**
- ✅ **Simple** - One embedding per node, one index
- ✅ **Holistic search** - Finds nodes relevant to ANY field
- ✅ **Efficient** - Single API call, single storage
- ✅ **Standard practice** - Neo4j GraphRAG uses this pattern
- ✅ **Cost-effective** - 1 embedding vs N embeddings per field

**Disadvantages:**
- ⚠️ **Field dilution** - Important field may get "diluted" by less important fields
- ⚠️ **No field-specific search** - Can't search "only in titles" vs "only in descriptions"
- ⚠️ **Weighting challenges** - All fields contribute equally

**When to Use:**
- ✅ **General semantic search** - "Find relevant missions"
- ✅ **Most use cases** - Default recommendation
- ✅ **Budget-conscious** - Minimize API costs
- ✅ **Simple requirements** - Don't need field-specific search

---

### Strategy 2: Separate Field Embeddings (ADVANCED)

**Approach:** Create separate embedding property for each text field.

**Example:**
```python
# Generate separate embeddings
title_embedding = embed(mission['title'])
description_embedding = embed(mission['description'])

# Store both in node
{
    ...mission properties...,

    "title_embedding": title_embedding,        // 1536-dim
    "title_embedding_model": "text-embedding-3-small",

    "description_embedding": description_embedding,  // 1536-dim
    "description_embedding_model": "text-embedding-3-small",
}

// Need separate indices
CREATE VECTOR INDEX mission_title_embedding ON :Mission(title_embedding) ...
CREATE VECTOR INDEX mission_description_embedding ON :Mission(description_embedding) ...
```

**Search Pattern:**
```cypher
// Search titles
CALL vector.search('mission_title_embedding', $query_vector, 10)

// OR search descriptions
CALL vector.search('mission_description_embedding', $query_vector, 10)

// OR combine both (weighted)
CALL vector.search('mission_title_embedding', $query_vector, 20) YIELD node as n1, score as s1
WITH collect({node: n1, score: s1 * 0.7}) as title_results
CALL vector.search('mission_description_embedding', $query_vector, 20) YIELD node as n2, score as s2
WITH title_results, collect({node: n2, score: s2 * 0.3}) as desc_results
// Combine and deduplicate with weighted scores
...
```

**Advantages:**
- ✅ **Field-specific search** - Can target specific fields
- ✅ **Weighted combination** - Control importance of each field
- ✅ **Precision** - Better for field-specific queries

**Disadvantages:**
- ❌ **3x Cost** - Multiple embeddings per node
- ❌ **3x Storage** - Larger node properties
- ❌ **Complex queries** - Need to combine multiple searches
- ❌ **Index management** - Multiple indices per entity type
- ❌ **Slower** - Multiple vector searches per query

**When to Use:**
- ⚠️ **Field-specific requirements** - "Search only in titles"
- ⚠️ **Weighted search** - Different importance per field
- ⚠️ **Advanced use cases** - Precision critical
- ❌ **NOT recommended for V1** - Too complex for initial implementation

---

### Strategy 3: Weighted Field Concatenation (MIDDLE GROUND)

**Approach:** Concatenate fields with structural markers, allowing field importance through text repetition.

**Example:**
```python
# Emphasize title by repeating it
embedding_text = f"Title: {mission['title']}. {mission['title']}. Description: {mission['description']}"
# "Title: Feeding Florida MOU Activation. Feeding Florida MOU Activation. Description: Coordinate food distribution..."

# Or use structured format
embedding_text = f"""
Mission: {mission['title']}
Objective: {mission['description']}
Type: {mission['mission_type']}
"""

# Generate single embedding
embedding = embed(embedding_text)
```

**Advantages:**
- ✅ **Single embedding** - Simpler than Strategy 2
- ✅ **Field weighting** - Repetition emphasizes importance
- ✅ **Structured** - Clear field boundaries
- ✅ **Flexible** - Easy to adjust weighting

**Disadvantages:**
- ⚠️ **Token overhead** - Repetition uses more tokens (costs more)
- ⚠️ **Unclear effectiveness** - Embedding models may not weight repetition predictably
- ⚠️ **Not standard** - Less documented than Strategy 1

**When to Use:**
- ⚠️ **Experimental** - Test if field weighting improves results
- ⚠️ **Middle ground** - Between Strategy 1 and 2

---

## Recommendation for Your Use Case

### For Mission Nodes:

**Current Structure:**
```python
{
    "id": "566189",
    "title": "Feeding Florida MOU Activation",
    "mission_number": "00787",
    "comments": ["Comment 1", "Comment 2", ...]
}
```

**Recommended: Strategy 1 (Combined Field Embedding)**

**Phase 1 - Simple:**
```python
# Just title for now (simple, clean)
embedding_text = mission['title']
```

**Phase 2 - Add comments when relevant:**
```python
# Title + recent comments (limit to avoid token bloat)
embedding_text = f"{mission['title']}. {' '.join(mission['comments'][:3])}"
# "Feeding Florida MOU Activation. Comment 1. Comment 2. Comment 3"
```

**Phase 3 - When description field added:**
```python
# Title + description + key comments
embedding_text = f"{mission['title']}. {mission['description']}. Recent: {' | '.join(mission['comments'][:2])}"
```

**Store:**
```python
{
    ...mission properties...,
    "embedding": [1536 floats],
    "embedding_source_fields": ["title", "comments"],  // Track what was embedded
    "embedding_text_hash": sha256(embedding_text)
}
```

---

## Field Selection Guidelines

**For each entity type, choose fields that:**
1. ✅ **Have semantic meaning** - Not IDs, codes, or numbers
2. ✅ **Are user-searchable** - What users would query for
3. ✅ **Are relatively stable** - Not frequently changing
4. ✅ **Have reasonable length** - Not too short (<10 tokens) or too long (>1000 tokens)

**Entity-Specific Recommendations:**

### Mission:
```python
# Phase 1 (Now): Title only
embedding_text = mission['title']

# Phase 2 (Later): Title + top comments
embedding_text = f"{mission['title']}. {' '.join(mission['comments'][:3])}"

# Track source
embedding_source_fields = ["title", "comments"]
```

### Incident:
```python
# Combine type + description
embedding_text = f"{incident['type']}: {incident['description']}"
# "Hurricane: Widespread coastal damage with flooding"

embedding_source_fields = ["type", "description"]
```

### Resource:
```python
# Combine type + capabilities/description
embedding_text = f"{resource['type']} - {resource['description'] or resource['capabilities']}"
# "Fire Truck - Equipped with 1000-gallon tank and ladder"

embedding_source_fields = ["type", "description"]
```

### Future: Comment Nodes
```python
# When comments become separate nodes
embedding_text = comment['text']  # Single field, already atomic

embedding_source_fields = ["text"]
```

---

## Migration Strategy When Schema Changes

**Scenario:** Comments become separate nodes.

**Current:**
```cypher
(:Mission {
    title: "...",
    comments: ["Comment 1", "Comment 2"],
    embedding: [...],  // Embedded from title + comments
    embedding_source_fields: ["title", "comments"]
})
```

**After Migration:**
```cypher
(:Mission {
    title: "...",
    embedding: [...],  // Re-embed from title only
    embedding_source_fields: ["title"]
})
    -[:HAS_COMMENT]->
(:Comment {
    id: "COM-001",
    text: "Comment 1",
    timestamp: "...",
    embedding: [...],  // Separate embedding for comment
    embedding_source_fields: ["text"]
})
```

**Migration Process:**
1. Create Comment nodes from Mission.comments arrays
2. Re-generate Mission embeddings (title only)
3. Generate Comment embeddings (comment text)
4. Create new vector index for Comments
5. Update embedding_source_fields metadata

---

## Performance and Cost Comparison

**Scenario: 1000 Missions with title + 5 comments each**

### Strategy 1 (Combined):
- **API Calls:** 1,000
- **Embeddings Stored:** 1,000
- **Storage:** 1,000 × 1536 floats = 6 MB
- **Cost:** ~$0.02 (assuming 100 tokens per mission)
- **Search Speed:** 1 vector search
- **Complexity:** Low

### Strategy 2 (Separate):
- **API Calls:** 2,000 (title + comments)
- **Embeddings Stored:** 2,000
- **Storage:** 2,000 × 1536 floats = 12 MB
- **Cost:** ~$0.04 (2x)
- **Search Speed:** 2 vector searches + merge
- **Complexity:** High

### Strategy 3 (Weighted Concat):
- **API Calls:** 1,000
- **Embeddings Stored:** 1,000
- **Storage:** 6 MB
- **Cost:** ~$0.03 (extra tokens from repetition)
- **Search Speed:** 1 vector search
- **Complexity:** Medium

---

## Implementation Example

**Define field selection per entity type:**

```python
# src/embeddings/field_selector.py

ENTITY_EMBEDDING_FIELDS = {
    "Mission": {
        "fields": ["title", "comments"],
        "template": "{title}. {comments_joined}",
        "comments_limit": 3  # Only use first 3 comments
    },
    "Incident": {
        "fields": ["type", "description"],
        "template": "{type}: {description}"
    },
    "Resource": {
        "fields": ["type", "description"],
        "template": "{type} - {description}"
    },
    "Comment": {  # When comments become nodes
        "fields": ["text"],
        "template": "{text}"
    }
}

def extract_embedding_text(node_type: str, node_data: dict) -> str:
    """Extract text for embedding based on entity type"""
    config = ENTITY_EMBEDDING_FIELDS.get(node_type)

    if not config:
        raise ValueError(f"No embedding configuration for {node_type}")

    # Build embedding text from template
    if node_type == "Mission":
        comments_joined = '. '.join(node_data.get('comments', [])[:config['comments_limit']])
        return config['template'].format(
            title=node_data['title'],
            comments_joined=comments_joined
        )

    elif node_type in ["Incident", "Resource"]:
        return config['template'].format(
            type=node_data.get('type', ''),
            description=node_data.get('description', '')
        )

    elif node_type == "Comment":
        return node_data['text']
```

---

## Ontology Best Practices

**From Research:**

### ✅ Multiple Text Fields in Ontology: RECOMMENDED
- Standard practice in knowledge graphs
- Rich semantic information
- Enables comprehensive search

### ✅ Single Embedding Per Node: RECOMMENDED
- Industry standard (Neo4j, Memgraph, Qdrant, Pinecone)
- Simpler, faster, cheaper
- Combine fields at embedding time

### ❌ Multiple Embeddings Per Node: ADVANCED ONLY
- Only if field-specific search is critical requirement
- 2-3x cost and complexity
- Requires weighted search logic

---

## Recommended Implementation Plan

### Phase 1 (Initial Implementation):
```python
# Simple, clean approach
Mission.embedding_text = mission['title']
Incident.embedding_text = f"{incident['type']}: {incident['description']}"
Resource.embedding_text = f"{resource['type']} - {resource['description']}"
```

**Metadata:**
```python
{
    "embedding_source_fields": ["title"],  // Track what was embedded
    "embedding_text_hash": sha256(title)   // Detect changes
}
```

### Phase 2 (Enhanced - Add More Context):
```python
# Add comments to missions
Mission.embedding_text = f"{mission['title']}. {' '.join(mission['comments'][:3])}"

# Update metadata
{
    "embedding_source_fields": ["title", "comments"],
    "embedding_text_hash": sha256(combined_text),
    "embedding_comments_count": 3  // Track how many comments included
}
```

### Phase 3 (Schema Evolution - Comments as Nodes):
```python
# Mission: Title only (comments now separate)
Mission.embedding_text = mission['title']
Mission.embedding_source_fields = ["title"]

# Comment: Text only
Comment.embedding_text = comment['text']
Comment.embedding_source_fields = ["text"]

# New vector index for comments
CREATE VECTOR INDEX comment_embedding ON :Comment(embedding) ...
```

---

## Real-World Examples

### Neo4j Movie Database:
```python
# Combines title + plot into single embedding
embedding_text = f"Movie title: {movie['title']}, plot: {movie['plot']}"
```

### Emergency Management:
```python
# Incident with multiple fields
embedding_text = f"""
Type: {incident['type']}
Description: {incident['description']}
Location: {incident['location']}
Severity: {incident['severity']}/5
"""
```

### Document Management:
```python
# Document with metadata
embedding_text = f"""
{document['title']}
{document['abstract']}
Tags: {', '.join(document['tags'])}
"""
```

---

## Decision Matrix

| Criteria | Single Combined | Separate Per Field | Weighted Concat |
|----------|----------------|-------------------|-----------------|
| **Cost** | $ | $$$ | $$ |
| **Storage** | ⭐⭐⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐⭐ |
| **Query Speed** | ⭐⭐⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐⭐⭐ |
| **Simplicity** | ⭐⭐⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐ |
| **Field-Specific Search** | ❌ | ✅ | ❌ |
| **General Search Quality** | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| **Maintenance** | ⭐⭐⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐⭐ |

---

## Implementation Code

**Field Selector Configuration (Recommended):**

```python
# config/embedding_fields.py

EMBEDDING_FIELD_CONFIG = {
    "Mission": {
        "v1": {
            "fields": ["title"],
            "format": "{title}"
        },
        "v2": {  # Future: Add comments
            "fields": ["title", "comments"],
            "format": "{title}. {comments_summary}",
            "comments_limit": 3
        }
    },
    "Incident": {
        "v1": {
            "fields": ["type", "description"],
            "format": "{type}: {description}"
        }
    },
    "Resource": {
        "v1": {
            "fields": ["type", "description"],
            "format": "{type} - {description}"
        }
    },
    "Comment": {  # Future: When comments become nodes
        "v1": {
            "fields": ["text"],
            "format": "{text}"
        }
    }
}

def get_embedding_text(entity_type: str, entity_data: dict, version: str = "v1") -> tuple[str, list]:
    """
    Extract text for embedding based on entity type configuration

    Returns:
        (embedding_text, source_fields)
    """
    config = EMBEDDING_FIELD_CONFIG[entity_type][version]

    if entity_type == "Mission":
        if version == "v1":
            text = entity_data['title']
            fields = ["title"]
        elif version == "v2":
            comments = entity_data.get('comments', [])[:config['comments_limit']]
            comments_summary = '. '.join(comments)
            text = f"{entity_data['title']}. {comments_summary}"
            fields = ["title", "comments"]

    elif entity_type == "Incident":
        text = f"{entity_data['type']}: {entity_data.get('description', '')}"
        fields = ["type", "description"]

    elif entity_type == "Resource":
        text = f"{entity_data['type']} - {entity_data.get('description', '')}"
        fields = ["type", "description"]

    elif entity_type == "Comment":
        text = entity_data['text']
        fields = ["text"]

    return text, fields
```

---

## Answer to "Is This Recommended in Ontology?"

### YES ✅ - Multiple text fields are STANDARD in ontologies

**Examples from Neo4j:**
- Movie: `title`, `plot`, `tagline`, `overview`
- Person: `name`, `biography`, `summary`
- Article: `title`, `abstract`, `body`, `summary`

**Examples from Your Domain:**
- Mission: `title`, `description`, `comments`, `objectives`
- Incident: `type`, `description`, `location`, `narrative`
- Resource: `type`, `description`, `capabilities`, `specifications`

**Key Principle:**
- ✅ **Ontology:** Multiple text fields = GOOD (rich semantic data)
- ✅ **Embedding:** One combined embedding = RECOMMENDED (simple, efficient)
- ⚠️ **Advanced:** Multiple embeddings per field = ONLY when necessary

---

## Summary

**Your Question:** Can nodes have multiple text fields? What do we do for embeddings?

**Answer:**
1. ✅ **Multiple text fields:** Standard and recommended in ontologies
2. ✅ **Embedding strategy:** Combine fields into single embedding (Strategy 1)
3. ✅ **Track source fields:** Store metadata about which fields were embedded
4. ✅ **Evolution-friendly:** Easy to change field combination over time

**Recommended for Mission:**
- **Now:** `embedding_text = title`
- **Later:** `embedding_text = title + comments[:3]`
- **Future:** When comments become nodes, embed separately

**Code Pattern:**
```python
# Flexible, version-tracked field selection
text, source_fields = get_embedding_text("Mission", mission_data, version="v1")
embedding = generate_embedding(text)
node['embedding_source_fields'] = source_fields  # Track what was embedded
```

---

**Created:** October 1, 2025
**Recommendation:** Strategy 1 (Combined Field Embedding) with version-tracked field selection
**Future:** Easy migration when schema evolves (comments → nodes)

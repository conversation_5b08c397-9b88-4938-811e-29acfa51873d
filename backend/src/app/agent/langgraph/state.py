"""
LangGraph state definitions.

Defines the data structure that flows through the graph.
"""

from typing import TypedDict, Annotated, Sequence, Optional, List, Dict, Any
from langchain_core.messages import BaseMessage
import operator


class AgentState(TypedDict):
    """
    State for LangGraph workflow with OpenAI function calling.

    This state flows through all nodes in the graph and accumulates data.
    The Annotated[Sequence[BaseMessage], operator.add] syntax means that
    when multiple nodes return messages, they are concatenated rather than overwritten.
    """
    # Conversation messages (accumulates)
    messages: Annotated[Sequence[BaseMessage], operator.add]

    # User query for easy access
    user_query: str

    # Function calling from LLM
    tool_calls: Optional[List[Any]]  # OpenAI tool call objects
    tools_to_execute: List[str]  # List of tool names to execute

    # MCP results
    tool_results: Dict[str, Any]  # Maps tool_call_id to result

    # Final response
    final_response: str

    # Metadata for tracing
    conversation_id: str
    request_id: str

    # Performance tracking
    tool_latencies: Dict[str, float]  # Maps tool name to latency
    total_latency: Optional[float]

    # Graph context cache (fetched once at workflow start from analyze_knowledge_graph tool)
    graph_context: Optional[Dict[str, Any]]

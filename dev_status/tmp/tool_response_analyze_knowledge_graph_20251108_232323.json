{"success": true, "statistics": {"totalNodes": 152369, "totalRelationships": 165825, "nodeLabelCount": 10, "relationshipTypeCount": 11, "averageDegree": 2.18, "timestamp": "1762662203233144"}, "highDegreeNodes": [{"node_labels": ["Incident"], "properties": {"incident_created_at": "2020-09-01T20:18:24.000000000+00:00", "incident_id": 88, "incident_name": "COVID-19 Public Health Emergency", "incident_status": "Active", "incident_type": "null", "incident_updated_at": "2022-04-11T18:40:56.000000000+00:00"}, "degree": 48962}, {"node_labels": ["Incident"], "properties": {"incident_created_at": "2024-10-05T17:09:31.000000000+00:00", "incident_id": 172, "incident_name": "2024 Milton", "incident_status": "Active", "incident_type": "Hurricane", "incident_updated_at": "2025-10-02T13:48:28.000000000+00:00"}, "degree": 7002}, {"node_labels": ["Incident"], "properties": {"incident_created_at": "2022-09-30T22:04:19.000000000+00:00", "incident_id": 133, "incident_name": "2022 Ian", "incident_status": "Active", "incident_type": "null", "incident_updated_at": "2022-11-14T17:50:52.000000000+00:00"}, "degree": 6891}, {"node_labels": ["Incident"], "properties": {"incident_created_at": "2024-09-24T13:59:50.000000000+00:00", "incident_id": 171, "incident_name": "2024 <PERSON><PERSON>", "incident_status": "Active", "incident_type": "Hurricane", "incident_updated_at": "2025-10-02T13:48:28.000000000+00:00"}, "degree": 4759}, {"node_labels": ["<PERSON><PERSON><PERSON>"], "properties": {"mission_vendor_name": "World Fuel Services", "vendor_id": "289b3d4c0e96911312fdaa6e43eeebd2520a5a6d0a18fe05bf627e50b9c8b0f7", "vendor_updated_at": "2025-10-03T20:47:24.058606000+00:00"}, "degree": 2803}, {"node_labels": ["Incident"], "properties": {"incident_created_at": "2023-08-25T16:49:47.000000000+00:00", "incident_id": 148, "incident_name": "2023 Idalia", "incident_status": "null", "incident_type": "<PERSON><PERSON>(s)", "incident_updated_at": "2023-08-28T11:48:40.000000000+00:00"}, "degree": 2128}, {"node_labels": ["Mission"], "properties": {"id": 124836, "incidents": "[{\"incident_id\": 88, \"incident_name\": \"COVID-19 Public Health Emergency\", \"incident_status\": \"Active\", \"incident_type\": \"null\", \"incident_created_at\": \"2020-09-01T20:18:24\", \"incident_updated_at\": \"2022-04-11T18:40:56\"}]", "mission_number": "13678", "title": "Deployment of EREC Team to SLRC ", "updated_at": "2020-10-16T12:16:04.000000000+00:00"}, "degree": 1466}, {"node_labels": ["Incident"], "properties": {"incident_created_at": "2024-08-01T16:18:18.000000000+00:00", "incident_id": 170, "incident_name": "2024 Hurricane <PERSON><PERSON>", "incident_status": "Active", "incident_type": "Hurricane", "incident_updated_at": "2024-08-15T12:29:14.000000000+00:00"}, "degree": 1349}, {"node_labels": ["Incident"], "properties": {"incident_created_at": "2020-11-24T17:07:44.000000000+00:00", "incident_id": 76, "incident_name": "2019 Dorian", "incident_status": "Active", "incident_type": "Hurricane", "incident_updated_at": "2021-04-23T20:39:03.000000000+00:00"}, "degree": 1322}, {"node_labels": ["Mission"], "properties": {"id": 290514, "incidents": "[{\"incident_id\": 88, \"incident_name\": \"COVID-19 Public Health Emergency\", \"incident_status\": \"Active\", \"incident_type\": \"null\", \"incident_created_at\": \"2020-09-01T20:18:24\", \"incident_updated_at\": \"2022-04-11T18:40:56\"}]", "mission_number": "36742", "title": "Region 1/2 IMT for LTC vaccinations", "updated_at": "2021-07-12T09:03:43.000000000+00:00"}, "degree": 1249}], "multiLabelNodes": [], "nodeLabels": [{"label": "Mission", "count": 79840}, {"label": "Comment", "count": 69026}, {"label": "FuelTransaction", "count": 2798}, {"label": "FuelBurnHourly", "count": 362}, {"label": "EEIStatus", "count": 134}, {"label": "County", "count": 69}, {"label": "<PERSON><PERSON><PERSON>", "count": 63}, {"label": "Incident", "count": 39}, {"label": "FuelBurnDaily", "count": 26}, {"label": "FuelBurnWeekly", "count": 12}], "relationshipTypes": [{"source_label": ["Mission"], "relationship_type": "RELATED_TO_INCIDENT", "target_label": ["Incident"], "count": 75143}, {"source_label": ["Comment"], "relationship_type": "COMMENTED_ON", "target_label": ["Mission"], "count": 62240}, {"source_label": ["Mission"], "relationship_type": "IS_PARENT_TO", "target_label": ["Mission"], "count": 24905}, {"source_label": ["FuelTransaction"], "relationship_type": "FROM_VENDOR", "target_label": ["<PERSON><PERSON><PERSON>"], "count": 2798}, {"source_label": ["Incident"], "relationship_type": "HAS_HOURLY_FUEL_BURN", "target_label": ["FuelBurnHourly"], "count": 362}, {"source_label": ["County"], "relationship_type": "HAD_STATUS", "target_label": ["EEIStatus"], "count": 196}, {"source_label": ["County"], "relationship_type": "HAS_CURRENT_STATUS", "target_label": ["EEIStatus"], "count": 134}, {"source_label": ["Incident"], "relationship_type": "HAS_DAILY_FUEL_BURN", "target_label": ["FuelBurnDaily"], "count": 26}, {"source_label": ["Incident"], "relationship_type": "HAS_WEEKLY_FUEL_BURN", "target_label": ["FuelBurnWeekly"], "count": 12}, {"source_label": ["Mission"], "relationship_type": "ASSIGNED_VENDOR", "target_label": ["<PERSON><PERSON><PERSON>"], "count": 5}, {"source_label": ["Incident"], "relationship_type": "AFFECTS", "target_label": ["County"], "count": 4}], "labelProperties": [{"label": "County", "props": ["county_name", "region", "latitude", "longitude"]}, {"label": "FuelBurnWeekly", "props": ["incident_name", "total_fuel_dispensed", "fuel_type", "burn_weekly_id", "depot_name", "week_of_year", "lat", "long", "avg_fuel_per_transaction", "transactions_in_week"]}, {"label": "EEIStatus", "props": ["updated_at", "county_name", "region", "eei_history_id", "eoc_activation", "evacuation_status", "lse_status", "govt_status", "isActive"]}, {"label": "FuelBurnDaily", "props": ["incident_name", "total_fuel_dispensed", "fuel_type", "burn_daily_id", "depot_name", "day_start", "lat", "long", "avg_fuel_per_transaction", "transactions_in_day"]}, {"label": "Incident", "props": ["incident_id", "incident_name", "incident_type", "incident_status", "incident_created_at", "incident_updated_at"]}, {"label": "FuelBurnHourly", "props": ["incident_name", "total_fuel_dispensed", "fuel_type", "burn_hourly_id", "depot_name", "hour_start", "lat", "long", "avg_fuel_per_transaction"]}, {"label": "<PERSON><PERSON><PERSON>", "props": ["vendor_id", "mission_vendor_name", "vendor_updated_at"]}, {"label": "Comment", "props": ["comment_id", "text", "created_at", "created_by"]}, {"label": "FuelTransaction", "props": ["fuel_transaction_id", "fuel_type", "fuel_vendor", "county", "date_of_fueling", "address", "amount_dispensed"]}, {"label": "Mission", "props": ["id", "mission_number", "title", "incidents", "updated_at"]}], "propertyValueSamples": [{"label": "Mission", "property": "incidents", "sampleValues": ["[{\"incident_id\": null, \"incident_name\": null, \"incident_status\": null, \"incident_type\": null, \"incident_created_at\": null, \"incident_updated_at\": null}]", "[{\"incident_id\": 141, \"incident_name\": null, \"incident_status\": null, \"incident_type\": null, \"incident_created_at\": null, \"incident_updated_at\": null}]", "[{\"incident_id\": 98, \"incident_name\": \"2020 <PERSON>\", \"incident_status\": \"Inactive\", \"incident_type\": \"null\", \"incident_created_at\": \"2020-09-17T12:51:21\", \"incident_updated_at\": \"2021-04-23T20:39:03\"}]", "[{\"incident_id\": 165, \"incident_name\": \"2024 South FL Flooding\", \"incident_status\": \"Active\", \"incident_type\": \"Flood\", \"incident_created_at\": \"2024-06-12T21:02:26\", \"incident_updated_at\": \"2024-06-14T15:16:19\"}]", "[{\"incident_id\": 162, \"incident_name\": \"2024 May Severe Weather\", \"incident_status\": \"Active\", \"incident_type\": \"Severe Storm(s)\", \"incident_created_at\": \"2024-05-10T21:09:54\", \"incident_updated_at\": \"2024-05-10T21:10:36\"}]"]}, {"label": "Mission", "property": "updated_at", "sampleValues": ["2022-04-22T10:42:53.000000000+00:00", "2022-04-22T10:44:53.000000000+00:00", "2023-04-20T09:15:56.000000000+00:00", "2020-09-30T16:59:30.000000000+00:00", "2024-06-13T16:48:39.000000000+00:00"]}, {"label": "Mission", "property": "title", "sampleValues": ["Test 1", "2625-RSA-12116: Type 3 All-Hazards Incident Management Team (AHIMT)", "Activate FLNG", "Broward County request for pumps", "Debris removal and monitoring "]}, {"label": "Mission", "property": "mission_number", "sampleValues": ["", "00001", "00002", "00003", "00004"]}, {"label": "Mission", "property": "id", "sampleValues": [395452, 395455, 465952, 267996, 503639]}], "importantPropertyPatterns": [{"labels": ["Incident"], "properties": {"incident_created_at": "2022-11-15T17:48:36.000000000+00:00", "incident_id": 135, "incident_name": "2022 Nicole", "incident_status": "null", "incident_type": "<PERSON><PERSON>(s)", "incident_updated_at": "2023-08-23T16:15:39.000000000+00:00"}, "important_props": ["incident_id", "incident_name", "incident_type", "incident_status", "incident_created_at", "incident_updated_at"]}, {"labels": ["Incident"], "properties": {"incident_created_at": "2021-04-20T04:17:23.000000000+00:00", "incident_id": 107, "incident_name": "2021 Turkey Point Nuclear Power Plant Evaluated Exercise", "incident_status": "null", "incident_type": "null", "incident_updated_at": "2021-04-23T20:40:42.000000000+00:00"}, "important_props": ["incident_id", "incident_name", "incident_type", "incident_status", "incident_created_at", "incident_updated_at"]}, {"labels": ["Incident"], "properties": {"incident_created_at": "2022-10-24T16:40:53.000000000+00:00", "incident_id": 131, "incident_name": "2022 EMAC Kentucky Flooding", "incident_status": "Active", "incident_type": "Flood", "incident_updated_at": "2022-10-24T16:49:41.000000000+00:00"}, "important_props": ["incident_id", "incident_name", "incident_type", "incident_status", "incident_created_at", "incident_updated_at"]}, {"labels": ["Incident"], "properties": {"incident_created_at": "2021-04-20T04:17:23.000000000+00:00", "incident_id": 109, "incident_name": "2021 Piney Point Incident", "incident_status": "null", "incident_type": "null", "incident_updated_at": "2021-04-23T20:40:42.000000000+00:00"}, "important_props": ["incident_id", "incident_name", "incident_type", "incident_status", "incident_created_at", "incident_updated_at"]}, {"labels": ["Incident"], "properties": {"incident_created_at": "2024-08-01T16:18:18.000000000+00:00", "incident_id": 170, "incident_name": "2024 Hurricane <PERSON><PERSON>", "incident_status": "Active", "incident_type": "Hurricane", "incident_updated_at": "2024-08-15T12:29:14.000000000+00:00"}, "important_props": ["incident_id", "incident_name", "incident_type", "incident_status", "incident_created_at", "incident_updated_at"]}, {"labels": ["Incident"], "properties": {"incident_created_at": "2024-03-26T22:03:15.000000000+00:00", "incident_id": 158, "incident_name": "2024 Haiti Humanitarian Relief", "incident_status": "null", "incident_type": "Crisis", "incident_updated_at": "2024-03-27T19:24:31.000000000+00:00"}, "important_props": ["incident_id", "incident_name", "incident_type", "incident_status", "incident_created_at", "incident_updated_at"]}, {"labels": ["Incident"], "properties": {"incident_created_at": "2023-05-05T13:56:31.000000000+00:00", "incident_id": 142, "incident_name": "Broward County Flooding", "incident_status": "null", "incident_type": "Flood", "incident_updated_at": "2023-08-23T16:16:04.000000000+00:00"}, "important_props": ["incident_id", "incident_name", "incident_type", "incident_status", "incident_created_at", "incident_updated_at"]}, {"labels": ["Incident"], "properties": {"incident_created_at": "2023-10-12T23:01:37.000000000+00:00", "incident_id": 149, "incident_name": "2023 Israel Evacuation", "incident_status": "Active", "incident_type": "Crisis", "incident_updated_at": "2023-10-14T13:53:58.000000000+00:00"}, "important_props": ["incident_id", "incident_name", "incident_type", "incident_status", "incident_created_at", "incident_updated_at"]}, {"labels": ["Incident"], "properties": {"incident_created_at": "2025-09-30T13:59:50.000000000+00:00", "incident_id": 180, "incident_name": "2025 Kramer", "incident_status": "Active", "incident_type": "Hurricane", "incident_updated_at": "2025-09-30T13:59:50.000000000+00:00"}, "important_props": ["incident_id", "incident_name", "incident_type", "incident_status", "incident_created_at", "incident_updated_at"]}, {"labels": ["Incident"], "properties": {"incident_created_at": "2021-07-06T21:48:07.000000000+00:00", "incident_id": 115, "incident_name": "2021 Surfside Building Incident", "incident_status": "Active", "incident_type": "null", "incident_updated_at": "2021-07-06T21:56:03.000000000+00:00"}, "important_props": ["incident_id", "incident_name", "incident_type", "incident_status", "incident_created_at", "incident_updated_at"]}], "sampleData": {"nodeSamples": [{"label": "County", "samples": [{"properties": {"county_name": "Orange County", "latitude": 28.347097396850586, "longitude": -81.65726470947266, "region": "Region 5"}}, {"properties": {"county_name": "Sumter County", "latitude": 28.305320739746094, "longitude": -81.98577880859375, "region": "Region 5"}}, {"properties": {"county_name": "Brevard County", "latitude": 27.822057723999023, "longitude": -80.50907135009766, "region": "Region 5"}}]}, {"label": "FuelBurnWeekly", "samples": [{"properties": {"avg_fuel_per_transaction": 13.762666660944621, "burn_weekly_id": "ebc3b7008631a6519afaea64660fef9d707e88ac5b36073e9ac9fa7832f6d5d1", "depot_name": "Steinhatchee Public Fueling", "fuel_type": "Diesel", "incident_name": "2024 <PERSON><PERSON>", "lat": 29.67159080505371, "long": -83.38910675048828, "total_fuel_dispensed": 2064.399999141693, "transactions_in_week": 150, "week_of_year": 40}}, {"properties": {"avg_fuel_per_transaction": 21.79166681236691, "burn_weekly_id": "ea384ec32b1fcadd9e232bfcfd09698d288e4f82e5435a465c9b4e13b4e24b40", "depot_name": "Steinhatchee Public Fueling", "fuel_type": "Diesel", "incident_name": "2024 <PERSON><PERSON>", "lat": 29.67159080505371, "long": -83.38910675048828, "total_fuel_dispensed": 784.5000052452087, "transactions_in_week": 36, "week_of_year": 39}}, {"properties": {"avg_fuel_per_transaction": 13.585133797704168, "burn_weekly_id": "9db0aad1fa170d1cc97bf4580ab6117a97e00cde36c510263a388ae4b4be4226", "depot_name": "Steinhatchee Public Fueling", "fuel_type": "MoGas", "incident_name": "2024 <PERSON><PERSON>", "lat": 29.67159080505371, "long": -83.38910675048828, "total_fuel_dispensed": 13707.400001883507, "transactions_in_week": 1009, "week_of_year": 40}}]}, {"label": "EEIStatus", "samples": [{"properties": {"county_name": "Liberty County", "eei_history_id": "aea92132c4cbeb263e6ac2bf6c183b5d81737f179f21efdc5863739672f0f470", "eoc_activation": "Normal Operations - Monitoring", "evacuation_status": "Not Reporting", "govt_status": "Not Reporting", "isActive": true, "lse_status": "Not Reporting", "region": "Region 2", "updated_at": "2025-11-05T20:56:16.036853000+00:00"}}, {"properties": {"county_name": "Osceola County", "eei_history_id": "0e17daca5f3e175f448bacace3bc0da47d0655a74c8dd0dc497a3afbdad95f1f", "eoc_activation": "Returned to Monitoring", "evacuation_status": "Information", "govt_status": "Reopened", "isActive": true, "lse_status": "Not Declared", "region": "Region 5", "updated_at": "2025-11-05T20:56:16.036853000+00:00"}}, {"properties": {"county_name": "St. Johns County", "eei_history_id": "6208ef0f7750c111548cf90b6ea1d0d0a66f6bff40dbef07cb45ec436263c7d6", "eoc_activation": "Normal Operations - Monitoring", "evacuation_status": "No Evacuations", "govt_status": "No Closures", "isActive": false, "lse_status": "Not Declared", "region": "Region 4", "updated_at": "2025-11-05T20:56:16.036853000+00:00"}}]}, {"label": "FuelBurnDaily", "samples": [{"properties": {"avg_fuel_per_transaction": 9.439999866485596, "burn_daily_id": "e525b067bc67783d5bc08484e5cca84a1d1d68c31bcf1dbf4c8f83e7ef96dbb7", "day_start": "2024-09-30T00:00:00.000000000+00:00", "depot_name": "Horseshoe Beach Public Fueling", "fuel_type": "Diesel", "incident_name": "2024 <PERSON><PERSON>", "lat": 29.439830780029297, "long": -83.2929916381836, "total_fuel_dispensed": 47.19999933242798, "transactions_in_day": 5}}, {"properties": {"avg_fuel_per_transaction": 13.385915480868917, "burn_daily_id": "45a63e415a860c19e55b75172cddff154fee24a6a0e934147a8a4c640772c988", "day_start": "2024-10-01T00:00:00.000000000+00:00", "depot_name": "Steinhatchee Public Fueling", "fuel_type": "Diesel", "incident_name": "2024 <PERSON><PERSON>", "lat": 29.67159080505371, "long": -83.38910675048828, "total_fuel_dispensed": 950.3999991416931, "transactions_in_day": 71}}, {"properties": {"avg_fuel_per_transaction": 11.1041666738413, "burn_daily_id": "813634cd21e10853681b38a06fcd9a23e836f4db0c9773b64a52afee2d9c51e6", "day_start": "2024-10-01T00:00:00.000000000+00:00", "depot_name": "Suwanee Public Fueling", "fuel_type": "MoGas", "incident_name": "2024 <PERSON><PERSON>", "lat": 29.32977294921875, "long": -83.14508056640625, "total_fuel_dispensed": 2398.5000015497208, "transactions_in_day": 216}}]}, {"label": "Incident", "samples": [{"properties": {"incident_created_at": "2020-12-28T16:39:59.000000000+00:00", "incident_id": 100, "incident_name": "2020 Eta", "incident_status": "Active", "incident_type": "null", "incident_updated_at": "2021-04-23T20:39:03.000000000+00:00"}}, {"properties": {"incident_created_at": "2024-09-24T13:59:50.000000000+00:00", "incident_id": 171, "incident_name": "2024 <PERSON><PERSON>", "incident_status": "Active", "incident_type": "Hurricane", "incident_updated_at": "2025-10-02T13:48:28.000000000+00:00"}}, {"properties": {"incident_created_at": "2021-04-20T04:17:23.000000000+00:00", "incident_id": 103, "incident_name": "2020 St. Lucie Nuclear Power Plant Ingestion Pathway Exercise", "incident_status": "null", "incident_type": "null", "incident_updated_at": "2021-04-23T20:40:42.000000000+00:00"}}]}, {"label": "FuelBurnHourly", "samples": [{"properties": {"avg_fuel_per_transaction": 11.700000286102295, "burn_hourly_id": "93add40c86c407fec39d88c581df7b4906b4c93d58505d43fae12bf12138374c", "depot_name": "Steinhatchee Public Fueling", "fuel_type": "MoGas", "hour_start": "2024-09-28T22:00:00.000000000+00:00", "incident_name": "2024 <PERSON><PERSON>", "lat": 29.67159, "long": -83.38911, "total_fuel_dispensed": 23.40000057220459}}, {"properties": {"avg_fuel_per_transaction": 21.88333336512248, "burn_hourly_id": "2f109257ce91c9e93a7ca0464eb89728b338165298af70894dc7c34300a1e99f", "depot_name": "Steinhatchee Public Fueling", "fuel_type": "MoGas", "hour_start": "2024-09-29T08:00:00.000000000+00:00", "incident_name": "2024 <PERSON><PERSON>", "lat": 29.67159, "long": -83.38911, "total_fuel_dispensed": 131.30000019073486}}, {"properties": {"avg_fuel_per_transaction": 11.224999785423279, "burn_hourly_id": "8aa489a033b7b578218de2dd6c2587e3d7e719bc8c56b05e942b05c4e2701249", "depot_name": "Steinhatchee Public Fueling", "fuel_type": "Diesel", "hour_start": "2024-09-30T11:00:00.000000000+00:00", "incident_name": "2024 <PERSON><PERSON>", "lat": 29.67159, "long": -83.38911, "total_fuel_dispensed": 44.899999141693115}}]}, {"label": "<PERSON><PERSON><PERSON>", "samples": [{"properties": {"mission_vendor_name": "SLS", "vendor_id": "07957569728f619bc7ad0d27d705de1962d8406505866add0e2490a8b3e11911", "vendor_updated_at": "2025-10-03T20:47:24.058606000+00:00"}}, {"properties": {"mission_vendor_name": "L3/Harris MFN II", "vendor_id": "0691bb41ac3703f50915e11290c68d7d7b57ddca6e28f37d683d9216551dd7a8", "vendor_updated_at": "2025-10-03T20:47:24.058606000+00:00"}}, {"properties": {"mission_vendor_name": "Longview Solutions Group", "vendor_id": "64a8acf82fe8f1f8db645b927ed6c0d56d065be71a0b98224ec4d922c5b6f441", "vendor_updated_at": "2025-10-03T20:47:24.058606000+00:00"}}]}, {"label": "Comment", "samples": [{"properties": {"comment_id": "comment_0", "created_at": "2025-06-10T14:58:09.000000000+00:00", "created_by": "<PERSON>", "text": "Please extend TAR for <PERSON> from October 2024 through June 2025."}}, {"properties": {"comment_id": "comment_1", "created_at": "2025-06-05T09:22:28.000000000+00:00", "created_by": "<PERSON><PERSON>", "text": "Order No. C586ED"}}, {"properties": {"comment_id": "comment_2", "created_at": "2025-06-03T17:11:01.000000000+00:00", "created_by": "<PERSON>", "text": "Mission on hold "}}]}, {"label": "FuelTransaction", "samples": [{"properties": {"address": "23103 SE 349 Hwy Suwannee FL32692", "amount_dispensed": "15.0", "county": "DIXIE COUNTY", "date_of_fueling": "2024-10-03T00:00:00.000000000+00:00", "fuel_transaction_id": "17975", "fuel_type": "MoGas", "fuel_vendor": "World Fuel Services"}}, {"properties": {"address": "Steinhatchee boat ramp 1st st. east Steinhatchee FL32359", "amount_dispensed": "25.8", "county": "TAYLOR COUNTY", "date_of_fueling": "2024-09-29T00:00:00.000000000+00:00", "fuel_transaction_id": "14769", "fuel_type": "MoGas", "fuel_vendor": "World Fuel Services"}}, {"properties": {"address": "Steinhatchee boat ramp 1st st. east Steinhatchee FL32359", "amount_dispensed": "14.8", "county": "TAYLOR COUNTY", "date_of_fueling": "2024-09-30T00:00:00.000000000+00:00", "fuel_transaction_id": "15862", "fuel_type": "MoGas", "fuel_vendor": "World Fuel Services"}}]}, {"label": "Mission", "samples": [{"properties": {"id": 395452, "incidents": "[{\"incident_id\": null, \"incident_name\": null, \"incident_status\": null, \"incident_type\": null, \"incident_created_at\": null, \"incident_updated_at\": null}]", "mission_number": "", "title": "Test 1", "updated_at": "2022-04-22T10:42:53.000000000+00:00"}}, {"properties": {"id": 395455, "incidents": "[{\"incident_id\": null, \"incident_name\": null, \"incident_status\": null, \"incident_type\": null, \"incident_created_at\": null, \"incident_updated_at\": null}]", "mission_number": "", "title": "Test 1", "updated_at": "2022-04-22T10:44:53.000000000+00:00"}}, {"properties": {"id": 465952, "incidents": "[{\"incident_id\": 141, \"incident_name\": null, \"incident_status\": null, \"incident_type\": null, \"incident_created_at\": null, \"incident_updated_at\": null}]", "mission_number": "00001", "title": "2625-RSA-12116: Type 3 All-Hazards Incident Management Team (AHIMT)", "updated_at": "2023-04-20T09:15:56.000000000+00:00"}}]}], "relationshipSamples": [{"type": "HAD_STATUS", "samples": [{"properties": {"from_date": "2025-07-04T18:02:39.000000000+00:00"}, "source_labels": ["County"], "target_labels": ["EEIStatus"]}, {"properties": {"from_date": "2025-07-04T18:02:39.000000000+00:00"}, "source_labels": ["County"], "target_labels": ["EEIStatus"]}, {"properties": {"from_date": "2025-07-04T18:02:39.000000000+00:00", "to_date": "2025-07-04T18:02:39.000000000+00:00"}, "source_labels": ["County"], "target_labels": ["EEIStatus"]}]}, {"type": "HAS_CURRENT_STATUS", "samples": [{"properties": {}, "source_labels": ["County"], "target_labels": ["EEIStatus"]}, {"properties": {}, "source_labels": ["County"], "target_labels": ["EEIStatus"]}, {"properties": {}, "source_labels": ["County"], "target_labels": ["EEIStatus"]}]}, {"type": "FROM_VENDOR", "samples": [{"properties": {}, "source_labels": ["FuelTransaction"], "target_labels": ["<PERSON><PERSON><PERSON>"]}, {"properties": {}, "source_labels": ["FuelTransaction"], "target_labels": ["<PERSON><PERSON><PERSON>"]}, {"properties": {}, "source_labels": ["FuelTransaction"], "target_labels": ["<PERSON><PERSON><PERSON>"]}]}, {"type": "AFFECTS", "samples": [{"properties": {}, "source_labels": ["Incident"], "target_labels": ["County"]}, {"properties": {}, "source_labels": ["Incident"], "target_labels": ["County"]}, {"properties": {}, "source_labels": ["Incident"], "target_labels": ["County"]}]}, {"type": "HAS_WEEKLY_FUEL_BURN", "samples": [{"properties": {}, "source_labels": ["Incident"], "target_labels": ["FuelBurnWeekly"]}, {"properties": {}, "source_labels": ["Incident"], "target_labels": ["FuelBurnWeekly"]}, {"properties": {}, "source_labels": ["Incident"], "target_labels": ["FuelBurnWeekly"]}]}, {"type": "HAS_HOURLY_FUEL_BURN", "samples": [{"properties": {}, "source_labels": ["Incident"], "target_labels": ["FuelBurnHourly"]}, {"properties": {}, "source_labels": ["Incident"], "target_labels": ["FuelBurnHourly"]}, {"properties": {}, "source_labels": ["Incident"], "target_labels": ["FuelBurnHourly"]}]}, {"type": "ASSIGNED_VENDOR", "samples": [{"properties": {}, "source_labels": ["Mission"], "target_labels": ["<PERSON><PERSON><PERSON>"]}, {"properties": {}, "source_labels": ["Mission"], "target_labels": ["<PERSON><PERSON><PERSON>"]}, {"properties": {}, "source_labels": ["Mission"], "target_labels": ["<PERSON><PERSON><PERSON>"]}]}, {"type": "HAS_DAILY_FUEL_BURN", "samples": [{"properties": {}, "source_labels": ["Incident"], "target_labels": ["FuelBurnDaily"]}, {"properties": {}, "source_labels": ["Incident"], "target_labels": ["FuelBurnDaily"]}, {"properties": {}, "source_labels": ["Incident"], "target_labels": ["FuelBurnDaily"]}]}, {"type": "RELATED_TO_INCIDENT", "samples": [{"properties": {}, "source_labels": ["Mission"], "target_labels": ["Incident"]}, {"properties": {}, "source_labels": ["Mission"], "target_labels": ["Incident"]}, {"properties": {}, "source_labels": ["Mission"], "target_labels": ["Incident"]}]}, {"type": "COMMENTED_ON", "samples": [{"properties": {}, "source_labels": ["Comment"], "target_labels": ["Mission"]}, {"properties": {}, "source_labels": ["Comment"], "target_labels": ["Mission"]}, {"properties": {}, "source_labels": ["Comment"], "target_labels": ["Mission"]}]}, {"type": "IS_PARENT_TO", "samples": [{"properties": {}, "source_labels": ["Mission"], "target_labels": ["Mission"]}, {"properties": {}, "source_labels": ["Mission"], "target_labels": ["Mission"]}, {"properties": {}, "source_labels": ["Mission"], "target_labels": ["Mission"]}]}]}, "ontology": {"coreConcepts": ["Mission", "Incident", "Comment", "<PERSON><PERSON><PERSON>", "FuelTransaction", "FuelBurnHourly", "County", "EEIStatus", "FuelBurnDaily", "FuelBurnWeekly"], "hierarchicalRelationships": [{"source": "Mission", "type": "IS_PARENT_TO", "target": "Mission"}], "entityTypes": ["Mission", "Comment", "FuelTransaction", "FuelBurnHourly", "EEIStatus", "County", "<PERSON><PERSON><PERSON>", "Incident", "FuelBurnDaily", "FuelBurnWeekly"]}, "businessContext": {"operationalModel": "Mission Critical", "keyProcesses": ["RELATED_TO_INCIDENT", "COMMENTED_ON", "IS_PARENT_TO", "FROM_VENDOR", "HAS_HOURLY_FUEL_BURN", "HAD_STATUS", "HAS_CURRENT_STATUS", "HAS_DAILY_FUEL_BURN", "HAS_WEEKLY_FUEL_BURN", "ASSIGNED_VENDOR"], "keyEntities": ["Mission", "Incident", "Comment", "<PERSON><PERSON><PERSON>", "FuelTransaction", "FuelBurnHourly", "County", "EEIStatus", "FuelBurnDaily", "FuelBurnWeekly"], "totalEntityTypes": 10, "graphMetrics": {"nodeCount": 152369, "relationshipCount": 165825, "relationshipTypes": 11, "averageNodeDegree": 2.18}}}
"""
Embedding generation helper functions

Convenience functions for generating embeddings without managing client directly.
"""

import os
import logging
from typing import List, Dict, Any, Union
from .factory import create_embedding_client
from .onnx_client import OnnxEmbeddingClient
from .azure_client import AzureEmbeddingClient

logger = logging.getLogger(__name__)

# Singleton client instance
_embedding_client: Union[OnnxEmbeddingClient, AzureEmbeddingClient, None] = None


def get_embedding_client() -> Union[OnnxEmbeddingClient, AzureEmbeddingClient]:
    """
    Get or create singleton embedding client

    Uses factory to create appropriate client based on LOCAL_EMBEDDING configuration.

    Returns:
        OnnxEmbeddingClient or AzureEmbeddingClient instance

    Raises:
        RuntimeError: If vector search is not enabled
    """
    global _embedding_client

    if _embedding_client is None:
        enabled = os.getenv("ENABLE_VECTOR_SEARCH", "false").lower() == "true"

        if not enabled:
            raise RuntimeError(
                "Vector search is not enabled. Set ENABLE_VECTOR_SEARCH=true in .env"
            )

        _embedding_client = create_embedding_client()
        logger.info("Initialized singleton embedding client via factory")

    return _embedding_client


def generate_embedding(text: str, include_metadata: bool = True) -> Dict[str, Any]:
    """
    Generate embedding for a single text

    Args:
        text: Input text to embed
        include_metadata: Include embedding metadata

    Returns:
        Dictionary with 'embedding' and optionally metadata fields
    """
    client = get_embedding_client()
    return client.generate_embedding(text, include_metadata=include_metadata)


def generate_embeddings_batch(
    texts: List[str],
    include_metadata: bool = True
) -> List[Dict[str, Any]]:
    """
    Generate embeddings for multiple texts

    Args:
        texts: List of input texts to embed
        include_metadata: Include embedding metadata

    Returns:
        List of dictionaries with 'embedding' and optionally metadata fields
    """
    client = get_embedding_client()
    return client.generate_embeddings_batch(texts, include_metadata=include_metadata)

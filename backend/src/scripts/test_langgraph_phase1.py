#!/usr/bin/env python
"""
Integration test script for Phase 1 LangGraph implementation.

Tests the parallel MCP calls workflow with different query types.

Usage:
    cd /Users/<USER>/Repos/DEMES/assistant/backend/src
    uv run python scripts/test_langgraph_phase1.py
"""

import asyncio
import time
import sys
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.core.config import settings
from app.dependencies import get_mcp_manager
from app.services.langgraph_service import LangGraphService


async def test_parallel_execution():
    """Test that parallel MCP calls work correctly."""
    print("🧪 Testing LangGraph Phase 1: Parallel MCP Calls")
    print("=" * 60)

    # Initialize MCP manager
    print("\n1️⃣  Initializing MCP Manager...")
    mcp_manager = get_mcp_manager()

    try:
        await mcp_manager.initialize()
        print(f"   ✓ {len(mcp_manager._all_tools)} tools available")
    except Exception as e:
        print(f"   ✗ MCP initialization failed: {e}")
        print("   Make sure Genie and GraphRAG services are configured in .env")
        return False

    # Create LangGraph service
    print("\n2️⃣  Creating LangGraph service...")
    langgraph_service = LangGraphService(mcp_manager)
    print("   ✓ Service created")

    # Test cases
    test_queries = [
        {
            "name": "Parallel query (should call both services)",
            "query": "What's the status of Hurricane Idalia and its impact on evacuation routes?",
            "expected_routing": "parallel"
        },
        {
            "name": "Genie only (incident data)",
            "query": "What shelters are currently open?",
            "expected_routing": "genie_only"
        },
        {
            "name": "Graph only (relationships)",
            "query": "Show me relationships between emergency entities",
            "expected_routing": "graph_only"
        },
        {
            "name": "Conversational (no MCP)",
            "query": "Hello, how can you help me?",
            "expected_routing": "none"
        }
    ]

    all_passed = True

    for test_case in test_queries:
        test_name = test_case["name"]
        query = test_case["query"]
        expected_routing = test_case["expected_routing"]

        print(f"\n3️⃣  Testing: {test_name}")
        print(f"   Query: {query[:70]}...")

        start_time = time.time()

        messages = [{"role": "user", "content": query}]

        # Execute workflow and collect stream
        response_chunks = []
        reasoning_steps = []
        sources = []

        try:
            async for stream_msg in langgraph_service.generate_response_with_streaming(messages, "test-123"):
                if stream_msg.type.value == "reasoning":
                    step = stream_msg.data.get('step', '')
                    reasoning_steps.append(step)
                    print(f"   🧠 {step}")
                elif stream_msg.type.value == "sources":
                    source = stream_msg.data.get('source', '')
                    sources.append(source)
                    print(f"   📚 {source}")
                elif stream_msg.type.value == "content":
                    response_chunks.append(stream_msg.data.get("chunk", ""))

            elapsed = time.time() - start_time
            response = "".join(response_chunks)

            print(f"   ⏱️  Completed in {elapsed:.2f}s")
            print(f"   💬 Response: {response[:100]}...")

            # Validate routing
            # For now, we'll just check that we got a response
            # In a more sophisticated test, we'd check the routing decision
            if response:
                print("   ✓ Test passed - got response")
            else:
                print("   ✗ Test failed - no response")
                all_passed = False

        except Exception as e:
            print(f"   ✗ Test failed with error: {e}")
            import traceback
            traceback.print_exc()
            all_passed = False

    print("\n" + "=" * 60)
    if all_passed:
        print("✅ All Phase 1 tests passed!")
        return True
    else:
        print("❌ Some tests failed - see details above")
        return False


async def main():
    """Main entry point."""
    success = await test_parallel_execution()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    asyncio.run(main())

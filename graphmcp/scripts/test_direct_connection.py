"""
Test direct connection to Memgraph from host machine

This verifies we can connect to Memgraph at localhost:7687
"""
import os
import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent.parent))

from dotenv import load_dotenv
from neo4j import GraphDatabase

load_dotenv()

def test_connection():
    """Test direct Memgraph connection"""

    print("Testing Memgraph connection...")
    print(f"Host: {os.getenv('MEMGRAPH_HOST', 'localhost')}")
    print(f"Port: {os.getenv('MEMGRAPH_PORT', '7687')}")

    host = os.getenv("MEMGRAPH_HOST", "localhost")
    port = int(os.getenv("MEMGRAPH_PORT", "7687"))

    uri = f"bolt://{host}:{port}"

    try:
        print(f"\nConnecting to {uri}...")
        driver = GraphDatabase.driver(uri, auth=None)

        with driver.session() as session:
            result = session.run("RETURN 1 as test")
            record = result.single()
            print(f"✓ Connection successful! Test query returned: {record['test']}")

            # Get node count
            result = session.run("MATCH (n) RETURN count(n) as count")
            count = result.single()['count']
            print(f"✓ Graph has {count} nodes")

            # Get labels
            result = session.run("MATCH (n) RETURN DISTINCT labels(n)[0] as type, count(n) as count ORDER BY count DESC LIMIT 5")
            print(f"\nTop entity types:")
            for record in result:
                print(f"  {record['type']}: {record['count']} nodes")

        driver.close()
        return True

    except Exception as e:
        print(f"✗ Connection failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_connection()
    sys.exit(0 if success else 1)

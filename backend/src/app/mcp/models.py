"""
Shared Pydantic models for MCP module.
"""

from typing import Dict, Any, Optional, List
from pydantic import BaseModel, ConfigDict, Field
from dataclasses import dataclass


@dataclass
class MCPServerConfig:
    """Configuration for an MCP server."""
    name: str
    url: str
    description: str = ""
    enabled: bool = True
    namespace: str = ""  # Prefix for tools from this server


class MCPToolDefinition(BaseModel):
    """Definition of an MCP tool."""
    model_config = ConfigDict(extra="allow")
    
    name: str = Field(..., description="Tool name")
    description: str = Field("", description="Tool description")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Tool parameters schema")
    server: Optional[str] = Field(None, description="Source MCP server name")


class MCPToolResult(BaseModel):
    """Result from an MCP tool execution."""
    model_config = ConfigDict(extra="allow")
    
    success: bool = Field(..., description="Whether the tool executed successfully")
    content: List[Dict[str, Any]] = Field(default_factory=list, description="Tool result content")
    error: Optional[str] = Field(None, description="Error message if failed")
    isError: bool = Field(False, description="Whether this is an error response")


class MCPToolCall(BaseModel):
    """Request to call an MCP tool."""
    tool_name: str = Field(..., description="Name of the tool to call")
    arguments: Dict[str, Any] = Field(default_factory=dict, description="Tool arguments")
    server: Optional[str] = Field(None, description="Target MCP server (if specified)")
version: '3.8'

services:
  phoenix:
    image: arizephoenix/phoenix:latest  # Must be >= 4.0 for PostgreSQL support
    container_name: phoenix-server
    depends_on:
      - postgres
    ports:
      - "6006:6006"   # Phoenix UI
      - "4317:4317"   # OTLP gRPC collector
      - "9090:9090"   # Prometheus metrics (optional)
    environment:
      - PHOENIX_SQL_DATABASE_URL=***************************************************/phoenix
      - PHOENIX_PORT=6006
      - PHOENIX_GRPC_PORT=4317
      - PHOENIX_HOST=0.0.0.0
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6006/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped

  postgres:
    image: postgres:15
    container_name: phoenix-postgres
    environment:
      - POSTGRES_USER=phoenix
      - POSTGRES_PASSWORD=phoenix_password
      - POSTGRES_DB=phoenix
    ports:
      - "5432:5432"  # Exposed for debugging/direct access
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro  # Optional: custom initialization
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U phoenix -d phoenix"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
    # Data persists in Docker-managed volume
    # Located at: /var/lib/docker/volumes/phoenix_postgres_data/_data

networks:
  default:
    name: phoenix-network
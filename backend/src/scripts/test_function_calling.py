#!/usr/bin/env python
"""
Quick test script to verify LangGraph function calling integration.

Tests that:
1. Workflow compiles successfully
2. LLM sees available tools
3. Tool calls are executed
4. Final response is generated

Usage:
    cd /Users/<USER>/Repos/DEMES/assistant/backend/src
    uv run python scripts/test_function_calling.py
"""

import asyncio
import sys
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.core.config import settings
from app.dependencies import get_mcp_manager
from app.services.langgraph_service import LangGraphService


async def main():
    """Test function calling integration."""
    print("=" * 60)
    print("🧪 Testing LangGraph Function Calling")
    print("=" * 60)

    # Initialize MCP manager
    print("\n1️⃣ Initializing MCP Manager...")
    mcp_manager = get_mcp_manager()
    await mcp_manager.initialize()
    print(f"   ✓ {len(mcp_manager._all_tools)} tools available")

    # Show available tools
    print("\n   Available tools:")
    for tool in mcp_manager._all_tools[:5]:  # Show first 5
        print(f"      - {tool['name']}: {tool['description'][:60]}...")
    if len(mcp_manager._all_tools) > 5:
        print(f"      ... and {len(mcp_manager._all_tools) - 5} more")

    # Create LangGraph service
    print("\n2️⃣ Creating LangGraph service...")
    langgraph_service = LangGraphService(mcp_manager)
    print("   ✓ Service created")

    # Test query that should trigger tool calling
    test_query = "What are the current emergency incidents?"

    print(f"\n3️⃣ Testing with query: '{test_query}'")
    messages = [{"role": "user", "content": test_query}]

    # Execute workflow
    print("\n   Workflow execution:")
    response_chunks = []
    tools_called = []

    try:
        async for stream_msg in langgraph_service.generate_response_with_streaming(messages, "test-func-call"):
            msg_type = stream_msg.type.value

            if msg_type == "reasoning":
                step = stream_msg.data.get('step', '')
                print(f"   🧠 {step}")
            elif msg_type == "sources":
                source = stream_msg.data.get('source', '')
                print(f"   📚 Source: {source}")
                tools_called.append(source)
            elif msg_type == "content":
                response_chunks.append(stream_msg.data.get("chunk", ""))

        response = "".join(response_chunks)

        print("\n✅ Test Complete!")
        print(f"\n   Tools called: {len(tools_called)}")
        if tools_called:
            for tool in tools_called:
                print(f"      - {tool}")
        else:
            print("      - None (LLM decided no tools needed)")

        print(f"\n   Response length: {len(response)} chars")
        print(f"\n   Response preview: {response[:200]}...")

        # Verify function calling worked
        if len(tools_called) > 0:
            print("\n🎉 SUCCESS: Function calling is working!")
            print("   LLM saw the tools and decided to use them.")
            return True
        else:
            print("\n⚠️  WARNING: No tools were called")
            print("   LLM may have decided tools weren't needed for this query.")
            print("   Try a more specific emergency query.")
            return False

    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

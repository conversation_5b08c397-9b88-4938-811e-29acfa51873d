# GraySkyGenAI Assistant - Backend

A production-ready FastAPI backend implementing **LangGraph agent orchestration** with Azure OpenAI function calling for intelligent tool usage. Features multi-type streaming, MCP server integration for external data access, and session context management.

> **✅ Current Status (2025-10-07)**: Production-ready with `langgraph_supervisor` library. All agents use `create_react_agent`, native token-by-token streaming, single clean responses.
>
> **Known Limitation**: Using manual node filtering for streaming (see [Technical Notes](#technical-notes))

## Table of Contents

- [Overview](#overview)
- [Architecture](#architecture)
- [Project Structure](#project-structure)
- [Entry Point and Application Flow](#entry-point-and-application-flow)
- [API Endpoints](#api-endpoints)
  - [Base Endpoints](#base-endpoints)
  - [Chat Endpoints](#chat-endpoints-apiv1chat)
  - [MCP Endpoints](#mcp-endpoints-apiv1mcp)
- [Multi-Type Streaming System](#multi-type-streaming-system)
- [Technical Notes](#technical-notes)
- [Development Setup](#development-setup)
- [Code Examples](#code-examples)
- [Configuration](#configuration)
- [Extending the Application](#extending-the-application)
- [Testing](#testing)
- [Documentation](#documentation)

## Overview

The backend is built with **FastAPI** and **LangGraph**, implementing a production-ready agent orchestration system with intelligent tool usage via Azure OpenAI function calling. It provides real-time streaming responses with multiple data types (status, reasoning, sources, content) and integrates with MCP servers for external data access.

### Key Features

- **Multi-Agent Architecture**: Using `langgraph_supervisor` library for automatic routing and synthesis
  - **Supervisor**: Automatically routes to specialized subagents and synthesizes results
  - **Genie Subagent**: Emergency incident data (4 MCP tools, recursion_limit=21)
  - **GraphRAG Subagent**: Graph database queries (18 MCP tools, recursion_limit=21)
  - All agents use `create_react_agent` for consistency
- **LangGraph Agent Orchestration**: State-of-the-art agent framework with parameterless workflows and dependency injection
- **Azure OpenAI Integration**: Native function calling for intelligent tool usage with gpt-4.1-mini
- **MCP Integration**: Model Context Protocol support for external data access (Genie emergency data + GraphRAG graph database)
  - 34 tools auto-discovered from 2 MCP servers
  - Official `langchain-mcp-adapters` integration
- **Multi-type Streaming**: STATUS, REASONING, SOURCES, CONTENT message types with proper ownership
- **Session Context Management**: In-memory conversation history across requests
- **Layered Architecture**: Clean separation of concerns with 6 distinct layers
- **Two-Layer Configuration**: Pydantic Settings + RuntimeContext defaults with clear precedence rules
- **Production Observability**: Phoenix tracing, structured logging, correlation IDs, performance tracking
- **CORS Support**: Configured for frontend development
- **Async/Await Throughout**: Built for high-performance concurrent operations

## Architecture

### Full Application Architecture

The application is built on a layered architecture with **LangGraph** as the agent orchestration framework:

```mermaid
graph TB
    subgraph "Client Layer"
        CLIENT[Frontend Client<br/>Next.js App]
    end

    subgraph "API Layer - FastAPI"
        MAIN[main.py<br/>FastAPI App Instance]
        CORS[CORS Middleware]
        ROUTER[API Router v1<br/>api/api_v1/api.py]
        CHAT_EP[Chat Endpoints<br/>endpoints/chat.py]
        MCP_EP[MCP Endpoints<br/>endpoints/mcp.py]
    end

    subgraph "Configuration Layer"
        SETTINGS[Settings<br/>core/config.py<br/>Pydantic BaseSettings]
        ENV_VARS[Environment Variables<br/>.env file]
        CONTEXT_DEFAULTS[RuntimeContext Defaults<br/>agent/langgraph/context.py]
    end

    subgraph "Orchestration Layer - LangGraph Service"
        LANGGRAPH_SVC[LangGraphService<br/>services/langgraph_service.py<br/><b>CORE ORCHESTRATOR</b>]
        RUNTIME_CTX[RuntimeContext<br/>Dependency Injection Container]
        WORKFLOW[LangGraph Workflow<br/>agent/langgraph/workflows.py]
    end

    subgraph "Agent Execution Layer - Multi-Agent Workflow"
        SUPERVISOR[Supervisor Agent<br/>langgraph_supervisor<br/>create_react_agent<br/>synthesis model @ 0.7<br/>tags: supervisor]
        GENIE_SUBAGENT[Genie Subagent<br/>create_react_agent<br/>4 MCP tools, recursion_limit=21<br/>tags: subagent, genie]
        GRAPHRAG_SUBAGENT[GraphRAG Subagent<br/>create_react_agent<br/>18 MCP tools, recursion_limit=21<br/>tags: subagent, graphrag]
    end

    subgraph "Tool Discovery & Execution"
        MCP_ADAPTER[MCPToolAdapter<br/>mcp/adapter.py<br/>Official langchain-mcp-adapters]
        MULTI_CLIENT[MultiServerMCPClient<br/>Manages Multiple MCP Servers]
        GENIE_SERVER[Genie MCP Server<br/>Azure Hosted<br/>Emergency Data]
        GRAPHRAG_SERVER[GraphRAG MCP Server<br/>Local/Azure<br/>Graph Database]
    end

    subgraph "LLM Provider"
        AZURE_OPENAI[Azure OpenAI<br/>AzureChatOpenAI<br/>Function Calling]
    end

    subgraph "Supporting Services"
        CONV_CTX[ConversationContext<br/>services/context.py<br/>Session Management]
        STREAMING[StreamMessage<br/>services/streaming.py<br/>Multi-type Streaming]
        LOGGING[Enhanced Logger<br/>core/logging.py<br/>Structured Logging]
        PHOENIX[Phoenix Tracing<br/>Observability]
    end

    %% Client to API Flow
    CLIENT -->|HTTP Request| MAIN
    MAIN --> CORS
    CORS --> ROUTER
    ROUTER --> CHAT_EP
    ROUTER --> MCP_EP

    %% Configuration Flow
    ENV_VARS -->|Loads| SETTINGS
    SETTINGS -->|Injects| CHAT_EP
    SETTINGS -->|Configures| LANGGRAPH_SVC
    CONTEXT_DEFAULTS -.->|Fallback Defaults| RUNTIME_CTX

    %% Orchestration Setup
    CHAT_EP -->|Calls| LANGGRAPH_SVC
    LANGGRAPH_SVC -->|Creates on First Request| RUNTIME_CTX
    LANGGRAPH_SVC -->|Discovers Tools| MCP_ADAPTER
    LANGGRAPH_SVC -->|Compiles| WORKFLOW
    LANGGRAPH_SVC -->|Injects| RUNTIME_CTX
    MCP_ADAPTER --> MULTI_CLIENT
    MULTI_CLIENT --> GENIE_SERVER
    MULTI_CLIENT --> GRAPHRAG_SERVER

    %% Workflow Execution - Supervisor Pattern
    RUNTIME_CTX -->|Contains Tools + Config| WORKFLOW
    WORKFLOW -->|Starts with| SUPERVISOR
    SUPERVISOR -->|Calls LLM| AZURE_OPENAI
    AZURE_OPENAI -->|Routing Decision| SUPERVISOR
    SUPERVISOR -->|Routes to| GENIE_SUBAGENT
    SUPERVISOR -->|Routes to| GRAPHRAG_SUBAGENT
    SUPERVISOR -->|Direct answer| CHAT_EP
    GENIE_SUBAGENT -->|Calls LLM + Tools| AZURE_OPENAI
    GENIE_SUBAGENT -->|Executes Tools| MULTI_CLIENT
    GRAPHRAG_SUBAGENT -->|Calls LLM + Tools| AZURE_OPENAI
    GRAPHRAG_SUBAGENT -->|Executes Tools| MULTI_CLIENT
    GENIE_SUBAGENT -->|Results| CHAT_EP
    GRAPHRAG_SUBAGENT -->|Results| CHAT_EP

    %% Supporting Services
    CHAT_EP --> CONV_CTX
    LANGGRAPH_SVC --> STREAMING
    STREAMING -->|SSE Stream| CLIENT
    LANGGRAPH_SVC --> LOGGING
    LANGGRAPH_SVC --> PHOENIX

    %% Styling
    style LANGGRAPH_SVC fill:#1565c0,color:#fff,stroke:#0d47a1,stroke-width:4px
    style WORKFLOW fill:#1976d2,color:#fff,stroke:#0d47a1,stroke-width:3px
    style RUNTIME_CTX fill:#42a5f5,color:#fff,stroke:#1976d2,stroke-width:3px
    style SUPERVISOR fill:#1565c0,color:#fff,stroke:#0d47a1,stroke-width:4px
    style GENIE_SUBAGENT fill:#00c853,color:#fff,stroke:#00a152,stroke-width:3px
    style GRAPHRAG_SUBAGENT fill:#f57c00,color:#fff,stroke:#e65100,stroke-width:3px
    style AZURE_OPENAI fill:#00c853,color:#fff,stroke:#00a152,stroke-width:3px
    style MCP_ADAPTER fill:#f57c00,color:#fff,stroke:#e65100,stroke-width:3px
    style GENIE_SERVER fill:#ff9800,color:#fff
    style GRAPHRAG_SERVER fill:#ff9800,color:#fff
    style SETTINGS fill:#9c27b0,color:#fff,stroke:#7b1fa2,stroke-width:3px
    style STREAMING fill:#e1f5fe
    style LOGGING fill:#fff3e0
    style PHOENIX fill:#f3e5f5
```

### Architecture Key Points

**🎯 LangGraphService: The Core Orchestrator**

You're absolutely correct! `LangGraphService` (`services/langgraph_service.py`) is the **abstraction layer that manages the entire agent**:

1. **Lifecycle Management**
   - Lazy initialization (tools/context created on first request)
   - Tool discovery from MCP servers
   - Workflow compilation
   - RuntimeContext creation and injection

2. **Configuration Bridge**
   - Reads from `Settings` (Pydantic config layer)
   - Combines with `RuntimeContext` defaults (dataclass layer)
   - Injects final configuration into LangGraph workflow

3. **Execution Orchestration**
   - Manages conversation context
   - Streams responses with multi-type messages
   - Handles errors and logging
   - Provides clean API for chat endpoints

**📊 Layer Responsibilities**

| Layer | Purpose | Key Files |
|-------|---------|-----------|
| **API Layer** | HTTP endpoints, request/response handling | `main.py`, `api/api_v1/`, `endpoints/` |
| **Configuration Layer** | Environment-specific settings, defaults | `core/config.py`, `agent/langgraph/context.py` |
| **Orchestration Layer** | Agent workflow management, tool discovery | `services/langgraph_service.py` |
| **Agent Execution Layer** | LangGraph nodes (agent, tools, routing) | `agent/langgraph/workflows.py` |
| **Tool Layer** | MCP integration, external data access | `mcp/adapter.py`, MCP servers |
| **LLM Layer** | Azure OpenAI function calling | LangChain AzureChatOpenAI |

**🔄 Request Flow (Multi-Agent Supervisor Pattern)**

1. **Client** sends chat message
2. **Chat Endpoint** receives request, gets settings
3. **LangGraphService** orchestrates (the key abstraction!):
   - Ensures tools are discovered (lazy load)
   - Creates RuntimeContext with config
   - Invokes LangGraph workflow
4. **LangGraph Workflow** executes multi-agent pattern:
   - **Supervisor Node**: LLM analyzes query and routes to appropriate subagent
   - **Genie Subagent**: Handles emergency incident queries (up to 2 iterations)
   - **GraphRAG Subagent**: Handles graph database queries (up to 10 iterations)
   - Each subagent iterates with its specialized tools until complete
5. **Streaming** returns multi-type messages (STATUS, REASONING, SOURCES, CONTENT)

**💡 Why This Architecture Works**

- **Clean Separation**: Each layer has clear responsibilities
- **Testability**: Can mock any layer independently
- **Extensibility**: Easy to add new tools, models, or endpoints
- **Production-Ready**: Lazy loading, proper error handling, observability

*Note: The Full Application Architecture diagram above reflects the current LangGraph-based architecture with agent orchestration.*

### Multi-Agent Supervisor Pattern

The system uses **`langgraph_supervisor`** library with all agents built using `create_react_agent`:

```mermaid
graph TB
    START([START]) --> SUPERVISOR[Supervisor Agent<br/>langgraph_supervisor.create_supervisor<br/>Model: synthesis @ temp 0.7<br/>Tags: supervisor]

    SUPERVISOR -->|Calls transfer_to_genie<br/>handoff tool| GENIE_ENTRY[Genie Subagent Entry<br/>create_react_agent<br/>Tags: subagent, genie]
    SUPERVISOR -->|Calls transfer_to_graphrag<br/>handoff tool| GRAPHRAG_ENTRY[GraphRAG Subagent Entry<br/>create_react_agent<br/>Tags: subagent, graphrag]
    SUPERVISOR -->|No handoff needed<br/>simple query| END_DIRECT([END<br/>Direct answer])

    GENIE_ENTRY --> GENIE_AGENT[Genie Agent Node<br/>LLM with 4 MCP tools<br/>Model: fast @ temp 0.0]
    GENIE_AGENT --> GENIE_ROUTER{Tool calls?<br/>Iteration < 10?}
    GENIE_ROUTER -->|Yes| GENIE_TOOLS[Execute Genie MCP Tools<br/>ask_genie, follow_up, etc.]
    GENIE_ROUTER -->|No| GENIE_RETURN[Return findings<br/>to supervisor]
    GENIE_TOOLS --> GENIE_AGENT
    GENIE_RETURN --> SUPERVISOR

    GRAPHRAG_ENTRY --> GRAPHRAG_AGENT[GraphRAG Agent Node<br/>LLM with 18 MCP tools<br/>Model: fast @ temp 0.0]
    GRAPHRAG_AGENT --> GRAPHRAG_ROUTER{Tool calls?<br/>Iteration < 10?}
    GRAPHRAG_ROUTER -->|Yes| GRAPHRAG_TOOLS[Execute GraphRAG MCP Tools<br/>search_entities, get_neighbors, etc.]
    GRAPHRAG_ROUTER -->|No| GRAPHRAG_RETURN[Return findings<br/>to supervisor]
    GRAPHRAG_TOOLS --> GRAPHRAG_AGENT
    GRAPHRAG_RETURN --> SUPERVISOR

    SUPERVISOR --> SYNTHESIS[Supervisor Synthesizes<br/>output_mode=last_message<br/>Full conversation context]
    SYNTHESIS --> END_SYNTH([END<br/>Final synthesis])

    style SUPERVISOR fill:#1565c0,color:#fff,stroke:#0d47a1,stroke-width:4px
    style GENIE_ENTRY fill:#00c853,color:#fff,stroke:#00a152,stroke-width:3px
    style GRAPHRAG_ENTRY fill:#f57c00,color:#fff,stroke:#e65100,stroke-width:3px
    style GENIE_AGENT fill:#64b5f6,color:#000
    style GENIE_TOOLS fill:#64b5f6,color:#000
    style GRAPHRAG_AGENT fill:#ffb74d,color:#000
    style GRAPHRAG_TOOLS fill:#ffb74d,color:#000
    style SYNTHESIS fill:#5e35b1,color:#fff,stroke:#4527a0,stroke-width:3px
```

**Detailed Flow:**

1. **START** → Query enters supervisor agent
2. **Supervisor routing** (synthesis model @ 0.7):
   - Analyzes query intent
   - Decides: handoff to subagent OR answer directly
   - Uses `langgraph_supervisor` automatic handoff tools
3. **If handoff to Genie**:
   - Calls `transfer_to_genie()` handoff tool
   - Genie subagent (`create_react_agent`) receives query
   - Iterates with 4 MCP tools (up to recursion_limit=21)
   - Returns findings to supervisor
4. **If handoff to GraphRAG**:
   - Calls `transfer_to_graphrag()` handoff tool
   - GraphRAG subagent (`create_react_agent`) receives query
   - Iterates with 18 MCP tools (up to recursion_limit=21)
   - Returns findings to supervisor
5. **Supervisor synthesis**:
   - Receives subagent findings (if delegated)
   - Synthesizes with full conversation context
   - `output_mode="last_message"` keeps only final synthesis in state
6. **Streaming filter**:
   - Subagent content tagged with `"subagent"` → filtered out
   - Supervisor content tagged with `"supervisor"` → streamed to user
   - User sees only final synthesis, not intermediate subagent work

**Why This Architecture?**

- ✅ **LangGraph-native**: Uses official `langgraph_supervisor` library + `create_react_agent` for all agents
- ✅ **Automatic handoffs**: Library creates `transfer_to_*` tools automatically
- ✅ **Consistent pattern**: All agents use same `create_react_agent` interface
- ✅ **Tag-based filtering**: Clean separation via model tags (`supervisor` vs `subagent`)
- ✅ **Native iteration**: `recursion_limit` formula (2 * max_iterations + 1) instead of custom state
- ✅ **Simplified code**: 487 lines removed (-44%) vs custom supervisor implementation
- ✅ **Synthesis quality**: Supervisor sees full context, subagents focus on data gathering

**Configuration:**

```bash
# .env configuration
GENIE_SUBAGENT_MAX_ITERATIONS=10   # Used in formula: recursion_limit = 2 * 10 + 1 = 21
GRAPHRAG_SUBAGENT_MAX_ITERATIONS=10  # Used in formula: recursion_limit = 2 * 10 + 1 = 21
SYNTHESIS_MODEL_DEPLOYMENT=gpt-4.1-mini  # Used by supervisor for routing and synthesis
SYNTHESIS_MODEL_TEMPERATURE=0.7  # Higher temp for nuanced decisions
FAST_MODEL_DEPLOYMENT=gpt-4.1-mini  # Used by subagents for tool calling
FAST_MODEL_TEMPERATURE=0.0  # Low temp for precise tool usage
```

**Note**: Iteration control now uses `recursion_limit` (formula: `2 * max_iterations + 1`) instead of custom state tracking.

**Tool Distribution:**

- **Genie Tools (4)**: `get_genie_spaces`, `ask_genie`, `follow_up`, `get_genie_space_metadata`
- **GraphRAG Tools (18)**: `search_entities`, `describe_entity_type`, `get_neighbors`, `execute_cypher`, etc.

**Key Implementation Decisions:**

1. **`langgraph_supervisor` library**: Automatic handoff tool creation and synthesis management
2. **All agents use `create_react_agent`**: Consistent interface for supervisor and subagents
3. **Tag-based filtering**: Models tagged (`supervisor` vs `subagent`) for streaming control
4. **Recursion limits**: Formula `2 * max_iterations + 1` replaces custom iteration state
5. **`output_mode="last_message"`**: Only final synthesis added to state (not full history)
6. **Tool grouping by MCP server**: Natural separation - Genie (emergency data) vs GraphRAG (graph queries)
7. **Synthesis quality**: Supervisor receives full conversation context from subagents

**Code Reduction**: 487 lines removed (-44%) compared to custom supervisor implementation.

## Project Structure

```
src/
├── app/                          # Main application package
│   ├── __init__.py              # Package initialization
│   ├── main.py                  # FastAPI app entry point & CORS setup
│   ├── dependencies.py          # Shared application dependencies
│   │
│   ├── core/                    # Core infrastructure
│   │   ├── __init__.py
│   │   ├── config.py           # Pydantic settings (PRIMARY CONFIG)
│   │   ├── logging.py          # Enhanced structured logging
│   │   └── phoenix_config.py   # Phoenix observability configuration
│   │
│   ├── api/                     # API layer
│   │   ├── __init__.py
│   │   ├── deps.py             # API-specific dependencies
│   │   └── api_v1/             # API version 1
│   │       ├── __init__.py
│   │       ├── api.py          # Router aggregation
│   │       └── endpoints/      # Individual endpoint modules
│   │           ├── __init__.py
│   │           ├── chat.py     # Chat endpoints (streaming & non-streaming)
│   │           └── mcp.py      # MCP server status/tools endpoints
│   │
│   ├── middleware/              # Request/response middleware
│   │   ├── __init__.py
│   │   └── correlation.py      # Correlation ID tracking
│   │
│   ├── services/                # Orchestration & business logic layer
│   │   ├── __init__.py
│   │   ├── langgraph_service.py # LangGraph orchestration (CORE SERVICE)
│   │   ├── streaming.py         # Multi-type streaming utilities
│   │   └── context.py           # Conversation session management
│   │
│   ├── agent/                   # LangGraph agent implementation
│   │   ├── __init__.py
│   │   └── langgraph/           # LangGraph workflows and configuration
│   │       ├── __init__.py
│   │       ├── context.py       # RuntimeContext (dependency injection)
│   │       ├── state.py         # AgentState definition
│   │       └── workflows.py     # LangGraph workflow nodes & graph
│   │
│   └── mcp/                     # MCP (Model Context Protocol) integration
│       ├── __init__.py
│       ├── adapter.py           # MCPToolAdapter (official langchain-mcp-adapters)
│       ├── base.py              # Base MCP types and interfaces
│       ├── models.py            # MCP data models
│       └── clients/             # MCP server client implementations
│           ├── __init__.py
│           ├── genie.py         # Genie MCP client configuration
│           └── graphrag.py      # GraphRAG MCP client configuration
│
├── scripts/                     # Development & testing scripts
│   ├── test_service_integration.py
│   ├── test_tool_discovery.py
│   ├── test_workflow.py
│   └── ...
│
├── langgraph.json              # LangGraph Studio configuration
├── pyproject.toml             # Python dependencies (uv managed)
├── uv.lock                    # uv lock file
├── .env.example               # Environment variables template
└── README.md                  # This file
```

### Folder Explanations

#### `/app/` - Main Application Package
Contains all application code organized into distinct layers for clean separation of concerns.

#### `/app/core/` - Core Infrastructure
- **`config.py`** - **Primary configuration management** using Pydantic BaseSettings
  - Handles environment variables, validation, and application-wide settings
  - Two-layer config system: Pydantic Settings + RuntimeContext defaults
- **`logging.py`** - Enhanced structured logging with correlation IDs, performance tracking, and security-aware scrubbing
- **`phoenix_config.py`** - Arize Phoenix observability configuration for LLM tracing

#### `/app/api/` - API Layer
- **`deps.py`** - Dependency injection for API endpoints
- **`api_v1/api.py`** - Aggregates all v1 routers with prefixes
- **`endpoints/chat.py`** - Chat endpoints (streaming & non-streaming) - simplified to ~30 lines with LangGraph
- **`endpoints/mcp.py`** - MCP server status and tool discovery endpoints

#### `/app/middleware/` - Request/Response Middleware
- **`correlation.py`** - Automatic correlation ID tracking for request tracing across services

#### `/app/services/` - Orchestration & Business Logic
- **`langgraph_service.py`** - **CORE ORCHESTRATION SERVICE**
  - Manages entire agent lifecycle (tool discovery, context injection, workflow execution)
  - Bridges configuration layer with LangGraph execution layer
  - Lazy initialization for performance optimization
  - Handles streaming, logging, and observability
- **`streaming.py`** - Multi-type streaming system with `StreamMessage` class (STATUS, REASONING, SOURCES, CONTENT)
- **`context.py`** - In-memory conversation session management (ConversationContext)

#### `/app/agent/langgraph/` - LangGraph Agent Implementation
- **`workflows.py`** - **LangGraph workflow definition** using `langgraph_supervisor` + `create_react_agent`
  - Supervisor creation with automatic handoff tools
  - Subagent creation with MCP tools and recursion limits
  - Tag configuration for streaming control
- **`context.py`** - **RuntimeContext** dataclass for dependency injection into workflow nodes
  - Contains tools, model configuration, timeouts
  - Acts as second configuration layer with application defaults
- **`state.py`** - AgentState definition for LangGraph graph state management

#### `/app/mcp/` - Model Context Protocol Integration
- **`adapter.py`** - **MCPToolAdapter** using official `langchain-mcp-adapters` library
  - Discovers tools from multiple MCP servers
  - Converts MCP tools to LangChain tool format
  - Auto-discovery with 34 tools (4 from Genie, 30 from GraphRAG)
- **`base.py`** - Base MCP types and interfaces
- **`models.py`** - MCP data models (ServerConfig, ToolInfo, etc.)
- **`clients/`** - MCP server-specific client configurations
  - **`genie.py`** - Genie emergency data MCP server (Azure hosted)
  - **`graphrag.py`** - GraphRAG graph database MCP server (local/Azure)

#### `/scripts/` - Development & Testing
- **`test_service_integration.py`** - End-to-end service integration tests
- **`test_tool_discovery.py`** - MCP tool discovery and adapter tests
- **`test_workflow.py`** - LangGraph workflow execution tests

## Entry Point and Application Flow

### 1. Application Initialization (`main.py`)

```python
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.api.api_v1.api import api_router
from app.core.config import settings

# Create FastAPI instance
app = FastAPI(
    title=settings.app_name,
    version=settings.version,
    openapi_url=f"{settings.api_v1_str}/openapi.json"
)

# Add CORS middleware
if settings.backend_cors_origins:
    app.add_middleware(CORSMiddleware, ...)

# Include API router
app.include_router(api_router, prefix=settings.api_v1_str)
```

### 2. Request Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant E as Chat Endpoint
    participant LG as LangGraph Service
    participant WF as Agent Workflow
    participant LLM as Azure OpenAI
    participant MCP as MCP Servers
    participant G as Genie Server
    participant GR as GraphRAG Server

    C->>E: POST /api/v1/chat/stream
    E->>C: STATUS: "Processing request..."

    Note over E,LG: Lazy initialization on first request
    E->>LG: process_message_stream()
    LG->>MCP: discover_tools() [if not cached]
    MCP->>G: Connect to Genie MCP
    MCP->>GR: Connect to GraphRAG MCP
    MCP-->>LG: 34 tools discovered
    LG->>LG: Create RuntimeContext
    LG->>WF: Compile workflow [if not cached]

    Note over LG,WF: Agent execution begins (native streaming)
    LG->>WF: astream_events(messages, config)

    Note over WF: Supervisor (langgraph_supervisor)
    WF->>LLM: Supervisor routes query
    LLM-->>WF: Handoff tool call (transfer_to_graphrag_subagent)
    WF->>C: STATUS: "Routing to GraphRAG..."

    Note over WF: GraphRAG Subagent (create_react_agent)
    WF->>LLM: Subagent with 18 MCP tools
    LLM-->>WF: Tool call (search_entities)
    WF->>C: STATUS: "Calling tool..."
    WF->>MCP: Execute MCP tool
    MCP->>GR: Query graph database
    GR-->>MCP: Entity data
    MCP-->>WF: Tool result
    WF->>C: SOURCES: Tool attribution

    WF->>LLM: Subagent synthesizes findings
    LLM-->>WF: Token stream (tagged: subagent)
    Note over WF,C: Subagent content filtered out by tags

    Note over WF: Supervisor Synthesis
    WF->>LLM: Supervisor with full context
    LLM-->>WF: Final synthesis stream (tagged: supervisor)
    WF->>C: CONTENT: Synthesized response (streaming)
    WF-->>LG: Complete

    LG->>C: STATUS: "Response complete"
```

### 3. Dependency Injection Flow

```python
# settings.py (core/config.py)
settings = Settings()  # Loads from environment

# dependencies.py
def get_settings():
    return settings

# api/deps.py  
def get_current_settings() -> Settings:
    return get_settings()

# endpoints/chat.py
async def stream_chat(settings: Settings = Depends(get_current_settings)):
    # Endpoint has access to validated settings
```

## API Endpoints

### Base Endpoints

#### `GET /`
**Root endpoint for health check**
```python
@app.get("/")
async def root():
    return {"message": f"Welcome to {settings.app_name} API"}
```

#### `GET /health` 
**Health check endpoint**
```python
@app.get("/health")
async def health_check():
    return {"status": "healthy", "version": settings.version}
```

### Chat Endpoints (`/api/v1/chat/`)

#### `POST /api/v1/chat/` - Non-Streaming Chat
**Simple request-response chat endpoint**

**Request:**
```json
{
  "message": "Hello, how are you?",
  "conversation_id": "optional-conversation-id"
}
```

**Response:**
```json
{
  "response": "Echo: Hello, how are you?",
  "conversation_id": "optional-conversation-id"
}
```

**Example Usage:**
```bash
curl -X POST "http://localhost:8000/api/v1/chat/" \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello", "conversation_id": "test-123"}'
```

#### `POST /api/v1/chat/stream` - Multi-Type Streaming Chat
**Real-time streaming endpoint with multiple data types**

**Request:**
```json
{
  "message": "What is FastAPI?",
  "conversation_id": "stream-test-456"
}
```

**Response:** Server-Sent Events stream
```
data: {"type": "status", "data": {"message": "Initializing agent..."}, "metadata": {}}

data: {"type": "reasoning", "data": {"step": "Analyzing the user's query to determine the best approach."}, "metadata": {}}

data: {"type": "sources", "data": {"source": "FastAPI Documentation (https://fastapi.tiangolo.com/)"}, "metadata": {}}

data: {"type": "content", "data": {"chunk": "This is a demonstration of multi-type streaming..."}, "metadata": {}}
```

**Example Usage:**
```bash
curl -N -X POST "http://localhost:8000/api/v1/chat/stream" \
  -H "Content-Type: application/json" \
  -d '{"message": "What is FastAPI?"}' \
  --no-buffer
```

**JavaScript Client Example:**
```javascript
const response = await fetch('/api/v1/chat/stream', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ message: 'Hello', conversation_id: 'test' })
});

const reader = response.body.getReader();
const decoder = new TextDecoder();

while (true) {
  const { done, value } = await reader.read();
  if (done) break;
  
  const chunk = decoder.decode(value);
  const lines = chunk.split('\\n');
  
  for (const line of lines) {
    if (line.startsWith('data: ')) {
      const data = JSON.parse(line.slice(6));
      console.log(`${data.type}: ${data.data}`);
    }
  }
}
```

### MCP Endpoints (`/api/v1/mcp/`)

#### `GET /api/v1/mcp/servers` - MCP Server Status
**Get connection status and configuration for all MCP servers**

**Response:**
```json
{
  "servers": [
    {
      "name": "genie",
      "namespace": "genie",
      "configured": true,
      "connected": true,
      "url": "https://genai-graysky-dev-mcp.azurewebsites.net",
      "tool_count": 4,
      "status": "connected",
      "error": null
    },
    {
      "name": "graphrag",
      "namespace": "graph",
      "configured": true,
      "connected": true,
      "url": "http://localhost:8001/mcp",
      "tool_count": 22,
      "status": "connected",
      "error": null
    }
  ],
  "total_tools": 26,
  "initialized": true
}
```

**Status Values:**
- `connected` - Server is configured and successfully connected
- `disconnected` - Server is configured but not connected
- `not_configured` - Server is not configured in environment variables

**Example Usage:**
```bash
curl "http://localhost:8000/api/v1/mcp/servers" | jq
```

#### `GET /api/v1/mcp/tools` - MCP Tools by Server
**Get all available MCP tools organized by their server namespace**

**Response:**
```json
{
  "servers": {
    "genie": {
      "server_name": "genie",
      "namespace": "genie",
      "connected": true,
      "tool_count": 4,
      "tools": [
        {
          "name": "genie.ask_genie",
          "original_name": "ask_genie",
          "description": "Start a new Databricks Genie conversation, ask a question, and return the SQL + query result",
          "parameters": {
            "properties": {
              "space_id": {"title": "Space Id", "type": "string"},
              "question": {"title": "Question", "type": "string"},
              "return_markdown_table": {"default": true, "title": "return_markdown_table", "type": "string"}
            },
            "required": ["space_id", "question"],
            "title": "ask_genieArguments",
            "type": "object"
          }
        },
        {
          "name": "genie.follow_up",
          "original_name": "follow_up",
          "description": "Ask a follow-up question in an existing Genie conversation",
          "parameters": {...}
        }
      ]
    },
    "graph": {
      "server_name": "graphrag",
      "namespace": "graph",
      "connected": true,
      "tool_count": 22,
      "tools": [
        {
          "name": "graph.get_schema",
          "original_name": "get_schema",
          "description": "Get the complete graph schema including all entity types, relationship types, and their properties.",
          "parameters": {...}
        }
      ]
    }
  },
  "total_tools": 26
}
```

**Tool Name Convention:**
- Tools are namespaced by server (e.g., `genie.ask_genie`, `graph.get_schema`)
- `name` field contains the full namespaced name used for calling
- `original_name` field contains the tool's native name without namespace

**Example Usage:**
```bash
# Get all tools
curl "http://localhost:8000/api/v1/mcp/tools" | jq

# Get tools for a specific server
curl "http://localhost:8000/api/v1/mcp/tools" | jq '.servers.genie'

# Get just the tool names
curl "http://localhost:8000/api/v1/mcp/tools" | jq '.servers[].tools[].name'
```

## Multi-Type Streaming System

### StreamMessage Class

The core of the streaming system is the `StreamMessage` class:

```python
from enum import Enum
from typing import Dict, Any

class StreamType(str, Enum):
    STATUS = "status"
    REASONING = "reasoning"
    SOURCES = "sources"
    CONTENT = "content"

class StreamMessage:
    def __init__(self, stream_type: StreamType, data: Any, metadata: Dict[str, Any] = None):
        self.type = stream_type
        self.data = data
        self.metadata = metadata or {}
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "type": self.type.value,
            "data": self.data,
            "metadata": self.metadata
        }
```

### Message Types

#### 1. STATUS Messages
**Purpose:** Provide real-time updates on processing state
```python
StreamMessage(StreamType.STATUS, "Initializing agent...")
StreamMessage(StreamType.STATUS, "Processing request...")
StreamMessage(StreamType.STATUS, "Response complete")
```

#### 2. REASONING Messages
**Purpose:** Show agent's decision-making process
```python
StreamMessage(StreamType.REASONING, "Analyzing the user's query to determine the best approach.")
StreamMessage(StreamType.REASONING, "Breaking down the problem into smaller components.")
```

#### 3. SOURCES Messages
**Purpose:** Cite data sources and references
```python
StreamMessage(StreamType.SOURCES, {
    "title": "FastAPI Documentation",
    "url": "https://fastapi.tiangolo.com/",
    "relevance": "high"
})
```

#### 4. CONTENT Messages
**Purpose:** Main response content (can be chunked)
```python
StreamMessage(StreamType.CONTENT, "This is the main response content...")
```

### Creating Custom Streaming Endpoints

```python
from app.services.streaming import StreamMessage, StreamType, format_sse_message

@router.post("/custom-stream", response_class=StreamingResponse)
async def custom_stream(request: CustomRequest):
    async def generate_response():
        # Status update
        yield format_sse_message(
            StreamMessage(StreamType.STATUS, "Starting custom processing...")
        )
        
        # Your custom logic here
        for item in await process_custom_data(request):
            yield format_sse_message(
                StreamMessage(StreamType.CONTENT, item)
            )
        
        # Final status
        yield format_sse_message(
            StreamMessage(StreamType.STATUS, "Custom processing complete")
        )
    
    return StreamingResponse(
        generate_response(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
        }
    )
```

## Technical Notes

### Streaming Architecture Decisions

**Using `astream_events()` vs `astream()`:**

We use `astream_events()` (not `astream()`) for fine-grained event control:
- **Need**: Multi-type streaming (STATUS, SOURCES, CONTENT) requires event-level granularity
- **Events**: `on_chat_model_stream`, `on_tool_start`, `on_tool_end`, `on_chain_start`
- **Alternative**: `astream()` only provides node-level updates or final state

**Filtering Subagent Content:**

Subagent intermediate responses are filtered manually in `langgraph_service.py:224-228`:

```python
# Filter content: only stream from supervisor, not subagents
if langgraph_node in ["genie_subagent", "graphrag_subagent"]:
    return None  # Skip subagent intermediate responses
```

**Why tags-based filtering?**
- Both supervisor and subagents have `langgraph_node='agent'` in metadata (can't distinguish by node name)
- `exclude_names` doesn't work because nodes are nested within supervisor graph
- `astream_events()` doesn't have `subgraphs` parameter (only `astream()` does)
- Tags are the **LangGraph-recommended approach** for filtering specific LLM invocations

**Tag Configuration** (`workflows.py`):
```python
# Supervisor model
model = AzureChatOpenAI(..., tags=["supervisor"])

# Subagent models
model = AzureChatOpenAI(..., tags=["subagent", "genie"])
model = AzureChatOpenAI(..., tags=["subagent", "graphrag"])
```

**Filtering Logic** (`langgraph_service.py:222-233`):
```python
tags = event.get("tags", [])
if "subagent" in tags:
    return None  # Skip subagent intermediate responses
```

**Result**: Users only see supervisor's final synthesis, not subagent intermediate work.

### Output Mode Configuration

**Setting**: `output_mode="last_message"` in `workflows.py:159`

**Purpose**:
- Controls what messages are added to STATE (not streaming)
- `"last_message"`: Only supervisor's final response added to state
- `"full_history"`: All intermediate messages added (would clutter state)

**Important**: This affects state management, NOT what users see in the stream. Streaming filter is separate.

### Recursion Limits

Iteration control uses LangGraph's native `recursion_limit` instead of custom state tracking:

**Formula**: `recursion_limit = 2 * max_iterations + 1`

**Why**: Each iteration = agent call + tool call = 2 steps, plus 1 for final response

**Implementation** (`workflows.py:202, 240`):
```python
agent.with_config({"recursion_limit": 2 * settings.genie_subagent_max_iterations + 1})
```

**Benefits**:
- No custom `IterationState` needed
- Uses LangGraph native mechanism
- Simpler code (-347 lines removed)

## Development Setup

### Prerequisites

- Python 3.10 or higher (required for MCP package)
- [uv](https://github.com/astral-sh/uv) - Fast Python package manager

To install uv:
```bash
# On macOS/Linux:
curl -LsSf https://astral.sh/uv/install.sh | sh

# Or using pip:
pip install uv
```

### 1. Environment Setup

```bash
# From the src directory, install all dependencies using uv
uv sync  # This creates .venv and installs all packages from pyproject.toml

# The above command automatically:
# - Creates a virtual environment in .venv
# - Installs all production dependencies
# - Installs dev dependencies (pytest, black, ruff, etc.)
# - Creates/updates uv.lock file
```

### 2. Environment Variables

Create `.env` file from template:
```bash
cp .env.example .env
```

Edit `.env`:
```env
# Application Configuration
APP_NAME="GraySkyGenAI Assistant"
VERSION="0.1.0"
DEBUG=false

# API Configuration
API_V1_STR="/api/v1"

# Server Configuration
HOST="0.0.0.0"
PORT=8000

# CORS Settings (comma-separated list)
BACKEND_CORS_ORIGINS="http://localhost:3000,http://127.0.0.1:3000"
```

### 3. Running the Server

```bash
# Development server with auto-reload (using uv)
uv run uvicorn app.main:app --reload

# Alternative: Activate virtual environment first
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
uvicorn app.main:app --reload

# Production server
uv run uvicorn app.main:app --host 0.0.0.0 --port 8000

# With custom settings
uv run uvicorn app.main:app --reload --port 8080
```

### 4. Verify Installation

```bash
# Test root endpoint
curl http://localhost:8000/

# Test health endpoint
curl http://localhost:8000/health

# View API documentation
open http://localhost:8000/docs
```

### 5. Managing Dependencies with uv

```bash
# Add a new production dependency
uv add package-name

# Add a development dependency
uv add --dev package-name

# Update all dependencies to latest compatible versions
uv sync --upgrade

# Remove a dependency
uv remove package-name

# Show installed packages
uv pip list
```

**Note:** All dependencies are defined in `pyproject.toml`. The `uv.lock` file ensures reproducible installs across all environments.

### 6. Docker Development (Alternative)

Docker provides an alternative development environment with the same auto-reload capabilities as local development.

**Note**: Docker files (`Dockerfile`, `docker-compose.yml`, `.dockerignore`) are located at `/backend/` root, not in `src/`.

#### Quick Start with Docker

```bash
# Navigate to backend root (parent directory)
cd ..

# Start the backend with Docker Compose (auto-reload enabled)
docker-compose up

# Or build and run in background
docker-compose up -d

# View logs
docker-compose logs -f backend

# Stop the container
docker-compose down
```

#### How Auto-Reload Works in Docker

The Docker setup uses **volume mounting** to enable hot-reload:
- Your source code is mounted from the host into the container at `/app`
- The container's `.venv` is excluded from the mount (each has its own)
- uvicorn's `--reload` flag detects changes via the volume mount
- Code changes on your host are immediately reflected in the container

This is similar to the frontend's `WATCHPACK_POLLING` approach.

#### Docker Commands

```bash
# All commands run from /backend/ root (cd .. from src/)

# Development (with auto-reload)
docker-compose up

# Production build (no reload, optimized)
docker build --target production -t backend:prod .
docker run -p 8000:8000 --env-file src/.env backend:prod

# Rebuild after dependency changes
docker-compose build

# Run without docker-compose
docker build --target development -t backend:dev .
docker run -p 8000:8000 -v $(pwd)/src:/app -v /app/.venv --env-file src/.env backend:dev
```

#### Docker vs Local Development

| Feature | Local (`uv run uvicorn`) | Docker (`docker-compose up`) |
|---------|-------------------------|------------------------------|
| **Auto-reload** | ✅ Yes | ✅ Yes (via volume mount) |
| **Setup time** | Fast (if dependencies installed) | Slower first build |
| **Isolation** | Uses local Python | Fully isolated container |
| **Dependencies** | Need to install uv | Only need Docker |
| **Environment** | Native OS | Linux container |
| **Best for** | Day-to-day development | Testing deployment, team consistency |

**Recommendation**: Use local development for speed, Docker for testing deployment or environment consistency.

#### Environment Variables in Docker

The `docker-compose.yml` (located at `/backend/` root) loads variables from your `src/.env` file automatically. You can also override variables:

```bash
# From /backend/ root
# Override specific variables
PHOENIX_ENABLED=true docker-compose up

# Use different env file
docker-compose --env-file src/.env.development up
```

#### Connecting to Services on Host

To access services running on your host machine (like Phoenix, PostgreSQL) from Docker:

```yaml
# Already configured in docker-compose.yml
extra_hosts:
  - "host.docker.internal:host-gateway"

# Use in environment variables
PHOENIX_ENDPOINT=http://host.docker.internal:6006
```

## Code Examples

### Adding a New Endpoint

1. **Create endpoint in `endpoints/` folder:**

```python
# app/api/api_v1/endpoints/new_feature.py
from fastapi import APIRouter, Depends
from pydantic import BaseModel
from app.core.config import Settings
from app.api.deps import get_current_settings

router = APIRouter()

class FeatureRequest(BaseModel):
    input_data: str

class FeatureResponse(BaseModel):
    result: str

@router.post("/process", response_model=FeatureResponse)
async def process_feature(
    request: FeatureRequest,
    settings: Settings = Depends(get_current_settings)
):
    # Your logic here
    return FeatureResponse(result=f"Processed: {request.input_data}")
```

2. **Add to API router:**

```python
# app/api/api_v1/api.py
from app.api.api_v1.endpoints import chat, new_feature

api_router = APIRouter()
api_router.include_router(chat.router, prefix="/chat", tags=["chat"])
api_router.include_router(new_feature.router, prefix="/feature", tags=["feature"])
```

### Adding Streaming to Existing Endpoints

```python
from fastapi.responses import StreamingResponse
from app.services.streaming import generate_hardcoded_stream, format_sse_message

@router.post("/feature/stream", response_class=StreamingResponse)
async def stream_feature(request: FeatureRequest):
    async def generate_response():
        # Use existing streaming utilities
        async for message in generate_hardcoded_stream():
            yield format_sse_message(message)
    
    return StreamingResponse(
        generate_response(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
        }
    )
```

### Creating Custom Dependencies

```python
# app/api/deps.py
from fastapi import Depends, HTTPException
from typing import Optional

def get_user_from_token(token: Optional[str] = None) -> dict:
    if not token:
        raise HTTPException(status_code=401, detail="Token required")
    # Validate token logic
    return {"user_id": "12345", "username": "user"}

# Use in endpoints:
@router.get("/protected")
async def protected_endpoint(user: dict = Depends(get_user_from_token)):
    return {"message": f"Hello {user['username']}"}
```

## Session Context Management

### ContextManager (Current Implementation)

The application includes a simple in-memory session context manager for maintaining conversation history:

```python
# app/services/context.py
from typing import Dict, List, Any
from dataclasses import dataclass, field
import time

@dataclass
class ConversationContext:
    """Represents a conversation session with message history."""
    conversation_id: str
    messages: List[Dict[str, str]] = field(default_factory=list)
    created_at: float = field(default_factory=time.time)
    last_updated: float = field(default_factory=time.time)

class ContextManager:
    """Simple in-memory session context management for MVP."""
    
    def __init__(self):
        self._sessions: Dict[str, ConversationContext] = {}
    
    def add_message(self, conversation_id: str, role: str, content: str):
        """Add a message to the conversation history."""
        context = self.get_context(conversation_id)
        context.messages.append({"role": role, "content": content})
        context.last_updated = time.time()
    
    def get_messages(self, conversation_id: str) -> List[Dict[str, str]]:
        """Get all messages for a conversation in OpenAI format."""
        return self.get_context(conversation_id).messages
```

### Context Manager Usage

```python
# In chat endpoints
from app.services.context import context_manager

@router.post("/stream")
async def stream_chat(request: ChatRequest):
    # Add user message to context
    context_manager.add_message(request.conversation_id, "user", request.message)

    # Get conversation history for LangGraph agent
    messages = context_manager.get_messages(request.conversation_id)

    # Generate response using full context
    langgraph_service = LangGraphService()
    async for message in langgraph_service.process_message_stream(messages, request.conversation_id):
        yield message
```

### Context Manager Improvements (Future)

**Current Limitations:**
- **In-memory only** - Sessions lost on server restart
- **No persistence** - Cannot maintain context across application deployments
- **Memory usage** - Grows with active conversations

**Planned Improvements:**
1. **Cross-session persistence** - Redis or database backend for persistent context storage
2. **FOSS alternatives** - Migrate to established solutions like:
   - **LangChain Memory**: `ConversationBufferMemory` with Redis backend
   - **Haystack ConversationMemory**: Built-in conversation management
   - **Semantic Kernel Memory**: Vector-based semantic memory with Azure Cognitive Search
3. **Smart context management** - Automatic summarization for long conversations
4. **Multi-user support** - User-scoped conversation isolation

**Example Future Implementation:**
```python
# Future: LangChain-based context manager
from langchain.memory import ConversationBufferMemory
from langchain.memory.chat_message_histories import RedisChatMessageHistory

class PersistentContextManager:
    def __init__(self):
        self.redis_url = "redis://localhost:6379"
    
    def get_memory(self, conversation_id: str):
        chat_history = RedisChatMessageHistory(
            url=self.redis_url,
            session_id=conversation_id
        )
        return ConversationBufferMemory(chat_memory=chat_history)
```

## Configuration

### Configuration Flow Architecture

The application uses a layered configuration system with clear precedence rules. Here's how configuration values flow through the system:

```mermaid
graph TB
    subgraph "Layer 1: Pydantic Configuration Sources"
        ENV[Environment Variables<br/>export FAST_MODEL_DEPLOYMENT=gpt-4o]
        DOTENV[.env File<br/>FAST_MODEL_DEPLOYMENT=gpt-4.1-mini]
        CONFIG_DEFAULTS[Config Class Defaults<br/>app/core/config.py:57-60<br/>fast_model_deployment = 'gpt-4.1-mini']
    end

    subgraph "Layer 2: Pydantic Settings Resolution"
        SETTINGS[Settings Class<br/>app/core/config.py:9-93]
        SETTINGS_INST[settings = Settings<br/>Line 47: Pydantic resolves priority]
    end

    subgraph "Layer 3: RuntimeContext Defaults"
        CONTEXT_DEFAULTS[RuntimeContext Dataclass Defaults<br/>app/agent/langgraph/context.py:64-75<br/>fast_model_temperature = 0.0<br/>synthesis_model_temperature = 0.7<br/>max_iterations = 3<br/>enable_graph_iteration = True<br/>tool_timeout = 30<br/>llm_timeout = 60]
    end

    subgraph "Layer 4: Service Layer Override Point"
        LANGGRAPH_SVC[LangGraphService<br/>langgraph_service.py:104-112<br/>Creates RuntimeContext]
    end

    subgraph "Layer 5: Final Runtime Context"
        CONTEXT[RuntimeContext Instance<br/>Used by workflows]
    end

    subgraph "Configuration Flow by Field Type"
        FULL_OVERRIDE[✅ Full Config Chain<br/>ENV → .env → config.py → service → context<br/><br/>- fast_model<br/>- synthesis_model<br/>- fast_model_temperature<br/>- synthesis_model_temperature]

        HARDCODED[⚠️ Hardcoded in Service<br/>Bypasses both config layers<br/><br/>- max_iterations = 3]

        CONTEXT_DEFAULTS_ONLY[📋 Context Defaults Only<br/>Service doesn't pass parameter<br/>Falls back to context.py defaults<br/><br/>- enable_graph_iteration = True<br/>- tool_timeout = 30<br/>- llm_timeout = 60]
    end

    ENV -->|1st Priority| SETTINGS
    DOTENV -->|2nd Priority| SETTINGS
    CONFIG_DEFAULTS -->|3rd Priority| SETTINGS

    SETTINGS -->|Instantiation<br/>Line 47| SETTINGS_INST

    SETTINGS_INST -->|settings.fast_model_deployment| LANGGRAPH_SVC
    SETTINGS_INST -->|settings.synthesis_model_deployment| LANGGRAPH_SVC
    SETTINGS_INST -->|settings.fast_model_temperature| LANGGRAPH_SVC
    SETTINGS_INST -->|settings.synthesis_model_temperature| LANGGRAPH_SVC

    CONTEXT_DEFAULTS -.->|Fallback if not<br/>passed by service| LANGGRAPH_SVC

    LANGGRAPH_SVC -->|Passes explicit<br/>parameters| FULL_OVERRIDE
    LANGGRAPH_SVC -->|Hardcodes value| HARDCODED
    LANGGRAPH_SVC -->|Doesn't pass<br/>parameter| CONTEXT_DEFAULTS_ONLY

    FULL_OVERRIDE --> CONTEXT
    HARDCODED --> CONTEXT
    CONTEXT_DEFAULTS_ONLY --> CONTEXT

    style ENV fill:#e8f5e9
    style DOTENV fill:#fff3e0
    style CONFIG_DEFAULTS fill:#f3e5f5
    style CONTEXT_DEFAULTS fill:#e1bee7,stroke:#9c27b0,stroke-width:3px
    style FULL_OVERRIDE fill:#c8e6c9
    style HARDCODED fill:#ffe0b2
    style CONTEXT_DEFAULTS_ONLY fill:#ce93d8
    style LANGGRAPH_SVC fill:#bbdefb,stroke:#1976d2,stroke-width:3px
    style SETTINGS_INST fill:#90caf9
```

### Two Configuration Layers

The application has **two separate configuration layers** that work together:

**Layer 1: Pydantic Settings (`app/core/config.py`)**
- Handles environment-specific values (API keys, model names, endpoints)
- Priority: Environment Variables → `.env` file → Class defaults
- Resolution happens at `config.py:47` when `settings = Settings()` is instantiated

**Layer 2: RuntimeContext Defaults (`app/agent/langgraph/context.py`)**
- Handles application constants (timeouts, iteration limits, default temperatures)
- These are dataclass field defaults that act as fallbacks
- Only used if the service layer doesn't explicitly pass a value

**The Bridge:** `LangGraphService` at `langgraph_service.py:104-112` connects these layers by:
1. Reading values from `settings` (Layer 1)
2. Passing them as parameters to `RuntimeContext` (Layer 2)
3. For parameters NOT passed, RuntimeContext uses its own defaults

### Configuration Override Points

**Key Location:** The configuration override happens at `langgraph_service.py:104-112`:

```python
# This is where config values are passed to RuntimeContext
self._context = RuntimeContext(
    tools=self._tools,
    genie_space_id=settings.genie_space or "default-space",

    # ✅ OVERRIDDEN - Values from config/env
    fast_model=settings.fast_model_deployment,
    synthesis_model=settings.synthesis_model_deployment,
    fast_model_temperature=settings.fast_model_temperature,
    synthesis_model_temperature=settings.synthesis_model_temperature,

    # ⚠️ HARDCODED - Same as default but explicitly set
    max_iterations=3,

    # 📋 NOT PASSED - Uses RuntimeContext dataclass defaults
    # enable_graph_iteration defaults to True
    # tool_timeout defaults to 30
    # llm_timeout defaults to 60
)
```

**Why Some Fields Aren't Overridden:**

1. **Infrastructure Defaults** - Timeout values and iteration limits are application constants that rarely change per environment
2. **No Config Fields** - These fields don't exist in `config.py` (no corresponding env vars)
3. **Reasonable Defaults** - Values are sensible for all environments (unlike model names which are deployment-specific)

### Fail-Fast Validation

The application uses Pydantic validators to catch configuration errors **at startup** rather than at runtime:

```python
@field_validator("fast_model_deployment", "synthesis_model_deployment")
@classmethod
def validate_model_deployments(cls, v: str, info) -> str:
    if not v or v.strip() == "":
        field_name = info.field_name.upper()
        raise ValueError(
            f"{field_name} cannot be empty. "
            f"Check your .env file and ensure it contains:\n"
            f"  {field_name}=gpt-4.1-mini\n"
            f"or your preferred Azure OpenAI deployment name."
        )
    return v.strip()
```

**Benefits:**
- ✅ **Immediate feedback** - App won't start with invalid config
- ✅ **Clear error messages** - Shows exactly what's missing and how to fix it
- ✅ **Prevents runtime failures** - Catches issues before first API call
- ✅ **Developer-friendly** - Error includes example value to add

**Example Error:**
```
ValidationError: 1 validation error for Settings
fast_model_deployment
  Value error, FAST_MODEL_DEPLOYMENT cannot be empty. Check your .env file and ensure it contains:
  FAST_MODEL_DEPLOYMENT=gpt-4.1-mini
or your preferred Azure OpenAI deployment name.
```

**Validation Points:**
1. **Startup** - Settings validated when `settings = Settings()` is called (config.py:116)
2. **Runtime** - RuntimeContext validated in `__post_init__()` (context.py:77-91)
3. **Two-layer validation** - Ensures config errors caught early at both configuration layers

**To Override a Default Field:**

1. Add field to `config.py`:
   ```python
   tool_timeout: int = 30
   llm_timeout: int = 60
   ```

2. Add to `.env`:
   ```env
   TOOL_TIMEOUT=45
   LLM_TIMEOUT=90
   ```

3. Pass to RuntimeContext in `langgraph_service.py`:
   ```python
   tool_timeout=settings.tool_timeout,
   llm_timeout=settings.llm_timeout,
   ```

### Settings Management

The application uses Pydantic `BaseSettings` for automatic environment variable management with **fail-fast validation**:

```python
# app/core/config.py
from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import field_validator

class Settings(BaseSettings):
    app_name: str = "GraySkyGenAI Assistant"
    version: str = "0.1.0"
    debug: bool = False
    api_v1_str: str = "/api/v1"
    openai_api_key: str = ""  # Default fallback - gets replaced by OPENAI_API_KEY
    genie_mcp_url: str = ""   # Default fallback - gets replaced by GENIE_MCP_URL

    # Model deployments (required - validated at startup)
    fast_model_deployment: str = "gpt-4.1-mini"
    synthesis_model_deployment: str = "gpt-4.1-mini"

    @field_validator("fast_model_deployment", "synthesis_model_deployment")
    @classmethod
    def validate_model_deployments(cls, v: str, info) -> str:
        """
        Validate model deployment names are not empty.

        Provides fail-fast validation at application startup with helpful
        error messages showing exactly what to add to .env file.
        """
        if not v or v.strip() == "":
            field_name = info.field_name.upper()
            raise ValueError(
                f"{field_name} cannot be empty. "
                f"Check your .env file and ensure it contains:\n"
                f"  {field_name}=gpt-4.1-mini\n"
                f"or your preferred Azure OpenAI deployment name."
            )
        return v.strip()

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"  # Ignore extra fields from .env that don't map to Settings fields
    )
```

### How Environment Variables Work

**Automatic Field Mapping**: Pydantic BaseSettings automatically maps environment variables to class fields:

```python
# Class field                    →  Environment variable (automatic)
version: str                      →  VERSION
openai_api_key: str               →  OPENAI_API_KEY
fast_model_deployment: str        →  FAST_MODEL_DEPLOYMENT
synthesis_model_deployment: str   →  SYNTHESIS_MODEL_DEPLOYMENT
genie_mcp_url: str                →  GENIE_MCP_URL
```

**Loading Priority** (highest to lowest):
1. **System environment variables** (e.g., `export VERSION=1.0.0`)
2. **`.env` file values** (e.g., `VERSION=1.0.0`)  
3. **Class default values** (e.g., `version: str = "0.1.0"`)

**Usage in Code**:
```python
from app.core.config import settings

# Direct access to environment-loaded values
client = AzureOpenAI(
    api_key=settings.openai_api_key,        # Uses OPENAI_API_KEY env var
    azure_endpoint=settings.openai_endpoint # Uses OPENAI_ENDPOINT env var
)

app = FastAPI(
    title=settings.app_name,     # Uses APP_NAME env var  
    version=settings.version     # Uses VERSION env var
)
```

**Important**: The empty string defaults (like `openai_api_key: str = ""`) are just fallbacks. They get automatically replaced by the actual values from environment variables or the `.env` file.

### Adding New Configuration Options

1. **Add to Settings class:**

```python
class Settings(BaseSettings):
    # Existing settings...
    
    # New settings
    max_request_size: int = 1024 * 1024  # 1MB
    rate_limit_per_minute: int = 60
    enable_analytics: bool = False
```

2. **Use in application:**

```python
@router.post("/upload")
async def upload_data(
    file: bytes,
    settings: Settings = Depends(get_current_settings)
):
    if len(file) > settings.max_request_size:
        raise HTTPException(status_code=413, detail="File too large")
```

### Environment-Specific Configuration

```bash
# .env.development
DEBUG=true
BACKEND_CORS_ORIGINS="http://localhost:3000,http://localhost:3001"

# .env.production  
DEBUG=false
BACKEND_CORS_ORIGINS="https://yourdomain.com"
HOST="0.0.0.0"
PORT=80
```

## Extending the Application

### Adding Advanced Agent Implementation (Future Enhancement)

The current LangGraph implementation provides production agent orchestration. For additional specialized agent patterns, here's how to extend with custom agent interfaces:

1. **Create agent interface:**

```python
# app/agent/interfaces.py
from abc import ABC, abstractmethod
from typing import Dict, Any

class AgentInterface(ABC):
    @abstractmethod
    async def process_message(self, user_message: str, context: Dict[str, Any]) -> 'AgentExecution':
        pass

class AgentExecution:
    def __init__(self, plan=None, results=None):
        self.plan = plan
        self.results = results or []
```

2. **Implement concrete agent:**

```python
# app/agent/planning_agent.py
from app.agent.interfaces import AgentInterface, AgentExecution

class PlanningAgent(AgentInterface):
    async def process_message(self, user_message: str, context: Dict[str, Any]) -> AgentExecution:
        # Agent logic here
        return AgentExecution(plan="Simple response", results=[])
```

3. **Integrate with endpoints:**

```python
# Update chat.py
from app.agent.planning_agent import PlanningAgent

@router.post("/stream")
async def stream_chat(request: ChatRequest):
    agent = PlanningAgent()
    execution = await agent.process_message(request.message, {})
    
    async def generate_response():
        # Incorporate agent results into streaming
        pass
```

### Adding Database Integration

1. **Install database dependencies:**

```bash
uv add sqlalchemy alembic psycopg2-binary
```

2. **Create database models:**

```python
# app/models/base.py
from sqlalchemy import create_engine, Column, Integer, String, DateTime
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime

Base = declarative_base()

class Conversation(Base):
    __tablename__ = "conversations"
    
    id = Column(Integer, primary_key=True)
    title = Column(String(255))
    created_at = Column(DateTime, default=datetime.utcnow)
```

3. **Add database dependency:**

```python
# app/api/deps.py
from sqlalchemy.orm import Session
from app.database import SessionLocal

def get_db() -> Session:
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
```

### Adding Authentication

```python
# app/api/deps.py
from fastapi import HTTPException, Security
from fastapi.security import HTTPBearer

security = HTTPBearer()

async def authenticate(token: str = Security(security)) -> dict:
    # Validate JWT token
    if not validate_token(token.credentials):
        raise HTTPException(status_code=401, detail="Invalid token")
    return get_user_from_token(token.credentials)

# Use in endpoints:
@router.get("/private")
async def private_endpoint(user: dict = Depends(authenticate)):
    return {"user": user}
```

## Future Enhancements & Considerations

### LangGraph Integration (Phases 0-3 Complete)

The backend has been refactored to use LangGraph-native patterns with parameterless workflows and dependency injection via RuntimeContext. **Phases 0-3 are complete** with all tests passing.

**Completed Work:**
- ✅ Parameterless workflow creation (Studio-compatible)
- ✅ Context injection via `config["configurable"]["context"]`
- ✅ Tool auto-discovery from MCP servers using official `langchain-mcp-adapters`
- ✅ 31 tools discovered (4 from Genie, 27 from GraphRAG)
- ✅ Service layer refactored with lazy loading
- ✅ All integration tests passing

**See:** `dev_status/langgraph_refactoring_completed.md` for complete implementation details.

### Potential Considerations

#### 1. Error Handling
**Current state:** Basic try/catch in service layer

**Consideration:** Should we add retry logic for MCP server connection failures?
- Transient network issues could benefit from exponential backoff
- Could implement circuit breaker pattern for degraded service scenarios
- Health checks could proactively detect server unavailability

#### 2. Tool Discovery Caching
**Current state:** Tools cached after first request (stored in `self._tools`)

**Consideration:** Should we add cache invalidation or TTL for tool schemas?
- Current approach: Tools discovered once and cached indefinitely
- Benefit: Fast subsequent requests, no repeated discovery overhead
- Risk: If MCP server adds/removes tools, backend won't detect until restart
- Options:
  - Add TTL-based cache refresh (e.g., every 5 minutes)
  - Manual cache invalidation endpoint for administrators
  - Watch for MCP server version changes and auto-refresh

#### 3. GraphRAG MCP Deployment
**Current state:** GraphRAG MCP runs locally (`localhost:8001`)

**Consideration:** Will GraphRAG MCP be deployed to Azure like Genie MCP?
- Genie MCP: Already in Azure (`https://genai-graysky-dev-mcp.azurewebsites.net`)
- GraphRAG MCP: Currently local development only
- Action needed:
  - Update environment variables for Azure-hosted GraphRAG MCP
  - Update `GRAPHRAG_MCP_URL` in `.env.example` with production endpoint
  - Consider adding `GRAPHRAG_MCP_URL_PROD` separate from dev

#### 4. LangGraph Studio Testing
**Status:** Not yet tested in Studio UI

**Consideration:** The parameterless workflow should work with Studio, but we haven't verified.
- Implementation follows official LangGraph patterns
- `langgraph.json` configuration created
- Workflow function has no parameters (Studio requirement)
- Context injection via config dict (Studio-compatible)
- **Recommendation:** Test with Studio in Phase 4 before finalizing

**To test:**
```bash
# From backend/src directory
langgraph dev --port 8123

# Open browser
open http://localhost:8123
```

**Expected results:**
- Workflow appears in Studio UI
- Can invoke with sample input
- State inspection works at each node
- Tool calls visible in graph execution

#### 5. MCP Server Enabled Flags
**Current implementation:** Both servers check enabled flags before initialization

**Current defaults:**
```env
GENIE_MCP_ENABLED=true
GRAPHRAG_MCP_ENABLED=true
```

**Benefits:**
- Can disable individual servers without changing URLs
- Useful for debugging or staged rollouts
- Prevents failures if server is temporarily unavailable

**Future consideration:** Add health check monitoring to automatically disable failing servers

## Implementation Verification

### ✅ End-to-End Testing Results

The LangGraph agent orchestration has been fully tested and verified:

#### ✅ Basic Chat Test (LangGraph Agent)
```bash
curl -N -X POST http://localhost:8000/api/v1/chat/stream \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello, how are you?", "conversation_id": "test"}'
```
**Result**: Perfect streaming with STATUS → REASONING → CONTENT → STATUS flow

#### ✅ Intelligent Tool Usage Test (LangGraph Agent + MCP)
```bash
curl -N -X POST http://localhost:8000/api/v1/chat/stream \
  -H "Content-Type: application/json" \
  -d '{"message": "What are the current emergency incidents in Florida?", "conversation_id": "emergency"}'
```

**Complete Agent Flow Verified:**
- **STATUS:** "Processing your request..." → "Initializing emergency data access..."
- **REASONING:** "Analyzing user query..." → "Found 1 available tools..." → "Decided to call ask_genie..."
- **SOURCES:** Rich attribution with semantic names, response times (11689ms), technical metadata
- **CONTENT:** Integrated response with real Hurricane Milton/Helene data
- **STATUS:** "Response complete"

#### ✅ Real Emergency Data Retrieved
```
Incident 171 - 2024 Helene 
- Counties: Orange, Taylor, Lafayette, Jackson, Jefferson, Marion, Polk

Incident 172 - 2024 Milton
- Counties: Columbia, Duval, Hillsborough, Lake, Seminole, Charlotte, 
  Alachua, Pasco, Lee, Sarasota, Collier, Sumter, Brevard, Manatee, 
  Pinellas, Flagler

Incident 97 - 2020 Laura
- Counties: Leon
```

### ✅ Architecture Validation

**LangGraph Agent Orchestration:**
- ✅ Azure OpenAI function calling with intelligent tool selection
- ✅ Context-based decisions (no keyword detection)
- ✅ Tool results integrated into conversation context via state graph
- ✅ Error handling and retry logic

**Stream Type Ownership:**
- ✅ **STATUS:** Chat Endpoint (infrastructure progress)
- ✅ **REASONING:** LangGraph Agent (agent decision transparency)
- ✅ **SOURCES:** MCP Adapter (data attribution with metadata)
- ✅ **CONTENT:** LangGraph Agent (integrated responses)

**Service Integration:**
- ✅ Clean separation of concerns with LangGraphService orchestration layer
- ✅ Dependency injection via RuntimeContext
- ✅ Session context management working across requests
- ✅ 34 tools from 2 MCP servers discovered and available

## Testing

### Manual API Testing

```bash
# Test all endpoints
curl http://localhost:8000/
curl http://localhost:8000/health
curl -X POST http://localhost:8000/api/v1/chat/ -H "Content-Type: application/json" -d '{"message": "test"}'

# Test streaming (requires a streaming client)
curl -N -X POST http://localhost:8000/api/v1/chat/stream -H "Content-Type: application/json" -d '{"message": "stream test"}'
```

### Unit Testing Setup

1. **Install testing dependencies:**

```bash
# Dev dependencies are already included in pyproject.toml
# They're automatically installed when you run: uv sync
# To add more test dependencies:
uv add --dev pytest httpx pytest-asyncio
```

2. **Create test structure:**

```python
# tests/test_main.py
import pytest
from httpx import AsyncClient
from app.main import app

@pytest.mark.asyncio
async def test_root():
    async with AsyncClient(app=app, base_url="http://test") as ac:
        response = await ac.get("/")
    assert response.status_code == 200
    assert "Welcome to" in response.json()["message"]

@pytest.mark.asyncio  
async def test_health():
    async with AsyncClient(app=app, base_url="http://test") as ac:
        response = await ac.get("/health")
    assert response.status_code == 200
    assert response.json()["status"] == "healthy"
```

3. **Run tests:**

```bash
pytest tests/
pytest tests/ -v  # Verbose output
pytest tests/test_main.py::test_root  # Run specific test
```

### Testing Streaming Endpoints

```python
# tests/test_streaming.py
import pytest
from httpx import AsyncClient
from app.main import app

@pytest.mark.asyncio
async def test_streaming_endpoint():
    async with AsyncClient(app=app, base_url="http://test") as ac:
        async with ac.stream(
            "POST", 
            "/api/v1/chat/stream",
            json={"message": "test"}
        ) as response:
            assert response.status_code == 200
            assert response.headers["content-type"] == "text/event-stream; charset=utf-8"
            
            content = []
            async for chunk in response.aiter_text():
                if chunk.strip():
                    content.append(chunk)
            
            assert len(content) > 0
            assert "data:" in content[0]
```

## API Architecture Diagram

```mermaid
graph TD
    A[FastAPI Application] --> B[CORS Middleware]
    B --> C[API Router /api/v1]
    C --> D[Chat Router /chat]
    
    D --> E[POST /]
    D --> F[POST /stream]
    
    E --> G[ChatResponse Model]
    F --> H[StreamingResponse]
    H --> I[Multi-type Stream Generator]
    
    I --> J[Status Messages]
    I --> K[Reasoning Messages]  
    I --> L[Sources Messages]
    I --> M[Content Messages]
    
    N[Settings] --> O[Environment Variables]
    N --> P[Dependency Injection]
    P --> D
    
    Q[Services Layer] --> I
    R[Agent Layer - Future] -.-> I
    
    style A fill:#0066cc,color:#fff
    style C fill:#00cc66,color:#fff  
    style F fill:#cc6600,color:#fff
    style J fill:#e1f5fe
    style K fill:#fff3e0
    style L fill:#f3e5f5
    style M fill:#e8f5e8
    style R fill:#f9f,stroke:#333,stroke-width:2px,stroke-dasharray: 5 5
```

## Observability & Monitoring

The backend provides comprehensive observability through multiple integrated systems: structured logging with correlation IDs, performance tracking, and **Arize Phoenix** for LLM application monitoring and evaluation.

### Arize Phoenix Integration

**Arize Phoenix** provides comprehensive LLM observability, tracing, and evaluation capabilities for monitoring LangGraph agent interactions, tool usage, and streaming performance.

#### Architecture Overview

![Phoenix Architecture](../assets/phoenix_architecture.png)

```mermaid
graph TB
    subgraph "Development Environment"
        Docker[Phoenix Docker Container<br/>arizephoenix/phoenix:latest<br/>Port 6006/4317]
        SQLite[(SQLite Database<br/>phoenix-dev-data volume)]
        Docker --> SQLite
    end
    
    subgraph "FastAPI Application"
        App[GraySkyGenAI Assistant<br/>Port 8000]
        Phoenix_Config[phoenix_config.py<br/>Telemetry Setup]
        LangGraph[LangGraph Service<br/>Custom Tracing]
        
        App --> Phoenix_Config
        App --> LangGraph
    end
    
    subgraph "Azure Production Environment"
        ACI[Azure Container Instance<br/>Phoenix Server]
        AzureDB[(PostgreSQL<br/>Persistent Storage)]
        AppService[Azure App Service<br/>FastAPI Application]
        
        ACI --> AzureDB
        AppService --> ACI
    end
    
    
    %% Telemetry Connections
    App -.->|OTLP Traces<br/>Port 4317| Docker
    Foundation -.->|Custom Spans| Docker
    AppService -.->|OTLP Traces<br/>HTTPS| ACI
    
    %% UI Access
    Docker -.->|Phoenix UI<br/>Port 6006| Browser[Developer Browser]
    ACI -.->|Phoenix UI<br/>Port 6006| ProdBrowser[Production Dashboard]
    
    
    classDef container fill:#e1f5fe
    classDef service fill:#f3e5f5
    classDef storage fill:#e8f5e8
    classDef external fill:#fff3e0
    
    class Docker,ACI container
    class App,AppService,Foundation service
    class SQLite,AzureDB,Phoenix_Config storage
```

#### Features
- **🔍 LLM Call Tracing** - Automatic OpenAI API call instrumentation with token usage, model performance, and conversation flows
- **🛠️ Tool Usage Analytics** - MCP tool execution tracing with attribution, success rates, and performance metrics  
- **📊 Streaming Performance** - Multi-type streaming analysis (STATUS, REASONING, SOURCES, CONTENT) with latency tracking
- **🔗 Session Correlation** - Complete conversation and request tracing with correlation IDs
- **📈 Performance Monitoring** - Automatic performance bottleneck identification and optimization insights
- **🏷️ Custom Span Tagging** - Rich metadata for business logic tracing and debugging

#### Quick Start with Phoenix

**1. Start Phoenix locally:**
```bash
# Using Docker (recommended)
docker run -p 6006:6006 -p 4317:4317 -i -t arizephoenix/phoenix:latest
```

**2. Enable Phoenix in your environment:**
```bash
# Add to your .env file
PHOENIX_ENABLED=true
PHOENIX_LOCAL_HOST=127.0.0.1
PHOENIX_LOCAL_PORT=6006
PHOENIX_SERVICE_NAME=graysky-assistant
PHOENIX_SERVICE_VERSION=0.1.0
PHOENIX_ENVIRONMENT=development
```

**3. Start your backend (Phoenix auto-initializes):**
```bash
uvicorn app.main:app --reload
# Phoenix UI will be available at http://localhost:6006
```

**4. Generate traces by making API calls:**
```bash
curl -N http://localhost:8000/api/v1/chat/stream \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello, test Phoenix tracing", "conversation_id": "phoenix-test"}'
```

**5. View traces at [http://localhost:6006](http://localhost:6006)**

#### Phoenix Configuration

**Environment Variables:**
```env
# Phoenix Local Development
PHOENIX_ENABLED=true                    # Enable/disable Phoenix tracing
PHOENIX_LOCAL_HOST=127.0.0.1           # Phoenix server host
PHOENIX_LOCAL_PORT=6006                 # Phoenix UI port
PHOENIX_SERVICE_NAME=graysky-assistant  # Service identifier in traces
PHOENIX_SERVICE_VERSION=0.1.0           # Version for trace filtering
PHOENIX_ENVIRONMENT=development         # Environment label (dev/staging/prod)

# Phoenix Cloud (Production)
PHOENIX_CLOUD_ENDPOINT=https://app.phoenix.arize.com  # Phoenix cloud URL
PHOENIX_API_KEY=your_phoenix_api_key_here              # Cloud authentication
```

**Configuration Options:**

| Setting | Default | Description |
|---------|---------|-------------|
| `PHOENIX_ENABLED` | `true` | Master switch for Phoenix tracing |
| `PHOENIX_LOCAL_HOST` | `127.0.0.1` | Phoenix server host (local or external) |
| `PHOENIX_LOCAL_PORT` | `6006` | Phoenix UI port |
| `PHOENIX_AUTO_START` | `true` | **NEW:** Auto-start Phoenix locally (disable for external Phoenix) |
| `PHOENIX_SERVICE_NAME` | `graysky-assistant` | Service name in traces |
| `PHOENIX_SERVICE_VERSION` | `0.1.0` | Version for filtering |
| `PHOENIX_ENVIRONMENT` | `development` | Environment classification |

#### What Gets Traced Automatically

**✅ OpenAI API Calls** (Auto-instrumented):
- Request/response payloads (with privacy controls)
- Token usage (prompt/completion tokens)
- Model performance (latency, throughput)
- Temperature, max_tokens, and other parameters
- Function calling requests and responses

**✅ FastAPI Endpoints** (Auto-instrumented):
- HTTP request/response metadata
- Endpoint performance and status codes
- Request routing and middleware execution
- CORS and authentication flows

**✅ HTTP Client Requests** (Auto-instrumented):
- MCP server communication
- External API calls
- Request/response timing and status

#### What Gets Custom Tagged

**🔧 LangGraph Service** (`langgraph_service.py`):
```python
# Conversation-level tracing
span.set_attribute("conversation_id", conversation_id)
span.set_attribute("message_count", len(messages))
span.set_attribute("user_message_length", len(user_message))

# Tool usage tracing
span.set_attribute("tools_available", len(tools))
span.set_attribute("function_calls_requested", len(tool_calls))
span.set_attribute("function_calls_executed", len(function_calls))
span.set_attribute("function_call_name", tool_call.function.name)
```

**🔧 MCP Tool Execution** (`foundation_model.py:_execute_function_call_with_sources`):
```python
# Tool execution spans
span.set_attribute("function_name", tool_call.function.name)
span.set_attribute("tool_call_id", tool_call.id)
span.set_attribute("function_args_count", len(function_args))
span.set_attribute("sources_count", len(sources_messages))
```

**🔧 Multi-Type Streaming**:
- Stream type classifications (STATUS/REASONING/SOURCES/CONTENT)
- Message timing and ordering
- Content assembly and completion tracking

#### Phoenix UI Features

**Trace Overview:**
- Complete request lifecycle from user input to final response
- Hierarchical span visualization with timing
- Error identification and stack traces
- Performance bottleneck analysis

**LLM Performance Dashboard:**
- Token usage trends and cost analysis
- Model latency and throughput metrics
- Function call success/failure rates
- Conversation length and complexity analysis

**Tool Usage Analytics:**
- MCP tool execution patterns
- Tool response times and success rates
- Source attribution and data quality metrics
- Tool selection decision analysis

#### Phoenix Setup Options

This application implements **telemetry-only Phoenix integration** - it sends traces to an external Phoenix instance but does not manage Phoenix startup/shutdown. Choose your preferred setup method:

**🚀 Option 1: Docker Compose (Recommended)**
Use the pre-configured Docker Compose setup with PostgreSQL persistence:

```bash
# 1. Start Phoenix with PostgreSQL
cd ../phoenix
docker-compose up -d

# 2. Configure your application
# Add to backend/.env:
PHOENIX_ENABLED=true
PHOENIX_ENDPOINT=http://127.0.0.1:6006

# 3. Start your application
cd ../backend
uvicorn app.main:app --reload

# 4. Open Phoenix UI
open http://localhost:6006
```

**Benefits:**
- ✅ **PostgreSQL persistence** - data survives restarts
- ✅ **One-command setup** - `docker-compose up -d`
- ✅ **Production parity** - same database as production
- ✅ **Easy management** - start/stop/logs via Docker Compose

📖 **See [../phoenix/Phoenix_README.md](../phoenix/Phoenix_README.md) for complete setup instructions and production deployment guides.**

**⚡ Option 2: Manual Docker (Quick Testing)**
For quick testing without persistence:

```bash
# 1. Start Phoenix (temporary data - lost on restart)
docker run -p 6006:6006 -p 4317:4317 arizephoenix/phoenix:latest

# 2. Configure and start your application
PHOENIX_ENABLED=true PHOENIX_ENDPOINT=http://127.0.0.1:6006 uvicorn app.main:app --reload

# 3. Open Phoenix UI
open http://localhost:6006
```

*Perfect for quick testing, but traces disappear when container stops*

#### Production Deployment

For production deployments (Azure Container Instance, App Service, AKS), database configuration, troubleshooting, and advanced setup options, see the comprehensive deployment guide:

📖 **[Phoenix Deployment Guide](../phoenix/Phoenix_README.md)**

The guide covers:
- **Azure deployment options** - Container Instance, App Service, AKS
- **PostgreSQL configuration** - Production database setup
- **Environment configuration** - All environment variables and options
- **Troubleshooting** - Common issues and solutions
- **Best practices** - Security, performance, monitoring
- **Cost analysis** - Self-hosted vs Phoenix Cloud comparison

#### Basic Configuration

**Required Environment Variables:**
```bash
# Add to your backend/.env file
PHOENIX_ENABLED=true
PHOENIX_ENDPOINT=http://127.0.0.1:6006
PHOENIX_SERVICE_NAME=graysky-assistant
PHOENIX_SERVICE_VERSION=0.1.0
PHOENIX_ENVIRONMENT=development
```

**Common Settings:**
- `PHOENIX_ENVIRONMENT`: `development` (local), `staging` (test), `production` (live)
- `PHOENIX_ENDPOINT`: Your Phoenix server URL (Docker Compose uses `http://127.0.0.1:6006`)

📖 **See [Phoenix Deployment Guide](../phoenix/Phoenix_README.md) for complete configuration reference and all available options.**


## Structured Logging

The backend implements production-ready structured logging with correlation IDs, performance tracking, and security-aware practices for comprehensive observability alongside Phoenix tracing.

### Features

**🔗 Request Correlation**
- Automatic correlation IDs for tracking requests across all services
- Conversation ID support for multi-turn chat session tracking  
- Response headers include correlation IDs for client-side tracking

**📊 Performance Monitoring**
- Automatic timing for all HTTP requests
- Service-level performance tracking with context managers
- Operation-specific duration logging (LangGraph agent execution, MCP tool invocation)

**🔒 Security-Aware Logging**
- Automatic scrubbing of API keys, tokens, and credentials
- Environment-controlled sensitivity levels (dev vs production)

**📋 Structured Format**
- JSON output for production log aggregation (ELK, Splunk, CloudWatch)
- Human-readable text format for local development  
- Consistent field naming across all services

### Component Loggers

The application uses component-specific loggers following the pattern:

```python
from app.core.logging import get_logger

logger = get_logger(__name__)  # Creates enhanced logger with performance tracking
```

**Available Loggers:**
- `app.services.langgraph_service` - LangGraph agent orchestration operations
- `app.mcp.adapter` - MCP tool discovery and execution
- `app.api` - API endpoint operations
- `app.middleware.logging` - Request/response tracking

### Performance Tracking Usage

Use performance context managers for operation timing:

```python
# Automatic timing with structured logging
with logger.performance_context("foundation_model_generation"):
    result = await self.generate_response(messages)

# Outputs: {"operation": "foundation_model_generation", "duration_ms": 1250, "success": true}
```

### Configuration

Control logging behavior via environment variables:

```env
# Logging Level & Format
LOG_LEVEL=INFO                    # DEBUG, INFO, WARNING, ERROR, CRITICAL  
LOG_FORMAT=json                   # json (production), text (development)

# Correlation & Performance
LOG_CORRELATION_ENABLED=true     # Enable request correlation IDs
LOG_PERFORMANCE_TRACKING=true    # Enable operation timing

# Security & Privacy  
LOG_SCRUB_SENSITIVE=true         # Scrub API keys and credentials
```

### Development vs Production

**Development Configuration (.env.dev):**
```env
LOG_LEVEL=DEBUG
LOG_FORMAT=text                   # Human-readable output
LOG_SCRUB_SENSITIVE=false        # See full data for debugging
```

**Production Configuration (.env.prod):**
```env
LOG_LEVEL=INFO
LOG_FORMAT=json                  # Structured for log aggregation
LOG_SCRUB_SENSITIVE=true        # Always scrub sensitive data
```

### Correlation ID Usage

**Client Integration:**
Send conversation IDs in requests to track multi-turn conversations:

```javascript
// Frontend: Send conversation ID header
fetch('/api/v1/chat/stream', {
  headers: {
    'X-Conversation-ID': conversationId,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({message, conversation_id: conversationId})
});
```

**Response Headers:**
The API returns correlation IDs in response headers for client tracking:

```http
HTTP/1.1 200 OK
X-Request-ID: req_abc123def456      # Unique per request
X-Conversation-ID: conv_xyz789      # Echoed from client
```

**Log Correlation:**
All log entries within a request include correlation fields:

```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "request_id": "req_abc123def456",
  "conversation_id": "conv_xyz789", 
  "component": "foundation_model",
  "message": "Tool execution completed"
}
```

### Azure Deployment

**Azure Container Apps / App Service:**
```yaml
# Azure deployment - logs automatically flow to Azure Monitor
environment:
  - name: LOG_LEVEL
    value: INFO
  - name: LOG_FORMAT  
    value: json
  - name: LOG_SCRUB_SENSITIVE
    value: true
```

**Azure Monitor Integration:**
- JSON logs automatically parsed by Azure Monitor
- Correlation IDs enable cross-service tracing
- Performance metrics available in Application Insights
- Set up alerts based on error rates or performance thresholds

**Log Analytics Queries:**
```kusto
# Find all logs for a specific request
ContainerAppConsoleLogs_CL  
| where request_id_s == "req_abc123def456"
| order by TimeGenerated asc

# Performance monitoring  
ContainerAppConsoleLogs_CL
| where component_s == "foundation_model"
| where duration_ms_d > 2000  
| summarize avg(duration_ms_d) by bin(TimeGenerated, 5m)
```

### Local Development

**Viewing Logs:**
```bash
# Development server with text format
LOG_FORMAT=text uvicorn app.main:app --reload

# JSON format for testing log parsing
LOG_FORMAT=json uvicorn app.main:app --reload

# Debug level for detailed logging
LOG_LEVEL=DEBUG uvicorn app.main:app --reload
```

### How to Confirm Logging is Working

**Step 1: Basic Logging Test**
```bash
# Start the server with JSON logging
LOG_FORMAT=json uvicorn app.main:app --reload

# You should see structured JSON logs in the console:
{"event": "Application startup complete", "level": "info", "timestamp": "2025-01-15T10:30:00.123456Z"}
```

**Step 2: Test Health Endpoint with Correlation**
```bash
# Make a request to the health endpoint
curl -v http://localhost:8000/health

# Check the logs - you should see:
# 1. Request started log with request_id
# 2. Health check accessed log 
# 3. Request completed log with duration_ms
# 4. All logs should have the same request_id

# Example output:
{"event": "Request started", "request_id": "abc-123", "method": "GET", "path": "/health", "level": "info"}
{"event": "Health check accessed", "request_id": "abc-123", "level": "info"}  
{"event": "Request completed", "request_id": "abc-123", "status_code": 200, "duration_seconds": 0.005, "level": "info"}
```

**Step 3: Test Correlation ID Propagation**
```bash
# Send request with custom conversation ID
curl -H "X-Conversation-ID: test-conversation-123" http://localhost:8000/health

# Verify logs show correlation_id field:
{"event": "Request started", "correlation_id": "test-conversation-123", "request_id": "def-456", "level": "info"}
```

**Step 4: Test Sensitive Data Scrubbing**
```python
# Create a test script to verify scrubbing works:
import sys
sys.path.append('.')
from app.core.logging import setup_logging, get_logger, set_correlation_id
from app.core.config import settings

setup_logging(settings.logging)
logger = get_logger('test')

# Test with sensitive data - should be scrubbed in output
logger.info("API call made", api_key="sk-1234567890abcdef", token="bearer-token-123")

# Expected output (scrubbed):
{"api_key": "[REDACTED]", "token": "[REDACTED]", "event": "API call made", "level": "info"}
```

**Step 5: Test Text Format (Development Mode)**
```bash
# Switch to human-readable format for development
LOG_FORMAT=text uvicorn app.main:app --reload

# Make a request - should see readable format:
# 2025-01-15 10:30:00 [INFO    ] Request started [correlation] request_id=abc-123 method=GET path=/health
```

**Verification Checklist:**
- ✅ Logs appear in console when server starts
- ✅ All logs have timestamp, level, and event fields
- ✅ Each HTTP request generates request_id that appears across all related logs
- ✅ Custom X-Conversation-ID header appears as correlation_id in logs
- ✅ Response headers include X-Correlation-ID 
- ✅ JSON format is valid and parseable
- ✅ Text format is human-readable for development
- ✅ Sensitive data is automatically scrubbed when LOG_SCRUB_SENSITIVE=true

**Log Output Examples:**

Text format (development):
```
2025-01-15 10:30:00 [INFO    ] Request started [app.middleware.logging] request_id=req_abc123 method=POST path=/api/v1/chat/stream
2025-01-15 10:30:01 [INFO    ] Foundation model request initiated [app.services.foundation_model] request_id=req_abc123 provider=azure_openai
2025-01-15 10:30:02 [INFO    ] Foundation model request completed [app.services.foundation_model] request_id=req_abc123 duration_ms=1250
```

JSON format (production):
```json
{"timestamp": "2025-01-15T10:30:00Z", "level": "INFO", "message": "Request started", "request_id": "req_abc123", "component": "api", "method": "POST"}
{"timestamp": "2025-01-15T10:30:01Z", "level": "INFO", "message": "Foundation model request initiated", "request_id": "req_abc123", "component": "foundation_model", "provider": "azure_openai"}
```

**Debugging Tips:**
- Use `X-Conversation-ID` header to trace multi-turn conversations
- Search logs by `request_id` to see complete request flow
- Enable DEBUG level to see detailed service interactions
- Disable scrubbing temporarily (`LOG_SCRUB_SENSITIVE=false`) to debug data issues

## Documentation

### Agent Integration Documentation

**[Agent Context Guide](../docs/initial_context.md)**
- Complete guide to GraphRAG tool usage and navigation patterns
- Entity schemas, relationship types, and property details
- Tool selection workflows for different query types
- Traversal patterns and common filters
- Essential reference for AI agents using the GraphRAG MCP server

**[Graph Context Fallback](../docs/graph_context_fallback.json)**
- Fallback graph context data for offline development
- Contains sample statistics, high-degree nodes, and schema information
- Used when GraphRAG MCP server is unavailable
- Enables development without live graph database connection

### Architecture Documentation

**[LangGraph Workflows](app/agent/langgraph/workflows.py)**
- Multi-agent supervisor pattern implementation
- Dynamic context generation and injection
- Tool discovery and subagent configuration
- Core workflow orchestration logic

**[Context Utilities](app/agent/langgraph/context_utils.py)**
- Dynamic context generation from live graph data
- Tool documentation and formatting utilities
- Graph statistics processing and presentation

---

This backend provides a solid foundation for building sophisticated LLM applications with transparent agent interactions and real-time user feedback. The modular architecture makes it easy to extend with new features while maintaining clean separation of concerns.
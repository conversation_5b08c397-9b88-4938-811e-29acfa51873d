"""
Genie MCP client implementation.

Adapted from the original MCP client, focused on Genie server integration.
"""

import asyncio
from typing import List, Dict, Any, Optional

# Official MCP SDK imports
from mcp.client.streamable_http import streamablehttp_client
from mcp.client.session import ClientSession

from app.core.logging import get_mcp_logger, performance_context
from app.mcp.base import MCPClientBase
from app.mcp.models import MCPServerConfig

logger = get_mcp_logger()


class GenieMCPClient(MCPClientBase):
    """
    Genie MCP client for tool discovery and execution.

    Focused on basic tool discovery and execution with the Genie MCP server.
    """

    def __init__(self, server_url: str, max_retries: int = 3, timeout: int = 30):
        """Initialize the Genie MCP client."""
        super().__init__(server_url, timeout)
        self.max_retries = max_retries
        self._server_configs: List[MCPServerConfig] = []
        self._available_tools: Dict[str, List[Dict[str, Any]]] = {}

        logger.info("Initialized Genie MCP Client",
                   server_url=server_url,
                   max_retries=max_retries,
                   timeout=timeout)
    
    async def discover_tools(self) -> List[Dict[str, Any]]:
        """
        Discover available tools from the Genie MCP server.

        Returns:
            List of tool definitions with name, description, and parameters
        """
        try:
            # Ensure server_url ends with /mcp for official MCP protocol
            server_url = self.server_url
            if not server_url.endswith('/mcp'):
                server_url = f"{server_url}/mcp"

            logger.info(f"Discovering tools from Genie MCP server: {server_url}")
            
            # Use official MCP SDK
            async with streamablehttp_client(server_url) as streams:
                read, write, get_session_id = streams
                async with ClientSession(read, write) as session:
                    # Initialize the session
                    await session.initialize()
                    
                    # List available tools
                    tools_result = await session.list_tools()
                    
                    # Convert to standard format
                    tools = []
                    for tool in tools_result.tools:
                        tools.append({
                            "name": tool.name,
                            "description": tool.description or "",
                            "parameters": tool.inputSchema or {}
                        })
                    
                    logger.info(f"Discovered {len(tools)} tools from {server_url}")
                    return tools
                    
        except Exception as e:
            logger.error(f"Failed to discover tools from {server_url}: {e}")
            return []
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Call a tool on the Genie MCP server.

        Args:
            tool_name: Name of the tool to call
            arguments: Tool arguments

        Returns:
            Tool execution result
        """
        try:
            # Ensure server_url ends with /mcp for official MCP protocol
            server_url = self.server_url
            if not server_url.endswith('/mcp'):
                server_url = f"{server_url}/mcp"

            logger.info(f"Calling Genie tool '{tool_name}' on {server_url} with args: {arguments}")
            
            # Use official MCP SDK
            async with streamablehttp_client(server_url) as streams:
                read, write, get_session_id = streams
                async with ClientSession(read, write) as session:
                    # Initialize the session
                    await session.initialize()
                    
                    # Call the tool
                    result = await session.call_tool(tool_name, arguments)
                    
                    # Convert result to standard format
                    if hasattr(result, 'content'):
                        # Handle different content types
                        content = []
                        for item in result.content:
                            if hasattr(item, 'text'):
                                content.append({"type": "text", "text": item.text})
                            else:
                                content.append({"type": "unknown", "data": str(item)})
                        
                        response = {
                            "success": True,
                            "content": content,
                            "isError": getattr(result, 'isError', False)
                        }
                    else:
                        response = {
                            "success": True,
                            "content": [{"type": "text", "text": str(result)}],
                            "isError": False
                        }
                    
                    logger.info(f"Tool '{tool_name}' executed successfully")
                    return response
                    
        except Exception as e:
            logger.error(f"Failed to call tool '{tool_name}' on {server_url}: {e}")
            return {
                "success": False,
                "error": str(e),
                "content": [{"type": "text", "text": f"Error calling tool: {e}"}],
                "isError": True
            }
    

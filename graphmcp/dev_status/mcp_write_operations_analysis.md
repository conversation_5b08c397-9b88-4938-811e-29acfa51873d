# MCP Server Write Operations - Architecture Analysis

## Your Questions

1. **Should the MCP server expose tools that allow for edits?**
2. **Thoughts on directly hitting the graph instead of using the MCP tool?**
3. **Another option is to create an embedding tool but give access in limited fashion?**

---

## Research Findings

### Industry Standard: Read-Only MCP Servers

**Neo4j GraphRAG MCP Server:**
- ✅ **Read-only** - Only exposes vector search retrieval
- ❌ **No write operations** - No data ingestion, embedding generation, or updates
- ✅ **Tool annotation** - Explicitly marked as "read-only, non-destructive, idempotent"
- 📦 **Single tool:** `vector_search(query)` - semantic retrieval only

**Memgraph Official MCP Server:**
- ✅ **Mostly read-only** - `get_schema()`, `run_query()` (which CAN write if query includes SET/CREATE)
- ⚠️ **Flexible run_query** - Can execute any Cypher (including writes) but user controls this
- 🔄 **Early stage** - Just beginning, expanding capabilities

**MCP Security Best Practices (Official Spec):**
- ✅ **Principle of least privilege** - Only expose minimum required capabilities
- ✅ **Read-only mode** - Recommended default for production data
- ✅ **Human in the loop** - Confirmation prompts for dangerous operations
- ✅ **Tool annotations** - Mark tools as read-only vs destructive
- ✅ **Scope restrictions** - Limit what the server can access

---

## Analysis of Your 3 Options

### Option 1: Expose Write Tools via MCP

**Example:** Add `update_embedding()` or `generate_embeddings()` as MCP tools

**Pros:**
- ✅ Unified interface (everything through MCP)
- ✅ LLM agents could trigger embedding generation
- ✅ Convenient for admin operations

**Cons:**
- ❌ **Security risk** - LLM could accidentally modify production data
- ❌ **Against best practices** - MCP spec recommends read-only for data operations
- ❌ **Complexity** - Need permissions, validation, confirmation workflows
- ❌ **Not standard** - Neo4j doesn't expose writes, Memgraph is cautious
- ❌ **Dangerous** - `execute_cypher` already exists and is risky enough

**Verdict:** ❌ **NOT RECOMMENDED** for production

---

### Option 2: Direct Database Access (Separate Scripts)

**Example:** `scripts/generate_embeddings.py` connects directly to Memgraph

**Pros:**
- ✅ **Separation of concerns** - ETL/admin separate from query
- ✅ **Security** - No LLM access to write operations
- ✅ **Standard practice** - How Neo4j/Memgraph ecosystems work
- ✅ **Better control** - Explicit execution, not AI-triggered
- ✅ **Simpler** - No MCP permission logic needed

**Cons:**
- ⚠️ **Network access** - Need connection to Memgraph (not always available from host)
- ⚠️ **Deployment complexity** - Scripts need database credentials

**Verdict:** ✅ **RECOMMENDED** with caveat about network access

---

### Option 3: Embedding Tool with Limited Access

**Example:** Create embedding service as sidecar, expose via restricted MCP tool or separate API

**Architecture:**
```
┌─────────────────────────────────────┐
│  GraphMCP Server (Read-Only)        │
│  - search_entities                  │
│  - semantic_search                  │
│  - vector search (retrieval only)   │
└─────────────────────────────────────┘

┌─────────────────────────────────────┐
│  Admin Service (Restricted Access)  │
│  - generate_embeddings              │
│  - create_indices                   │
│  - refresh_embeddings               │
│  Accessed via: CLI, Admin API, CI/CD│
└─────────────────────────────────────┘
```

**Pros:**
- ✅ **Clean separation** - Read (MCP) vs Write (Admin)
- ✅ **Security** - Admin service has separate auth
- ✅ **Flexibility** - Can expose admin tools when needed
- ✅ **Best of both worlds** - Programmatic access without LLM risk

**Cons:**
- ⚠️ **More infrastructure** - Two services instead of one
- ⚠️ **Complexity** - Need to manage admin service deployment

**Verdict:** ✅ **BEST PRACTICE** for production, but overkill for now

---

## Recommended Architecture

### Current (Development):

**GraphMCP Server (Read + Limited Admin):**
```python
# Read-only tools (safe for LLM)
- get_schema()
- search_entities()
- semantic_search()
- get_neighbors()
- calculate_centrality()

# Admin tools (dangerous - marked clearly)
- execute_cypher()  # Already exists, clearly dangerous
- refresh_cache()   # Safe admin operation
```

**Embedding Scripts (Separate - CLI only):**
```bash
# Run manually or via CI/CD
uv run python scripts/generate_embeddings.py
uv run python scripts/create_vector_indices.py
```

### Future (Production):

**Option A: Keep Current + Network Fix**
- GraphMCP exposes read-only tools
- Admin scripts connect to Memgraph directly (via Azure Private Link or public endpoint)
- No embedding generation via MCP

**Option B: Admin Service**
- GraphMCP: Read-only MCP tools
- Admin Service: REST API for embedding generation, index management
- Admin Service: Restricted access (API key, Azure AD, etc.)

---

## Best Practice Recommendations

### 1. Separate ETL from Query (YES ✅)

**Direct database access for embedding generation is CORRECT:**
```bash
# Admin/ETL operation (not via MCP)
python scripts/generate_embeddings.py --all

# OR in CI/CD pipeline
az container exec --name graphmcp --command "python scripts/generate_embeddings.py"
```

**Why:**
- Industry standard (Neo4j separates ETL from GraphRAG query)
- Security best practice (MCP spec recommends read-only)
- Clearer operational model (who can modify data?)

### 2. MCP Server Should Be Read-Only (YES ✅)

**Keep MCP tools for retrieval:**
```python
# Safe MCP tools (expose to LLM)
- semantic_search_missions(query)  # Read-only
- semantic_search_incidents(query) # Read-only
- find_similar_resources(...)      # Read-only
```

**Remove or restrict dangerous tools:**
```python
# Consider removing or restricting
- execute_cypher()  # Can write - maybe remove or add confirmation

# Safe admin tools
- refresh_cache()   # OK - just refreshes memory cache
- get_health()      # OK - read-only
```

### 3. How to Handle Embedding Generation

**Recommended Approach:**

```
┌──────────────────────────────────────────┐
│ Data Ingestion Pipeline (Separate)       │
│                                          │
│ 1. Load data to Memgraph                │
│ 2. Run: scripts/generate_embeddings.py  │
│ 3. Run: scripts/create_vector_indices.py│
│                                          │
│ Execution: CLI, CI/CD, Admin API        │
│ Access: Database credentials required    │
└──────────────────────────────────────────┘
              ↓
┌──────────────────────────────────────────┐
│ GraphMCP Server (Read-Only MCP)         │
│                                          │
│ - semantic_search_missions(query)       │
│ - semantic_search_incidents(query)      │
│ - find_similar_resources(...)           │
│ - All other 27 tools (read-only)        │
│                                          │
│ Execution: Always running                │
│ Access: MCP protocol (LLM safe)         │
└──────────────────────────────────────────┘
```

---

## Addressing Network Access Issue

**Problem:** Scripts can't connect to Memgraph from host when it's in Docker network.

**Solutions:**

### Solution 1: Use Memgraph via localhost:7687 (SIMPLE)
- Memgraph is already exposed on `localhost:7687`
- Script should work if connection parameters are correct
- **Issue:** Your script had timeout - need to investigate why

### Solution 2: Run scripts inside Docker container
```bash
# From parent directory
cd /Users/<USER>/Repos/DEMES/assistant
docker-compose exec graphrag-mcp uv run python scripts/generate_embeddings.py
```

**Pros:**
- ✅ Uses Docker network (memgraph-db:7687)
- ✅ Same environment as server
- ✅ Works in development

**Cons:**
- ❌ Won't work in production (no Docker in Azure Container Instances)

### Solution 3: Direct Memgraph Connection via Exposed Port
```bash
# From graphmcp directory (host machine)
MEMGRAPH_HOST=localhost \
MEMGRAPH_PORT=7687 \
uv run python scripts/generate_embeddings.py
```

**This SHOULD work** - need to debug connection timeout

### Solution 4: Embedding Service (Future)
- Standalone service with REST API
- Connects to Memgraph directly
- Called by CI/CD or admins
- Not needed for MVP

---

## Final Recommendations

### For Your Use Case:

**1. Keep MCP Server Read-Only ✅**
- Remove `execute_cypher` or add clear warnings/confirmations
- Only expose retrieval tools to LLMs
- Mark dangerous tools with annotations

**2. Use Direct Database Scripts ✅**
- `scripts/generate_embeddings.py` connects directly to Memgraph
- Run via `uv run` from host OR from inside Docker
- Separate from MCP server (cleaner architecture)

**3. Fix Network Connection**
- Debug why localhost:7687 connection times out
- Ensure scripts can connect to exposed Memgraph port
- Works for both local Docker and future Azure managed Memgraph

**4. Document Operational Model**
```
Data Operations (Admin):
  → Run scripts directly with database credentials
  → python scripts/generate_embeddings.py
  → python scripts/create_vector_indices.py

Query Operations (LLM/Users):
  → Use MCP tools (read-only)
  → semantic_search_missions(query)
  → No write access
```

---

## Implementation Plan

### Immediate:

1. ✅ **Keep scripts separate from MCP** - Current approach is correct
2. ✅ **Fix connection issue** - Investigate localhost:7687 timeout
3. ✅ **Test with small sample** - Your request (3-10 nodes)
4. ⏭️ **Create read-only vector search MCP tools** - No write operations

### Future (Production):

1. Scripts connect to Azure managed Memgraph endpoint
2. MCP server remains read-only
3. Optional: Admin REST API if programmatic access needed

---

## Answer to Your Questions

### 1. Should MCP server expose tools that allow edits?

**NO** ❌ - Industry best practice is read-only MCP servers.
- Neo4j GraphRAG: Read-only
- MCP Security Spec: Read-only recommended
- Your `execute_cypher` tool: Consider removing or restricting

### 2. Thoughts on directly hitting the graph instead of using MCP tool?

**YES** ✅ - This is CORRECT approach for admin/ETL operations.
- Cleaner separation of concerns
- Security best practice
- Standard in Neo4j/Memgraph ecosystems
- Scripts should connect directly to Memgraph

### 3. Create embedding tool with limited access?

**FUTURE** ⏭️ - Good idea but not needed for MVP.
- For production: Separate admin service with auth
- For now: CLI scripts with database credentials sufficient
- MCP server stays read-only for LLM safety

---

**Created:** October 1, 2025
**Recommendation:** Keep scripts separate (direct DB access), MCP server read-only for retrieval
**Next:** Fix localhost:7687 connection, test small sample, implement read-only vector search tools

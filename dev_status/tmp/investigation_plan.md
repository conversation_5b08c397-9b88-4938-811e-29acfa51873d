# Investigation Plan: get_schema Response Discrepancy

## What We Know

### Three Different Outputs:

1. **Expected Output** (your reference)
   - Key: `node_labels`
   - Properties: `{"embedding": {"type": "LIST"}}`
   - Size: ~6K chars
   - No samples

2. **Agent Output** (what you saw)
   - Key: `entity_types`
   - Properties: `{"embedding": "vector"}`
   - Size: Unknown
   - No samples visible

3. **Test Script Output** (my measurement)
   - Key: `entity_types`
   - Properties: `["embedding", "id", ...]` (list of names)
   - Size: 97K chars
   - HAS samples with full embedding arrays

## The Mystery

- Agent and test both use `entity_types` (not `node_labels`)
- But agent has NO samples, test HAS samples
- Agent has simplified type strings, test has property name lists

## Investigation Plan

### Step 1: Capture Raw MCP Server Response
**Goal:** See exactly what GraphRAG MCP server returns
**Method:** Add logging to graphmcp server's `get_schema` tool
- Log the exact JSON being returned
- Log the size before sending
- Save to file for inspection

### Step 2: Capture What <PERSON><PERSON><PERSON><PERSON> Receives
**Goal:** See what `langchain-mcp-adapters` gets from MCP
**Method:** Add logging in backend before tool execution
- Log raw MCP response before LangChain wrapping
- Compare to Step 1

### Step 3: Capture What Agent Sees
**Goal:** See what's passed to the LLM in the workflow
**Method:** Add logging in workflow when tool returns
- Log the tool result before it goes to LLM
- Check if it matches Step 2

### Step 4: Check for Response Transformation
**Goal:** Find where format changes happen
**Method:**
- Search for code that transforms `properties` list → dict
- Search for code that strips `samples`
- Check if there's a response size limit causing truncation

### Step 5: Verify Against MCP Inspector
**Goal:** Confirm MCP Inspector sees the same as server
**Method:** Call get_schema via MCP Inspector and save full response

## Execution Order

1. Add logging to graphmcp `get_schema` (most upstream)
2. Call get_schema via test script with new logging
3. Compare outputs at each layer
4. Identify where transformation happens

## Expected Outcome

We should find ONE of:
- MCP server is returning different formats based on caller
- langchain-mcp-adapters is transforming the response
- The agent is post-processing before displaying
- There's caching returning stale data

## Questions to Answer

1. Why does my test get samples but agent doesn't?
2. Why does agent get type strings but test gets name lists?
3. Where does `node_labels` → `entity_types` conversion happen?
4. Is the 97K response real or an artifact of my testing method?

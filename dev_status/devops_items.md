# DevOps Items & Environment Configuration

**Purpose:** Document Docker orchestration, environment configuration, and deployment patterns.

**Last Updated:** October 3, 2025

---

## Docker Compose Configuration

### Two Docker Compose Files

The project has **two** docker-compose.yml files serving different purposes:

| Location | Purpose | Network | When Used |
|----------|---------|---------|-----------|
| `/assistant/docker-compose.yml` | **Multi-service orchestration** (recommended) | `demes-assistant-network` | `make up`, `make rebuild`, local dev |
| `/backend/docker-compose.yml` | Standalone backend development | `backend-network` | Manual `docker-compose up` from backend/ |

### Current Active Configuration

**Active:** Parent directory (`/assistant/docker-compose.yml`)

**Evidence:**
```bash
$ docker ps --format "table {{.Names}}\t{{.Networks}}"
backend            demes-assistant-network
graphrag-mcp       demes-assistant-network
memgraph-db        demes-assistant-network
```

All services are on the shared network defined in parent docker-compose.yml.

---

## Environment File Loading

### Backend Environment Priority

When the backend container starts, environment variables are loaded in this priority (highest to lowest):

1. **`docker-compose.yml` `environment:` section** (highest priority)
   ```yaml
   environment:
     DEBUG: "true"
     LOG_FORMAT: text
     LOG_LEVEL: DEBUG
     PHOENIX_ENABLED: "true"
     PHOENIX_ENDPOINT: http://phoenix-server:6006
     GRAPHRAG_MCP_URL: http://graphrag-mcp:8001/mcp
   ```

2. **`env_file:` directive** (from docker-compose.yml line 118-119)
   ```yaml
   env_file:
     - ./backend/src/.env
   ```
   Loads: `/assistant/backend/src/.env`

3. **Dockerfile ENV directives** (lowest priority)
   - Set during image build
   - Can be overridden by above

### Build vs Runtime

**Important distinction:**

- **Build time** (`make rebuild`, `docker-compose build`):
  - Only Dockerfile instructions are executed
  - `.env` files are NOT loaded during build
  - Environment variables from docker-compose.yml are NOT available

- **Runtime** (`make up`, `docker-compose up`):
  - Container starts with image from build
  - `.env` file is loaded via `env_file:` directive
  - `environment:` variables are set
  - Application reads from `backend/src/.env` via Pydantic BaseSettings

### Which .env File Does Backend Use?

**Answer:** `/assistant/backend/src/.env`

**Flow:**
1. `make rebuild` → `docker-compose build --no-cache` (parent directory)
2. `docker-compose.yml` specifies: `env_file: - ./backend/src/.env`
3. Container at runtime loads environment from `backend/src/.env`
4. Pydantic Settings in `app/core/config.py` reads from environment

### Network-Specific Overrides

The parent docker-compose.yml overrides certain URLs for container networking:

```yaml
# In docker-compose.yml:
GRAPHRAG_MCP_URL: http://graphrag-mcp:8001/mcp    # Container name
PHOENIX_ENDPOINT: http://phoenix-server:6006      # Container name

# In backend/src/.env:
GRAPHRAG_MCP_URL=http://localhost:8001/mcp        # Ignored, overridden by compose
```

This allows services to communicate via Docker's internal DNS.

---

## Service Access Patterns

### From Host Machine

When accessing from your local machine:
```bash
curl http://localhost:8000/api/v1/mcp/servers
curl http://localhost:8001              # graphrag-mcp
curl http://localhost:3001              # memgraph-lab
```

### Between Containers (Docker Network)

Services communicate using container names:
```python
# Backend connecting to GraphMCP:
GRAPHRAG_MCP_URL: http://graphrag-mcp:8001/mcp

# Backend connecting to Phoenix:
PHOENIX_ENDPOINT: http://phoenix-server:6006

# GraphMCP connecting to Memgraph:
MEMGRAPH_HOST: memgraph-db
MEMGRAPH_PORT: 7687
```

---

## Common Commands

### Parent Directory (Recommended)

```bash
cd /assistant

# Start all services
make up

# Rebuild after code changes
make rebuild

# Restart specific service
docker-compose restart backend

# View logs
make logs-backend
docker-compose logs -f backend

# Stop all
make down
```

### Backend Directory (Standalone)

```bash
cd /assistant/backend

# Start backend only (not recommended - services won't connect)
docker-compose up -d

# This creates backend-network, NOT demes-assistant-network
```

---

## Production Deployment

### Azure Container Instances

**Pattern:** Each service runs as separate container instance

**Differences from local:**
- No docker-compose orchestration
- Services communicate via Azure internal DNS or public URLs
- Environment variables come from Azure Key Vault
- No volume mounts (code is in image)

**Configuration:**
- Each service has its own `.env.example` for reference
- Production values stored in Azure Key Vault
- Azure DevOps pipelines inject environment variables at deploy time

### Environment Strategy

| Environment | Location | Purpose |
|-------------|----------|---------|
| **Parent `.env`** | `/assistant/.env` | Docker orchestration only (ports, container names) |
| **Submodule `.env`** | `/backend/src/.env`, `/graphmcp/.env`, etc. | Service configuration (API keys, models, URLs) |
| **Azure Key Vault** | Production | Secrets and configuration for deployed containers |

**Critical:** Azure DevOps pipelines only see submodule directories, NOT parent `.env`.

---

## Troubleshooting

### Issue: Environment variables not taking effect

**Symptoms:** Changed `.env` but service still uses old values

**Solution:**
```bash
# 1. Restart service (reloads .env)
docker-compose restart backend

# 2. If still not working, rebuild
make rebuild
docker-compose up -d
```

### Issue: Services can't communicate

**Symptoms:** Backend can't connect to graphrag-mcp or memgraph

**Check:**
```bash
# 1. Verify all services are on same network
docker ps --format "table {{.Names}}\t{{.Networks}}"

# 2. Should all show: demes-assistant-network
# If not, you're mixing parent and backend docker-composes

# 3. Fix: Stop all and use parent docker-compose
docker stop $(docker ps -aq)
cd /assistant
make up
```

### Issue: Port conflicts

**Symptoms:** `address already in use` error

**Solution:**
```bash
# Check what's using the port
lsof -i :8000

# Change port in parent .env
echo "BACKEND_PORT=8001" >> .env

# Restart
make restart
```

---

## References

- Parent orchestration: `/assistant/docker-compose.yml`
- Backend config: `/assistant/backend/src/.env`
- Makefile: `/assistant/Makefile`
- Network diagram: `/assistant/dev_status/docker_architecture.md`

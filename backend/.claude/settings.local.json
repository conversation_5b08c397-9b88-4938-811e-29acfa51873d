{"permissions": {"allow": ["Bash(tree:*)", "<PERSON><PERSON>(cat:*)", "Read(//Users/<USER>/Repos/DEMES/GraySkyGenAI-SitRep-Summarizer/**)", "Read(//Users/<USER>/Repos/DEMES/GraySkyGenAI-SitRep-Summarizer/app/**)", "Read(//Users/<USER>/Repos/DEMES/GraySkyGenAI-SitRep-Summarizer/app/mcp_client/**)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(uvicorn:*)", "<PERSON><PERSON>(curl:*)", "Bash(find:*)", "Bash(ls:*)", "Bash(uv sync:*)", "<PERSON><PERSON>(uv run:*)", "Bash(lsof:*)", "Read(//Users/<USER>/Repos/DEMES/**)", "WebSearch", "<PERSON><PERSON>(uv pip:*)", "<PERSON><PERSON>(timeout 3 uv run:*)", "Bash(docker build:*)", "Bash(./test-docker.sh:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(timeout 5 uv run:*)", "Bash(uv add:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(timeout 60 uv run:*)", "<PERSON><PERSON>(docker ps:*)", "Bash(docker logs:*)", "WebFetch(domain:docs.langchain.com)", "WebFetch(domain:langchain-ai.github.io)", "WebFetch(domain:github.com)", "mcp__langgraph-docs-mcp__list_doc_sources", "WebFetch(domain:memgraph.com)", "mcp__langgraph-docs-mcp__fetch_docs", "<PERSON><PERSON>(git mv:*)", "<PERSON><PERSON>(make:*)", "<PERSON><PERSON>(docker-compose:*)", "Bash(pgrep:*)", "<PERSON><PERSON>(docker:*)", "Bash(cp:*)", "WebFetch(domain:python.langchain.com)", "Read(//private/tmp/**)", "Bash(xargs kill:*)", "Bash(/dev/null)", "<PERSON><PERSON>(pkill:*)", "Read(//tmp/**)"], "deny": [], "ask": []}, "outputStyle": "default"}
# GraphRAG MCP Server for Emergency Management

A production-ready Model Context Protocol (MCP) server that exposes emergency management knowledge graphs through deterministic, composable operations. Built with Memgraph for high-performance graph operations and FastMCP for seamless LLM integration.

> 📚 **For detailed technical documentation, see [ARCHITECTURE.md](ARCHITECTURE.md)**  
> 🤖 **For AI agents using this server, see [initial_context.md](initial_context.md)** - Graph structure, entity schemas, and tool selection guide

## Table of Contents

- [Quick Start](#quick-start)
- [Features](#features)
- [Installation](#installation)
- [Configuration](#configuration)
- [Available Tools](#available-tools)
- [Usage Examples](#usage-examples)
- [Emergency Management Schema](#emergency-management-schema)
- [Deployment](#deployment)
- [Troubleshooting](#troubleshooting)
- [Contributing](#contributing)

## Quick Start

```bash
# 1. Start Memgraph database
cd memgraph && docker-compose up -d

# 2. Install and configure
curl -LsSf https://astral.sh/uv/install.sh | sh  # Install uv
uv sync                                          # Install dependencies
cp .env.example .env                             # Configure environment

# 3. Start the server
./run.sh

# 4. Test with interactive client
uv run python client.py
```

## Features

- 🚀 **High-Performance Graph Operations** - Lightning-fast queries with Memgraph
- 🔍 **22+ MCP Tools** - Comprehensive graph operations organized in 5 categories
- 🏥 **Emergency Management Focus** - Pre-built schema for incident response
- 🔒 **Safety First** - Query validation and dangerous operation prevention
- 📊 **Rich Analytics** - Aggregations, statistics, and pattern detection
- 🔄 **Smart Caching** - Optimized performance with intelligent schema caching
- 🌐 **Dual Transport Modes** - Both stdio (local) and HTTP (network) support
- 📝 **Production Ready** - Structured logging, error handling, and monitoring

## Installation

### Prerequisites

- **Python 3.10+** - Required for MCP package
- **uv** - Fast Python package manager ([install](https://github.com/astral-sh/uv))
- **Memgraph 2.0+** - Graph database ([download](https://memgraph.com/download))
- **Docker** (optional) - For containerized Memgraph

### Setup Steps

```bash
# Clone repository
git clone https://github.com/your-org/graphrag-mcp-server.git
cd graphrag-mcp-server

# Install uv if needed
curl -LsSf https://astral.sh/uv/install.sh | sh

# Install dependencies
uv sync

# Configure environment
cp .env.example .env
# Edit .env with your Memgraph connection details and embedding model dimension

# Start Memgraph (using Docker)
cd memgraph && docker-compose up -d

# Load sample data (optional)
docker exec -i graphrag-memgraph mgconsole < data/schema/emergency_management.cypher

# Run the server
./run.sh
```

### Vector Search Setup (Optional)

If your graph nodes have embeddings, enable vector search with your choice of embedding provider:

#### Option 1: Local Embeddings (Recommended - No API Costs)

Uses HuggingFace models via sentence-transformers. Runs locally, no API keys needed.

```bash
# 1. Configure local embeddings in .env
ENABLE_VECTOR_SEARCH=true
LOCAL_EMBEDDING=true
LOCAL_EMBEDDING_MODEL=BAAI/bge-small-en-v1.5
LOCAL_EMBEDDING_DEVICE=cpu  # or cuda for GPU
LOCAL_EMBEDDING_DIMENSION=384

# 2. Create vector indices
uv run python scripts/create_vector_indices.py

# 3. Restart server to load model
./run.sh

# On first run, the model will be downloaded from HuggingFace (~90MB for bge-small)
# Subsequent runs use cached model (fast startup)
```

**Benefits:**
- ✅ No API costs - runs completely locally
- ✅ Fast inference (50-100ms per query on CPU)
- ✅ Privacy - no data leaves your infrastructure
- ✅ Must match your database embeddings (BAAI/bge-small-en-v1.5)

#### Option 2: Azure OpenAI Embeddings

Uses Azure OpenAI API. Requires API credentials and incurs costs per request.

```bash
# 1. Configure Azure OpenAI in .env
ENABLE_VECTOR_SEARCH=true
LOCAL_EMBEDDING=false
OPENAI_API_KEY=your_api_key_here
OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
OPENAI_EMBEDDING_DEPLOYMENT=text-embedding-3-small
OPENAI_EMBEDDING_DIMENSION=1536

# 2. Create vector indices
uv run python scripts/create_vector_indices.py

# 3. Restart server
./run.sh
```

**Benefits:**
- ✅ Higher quality embeddings (larger models available)
- ✅ No local compute needed
- ❌ API costs per query
- ⚠️ Only use if your database embeddings were created with the same Azure OpenAI model

**IMPORTANT:** The embedding model used for queries **MUST match** the model used to create embeddings in your database. Mixing models (e.g., querying with OpenAI when database has BGE embeddings) produces meaningless results.

## Configuration

### Key Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `MCP_TRANSPORT` | Transport mode: `stdio` or `streamable-http` | `stdio` |
| `HTTP_PORT` | Port for HTTP transport | `8000` |
| `MEMGRAPH_HOST` | Memgraph server host | `localhost` |
| `MEMGRAPH_PORT` | Memgraph server port | `7687` |
| `LOG_LEVEL` | Logging level | `INFO` |
| `CACHE_TTL_SECONDS` | Schema cache TTL | `300` |

> 📖 **See [ARCHITECTURE.md#configuration](ARCHITECTURE.md#configuration) for complete configuration options**

## Available Tools

The server provides **22 tools** organized into 5 categories:

### 📊 Schema Discovery (5 tools)
- `get_schema` - Complete graph schema with statistics
- `describe_entity_type` - Detailed entity type information
- `describe_relationship_type` - Relationship type details
- `get_node_statistics` - Node type statistics
- `get_relationship_statistics` - Relationship statistics

### 🔍 Entity Search (4 tools)
- `search_entities` - Advanced entity search with filters
- `get_entity` - Get specific entity with relationships
- `find_entities_by_time_range` - Time-based entity search
- `search_by_pattern` - Custom Cypher pattern search

### 🌐 Graph Traversal (4 tools)
- `get_neighbors` - Find connected nodes at depth
- `find_paths` - Find paths between entities
- `traverse_pattern` - Complex traversal patterns
- `find_common_neighbors` - Find shared connections

### 📈 Aggregation & Analytics (4 tools)
- `aggregate_by_type` - Group and aggregate entities
- `calculate_statistics` - Statistical analysis
- `find_patterns` - Detect graph patterns
- `execute_cypher` - Custom Cypher queries (with safety)

### ⚙️ Administration (5 tools)
- `get_health` - Server and database health
- `refresh_cache` - Refresh schema cache
- `get_query_suggestions` - AI-powered query suggestions
- `get_database_info` - Database information
- `clear_graph` - Clear all data (use with caution!)

> 📖 **See [ARCHITECTURE.md#tool-architecture](ARCHITECTURE.md#tool-architecture) for detailed tool documentation with sequence and flow diagrams**

## Usage Examples

### Interactive Client

```bash
# Start interactive client
uv run python client.py

# Quick commands
🔧 MCP> quick schema       # Get complete graph schema
🔧 MCP> quick stats        # Get node statistics
🔧 MCP> quick incidents    # List recent incidents
🔧 MCP> quick bottlenecks  # Find resource bottlenecks

# Direct tool calls
🔧 MCP> call search_entities
🔧 MCP> describe get_neighbors
```

### Python SDK

```python
import asyncio
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

async def example():
    # Connect to server
    server_params = StdioServerParameters(
        command="uv",
        args=["run", "python", "main.py"]
    )

    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            await session.initialize()

            # Search for high-severity incidents
            result = await session.call_tool("search_entities", {
                "entity_type": "Incident",
                "properties": {"severity": {"min": 4}},
                "limit": 10
            })

            # Find resource bottlenecks
            patterns = await session.call_tool("find_patterns", {
                "base_entity": "Resource",
                "pattern_type": "resource_bottleneck",
                "threshold": {"utilization_pct": 75}
            })

            return result, patterns

asyncio.run(example())
```

### Common Query Patterns

```python
# Find all resources responding to hurricanes
await session.call_tool("traverse_pattern", {
    "pattern": "(:Incident {type: 'hurricane'})<-[:RESPONDED_TO]-(r:Resource)",
    "limit": 50
})

# Analyze incident distribution
await session.call_tool("aggregate_by_type", {
    "entity_type": "Incident",
    "group_by": "type",
    "metrics": ["count", "avg_severity"]
})

# Find paths between entities
await session.call_tool("find_paths", {
    "from_type": "Resource",
    "from_id": "TRUCK-001",
    "to_type": "Incident",
    "to_id": "INC-2024-001",
    "max_depth": 4
})
```

> 📖 **See [ARCHITECTURE.md#advanced-patterns](ARCHITECTURE.md#advanced-patterns) for complex query examples**

## Emergency Management Schema

### Core Entities

| Entity | Description | Key Properties |
|--------|-------------|---------------|
| **Incident** | Emergency events | `id`, `type`, `severity`, `timestamp`, `status` |
| **Resource** | Equipment/vehicles | `id`, `type`, `status`, `capacity` |
| **Department** | Response organizations | `id`, `name`, `type`, `staff_count` |
| **Location** | Geographic areas | `id`, `name`, `latitude`, `longitude` |
| **Person** | Personnel | `id`, `name`, `role`, `department_id` |

### Key Relationships

| Relationship | From → To | Properties |
|--------------|-----------|------------|
| **RESPONDED_TO** | Resource/Department → Incident | `response_time`, `arrival_time` |
| **LOCATED_AT** | Entity → Location | - |
| **BELONGS_TO** | Resource → Department | - |
| **COORDINATES_WITH** | Department ↔ Department | `protocol`, `priority` |

> 📖 **See [ARCHITECTURE.md#database-schema](ARCHITECTURE.md#database-schema) for complete schema documentation**

## Deployment

### Docker

```bash
# Build and run with Docker Compose
docker-compose up -d

# Or use the provided Dockerfile
docker build -t graphrag-mcp .
docker run -p 8000:8000 graphrag-mcp
```

### Kubernetes

```yaml
# Deploy to Kubernetes
kubectl apply -f k8s/deployment.yaml
kubectl apply -f k8s/service.yaml

# Scale horizontally
kubectl scale deployment graphrag-mcp --replicas=3
```

### Environment-Specific Configs

```bash
# Development
MCP_TRANSPORT=stdio LOG_LEVEL=DEBUG ./run.sh

# Production
MCP_TRANSPORT=streamable-http HTTP_PORT=8000 LOG_LEVEL=INFO ./run.sh

# With authentication
MEMGRAPH_USER=admin MEMGRAPH_PASSWORD=secret ./run.sh
```

> 📖 **See [ARCHITECTURE.md#deployment-considerations](ARCHITECTURE.md#deployment-considerations) for production deployment patterns**

## Troubleshooting

### Common Issues

#### Connection Failed
```bash
# Check Memgraph is running
docker ps | grep memgraph

# Test connection
docker exec -it graphrag-memgraph mgconsole
```

#### Schema Not Found
```bash
# Load schema
docker exec -i graphrag-memgraph mgconsole < data/schema/emergency_management.cypher

# Refresh cache
uv run python -c "from client import test_tool; test_tool('refresh_cache')"
```

#### Port Already in Use
```bash
# Find process using port
lsof -i :8000

# Use different port
HTTP_PORT=8001 ./run.sh
```

> 📖 **See [ARCHITECTURE.md#monitoring-and-observability](ARCHITECTURE.md#monitoring-and-observability) for debugging and monitoring details**

## API Reference

### Tool Response Format

All tools return responses in this format:
```json
{
    "success": true,
    "data": {...},      // Tool-specific results
    "metadata": {
        "query_time_ms": 45,
        "row_count": 10,
        "cache_hit": false
    }
}
```

### Error Response Format

```json
{
    "success": false,
    "error": {
        "type": "ValidationError",
        "message": "Invalid entity_type",
        "details": {...}
    }
}
```

### HTTP API Endpoints

When running in HTTP mode:

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/health` | GET | Health check |
| `/mcp/initialize` | POST | Initialize connection |
| `/mcp/tools/list` | GET | List available tools |
| `/mcp/tools/call` | POST | Execute tool |
| `/mcp/sse` | GET | Server-sent events stream |

## Agent Integration

> 🤖 **For AI agents using this server, see [initial_context.md](initial_context.md)** - Complete guide to graph structure, tool selection workflows, and navigation patterns for the Florida Emergency Management knowledge graph.

### Suggested Agent System Prompt

When using this GraphRAG MCP server with LLM agents, provide your governing agent with the following guidance to optimize tool selection and usage:

````markdown
You have access to a graph database containing interconnected entities and relationships.
Use the graph operations tools to analyze structure, find patterns, and answer complex queries.

**TOOL USAGE GUIDELINES:**

**Schema Discovery Tools** - Start here to understand the graph:
- `get_schema()` - Get complete graph structure (node types, relationships, properties)
- `describe_entity_type(type)` - Detailed information about a specific entity type
- `get_node_statistics()` - Count of nodes by type

**Entity Search Tools** - Find specific nodes:
- `search_entities(entity_type, properties, limit)` - Search with filters
- `get_entity(entity_type, entity_id)` - Get specific entity with relationships
- `find_entities_by_time_range(entity_type, start, end)` - Time-based search

**Graph Traversal Tools** - Explore connections:
- `get_neighbors(entity_type, entity_id, depth)` - Find connected nodes
- `find_paths(from_type, from_id, to_type, to_id)` - Find paths between entities
- `traverse_pattern(pattern, limit)` - Complex Cypher pattern traversal

**Analytics Tools** - Analyze patterns and aggregate data:
- `aggregate_by_type(entity_type, group_by, metrics)` - Group and aggregate
- `calculate_statistics(entity_type, property)` - Statistical analysis
- `find_patterns(base_entity, pattern_type)` - Detect graph patterns

**BEST PRACTICES:**

1. **Start Simple**: Begin with schema discovery (`get_schema`) before complex queries
2. **Use Filters**: Always use `limit` parameters to avoid overwhelming results (default: 100)
3. **Combine Tools**: Use multiple tools for comprehensive analysis
   - Example: `get_schema()` → `search_entities()` → `get_neighbors()`
4. **Explain Choices**: Tell users why you selected specific tools and what the results mean
5. **Handle Errors**: If a query fails, try simpler alternatives or ask for clarification
6. **Iterate**: Start with broad queries, then narrow based on results

**EXAMPLE WORKFLOWS:**

Exploring a new graph:
1. `get_schema()` - Understand available entity types and relationships
2. `get_node_statistics()` - See data distribution
3. `search_entities(entity_type, limit=10)` - Sample entities
4. `get_entity(entity_type, entity_id)` - Examine specific entity with relationships

Finding connections:
1. `search_entities()` - Find starting entity
2. `get_neighbors(entity_id, depth=2)` - Explore nearby connections
3. `find_paths(from_id, to_id)` - Discover specific relationships

Analyzing patterns:
1. `aggregate_by_type()` - Get distribution statistics
2. `find_patterns(pattern_type="hub_nodes")` - Identify important nodes
3. `calculate_statistics()` - Quantitative analysis

**WHEN TO USE CUSTOM CYPHER:**
Use `execute_cypher()` only for:
- Complex queries not covered by existing tools
- Performance-critical custom queries
- Domain-specific pattern matching

Always validate Cypher syntax before execution.
````

### Tool Selection Strategy

For agents with limited context windows, use this prioritized tool selection:

**Tier 1 (Always Available):**
- `get_schema()` - Essential for understanding graph structure
- `search_entities()` - Core search functionality
- `get_neighbors()` - Basic graph traversal

**Tier 2 (Common Operations):**
- `find_paths()` - Connection discovery
- `aggregate_by_type()` - Data analysis
- `get_entity()` - Detailed entity inspection

**Tier 3 (Advanced):**
- `traverse_pattern()` - Complex queries
- `find_patterns()` - Pattern detection
- `execute_cypher()` - Custom queries

*Note: Research shows that limiting tools to 5-10 most relevant tools (using RAG-based selection) improves accuracy by 3x compared to exposing all tools simultaneously.*

## Integration Examples

### LangChain Integration

```python
from langchain.tools import Tool
from mcp import Client

def create_graph_tools():
    client = Client("http://localhost:8000")

    return [
        Tool(
            name="search_incidents",
            func=lambda q: client.call_tool("search_entities", {
                "entity_type": "Incident",
                "contains_text": q
            }),
            description="Search emergency incidents"
        ),
        Tool(
            name="find_available_resources",
            func=lambda t: client.call_tool("search_entities", {
                "entity_type": "Resource",
                "properties": {"status": "available", "type": t}
            }),
            description="Find available resources by type"
        )
    ]
```

### Async Operations

```python
async def analyze_emergency_response():
    async with Client("http://localhost:8000") as client:
        # Parallel tool execution
        results = await asyncio.gather(
            client.call_tool("get_health", {}),
            client.call_tool("get_node_statistics", {}),
            client.call_tool("find_patterns", {
                "base_entity": "Resource",
                "pattern_type": "hub_nodes"
            })
        )
        return results
```

## Performance

### Optimization Tips

1. **Use indexes for common queries**
   ```cypher
   CREATE INDEX ON :Incident(id);
   CREATE INDEX ON :Incident(type, severity);
   ```

2. **Leverage caching**
   - Schema cache: 5-minute TTL by default
   - Adjust `CACHE_TTL_SECONDS` for your needs

3. **Limit traversal depth**
   - Default max depth: 5
   - Use `MAX_TRAVERSAL_DEPTH` to adjust

4. **Batch operations when possible**
   - Use `execute_cypher` for complex multi-operation queries

> 📖 **See [ARCHITECTURE.md#performance-monitoring](ARCHITECTURE.md#performance-monitoring) for detailed performance optimization**


## Backlog
Backlog items and minor future improvements

1. Change config to use Pydantic Settings like backend; note that also need to change /src/embeddings/onnx_client.py to import embedding_config class instead of directly using os.getenv()

## Contributing

We welcome contributions! Please follow these guidelines:

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Run tests (`uv run pytest tests/`)
4. Ensure code quality (`uv run black src/ && uv run ruff src/`)
5. Commit changes (`git commit -m 'Add amazing feature'`)
6. Push to branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

### Development Setup

```bash
# Install dev dependencies
uv sync

# Run tests
uv run pytest tests/

# Code formatting
uv run black src/
uv run ruff src/

# Type checking
uv run mypy src/
```

## License

MIT License - See [LICENSE](LICENSE) file for details

## Support

- **Documentation**: [ARCHITECTURE.md](ARCHITECTURE.md) for technical details
- **Issues**: [GitHub Issues](https://github.com/your-org/graphrag-mcp-server/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-org/graphrag-mcp-server/discussions)

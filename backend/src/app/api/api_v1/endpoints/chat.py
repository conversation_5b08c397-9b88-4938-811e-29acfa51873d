from fastapi import APIRouter, Depends
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import Optional

from app.api.deps import get_current_settings
from app.core.config import Settings
from app.services.streaming import format_sse_message, format_sse_done, StreamMessage, StreamType
from app.services.langgraph_service import LangGraphService
from app.services.context import context_manager
from app.core.logging import get_logger

router = APIRouter()
logger = get_logger(__name__)


class ChatRequest(BaseModel):
    """Request model for chat endpoint."""
    message: str
    conversation_id: Optional[str] = None


class ChatResponse(BaseModel):
    """Response model for non-streaming chat."""
    response: str
    conversation_id: str


@router.post("/stream", response_class=StreamingResponse)
async def stream_chat(
    request: ChatRequest,
    settings_obj: Settings = Depends(get_current_settings)
):
    """
    Stream a chat response using LangGraph orchestration.

    LangGraph orchestration provides:
    - Parallel MCP calls for optimal performance
    - Smart routing with LLM-driven tool selection
    - Multi-type streaming (STATUS, REASONING, SOURCES, CONTENT)
    - Automatic tool discovery from configured MCP servers

    The response uses SSE (Server-Sent Events) streaming format.
    """
    async def generate_response():
        conversation_id = request.conversation_id or "default"

        # Infrastructure status
        yield format_sse_message(StreamMessage(
            StreamType.STATUS,
            {"message": "Processing your request..."}
        ))

        # Add user message to context
        context_manager.add_message(conversation_id, "user", request.message)

        # Get conversation history
        messages = context_manager.get_messages(conversation_id)

        # Use LangGraph orchestration with context injection (parameterless)
        logger.info("Using LangGraph orchestration",
                   extra={"conversation_id": conversation_id})

        langgraph_service = LangGraphService()  # Parameterless initialization!
        assistant_response = ""

        async for message in langgraph_service.generate_response_with_streaming(messages, conversation_id):
            if message.type.value == "content":
                assistant_response += message.data.get("chunk", "")
            yield format_sse_message(message)

        # Store final response in context
        if assistant_response:
            context_manager.add_message(conversation_id, "assistant", assistant_response)

        # Send proper SSE stream termination
        yield format_sse_done()
    
    return StreamingResponse(
        generate_response(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control, Content-Type, Authorization",
            "Access-Control-Expose-Headers": "X-Correlation-ID",
            "X-Accel-Buffering": "no"  # Disable nginx buffering for real-time streaming
        }
    )


@router.post("/", response_model=ChatResponse)
async def chat(
    request: ChatRequest,
    settings: Settings = Depends(get_current_settings)
):
    """
    Non-streaming chat endpoint using LangGraph orchestration.
    
    Collects all streaming messages and returns final response.
    """
    conversation_id = request.conversation_id or "default"
    
    # Add user message to context
    context_manager.add_message(conversation_id, "user", request.message)
    
    # Get conversation history
    messages = context_manager.get_messages(conversation_id)
    
    # Use LangGraph orchestration
    logger.info("Using LangGraph orchestration (non-streaming)",
               extra={"conversation_id": conversation_id})
    
    langgraph_service = LangGraphService()
    assistant_response = ""
    
    # Collect all content chunks from streaming
    async for message in langgraph_service.generate_response_with_streaming(messages, conversation_id):
        if message.type.value == "content":
            assistant_response += message.data.get("chunk", "")
    
    # Store final response in context
    if assistant_response:
        context_manager.add_message(conversation_id, "assistant", assistant_response)
    
    return ChatResponse(
        response=assistant_response,
        conversation_id=conversation_id
    )
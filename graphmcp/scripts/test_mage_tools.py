"""
Test script for MAGE tools
"""
import asyncio
import json
from mcp import Client<PERSON>ession, StdioServerParameters
from mcp.client.stdio import stdio_client


async def test_all_mage_tools():
    """Test all 5 MAGE tools"""
    server_params = StdioServerParameters(
        command='uv',
        args=['run', 'python', 'main.py'],
        env=None
    )

    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            await session.initialize()

            print("=" * 80)
            print("TESTING MAGE TOOLS")
            print("=" * 80)

            # Test 1: calculate_centrality (degree)
            print("\n1. Testing calculate_centrality (degree algorithm)...")
            try:
                result = await session.call_tool(
                    'calculate_centrality',
                    arguments={'algorithm': 'degree', 'limit': 3}
                )
                data = json.loads(result.content[0].text)
                print(f"   ✓ Success! Found {data['result_count']} nodes")
                print(f"   Top node: {data['nodes'][0]['id']} with score {data['nodes'][0]['score']}")
            except Exception as e:
                print(f"   ✗ Failed: {e}")

            # Test 2: calculate_similarity
            print("\n2. Testing calculate_similarity (jaccard algorithm)...")
            try:
                result = await session.call_tool(
                    'calculate_similarity',
                    arguments={'algorithm': 'jaccard', 'node_id': '566189', 'limit': 3}
                )
                data = json.loads(result.content[0].text)
                if data['success']:
                    print(f"   ✓ Success! Found {data['result_count']} similar nodes")
                    if data['result_count'] > 0:
                        print(f"   Most similar: {data['similar_nodes'][0]['id']} (score: {data['similar_nodes'][0]['similarity_score']})")
                else:
                    print(f"   ⚠ Returned success=false: {data.get('error', 'unknown error')}")
            except Exception as e:
                print(f"   ✗ Failed: {e}")

            # Test 3: detect_communities
            print("\n3. Testing detect_communities (connected_components algorithm)...")
            try:
                result = await session.call_tool(
                    'detect_communities',
                    arguments={'algorithm': 'connected_components', 'min_size': 2}
                )
                data = json.loads(result.content[0].text)
                if data['success']:
                    print(f"   ✓ Success! Found {data['community_count']} communities")
                    if data['community_count'] > 0:
                        print(f"   Largest community: {data['communities'][0]['size']} nodes")
                else:
                    print(f"   ⚠ Returned success=false: {data.get('error', 'unknown error')}")
            except Exception as e:
                print(f"   ✗ Failed: {e}")

            # Test 4: find_shortest_paths
            print("\n4. Testing find_shortest_paths...")
            try:
                result = await session.call_tool(
                    'find_shortest_paths',
                    arguments={
                        'source_id': '566189',
                        'target_id': '524072',
                        'max_depth': 3,
                        'algorithm': 'shortest_path'
                    }
                )
                data = json.loads(result.content[0].text)
                if data['success']:
                    print(f"   ✓ Success! Found {data['path_count']} path(s)")
                    if data['path_count'] > 0:
                        print(f"   Path length: {data['paths'][0]['path_length']} hops")
                else:
                    print(f"   ⚠ Returned success=false: {data.get('error', 'unknown error')}")
            except Exception as e:
                print(f"   ✗ Failed: {e}")

            # Test 5: predict_relationships
            print("\n5. Testing predict_relationships (common_neighbors algorithm)...")
            try:
                result = await session.call_tool(
                    'predict_relationships',
                    arguments={
                        'algorithm': 'common_neighbors',
                        'source_type': 'Mission',
                        'target_type': 'County',
                        'limit': 3,
                        'min_confidence': 0.1
                    }
                )
                data = json.loads(result.content[0].text)
                if data['success']:
                    print(f"   ✓ Success! Found {data['prediction_count']} predictions")
                    if data['prediction_count'] > 0:
                        pred = data['predictions'][0]
                        print(f"   Top prediction: {pred['source']['id']} -> {pred['target']['id']} (confidence: {pred['confidence']})")
                else:
                    print(f"   ⚠ Returned success=false: {data.get('error', 'unknown error')}")
            except Exception as e:
                print(f"   ✗ Failed: {e}")

            print("\n" + "=" * 80)
            print("TESTING COMPLETE")
            print("=" * 80)


if __name__ == "__main__":
    asyncio.run(test_all_mage_tools())

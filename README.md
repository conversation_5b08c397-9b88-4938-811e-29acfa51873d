# GraySkyGenA<PERSON> Assistant - Parent Repository

This is the parent repository that manages five main components of the GraySkyGenAI Assistant project as Git submodules:

- **backend** - The main API backend service
- **front-end** - The React frontend application
- **graphmcp** - The Graph MCP (Model Context Protocol) service
- **memgraph** - The Memgraph MAGE database (with advanced graph algorithms)
- **phoenix** - The Phoenix web framework service

## Repository Structure

```
.
├── backend/          # Backend API service (submodule)
├── front-end/         # Frontend React app (submodule)
├── graphmcp/          # Graph MCP service (submodule)
├── memgraph/          # Memgraph MAGE database (submodule)
├── phoenix/           # Phoenix web framework service (submodule)
├── .gitmodules        # Submodule configuration
└── README.md          # This file
```

## Getting Started

### Initial Clone

When cloning this repository for the first time:

```bash
# Clone the parent repo
git clone https://<EMAIL>/FDEM/Demes-FDEM-GraySky-GenAI/_git/GraySkyGenAI-Assistant-Parent
cd GraySkyGenAI-Assistant-Parent

# Initialize and update all submodules
git submodule init
git submodule update

# Or do both in one command
git submodule update --init --recursive
```

### Alternative: Clone with Submodules

```bash
# Clone parent repo and all submodules in one command
git clone --recurse-submodules https://<EMAIL>/FDEM/Demes-FDEM-GraySky-GenAI/_git/GraySkyGenAI-Assistant-Parent
```

## Working with Submodules

### Understanding Submodule State

Each submodule points to a specific commit in its respective repository. The parent repository tracks these commit references.

```bash
# Check submodule status
git submodule status

# Check overall repository status (including submodules)
git status
```

### Making Changes to Submodules

#### 1. Working in Individual Submodules

```bash
# Navigate to a submodule and work normally
cd backend
git checkout feature/my-feature
# Make changes, add, commit as usual
git add .
git commit -m "Add new feature"
git push origin feature/my-feature
```

#### 2. When to Update the Parent Repository

**You need to update the parent repository in these scenarios:**

- ✅ **After merging a submodule branch to main** - Update parent to point to the new main commit
- ✅ **When you want to "release" a specific combination** - Lock all submodules to specific commits
- ✅ **When coordinating changes across multiple submodules** - Ensure compatible versions work together
- ❌ **For every individual commit** - Not necessary for work-in-progress commits

#### 3. Updating Parent After Submodule Changes

```bash
# After making changes in submodules, return to parent directory
cd /path/to/parent

# Check which submodules have new commits
git status
# Will show: modified: backend (new commits)
#           modified: front-end (new commits)

# Add the updated submodule references
git add backend front-end graphmcp
git commit -m "Update submodules: backend v1.2.0, frontend v2.1.0"

# Push to parent repository
git push origin main
```

### Pulling Latest Changes

#### Update Parent and All Submodules

```bash
# Pull parent repository changes
git pull

# Update all submodules to their tracked commits
git submodule update --recursive

# Or combine both
git pull --recurse-submodules
```

#### Pull Latest from Submodule Remotes

```bash
# Update submodules to latest commits on their tracked branches
git submodule update --remote

# Update specific submodule
git submodule update --remote backend

# Then commit the updated references in parent
git add .
git commit -m "Update submodules to latest versions"
```

### Useful Submodule Commands

```bash
# Run a command in all submodules
git submodule foreach 'git status'
git submodule foreach 'git pull origin main'

# Check which branch each submodule is on
git submodule foreach 'echo "$name: $(git branch --show-current)"'

# Reset submodules to tracked commits (discards local changes)
git submodule update --init --force
```

### Branch Management

```bash
# Create feature branch in parent
git checkout -b feature/major-update

# Make changes in submodules, then update parent
git add backend front-end graphmcp
git commit -m "Update all components for major feature"
git push origin feature/major-update
```

## Development Workflow

### Typical Daily Workflow

1. **Start work**: `git pull --recurse-submodules`
2. **Work in submodules**: Make changes, commit, push to feature branches
3. **Test integration**: Verify all components work together
4. **Merge submodule PRs**: Merge feature branches in individual repos
5. **Update parent**: Point parent to new main commits
6. **Create parent PR**: If working on a feature branch

### Release Workflow

1. **Tag submodule releases**: Tag stable versions in each submodule
2. **Update parent to tags**: Point parent to specific release tags
3. **Tag parent release**: Create a release tag in parent repository
4. **Document versions**: Include submodule versions in release notes

## Troubleshooting

### Common Issues

**Submodule in detached HEAD state:**
```bash
cd backend
git checkout main  # or your desired branch
```

**Merge conflicts in .gitmodules:**
```bash
# Edit .gitmodules manually, then:
git add .gitmodules
git rebase --continue
```

**Reset everything to clean state:**
```bash
git reset --hard HEAD
git submodule update --init --recursive --force
```

### Getting Help

```bash
# Submodule help
git submodule --help

# Specific command help
git submodule update --help
```

## Docker Architecture

### Overview

The GraySkyGenAI Assistant uses a **dual-mode Docker architecture** designed for both local development and production deployment:

- **Local Development**: Parent repository orchestrates all 7 containers with shared networking
- **Production**: Each service deploys independently with external service connections

### Architecture Diagram

```
┌─────────────────────────────────────────────────────────────┐
│                    LOCAL DEVELOPMENT                         │
│                                                              │
│  ┌──────────────────────────────────────────────────────┐  │
│  │         demes-assistant-network (bridge)              │  │
│  │                                                        │  │
│  │  ┌──────────┐  ┌──────────┐  ┌──────────┐           │  │
│  │  │ memgraph │  │ phoenix  │  │ graphmcp │           │  │
│  │  │   :7687  │  │  :6006   │  │  :8001   │           │  │
│  │  └────┬─────┘  └────┬─────┘  └────┬─────┘           │  │
│  │       │             │              │                  │  │
│  │  ┌────┴─────────────┴──────────────┴─────┐           │  │
│  │  │           backend :8000                │           │  │
│  │  └────────────────┬───────────────────────┘           │  │
│  │                   │                                    │  │
│  │  ┌────────────────┴───────────────────────┐           │  │
│  │  │          frontend :3000                │           │  │
│  │  └────────────────────────────────────────┘           │  │
│  │                                                        │  │
│  └──────────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────┐
│                  PRODUCTION DEPLOYMENT                       │
│                                                              │
│  ┌──────────┐       ┌──────────┐      ┌──────────┐         │
│  │ External │       │ External │      │ graphmcp │         │
│  │ Memgraph │◄──────┤ Phoenix  │◄─────┤Container │         │
│  │ Service  │       │ Service  │      │          │         │
│  └──────────┘       └──────────┘      └────┬─────┘         │
│                                             │               │
│                         ┌───────────────────┴─────┐         │
│                         │  backend Container      │         │
│                         └───────────┬─────────────┘         │
│                                     │                       │
│                         ┌───────────┴─────────────┐         │
│                         │  frontend Container     │         │
│                         └─────────────────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

### Service Deployment Strategy

| Service | Local Dev | Production | Notes |
|---------|-----------|------------|-------|
| **Memgraph** | Docker container (dev-only) | External managed service | Submodule provides dev setup only |
| **Phoenix** | Docker container (dev-only) | External managed service | Submodule provides dev setup only |
| **GraphMCP** | Docker container | Azure Container Instance | Uses external Memgraph endpoint |
| **Backend** | Docker container | Azure Container Instance | Connects to external Phoenix/GraphMCP |
| **Frontend** | Docker container | Azure Container Instance | Connects via browser to Backend URL |

### Local Development Setup

#### Prerequisites

- Docker and Docker Compose installed
- Git with submodule support
- Make (optional, for convenience commands)

#### Quick Start

```bash
# 1. Clone with submodules
git clone --recurse-submodules <parent-repo-url>
cd GraySkyGenAI-Assistant-Parent

# 2. Copy environment template
cp .env.example .env

# 3. Configure service-specific settings
cp backend/src/.env.example backend/src/.env
cp graphmcp/.env.example graphmcp/.env
# Edit these files with your API keys and model configurations

# 4. Start all services
make up
# OR: docker-compose up -d

# 5. Verify services are running
make status
# OR: docker-compose ps

# 6. Check health
make health
```

#### Makefile Commands

**Service Management:**
```bash
make help          # Show all available commands
make init          # Initialize environment files
make up            # Start all services
make down          # Stop all services
make restart       # Restart all services
make logs          # View logs from all services
make logs-follow   # Follow logs in real-time
make logs-backend  # View logs from specific service
make status        # Show service status
make health        # Run health checks
make clean         # Stop and remove volumes (WARNING: deletes data!)
make rebuild       # Rebuild all containers
make urls          # Show service access URLs
```

**Data Management:**
```bash
# Clear database and load data (recommended for fresh start)
make data-clear-write path=sample_data/Mission_Cypher_all.csv

# Add data without clearing (append to existing)
make data-add path=sample_data/Mission_Cypher.csv
```

**Direct Script Usage:**
```bash
# Using init/load_memgraph.py directly for more control
cd /Users/<USER>/Repos/DEMES/assistant

# Clear and load (recommended)
uv run python init/load_memgraph.py --clear --file sample_data/Mission_Cypher_all.csv

# Load without clearing (append)
uv run python init/load_memgraph.py --file sample_data/Mission_Cypher.csv

# Use default file
uv run python init/load_memgraph.py --clear

# Show help
uv run python init/load_memgraph.py --help
```

#### Data Loading Strategy

The `init/load_memgraph.py` script uses a **two-pass approach** for reliable data loading:

1. **Pass 1 - Create Nodes**: Executes all non-MATCH queries (typically MERGE/CREATE for nodes)
2. **Pass 2 - Create Relationships**: Executes all MATCH queries (typically relationship creation)

This ensures all nodes exist before any relationships reference them, avoiding dependency errors.

**CSV Format:**
- Expects a `cypher_json` column containing JSON objects with a `cypher` field
- Automatically increases CSV field size limit to 10MB for large Cypher statements
- Example format:
  ```csv
  cypher_json
  "{\"cypher\":\"MERGE (m:Mission {id:'123'}) SET m.title='Example'\"}"
  ```

**Available CSV files in `sample_data/`:**
- `Mission_Cypher.csv` - Smaller dataset for testing (~3.6MB)
- `Mission_Cypher_all.csv` - Complete dataset (~13MB)
- `Mission_Cypher_Embeddings.csv` - Dataset with embedding metadata

#### Service Access URLs

Once services are running, access them at:

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs
- **GraphMCP**: http://localhost:8001
- **Phoenix UI**: http://localhost:6006
- **Memgraph Lab**: http://localhost:3001
- **Memgraph Bolt**: bolt://localhost:7687

#### Docker Compose Hierarchy

The orchestration uses a hierarchical approach:

```
assistant/docker-compose.yml (master orchestration)
├── extends: memgraph/docker-compose.yml (memgraph + memgraph-lab)
├── extends: phoenix/docker-compose.yml (phoenix + postgres)
├── builds: graphmcp/Dockerfile
├── builds: backend/Dockerfile
└── builds: frontend/Dockerfile
```

**Key benefits:**
- Each submodule's docker-compose.yml works standalone
- Parent orchestration connects everything with shared networking
- Configuration via environment variables (no code changes needed)

### Production Deployment

#### Deployment Model

Each service deploys **independently** in production:

1. **Memgraph** - Azure Managed Database (endpoint provided)
2. **Phoenix** - Azure Container Instance (endpoint provided)
3. **GraphMCP** - Azure Container Instance
4. **Backend** - Azure Container Instance
5. **Frontend** - Azure Container Instance

#### Azure DevOps Pipeline Strategy

Each submodule repository has its own pipeline:

```yaml
# Example: backend/azure-pipelines.yml
trigger:
  - main

pool:
  vmImage: 'ubuntu-latest'

steps:
  - script: docker build -t backend:$(Build.BuildId) --target production .
  - script: docker push <registry>/backend:$(Build.BuildId)
  - task: AzureCLI@2
    inputs:
      script: az container create ...
```

**Critical**: Pipelines only see their submodule directory, not parent repository.

#### Environment Variable Strategy

**Development (Local):**
- Parent `.env` - orchestration only (ports, network names)
- Submodule `.env` files - complete service configuration

**Production (Azure DevOps):**
- No parent `.env` access
- Each pipeline provisions environment variables independently
- Source: Azure Key Vault, Pipeline Variables, App Configuration

**Service-Specific Configuration:**

| Service | Local Config | Production Config |
|---------|--------------|-------------------|
| **GraphMCP** | `graphmcp/.env` | Azure Key Vault + Pipeline Variables |
| **Backend** | `backend/src/.env` | Azure Key Vault + Pipeline Variables |
| **Frontend** | Build-time `NEXT_PUBLIC_API_URL` | Build argument in pipeline |

#### Production docker-compose.yml Usage

Each submodule's docker-compose.yml supports production via environment variables:

```bash
# Example: Deploy backend to production
cd backend
BUILD_TARGET=production \
EXTERNAL_NETWORK=true \
NETWORK_NAME=azure-vnet \
PHOENIX_ENDPOINT=https://phoenix.azure.com \
GRAPHRAG_MCP_URL=https://graphmcp.azure.com/mcp \
docker-compose up -d
```

**Key differences from development:**
- `BUILD_TARGET=production` - uses production Dockerfile stage
- `EXTERNAL_NETWORK=true` - joins existing Azure network
- External service URLs instead of container names
- No volume mounts for source code
- Production-optimized builds

### Network Topology

#### Local Development Network

**Shared Bridge Network**: `demes-assistant-network`

All containers join this network and communicate via:
- **Container names as hostnames** (e.g., `memgraph-db`, `phoenix-server`)
- **Internal Docker DNS** resolution
- **Port exposure** only for browser access (host machine)

**Service discovery examples:**
```python
# Backend connecting to GraphMCP
GRAPHRAG_MCP_URL = "http://graphrag-mcp:8001/mcp"

# GraphMCP connecting to Memgraph
MEMGRAPH_HOST = "memgraph-db"
MEMGRAPH_PORT = 7687
```

#### Production Network

Each service uses:
- **Public endpoints** or **Azure Private Link** for service connections
- **Environment variables** for dynamic endpoint configuration
- **No shared Docker network** (services deployed in isolation)

### Service Dependencies

**Container dependency tree:**

```
memgraph-db
├── memgraph-lab (depends_on: memgraph-db)
└── graphrag-mcp (depends_on: memgraph-db)

postgres
└── phoenix (depends_on: postgres)

phoenix
└── backend (depends_on: phoenix, graphrag-mcp, memgraph-db)

backend
└── frontend (depends_on: backend)
```

**Startup order:**
1. memgraph-db, postgres (databases)
2. memgraph-lab, phoenix (dependent services)
3. graphrag-mcp (MCP server)
4. backend (API server)
5. frontend (web UI)

### Troubleshooting

#### Container Issues

```bash
# Check container logs
make logs-backend
docker-compose logs -f backend

# Restart specific service
docker-compose restart backend

# Rebuild after Dockerfile changes
make rebuild-backend
docker-compose build --no-cache backend
```

#### Network Issues

```bash
# Check network connectivity
docker-compose exec backend ping graphrag-mcp
docker-compose exec backend curl http://graphrag-mcp:8001/health

# Inspect network
docker network inspect demes-assistant-network

# Recreate network
make down && make up
```

#### Port Conflicts

```bash
# Check port usage
lsof -i :8000
netstat -an | grep 8000

# Change ports in .env
BACKEND_PORT=8080
FRONTEND_PORT=3001
# Then restart: make restart
```

#### Volume/Data Issues

```bash
# View volumes
docker volume ls | grep demes

# Remove and recreate (WARNING: deletes data!)
make clean
make up

# Backup data before cleaning
docker run --rm -v memgraph_data:/data -v $(pwd):/backup \
  alpine tar czf /backup/memgraph-backup.tar.gz /data
```

#### Environment Variable Issues

```bash
# Verify environment variables are loaded
docker-compose config

# Check specific service environment
docker-compose exec backend env | grep PHOENIX

# Reload after .env changes
make restart
```

#### Submodule-Specific Issues

Each submodule has its own `CLAUDE.md` with service-specific troubleshooting:
- `backend/CLAUDE.md` - Backend API issues
- `frontend/CLAUDE.md` - Frontend build/runtime issues
- `graphmcp/CLAUDE.md` - MCP server and Memgraph connectivity
- `memgraph/CLAUDE.md` - Database initialization and queries
- `phoenix/CLAUDE.md` - Observability platform setup

## Repository Links

- **Backend**: https://dev.azure.com/FDEM/Demes-FDEM-GraySky-GenAI/_git/GraySkyGenAI-Assistant
- **Frontend**: https://dev.azure.com/FDEM/Demes-FDEM-GraySky-GenAI/_git/GraySkyGenAI-Assistant-Dev-FE
- **GraphMCP**: https://dev.azure.com/FDEM/Demes-FDEM-GraySky-GenAI/_git/GraySkyGenAI-Assistant-GraphMCP
- **Memgraph**: https://dev.azure.com/FDEM/Demes-FDEM-GraySky-GenAI/_git/GraySkyGenAI-assistant-memgraph
- **Phoenix**: https://dev.azure.com/FDEM/Demes-FDEM-GraySky-GenAI/_git/GraySkyGenAI-assistant-phoenix
- **Parent**: https://dev.azure.com/FDEM/Demes-FDEM-GraySky-GenAI/_git/GraySkyGenAI-Assistant-Parent

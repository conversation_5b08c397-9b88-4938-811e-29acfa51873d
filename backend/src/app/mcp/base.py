"""
Base class for MCP clients.

Provides a common interface for all MCP client implementations.
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional


class MCPClientBase(ABC):
    """Abstract base class for MCP client implementations."""

    def __init__(self, server_url: str, timeout: int = 30):
        """
        Initialize the MCP client.

        Args:
            server_url: The URL of the MCP server
            timeout: Request timeout in seconds
        """
        self.server_url = server_url
        self.timeout = timeout
        self._tools: List[Dict[str, Any]] = []

    @abstractmethod
    async def discover_tools(self) -> List[Dict[str, Any]]:
        """
        Discover available tools from the MCP server.

        Returns:
            List of tool definitions with name, description, and parameters
        """
        pass

    @abstractmethod
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Call a tool on the MCP server.

        Args:
            tool_name: Name of the tool to call
            arguments: Tool arguments

        Returns:
            Tool execution result
        """
        pass

    async def initialize(self) -> None:
        """Initialize the client and discover tools."""
        self._tools = await self.discover_tools()

    def get_tools(self) -> List[Dict[str, Any]]:
        """Get the list of discovered tools."""
        return self._tools

    def has_tool(self, tool_name: str) -> bool:
        """Check if a tool exists."""
        return any(tool["name"] == tool_name for tool in self._tools)
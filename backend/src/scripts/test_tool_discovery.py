#!/usr/bin/env python3
"""
Test tool discovery from MCP servers.

Verifies that tools are correctly discovered from Genie and GraphRAG MCP servers
and converted to LangChain tools via the MCPToolAdapter.

Run from backend/src directory:
    uv run python test_tool_discovery.py
"""

import asyncio
from app.mcp.adapter import MCPToolAdapter
from app.core.config import settings


async def test_tool_discovery():
    """Verify tools are discovered from MCP servers."""

    print("=" * 80)
    print("Testing MCP Tool Discovery")
    print("=" * 80)

    # 1. Test discovery from all configured servers
    print("\n1. Testing combined tool discovery from all MCP servers...")
    print(f"   Genie MCP URL: {settings.genie_mcp_url}")
    if hasattr(settings, 'graphrag_mcp_url'):
        print(f"   GraphRAG MCP URL: {settings.graphrag_mcp_url}")
    print()

    try:
        # Create adapter with enabled servers only
        server_configs = {}

        # Add Genie MCP if enabled
        if settings.genie_mcp_enabled:
            server_configs["genie"] = settings.genie_mcp_url

        # Add GraphRAG MCP if enabled
        if settings.graphrag_mcp_enabled:
            server_configs["graphrag"] = settings.graphrag_mcp_url

        if not server_configs:
            print("❌ No MCP servers enabled in configuration")
            return 1

        adapter = await MCPToolAdapter.discover_all_servers(server_configs)

        # Discover all tools
        all_tools = await adapter.discover_tools()

        print(f"✅ Successfully discovered {len(all_tools)} total tools")
        print()

        # 2. Display tool details
        print("2. Tool Details:")
        print("-" * 80)

        for i, tool in enumerate(all_tools, 1):
            print(f"\n   Tool {i}: {tool.name}")
            print(f"   Description: {tool.description[:100]}..." if len(tool.description) > 100 else f"   Description: {tool.description}")

            # Handle both dict (official library) and Pydantic model schemas
            if isinstance(tool.args_schema, dict):
                print(f"   Input Schema: JSON Schema (dict)")
                # Show parameters from JSON Schema
                props = tool.args_schema.get("properties", {})
                if props:
                    print(f"   Parameters ({len(props)}):")
                    for param_name, param_schema in list(props.items())[:3]:  # Show first 3
                        param_type = param_schema.get("type", "any")
                        print(f"      - {param_name}: {param_type}")
                    if len(props) > 3:
                        print(f"      ... and {len(props) - 3} more")
            elif hasattr(tool.args_schema, "__name__"):
                print(f"   Input Schema: {tool.args_schema.__name__}")
                # Show parameters from Pydantic model
                if hasattr(tool.args_schema, "__annotations__"):
                    params = tool.args_schema.__annotations__
                    if params:
                        print(f"   Parameters ({len(params)}):")
                        for param_name, param_type in list(params.items())[:3]:  # Show first 3
                            print(f"      - {param_name}: {param_type}")
                        if len(params) > 3:
                            print(f"      ... and {len(params) - 3} more")

        # 3. Test tool filtering
        print("\n\n3. Testing tool filtering by prefix:")
        print("-" * 80)

        # Note: Official library may or may not add namespace prefixes
        # Check for both prefixed and unprefixed tools
        genie_tools = [t for t in all_tools if t.name.startswith("genie.") or
                      any(keyword in t.name.lower() for keyword in ["genie", "space", "databricks"])]
        print(f"   Genie tools: {len(genie_tools)}")
        for tool in genie_tools[:10]:  # First 10
            print(f"      - {tool.name}")

        graph_tools = [t for t in all_tools if t.name.startswith("graph.") or
                      any(keyword in t.name.lower() for keyword in ["graph", "entity", "relationship", "cypher"])]
        print(f"\n   GraphRAG tools: {len(graph_tools)}")
        for tool in graph_tools[:10]:  # First 10
            print(f"      - {tool.name}")

        # 4. Verify tool conversion
        print("\n\n4. Verifying LangChain tool compatibility:")
        print("-" * 80)

        if all_tools:
            sample_tool = all_tools[0]
            print(f"   Sample tool: {sample_tool.name}")
            print(f"   Type: {type(sample_tool).__name__}")
            print(f"   Has coroutine: {sample_tool.coroutine is not None}")
            print(f"   Has args_schema: {sample_tool.args_schema is not None}")
            print("   ✅ Tool is LangChain-compatible")

        # 5. Final validation
        print("\n\n5. Validation Summary:")
        print("-" * 80)

        success = True
        issues = []

        if len(all_tools) == 0:
            success = False
            issues.append("❌ No tools discovered")
        else:
            print(f"   ✅ Total tools discovered: {len(all_tools)}")

        if len(genie_tools) == 0:
            success = False
            issues.append("❌ No Genie tools found")
        else:
            print(f"   ✅ Genie tools: {len(genie_tools)}")

        # GraphRAG is optional
        if len(graph_tools) > 0:
            print(f"   ✅ GraphRAG tools: {len(graph_tools)}")
        else:
            print(f"   ⚠️  GraphRAG tools: 0 (may not be configured)")

        # Check all tools have required attributes
        for tool in all_tools:
            if not tool.name:
                success = False
                issues.append(f"❌ Tool missing name: {tool}")
            if not tool.description:
                success = False
                issues.append(f"❌ Tool missing description: {tool.name}")
            if not tool.coroutine:
                success = False
                issues.append(f"❌ Tool missing coroutine: {tool.name}")

        if success:
            print("\n" + "=" * 80)
            print("✅ All tool discovery tests passed!")
            print("=" * 80)
            return 0
        else:
            print("\n" + "=" * 80)
            print("❌ Tool discovery tests failed:")
            for issue in issues:
                print(f"   {issue}")
            print("=" * 80)
            return 1

    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1


async def test_runtime_context():
    """Test RuntimeContext creation with discovered tools."""

    print("\n\n" + "=" * 80)
    print("Testing RuntimeContext Creation")
    print("=" * 80)

    try:
        from app.agent.langgraph.context import RuntimeContext

        # Discover tools from enabled servers
        server_configs = {}

        # Add Genie MCP if enabled
        if settings.genie_mcp_enabled:
            server_configs["genie"] = settings.genie_mcp_url

        # Add GraphRAG MCP if enabled
        if settings.graphrag_mcp_enabled:
            server_configs["graphrag"] = settings.graphrag_mcp_url

        adapter = await MCPToolAdapter.discover_all_servers(server_configs)
        tools = await adapter.discover_tools()

        # Create RuntimeContext
        context = RuntimeContext(
            tools=tools,
            genie_space_id="test-space",
            max_iterations=3,
            fast_model="gpt-35-turbo",
            synthesis_model="gpt-4o"
        )

        print(f"\n✅ RuntimeContext created successfully")
        print(f"   {context}")
        print(f"   Tool count: {context.tool_count}")
        print(f"   Tool names: {context.tool_names[:3]}..." if len(context.tool_names) > 3 else f"   Tool names: {context.tool_names}")

        # Test context methods
        if context.tool_count > 0:
            first_tool_name = context.tool_names[0]
            print(f"\n   Testing context.has_tool('{first_tool_name}'): {context.has_tool(first_tool_name)}")

            # Note: Official library may not add "genie." prefix, so test with actual tool names
            # Check if any tools exist (regardless of prefix)
            print(f"   Tool names available: {context.tool_names[:3]}...")
            print(f"   Testing context filtering works: {len(context.tools)} tools total")

        print("\n✅ RuntimeContext tests passed!")
        return 0

    except Exception as e:
        print(f"\n❌ RuntimeContext test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


async def main():
    """Run all tests."""
    print("\n🧪 Starting MCP Tool Discovery Tests\n")

    # Test 1: Tool discovery
    result1 = await test_tool_discovery()

    # Test 2: RuntimeContext
    result2 = await test_runtime_context()

    # Final summary
    print("\n\n" + "=" * 80)
    if result1 == 0 and result2 == 0:
        print("✅ ALL TESTS PASSED")
        print("=" * 80)
        print("\nReady to proceed to Phase 2: Refactor Workflows")
        return 0
    else:
        print("❌ SOME TESTS FAILED")
        print("=" * 80)
        print("\nPlease fix issues before proceeding to Phase 2")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)

/**
 * GraySkyGenAI Assistant API Client - TypeScript version
 * 
 * This file demonstrates how to interact with the streaming chat API.
 * It handles Server-Sent Events (SSE) and the four message types:
 * - STATUS: Processing updates
 * - REASONING: Agent decision-making transparency
 * - SOURCES: References and data sources
 * - CONTENT: Main response content
 */

import { StreamCallbacks, StreamController, StreamMessage, HealthResponse, ChatRequest } from '../types/chat';

export class ChatAPI {
  private baseUrl: string;
  private currentStream: StreamController | null = null;
  private conversationId: string;

  constructor(baseUrl: string = 'http://localhost:8000') {
    this.baseUrl = baseUrl;
    this.conversationId = this.generateConversationId();
  }

  private generateConversationId(): string {
    return 'conv_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Send a streaming chat message
   * @param message - The user's message
   * @param callbacks - Callbacks for different stream types
   */
  async sendMessage(message: string, callbacks: StreamCallbacks = {}): Promise<void> {
    // Cancel any existing stream
    if (this.currentStream) {
      this.currentStream.close();
    }

    const requestBody: ChatRequest = {
      message: message,
      conversation_id: this.conversationId
    };

    console.log('🚀 Sending API request:', requestBody);

    try {
      const response = await fetch(`${this.baseUrl}/api/v1/chat/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Handle Server-Sent Events stream
      this.currentStream = this.handleSSEStream(response, callbacks);
      
    } catch (error) {
      console.error('❌ API Error:', error);
      if (callbacks.onError) {
        callbacks.onError(error as Error);
      }
    }
  }

  /**
   * Handle Server-Sent Events stream from the API
   */
  private handleSSEStream(response: Response, callbacks: StreamCallbacks): StreamController {
    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('Response body is not readable');
    }

    const decoder = new TextDecoder();
    let buffer = '';

    const processStream = async (): Promise<void> => {
      try {
        while (true) {
          const { done, value } = await reader.read();
          
          if (done) {
            console.log('✅ Stream completed');
            if (callbacks.onComplete) {
              callbacks.onComplete();
            }
            break;
          }

          // Decode the chunk and add to buffer
          buffer += decoder.decode(value, { stream: true });
          
          // Process complete SSE messages
          const lines = buffer.split('\n');
          buffer = lines.pop() || ''; // Keep incomplete line in buffer

          for (const line of lines) {
            this.processSSELine(line, callbacks);
          }
        }
      } catch (error) {
        console.error('❌ Stream Error:', error);
        if (callbacks.onError) {
          callbacks.onError(error as Error);
        }
      }
    };

    processStream();

    // Return object with close method for cancellation
    return {
      close: () => {
        reader.cancel();
        console.log('🛑 Stream cancelled');
      }
    };
  }

  /**
   * Process a single Server-Sent Event line
   */
  private processSSELine(line: string, callbacks: StreamCallbacks): void {
    // Skip empty lines and comments
    if (!line.trim() || line.startsWith(':')) {
      return;
    }

    // Parse SSE format: "data: {...}"
    if (line.startsWith('data: ')) {
      const jsonStr = line.substring(6); // Remove "data: " prefix
      
      if (jsonStr.trim() === '[DONE]') {
        console.log('🏁 Stream end marker received');
        return;
      }

      try {
        const streamMessage: StreamMessage = JSON.parse(jsonStr);
        console.log('📦 Stream message:', streamMessage);
        
        this.handleStreamMessage(streamMessage, callbacks);
        
      } catch (error) {
        console.error('❌ Failed to parse stream message:', jsonStr, error);
      }
    }
  }

  /**
   * Handle different types of stream messages
   * Expected format: { type: "STATUS|REASONING|SOURCES|CONTENT", data: {...}, metadata: {...} }
   */
  private handleStreamMessage(streamMessage: StreamMessage, callbacks: StreamCallbacks): void {
    const { type, data, metadata } = streamMessage;
    
    console.log(`📨 ${type} message:`, data);

    switch (type.toLowerCase()) {
      case 'status':
        if (callbacks.onStatus) {
          callbacks.onStatus(data, metadata);
        }
        break;

      case 'reasoning':
        if (callbacks.onReasoning) {
          callbacks.onReasoning(data, metadata);
        }
        break;

      case 'sources':
        if (callbacks.onSources) {
          callbacks.onSources(data, metadata);
        }
        break;

      case 'content':
        if (callbacks.onContent) {
          callbacks.onContent(data, metadata);
        }
        break;

      default:
        console.warn('⚠️ Unknown stream message type:', type);
    }
  }

  /**
   * Test the health endpoint
   */
  async testHealth(): Promise<HealthResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/health`);
      const data: HealthResponse = await response.json();
      console.log('💚 Health check:', data);
      return data;
    } catch (error) {
      console.error('💔 Health check failed:', error);
      throw error;
    }
  }

  /**
   * Cancel current stream
   */
  cancel(): void {
    if (this.currentStream) {
      this.currentStream.close();
      this.currentStream = null;
    }
  }

  /**
   * Reset conversation (generate new ID)
   */
  resetConversation(): void {
    this.conversationId = this.generateConversationId();
    console.log('🔄 New conversation ID:', this.conversationId);
  }

  /**
   * Get current conversation ID
   */
  getConversationId(): string {
    return this.conversationId;
  }
}
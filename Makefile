# GraySkyGenAI Assistant - Local Development Makefile
# This Makefile provides convenience commands for local development orchestration

.PHONY: help up down restart logs logs-follow status ps clean rebuild test health check-env init create-vector-indices

# Default target
.DEFAULT_GOAL := help

# Colors for output
CYAN := \033[0;36m
GREEN := \033[0;32m
YELLOW := \033[0;33m
RED := \033[0;31m
NC := \033[0m # No Color

# Container tool detection (Docker or Podman)
DOCKER := $(shell command -v docker 2>/dev/null)
PODMAN := $(shell command -v podman 2>/dev/null)

ifdef DOCKER
    COMPOSE := docker compose
else ifdef PODMAN
    COMPOSE := podman-compose
else
    $(error No container runtime found. Install docker or podman)
endif

help: ## Show this help message
	@echo "$(CYAN)GraySkyGenAI Assistant - Local Development Commands$(NC)"
	@echo ""
	@echo "$(GREEN)Usage:$(NC)"
	@echo "  make [target]"
	@echo ""
	@echo "$(GREEN)Available targets:$(NC)"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | awk 'BEGIN {FS = ":.*?## "}; {printf "  $(CYAN)%-15s$(NC) %s\n", $$1, $$2}'

check-env: ## Check if .env file exists
	@if [ ! -f .env ]; then \
		echo "$(YELLOW)Warning: .env file not found. Creating from .env.example...$(NC)"; \
		cp .env.example .env; \
		echo "$(GREEN)Created .env file. Please review and customize if needed.$(NC)"; \
	fi

init: check-env ## Initialize environment (copy .env.example to .env if needed)
	@echo "$(GREEN)Environment initialized$(NC)"
	@echo "$(CYAN)Next steps:$(NC)"
	@echo "  1. Review and customize .env file if needed"
	@echo "  2. Ensure submodule .env files are configured:"
	@echo "     - backend/src/.env (API keys, model config)"
	@echo "     - graphmcp/.env (MCP server config)"
	@echo "  3. Run 'make up' to start all services"

up: check-env ## Start all services in detached mode
	@echo "$(CYAN)Starting all services...$(NC)"
	$(COMPOSE) up -d
	@echo "$(GREEN)All services started$(NC)"
	@echo ""
	@$(MAKE) status
	@echo "\n\n\n\n$(RED)IMPORTANT TEMPORARY WORKAROUND"
	@echo "$(RED)Backend will not work using make."
	@echo "$(RED)Run $(CYAN) docker kill backend$(RED) to stop backend container...$(NC)"
	@echo "$(RED)Followed by $(CYAN)uv run uvicorn app.main:app --reload $(RED)from $(CYAN)backend/src/ $(RED)... to start backend manually.$(NC)"

down: ## Stop all services
	@echo "$(CYAN)Stopping all services...$(NC)"
	$(COMPOSE) down
	@echo "$(GREEN)All services stopped$(NC)"

restart: ## Restart all services
	@echo "$(CYAN)Restarting all services...$(NC)"
	@$(MAKE) down
	@$(MAKE) up

logs: ## View logs from all services (non-following)
	$(COMPOSE) logs

logs-follow: ## Follow logs from all services
	$(COMPOSE) logs -f

logs-%: ## View logs from a specific service (e.g., make logs-backend)
	$(COMPOSE) logs -f $*

status: ## Show status of all services
	@echo "$(CYAN)Service Status:$(NC)"
	@$(COMPOSE) ps

ps: status ## Alias for status

images: ## Show Docker images being used by services
	@echo "$(CYAN)Service Images:$(NC)"
	@$(COMPOSE) images

health: ## Run health checks on all services
	@echo "$(CYAN)Running health checks...$(NC)"
	@echo ""
	@echo "$(YELLOW)Backend API:$(NC)"
	@curl -s http://localhost:8000/health || echo "$(RED)Failed$(NC)"
	@echo ""
	@echo "$(YELLOW)GraphMCP:$(NC)"
	@curl -s http://localhost:8001/health || echo "$(RED)Failed$(NC)"
	@echo ""
	@echo "$(YELLOW)Phoenix:$(NC)"
	@curl -s http://localhost:6006 > /dev/null && echo "$(GREEN)OK$(NC)" || echo "$(RED)Failed$(NC)"
	@echo ""
	@echo "$(YELLOW)Frontend:$(NC)"
	@curl -s http://localhost:3000 > /dev/null && echo "$(GREEN)OK$(NC)" || echo "$(RED)Failed$(NC)"
	@echo ""
	@echo "$(YELLOW)Memgraph Lab:$(NC)"
	@curl -s http://localhost:3001 > /dev/null && echo "$(GREEN)OK$(NC)" || echo "$(RED)Failed$(NC)"

test: health ## Alias for health checks

rebuild: ## Rebuild all services (useful after Dockerfile changes)
	@echo "$(CYAN)Rebuilding all services...$(NC)"
	$(COMPOSE) build --no-cache
	@echo "$(GREEN)Rebuild complete$(NC)"

rebuild-%: ## Rebuild a specific service (e.g., make rebuild-backend)
	@echo "$(CYAN)Rebuilding $*...$(NC)"
	$(COMPOSE) build --no-cache $*
	@echo "$(GREEN)Rebuild of $* complete$(NC)"

clean: ## Stop services and remove volumes (WARNING: deletes data!)
	@echo "$(RED)WARNING: This will delete all data volumes!$(NC)"
	@echo "Press Ctrl+C to cancel, or Enter to continue..."
	@read
	@echo "$(CYAN)Cleaning up...$(NC)"
	$(COMPOSE) down -v
	@echo "$(GREEN)Cleanup complete$(NC)"

shell-%: ## Open shell in a service container (e.g., make shell-backend)
	$(COMPOSE) exec $* /bin/bash

# Service-specific convenience targets
backend-shell: ## Open shell in backend container
	@$(MAKE) shell-backend

frontend-shell: ## Open shell in frontend container
	@$(MAKE) shell-frontend

graphmcp-shell: ## Open shell in graphmcp container
	@$(MAKE) shell-graphmcp

# Data management targets using init/load_memgraph.py
data-clear-write: ## Clear database and load data (usage: make data-clear-write path=sample_data/Mission_Cypher_all.csv)
	@if [ -z "$(path)" ]; then \
		echo "$(RED)Error: path parameter required$(NC)"; \
		echo "Usage: make data-clear-write path=sample_data/Mission_Cypher_all.csv"; \
		exit 1; \
	fi
	@echo "$(CYAN)Clearing database and loading data from $(path)...$(NC)"
	@cd init && uv run python load_memgraph.py --clear --file ../$(path)
	@echo "$(GREEN)Complete$(NC)"

data-add: ## Add data without clearing (usage: make data-add path=sample_data/Mission_Cypher.csv)
	@if [ -z "$(path)" ]; then \
		echo "$(RED)Error: path parameter required$(NC)"; \
		echo "Usage: make data-add path=sample_data/Mission_Cypher.csv"; \
		exit 1; \
	fi
	@echo "$(CYAN)Loading data from $(path)...$(NC)"
	@cd init && uv run python load_memgraph.py --file ../$(path)
	@echo "$(GREEN)Complete$(NC)"

create-vector-indices: ## Create vector indices in Memgraph for semantic search
	@echo "$(CYAN)Creating vector indices in Memgraph...$(NC)"
	@cd graphmcp && uv run python scripts/create_vector_indices.py
	@echo "$(GREEN)Vector indices created$(NC)"

# Development workflow targets
dev: up logs-follow ## Start services and follow logs

stop: down ## Alias for down

# Access URLs (printed)
urls: ## Show service access URLs
	@echo "$(CYAN)Service Access URLs:$(NC)"
	@echo "  $(GREEN)Frontend:$(NC)       http://localhost:3000"
	@echo "  $(GREEN)Backend API:$(NC)    http://localhost:8000"
	@echo "  $(GREEN)API Docs:$(NC)       http://localhost:8000/docs"
	@echo "  $(GREEN)GraphMCP:$(NC)       http://localhost:8001"
	@echo "  $(GREEN)Phoenix UI:$(NC)     http://localhost:6006"
	@echo "  $(GREEN)Memgraph Lab:$(NC)   http://localhost:3001"
	@echo "  $(GREEN)Memgraph Bolt:$(NC)  bolt://localhost:7687"
	@echo ""
	@echo "$(CYAN)LangGraph Studio (runs on host, not Docker):$(NC)"
	@echo "  Run: $(YELLOW)cd backend/src && langgraph dev --port 8123$(NC)"
	@echo "  URL: $(GREEN)http://localhost:8123$(NC)"

"""
Vector Search Tools for GraphRAG MCP Server

Provides semantic similarity search using vector embeddings.
These are READ-ONLY retrieval tools - no write operations.
"""

import os
import json
import logging
from typing import Optional

logger = logging.getLogger(__name__)


def register(mcp, graph_client, embedding_client):
    """
    Register vector search tools with the MCP server.

    Args:
        mcp: FastMCP server instance
        graph_client: GraphClient instance
        embedding_client: EmbeddingClient instance for query embedding generation
    """

    @mcp.tool()
    def semantic_search_missions(
        query: str,
        limit: int = 10
    ) -> str:
        """
        Search for missions semantically similar to query text.

        Uses vector similarity search to find missions whose content is semantically
        related to the query, even if they don't contain exact keyword matches.

        Args:
            query: Natural language search query describing what you're looking for
            limit: Maximum number of results to return (default: 10)

        Returns:
            Missions ranked by semantic similarity with scores

        Use Cases:
            - Find missions related to a topic without knowing exact keywords
            - Discover similar past missions for reference
            - Semantic matching based on meaning, not just keywords

        Example:
            semantic_search_missions("hurricane relief coordination", limit=5)
            # Returns missions about hurricane response, even if they use different wording

        Note: Requires embeddings to be generated for Mission nodes first.
        """
        try:
            logger.info(f"semantic_search_missions called: query='{query[:50]}...', limit={limit}")

            # Check if vector search is enabled
            if not os.getenv("ENABLE_VECTOR_SEARCH", "false").lower() == "true":
                return json.dumps({
                    "success": False,
                    "error": "Vector search is not enabled. Set ENABLE_VECTOR_SEARCH=true in .env"
                })

            # Generate query embedding
            try:
                query_data = embedding_client.generate_embedding(query, include_metadata=False)
                query_vector = query_data['embedding']
            except Exception as e:
                return json.dumps({
                    "success": False,
                    "error": f"Failed to generate query embedding: {str(e)}",
                    "details": "Check OPENAI_API_KEY and OPENAI_ENDPOINT configuration"
                })

            # Vector search using Memgraph (MAGE vector_search module)
            # Signature: vector_search.search(index_name, result_set_size, query_vector)
            cypher = """
            CALL vector_search.search('mission_embedding', $limit, $query_vector)
            YIELD node, similarity
            RETURN
                node.id as id,
                node.title as title,
                node.mission_number as mission_number,
                node.embedding_model as embedding_model,
                node.embedding_timestamp as embedding_timestamp,
                similarity as similarity_score,
                properties(node) as all_properties
            ORDER BY similarity DESC
            """

            # Execute search
            try:
                results = graph_client.execute_query(
                    cypher,
                    {
                        "query_vector": query_vector,
                        "limit": limit
                    }
                )
            except Exception as e:
                error_msg = str(e)
                logger.error(f"Vector search failed: {error_msg}")

                # Provide specific error messages based on failure type
                if "index" in error_msg.lower() and "not found" in error_msg.lower():
                    return json.dumps({
                        "success": False,
                        "error": "Vector index 'mission_embedding' not found",
                        "details": "Run scripts/create_vector_indices.py to create vector indices first"
                    })
                elif "procedure" in error_msg.lower() or "vector_search" in error_msg.lower():
                    return json.dumps({
                        "success": False,
                        "error": "Vector search procedure not available",
                        "details": f"Memgraph error: {error_msg}",
                        "troubleshooting": "Ensure MAGE vector_search module is loaded (run `CALL mg.load_all()` in Memgraph)"
                    })
                else:
                    return json.dumps({
                        "success": False,
                        "error": f"Vector search failed: {error_msg}",
                        "details": "Check graphmcp logs for more information"
                    })

            # Format results
            missions = []
            for record in results:
                missions.append({
                    "id": record.get("id"),
                    "title": record.get("title"),
                    "mission_number": record.get("mission_number"),
                    "similarity_score": float(record.get("similarity_score", 0)),
                    "embedding_model": record.get("embedding_model"),
                    "embedding_timestamp": record.get("embedding_timestamp")
                })

            return json.dumps({
                "success": True,
                "query": query,
                "result_count": len(missions),
                "missions": missions
            }, indent=2, default=str)

        except Exception as e:
            logger.error(f"Error in semantic_search_missions: {str(e)}", exc_info=True)
            return json.dumps({
                "success": False,
                "error": str(e),
                "details": "Semantic search failed. Ensure vector indices exist and nodes have embeddings."
            })

    @mcp.tool()
    def semantic_search_incidents(
        query: str,
        limit: int = 10
    ) -> str:
        """
        Search for incidents semantically similar to query text.

        Uses vector similarity search to find incidents whose descriptions are
        semantically related to the query.

        Args:
            query: Natural language search query describing the type of incident
            limit: Maximum number of results to return (default: 10)

        Returns:
            Incidents ranked by semantic similarity with scores

        Use Cases:
            - Find incidents similar to a description
            - Discover related historical incidents
            - Search by meaning rather than exact keywords

        Example:
            semantic_search_incidents("coastal flooding emergency", limit=5)
            # Returns flood-related incidents even if they use different terminology

        Note: Requires embeddings to be generated for Incident nodes first.
        """
        try:
            logger.info(f"semantic_search_incidents called: query='{query[:50]}...', limit={limit}")

            if not os.getenv("ENABLE_VECTOR_SEARCH", "false").lower() == "true":
                return json.dumps({
                    "success": False,
                    "error": "Vector search is not enabled"
                })

            # Generate query embedding
            query_data = embedding_client.generate_embedding(query, include_metadata=False)
            query_vector = query_data['embedding']

            # Vector search using MAGE vector_search module
            # Signature: vector_search.search(index_name, result_set_size, query_vector)
            cypher = """
            CALL vector_search.search('incident_embedding', $limit, $query_vector)
            YIELD node, similarity
            RETURN
                node.id as id,
                node.type as type,
                node.description as description,
                node.severity as severity,
                node.timestamp as timestamp,
                node.embedding_model as embedding_model,
                similarity as similarity_score,
                properties(node) as all_properties
            ORDER BY similarity DESC
            """

            results = graph_client.execute_query(
                cypher,
                {
                    "query_vector": query_vector,
                    "limit": limit
                }
            )

            # Format results
            incidents = []
            for record in results:
                incidents.append({
                    "id": record.get("id"),
                    "type": record.get("type"),
                    "description": record.get("description"),
                    "severity": record.get("severity"),
                    "timestamp": record.get("timestamp"),
                    "similarity_score": float(record.get("similarity_score", 0)),
                    "embedding_model": record.get("embedding_model")
                })

            return json.dumps({
                "success": True,
                "query": query,
                "result_count": len(incidents),
                "incidents": incidents
            }, indent=2, default=str)

        except Exception as e:
            logger.error(f"Error in semantic_search_incidents: {str(e)}", exc_info=True)
            return json.dumps({
                "success": False,
                "error": str(e)
            })

    @mcp.tool()
    def find_similar_resources(
        resource_id: str,
        limit: int = 10
    ) -> str:
        """
        Find resources similar to a given resource based on semantic similarity.

        Uses vector embeddings to find resources with similar descriptions, capabilities,
        or characteristics.

        Args:
            resource_id: ID of the reference resource
            limit: Maximum number of similar resources to return (default: 10)

        Returns:
            Similar resources ranked by semantic similarity with scores

        Use Cases:
            - Find alternative or backup resources
            - Discover resources with similar capabilities
            - Identify resource substitutes for planning

        Example:
            find_similar_resources("RES-001", limit=5)
            # Returns 5 resources most similar to RES-001

        Note: Requires embeddings to be generated for Resource nodes first.
        """
        try:
            logger.info(f"find_similar_resources called: resource_id={resource_id}, limit={limit}")

            if not os.getenv("ENABLE_VECTOR_SEARCH", "false").lower() == "true":
                return json.dumps({
                    "success": False,
                    "error": "Vector search is not enabled"
                })

            # Get the reference resource's embedding
            get_resource_query = """
            MATCH (r:Resource {id: $resource_id})
            RETURN r.embedding as embedding, r.type as type, r.description as description
            """

            resource_result = graph_client.execute_query(
                get_resource_query,
                {"resource_id": resource_id}
            )

            if not resource_result:
                return json.dumps({
                    "success": False,
                    "error": f"Resource {resource_id} not found"
                })

            reference_embedding = resource_result[0].get("embedding")
            if not reference_embedding:
                return json.dumps({
                    "success": False,
                    "error": f"Resource {resource_id} does not have an embedding",
                    "details": "Run scripts/generate_embeddings.py to generate embeddings first"
                })

            # Find similar resources using MAGE vector_search module
            # Signature: vector_search.search(index_name, result_set_size, query_vector)
            cypher = """
            CALL vector_search.search('resource_embedding', $limit, $query_vector)
            YIELD node, similarity
            WHERE node.id <> $resource_id
            RETURN
                node.id as id,
                node.type as type,
                node.description as description,
                node.status as status,
                node.capacity as capacity,
                similarity as similarity_score,
                properties(node) as all_properties
            ORDER BY similarity DESC
            """

            results = graph_client.execute_query(
                cypher,
                {
                    "query_vector": reference_embedding,
                    "resource_id": resource_id,
                    "limit": limit + 1  # +1 because we filter out the reference
                }
            )

            # Format results
            similar_resources = []
            for record in results:
                similar_resources.append({
                    "id": record.get("id"),
                    "type": record.get("type"),
                    "description": record.get("description"),
                    "status": record.get("status"),
                    "capacity": record.get("capacity"),
                    "similarity_score": float(record.get("similarity_score", 0))
                })

            return json.dumps({
                "success": True,
                "reference_resource_id": resource_id,
                "reference_type": resource_result[0].get("type"),
                "reference_description": resource_result[0].get("description"),
                "result_count": len(similar_resources),
                "similar_resources": similar_resources
            }, indent=2, default=str)

        except Exception as e:
            logger.error(f"Error in find_similar_resources: {str(e)}", exc_info=True)
            return json.dumps({
                "success": False,
                "error": str(e)
            })

    @mcp.tool()
    def check_vector_indices() -> str:
        """
        Check status of vector indices in Memgraph.

        Returns information about which vector indices exist, their configuration,
        and readiness for semantic search operations.

        Returns:
            JSON object with vector index information including:
            - Index names and associated node labels
            - Dimension and capacity settings
            - Number of indexed nodes

        Use Cases:
            - Verify vector indices are properly configured
            - Troubleshoot vector search issues
            - Confirm embeddings are indexed

        Example:
            check_vector_indices()
            # Returns list of all vector indices with their configuration
        """
        try:
            logger.info("check_vector_indices called")

            # Use MAGE vector_search module to list indices
            cypher = """
            CALL vector_search.show_index_info()
            YIELD index_name, label, property, dimension, capacity, similarity_function
            RETURN
                index_name,
                label,
                property,
                dimension,
                capacity,
                similarity_function
            """

            results = graph_client.execute_query(cypher)

            indices = []
            for record in results:
                indices.append({
                    "index_name": record.get("index_name"),
                    "label": record.get("label"),
                    "property": record.get("property"),
                    "dimension": int(record.get("dimension", 0)),
                    "capacity": int(record.get("capacity", 0)),
                    "similarity_function": record.get("similarity_function")
                })

            return json.dumps({
                "success": True,
                "index_count": len(indices),
                "indices": indices,
                "vector_search_enabled": len(indices) > 0
            }, indent=2)

        except Exception as e:
            logger.error(f"Error in check_vector_indices: {str(e)}", exc_info=True)
            error_msg = str(e)

            # Check if vector_search module is not loaded
            if "procedure" in error_msg.lower():
                return json.dumps({
                    "success": False,
                    "error": "Vector search module not available",
                    "details": f"Memgraph error: {error_msg}",
                    "troubleshooting": "Ensure MAGE vector_search module is installed and loaded"
                })
            else:
                return json.dumps({
                    "success": False,
                    "error": str(e)
                })

    logger.info("Vector search tools registered successfully")

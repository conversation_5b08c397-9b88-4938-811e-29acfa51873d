# Server Configuration
SERVER_NAME=GraphRAG-Emergency-Management
DEBUG=false
LOG_LEVEL=INFO

# Transport Configuration
# Options: stdio (default for local development), streamable-http (for network access)
MCP_TRANSPORT=streamable-http
# HTTP port (only used when MCP_TRANSPORT=streamable-http)
HTTP_PORT=8001
HOST=0.0.0.0

# Docker Configuration (for docker-compose)
CONTAINER_NAME=graphrag-mcp
NETWORK_NAME=graphmcp-network
EXTERNAL_NETWORK=false  # Set to true in production to join existing network

# Memgraph Configuration
MEMGRAPH_HOST=localhost
MEMGRAPH_PORT=7687
MEMGRAPH_USER=
MEMGRAPH_PASSWORD=
MEMGRAPH_DATABASE=memgraph
MEMGRAPH_SSL=false

# Alternative: Use connection URI
# MEMGRAPH_URI=bolt://localhost:7687

# Performance Settings
CACHE_TTL_SECONDS=300
MAX_TRAVERSAL_DEPTH=5
DEFAULT_LIMIT=100
MAX_LIMIT=1000
QUERY_TIMEOUT_SECONDS=30
CONNECTION_POOL_SIZE=10

# Vector Search Configuration
ENABLE_VECTOR_SEARCH=true

# ========================================
# Embedding Provider Configuration
# ========================================
# Choose embedding provider:
#   LOCAL_EMBEDDING=true  → Use local models via sentence-transformers (NO API COSTS)
#   LOCAL_EMBEDDING=false → Use Azure OpenAI API (requires API key, costs per request)
LOCAL_EMBEDDING=true

# Local Embedding Configuration (when LOCAL_EMBEDDING=true)
# Uses ONNX runtime with local model files for fast, offline inference
# IMPORTANT: Must match the model used to generate embeddings in your database!
LOCAL_EMBEDDING_MODEL_PATH=./models/bge-small-en-v1.5  # Path to ONNX model directory
LOCAL_EMBEDDING_MODEL_NAME=BAAI/bge-small-en-v1.5  # HuggingFace model identifier (for tokenizer)
LOCAL_EMBEDDING_DIMENSION=384  # Embedding dimension (bge-small-en-v1.5: 384, bge-base: 768, bge-large: 1024)
LOCAL_EMBEDDING_MAX_LENGTH=512  # Maximum token length for input text
LOCAL_EMBEDDING_DEVICE=cpu  # Options: cpu, cuda (NVIDIA GPU), mps (Apple Silicon GPU)

# Supported local models:
#   - BAAI/bge-small-en-v1.5: 384 dims (recommended, fast, good quality)
#   - BAAI/bge-base-en-v1.5: 768 dims (better quality, slower)
#   - BAAI/bge-large-en-v1.5: 1024 dims (best quality, much slower)

# Azure OpenAI Configuration (when LOCAL_EMBEDDING=false)
# Only needed if LOCAL_EMBEDDING=false
# OPENAI_API_KEY=your_azure_openai_api_key_here
# OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
# OPENAI_API_VERSION=2024-12-01-preview
# OPENAI_EMBEDDING_DEPLOYMENT=text-embedding-3-small
# OPENAI_EMBEDDING_DIMENSION=1536  # text-embedding-3-small produces 1536-dimensional vectors

# Supported Azure OpenAI models:
#   - text-embedding-3-small: 1536 dims (recommended, cost-effective)
#   - text-embedding-3-large: 3072 dims (higher quality, more expensive)
#   - text-embedding-ada-002: 1536 dims (older model)

# Vector Index Configuration
# VECTOR_INDEX_METRIC=cosine
# VECTOR_INDEX_CAPACITY_INCIDENT=1000
# VECTOR_INDEX_CAPACITY_RESOURCE=500
# VECTOR_INDEX_CAPACITY_MISSION=15000

# Embedding Generation Settings
# EMBEDDING_BATCH_SIZE=100
# EMBEDDING_RATE_LIMIT_RPM=3000
# EMBEDDING_RETRY_ATTEMPTS=3
# EMBEDDING_RETRY_DELAY_SECONDS=1
# EMBEDDING_VERSION=v1
# EMBEDDING_STORAGE_FORMAT=float32

# Monitoring (optional)
ENABLE_METRICS=true
METRICS_PORT=9090
ENABLE_TRACING=false

# Security Settings
# IMPORTANT: execute_cypher tool is disabled by default for security
# Only enable in development environments or with proper access controls
ALLOW_CUSTOM_CYPHER=false  # Set to true to enable execute_cypher MCP tool

# Data Ingestion
BATCH_SIZE=1000
ENABLE_VALIDATION=true
LOG_INVALID_RECORDS=true
#!/bin/bash

# API Test Script - Deploy with Docker and run API tests
set -e

echo "🧪 API Test Suite - Docker Deployment"
echo "======================================"
echo ""

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

API_BASE="http://localhost:8000"
BACKEND_DIR="/Users/<USER>/Repos/DEMES/assistant/backend"

# Cleanup function
cleanup() {
    echo -e "\n${YELLOW}🧹 Cleaning up...${NC}"
    cd "$BACKEND_DIR"
    docker-compose down 2>/dev/null || true
    echo -e "${GREEN}✅ Cleanup complete${NC}"
}

# Set up cleanup trap
trap cleanup EXIT INT TERM

cd "$BACKEND_DIR"

# Step 1: Deploy with docker-compose
echo -e "${BLUE}🚀 Step 1: Deploying with docker-compose...${NC}"
docker-compose up -d

echo -e "${BLUE}⏳ Waiting for container to start...${NC}"
sleep 3

# Check if container is running
if docker-compose ps | grep -q "Up"; then
    echo -e "${GREEN}✅ Container is running${NC}"
    docker-compose ps
else
    echo -e "${RED}❌ Container failed to start${NC}"
    docker-compose logs
    exit 1
fi
echo ""

# Step 2: Wait for API to be ready
echo -e "${BLUE}⏳ Step 2: Waiting for API to be ready...${NC}"
for i in {1..30}; do
    if curl -s "$API_BASE/health" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ API is ready!${NC}"
        break
    fi
    if [ $i -eq 30 ]; then
        echo -e "${RED}❌ API failed to start in time${NC}"
        echo "Container logs:"
        docker-compose logs backend
        exit 1
    fi
    echo "Waiting for API... ($i/30)"
    sleep 1
done
echo ""

# Step 3: Quick health check
echo -e "${BLUE}🏥 Step 3: Quick health check...${NC}"
health_response=$(curl -s "$API_BASE/health")
echo "Response: $health_response"
if echo "$health_response" | grep -q "healthy"; then
    echo -e "${GREEN}✅ Health check passed${NC}"
else
    echo -e "${RED}❌ Health check failed${NC}"
    exit 1
fi
echo ""

# Step 4: Run Python API test client
echo -e "${BLUE}🧪 Step 4: Running API test client (test_client.py)...${NC}"
echo "=================================================="

# Check if python3 and dependencies are available
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}❌ python3 not found${NC}"
    exit 1
fi

# Check for dependencies
if ! python3 -c "import httpx, rich" 2>/dev/null; then
    echo -e "${YELLOW}⚠️  Installing required dependencies (httpx, rich)...${NC}"
    pip3 install httpx rich || {
        echo -e "${RED}❌ Failed to install dependencies${NC}"
        exit 1
    }
fi

# Run the test client
echo -e "${GREEN}Running comprehensive API tests...${NC}"
echo ""
python3 test_client.py

echo ""
echo -e "${GREEN}✅ API tests completed!${NC}"
echo ""

# Step 5: Show container status and logs
echo -e "${BLUE}📋 Step 5: Container status and recent logs...${NC}"
echo "Container status:"
docker-compose ps
echo ""
echo "Recent logs (last 30 lines):"
docker-compose logs --tail=30 backend
echo ""

# Summary
echo -e "${GREEN}🎉 API Test Suite Complete!${NC}"
echo "============================"
echo -e "${BLUE}Summary:${NC}"
echo -e "  ✅ Docker container deployed successfully"
echo -e "  ✅ API health check passed"
echo -e "  ✅ Comprehensive API tests executed"
echo -e "  ✅ All endpoints verified"
echo ""
echo -e "${YELLOW}Container is still running. To view logs:${NC}"
echo -e "  docker-compose logs -f backend"
echo ""
echo -e "${YELLOW}To stop the container:${NC}"
echo -e "  docker-compose down"
echo ""

# Don't cleanup - leave container running for user inspection
trap - EXIT INT TERM

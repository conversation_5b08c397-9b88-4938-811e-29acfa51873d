#!/usr/bin/env python3
"""
Test script for refactored get_schema tool.

Usage:
    uv run python scripts/test_refactored_schema.py

This script tests the refactored schema manager to ensure:
1. Properties are consolidated under entities
2. Property types are detected
3. Relationship keys are renamed (from_labels/to_labels)
4. Cypher guidelines are included
5. Structure matches expected format
"""

import sys
import os
import json
from typing import Dict, Any

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Set required environment variables
os.environ.setdefault('MEMGRAPH_HOST', 'localhost')
os.environ.setdefault('MEMGRAPH_PORT', '7687')
os.environ.setdefault('MCP_TRANSPORT', 'stdio')

from graph.client import GraphClient
from graph.schema_manager import SchemaManager


def print_section(title: str):
    """Print a section header."""
    print(f"\n{'='*80}")
    print(f"  {title}")
    print(f"{'='*80}\n")


def print_check(condition: bool, message: str):
    """Print a check result."""
    status = "✅" if condition else "❌"
    print(f"{status} {message}")


def validate_structure(schema: Dict[str, Any]) -> Dict[str, bool]:
    """Validate the schema structure against expected format."""
    checks = {}

    # Check top-level keys
    checks["has_node_labels"] = "node_labels" in schema
    checks["has_relationship_types"] = "relationship_types" in schema
    checks["has_constraints"] = "constraints" in schema
    checks["has_statistics"] = "statistics" in schema
    checks["has_metadata"] = "metadata" in schema
    checks["has_cypher_guidelines"] = "cypher_guidelines" in schema

    # Check deprecated keys are removed
    checks["no_entity_types"] = "entity_types" not in schema
    checks["no_separate_properties"] = "properties" not in schema or "node_properties" not in schema.get("properties", {})

    # Check node label structure
    if "node_labels" in schema and schema["node_labels"]:
        first_label = list(schema["node_labels"].keys())[0]
        label_data = schema["node_labels"][first_label]
        checks["node_has_count"] = "count" in label_data
        checks["node_has_properties"] = "properties" in label_data

        # Check if properties have types
        if "properties" in label_data and label_data["properties"]:
            first_prop = list(label_data["properties"].values())[0]
            checks["properties_have_types"] = isinstance(first_prop, dict) and "type" in first_prop
        else:
            checks["properties_have_types"] = False
    else:
        checks["node_has_count"] = False
        checks["node_has_properties"] = False
        checks["properties_have_types"] = False

    # Check relationship structure
    if "relationship_types" in schema and schema["relationship_types"]:
        first_rel = list(schema["relationship_types"].values())[0]
        checks["rel_has_from_labels"] = "from_labels" in first_rel
        checks["rel_has_to_labels"] = "to_labels" in first_rel
        checks["rel_no_old_from"] = "from" not in first_rel
        checks["rel_no_old_to"] = "to" not in first_rel
        checks["rel_has_properties"] = "properties" in first_rel

        # Check if relationship properties have types (if any exist)
        if "properties" in first_rel and first_rel["properties"]:
            first_rel_prop = list(first_rel["properties"].values())[0]
            checks["rel_properties_have_types"] = isinstance(first_rel_prop, dict) and "type" in first_rel_prop
        else:
            checks["rel_properties_have_types"] = True  # Empty is OK
    else:
        checks["rel_has_from_labels"] = False
        checks["rel_has_to_labels"] = False
        checks["rel_no_old_from"] = True
        checks["rel_no_old_to"] = True
        checks["rel_has_properties"] = False
        checks["rel_properties_have_types"] = False

    # Check Cypher guidelines
    if "cypher_guidelines" in schema:
        guidelines = schema["cypher_guidelines"]
        checks["guidelines_has_examples"] = "example_queries" in guidelines
        checks["guidelines_has_syntax"] = "memgraph_syntax" in guidelines
        checks["guidelines_has_rules"] = "important_rules" in guidelines
    else:
        checks["guidelines_has_examples"] = False
        checks["guidelines_has_syntax"] = False
        checks["guidelines_has_rules"] = False

    return checks


def main():
    print_section("Testing Refactored Schema Manager")

    try:
        # Initialize
        print("🔧 Initializing graph client and schema manager...")
        graph_client = GraphClient()
        schema_manager = SchemaManager(graph_client)

        # Refresh cache
        print("🔄 Refreshing schema cache...")
        schema_manager.refresh_cache()

        # Get schema
        print("📊 Getting cached schema...")
        schema_json = schema_manager.get_cached_schema()
        schema = json.loads(schema_json)

        # Validate structure
        print_section("Structure Validation")
        checks = validate_structure(schema)

        print("Top-Level Structure:")
        print_check(checks["has_node_labels"], "Has 'node_labels' key")
        print_check(checks["has_relationship_types"], "Has 'relationship_types' key")
        print_check(checks["has_constraints"], "Has 'constraints' key")
        print_check(checks["has_statistics"], "Has 'statistics' key")
        print_check(checks["has_metadata"], "Has 'metadata' key")
        print_check(checks["has_cypher_guidelines"], "Has 'cypher_guidelines' key")
        print_check(checks["no_entity_types"], "No deprecated 'entity_types' key")
        print_check(checks["no_separate_properties"], "No separate 'properties' section")

        print("\nNode Label Structure:")
        print_check(checks["node_has_count"], "Nodes have 'count' field")
        print_check(checks["node_has_properties"], "Nodes have 'properties' field")
        print_check(checks["properties_have_types"], "Properties include type information")

        print("\nRelationship Structure:")
        print_check(checks["rel_has_from_labels"], "Relationships have 'from_labels'")
        print_check(checks["rel_has_to_labels"], "Relationships have 'to_labels'")
        print_check(checks["rel_no_old_from"], "No deprecated 'from' key")
        print_check(checks["rel_no_old_to"], "No deprecated 'to' key")
        print_check(checks["rel_has_properties"], "Relationships have 'properties' field")
        print_check(checks["rel_properties_have_types"], "Relationship properties include types")

        print("\nCypher Guidelines:")
        print_check(checks["guidelines_has_examples"], "Has example queries")
        print_check(checks["guidelines_has_syntax"], "Has Memgraph syntax notes")
        print_check(checks["guidelines_has_rules"], "Has important rules")

        # Print sample data
        print_section("Sample Data")

        # Sample node label
        if "node_labels" in schema and schema["node_labels"]:
            sample_label = "Mission" if "Mission" in schema["node_labels"] else list(schema["node_labels"].keys())[0]
            print(f"Sample Node Label: {sample_label}")
            print(json.dumps({sample_label: schema["node_labels"][sample_label]}, indent=2))

        # Sample relationship
        if "relationship_types" in schema and schema["relationship_types"]:
            sample_rel = "RELATED_TO_INCIDENT" if "RELATED_TO_INCIDENT" in schema["relationship_types"] else list(schema["relationship_types"].keys())[0]
            print(f"\nSample Relationship: {sample_rel}")
            print(json.dumps({sample_rel: schema["relationship_types"][sample_rel]}, indent=2))

        # Cypher guidelines sample
        if "cypher_guidelines" in schema:
            print("\nCypher Guidelines Sample:")
            guidelines = schema["cypher_guidelines"]
            if "example_queries" in guidelines and guidelines["example_queries"]:
                print(f"  Number of example queries: {len(guidelines['example_queries'])}")
                print(f"  First example: {guidelines['example_queries'][0].get('description', 'N/A')}")

        # Statistics
        print_section("Statistics")
        if "statistics" in schema:
            stats = schema["statistics"]
            print(f"Total Nodes: {stats.get('total_nodes', 'N/A')}")
            print(f"Total Relationships: {stats.get('total_relationships', 'N/A')}")
            print(f"Node Label Count: {stats.get('node_label_count', 'N/A')}")
            print(f"Relationship Type Count: {stats.get('relationship_type_count', 'N/A')}")

        # Summary
        print_section("Summary")
        total_checks = len(checks)
        passed_checks = sum(1 for v in checks.values() if v)
        failed_checks = total_checks - passed_checks

        print(f"Total Checks: {total_checks}")
        print(f"Passed: {passed_checks} ✅")
        print(f"Failed: {failed_checks} ❌")

        if failed_checks == 0:
            print("\n🎉 All checks passed! Refactor is complete.")
            return 0
        else:
            print(f"\n⚠️  {failed_checks} check(s) failed. Refactor in progress.")
            print("\nFailed checks:")
            for check_name, result in checks.items():
                if not result:
                    print(f"  ❌ {check_name}")
            return 1

    except Exception as e:
        print_section("Error")
        print(f"❌ Error occurred: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())

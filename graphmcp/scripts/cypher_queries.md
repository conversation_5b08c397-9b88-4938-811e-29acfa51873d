# Cypher Queries for Mission Knowledge Graph

Access Memgraph Lab at **http://localhost:3001** and paste these queries in the query editor!

## Graph Overview

The Mission KG contains:
- **12,723 Missions** with comments, incidents, transactions
- **39 Incidents** (hurricanes, crises) with timestamps
- **63 Vendors** (fuel distributors)
- **3 relationship types**: RELATED_TO_INCIDENT, IS_PARENT_TO, ASSIGNED_VENDOR

## 1. Basic Exploration

### Get graph statistics
```cypher
MATCH (n)
RETURN labels(n)[0] as node_type, count(n) as count
ORDER BY count DESC;
```

### Get all relationship types and counts
```cypher
MATCH ()-[r]->()
RETURN type(r) as relationship_type, count(r) as count
ORDER BY count DESC;
```

### Sample of each node type
```cypher
// Missions
MATCH (m:Mission)
RETURN m.id, m.title, m.mission_number
LIMIT 5;

// Incidents
MATCH (i:Incident)
RETURN i.incident_id, i.incident_name, i.incident_type
LIMIT 5;

// Vendors
MATCH (v:Vendor)
RETURN v.vendor_id, v.mission_vendor_name
LIMIT 5;
```

## 2. Mission Queries

### Find missions by title (case-insensitive search)
```cypher
MATCH (m:Mission)
WHERE toLower(m.title) CONTAINS 'fuel'
RETURN m.id, m.title, m.mission_number
LIMIT 10;
```

### Get a mission with all its properties
```cypher
MATCH (m:Mission {id: '577660'})
RETURN m.id, m.title, m.mission_number,
       m.comments, m.incidents, m.transactions;
```

### Count missions by number of comments
```cypher
MATCH (m:Mission)
RETURN size(m.comments) as comment_count, count(m) as missions
ORDER BY comment_count DESC;
```

### Find missions with specific comment text
```cypher
MATCH (m:Mission)
WHERE any(comment IN m.comments WHERE comment CONTAINS 'demobilization')
RETURN m.id, m.title, m.comments
LIMIT 10;
```

## 3. Mission Hierarchy (Parent-Child)

### Find parent missions with their children
```cypher
MATCH (parent:Mission)-[:IS_PARENT_TO]->(child:Mission)
RETURN parent.id, parent.title,
       collect(child.title) as children
LIMIT 10;
```

### Find missions with the most children
```cypher
MATCH (parent:Mission)-[:IS_PARENT_TO]->(child:Mission)
RETURN parent.id, parent.title, count(child) as child_count
ORDER BY child_count DESC
LIMIT 10;
```

### Find orphan missions (no parent)
```cypher
MATCH (m:Mission)
WHERE NOT (:Mission)-[:IS_PARENT_TO]->(m)
RETURN m.id, m.title, m.mission_number
LIMIT 10;
```

### Get full hierarchy tree from a parent
```cypher
MATCH path = (parent:Mission {id: '577660'})-[:IS_PARENT_TO*]->(child:Mission)
RETURN path
LIMIT 25;
```

## 4. Incident Relationships

### Count missions per incident
```cypher
MATCH (m:Mission)-[:RELATED_TO_INCIDENT]->(i:Incident)
RETURN i.incident_name, i.incident_type, count(m) as mission_count
ORDER BY mission_count DESC;
```

### Find missions for Hurricane Milton
```cypher
MATCH (m:Mission)-[:RELATED_TO_INCIDENT]->(i:Incident)
WHERE i.incident_name CONTAINS 'Milton'
RETURN m.id, m.title, i.incident_name, i.incident_type
LIMIT 20;
```

### Find all hurricane-related missions
```cypher
MATCH (m:Mission)-[:RELATED_TO_INCIDENT]->(i:Incident)
WHERE i.incident_type = 'Hurricane'
RETURN i.incident_name, count(m) as missions
ORDER BY missions DESC;
```

### Get incident timeline (missions by incident creation date)
```cypher
MATCH (i:Incident)
RETURN i.incident_name,
       i.incident_type,
       i.incident_created_at,
       i.incident_status
ORDER BY i.incident_created_at DESC;
```

## 5. Vendor Relationships

### Find all vendor assignments
```cypher
MATCH (m:Mission)-[:ASSIGNED_VENDOR]->(v:Vendor)
RETURN m.id, m.title, v.mission_vendor_name
ORDER BY v.mission_vendor_name;
```

### Count missions per vendor
```cypher
MATCH (m:Mission)-[:ASSIGNED_VENDOR]->(v:Vendor)
RETURN v.mission_vendor_name, count(m) as mission_count
ORDER BY mission_count DESC;
```

### Find fuel missions without vendors
```cypher
MATCH (m:Mission)
WHERE toLower(m.title) CONTAINS 'fuel'
  AND NOT (m)-[:ASSIGNED_VENDOR]->(:Vendor)
RETURN m.id, m.title
LIMIT 10;
```

### Get vendor details with their missions
```cypher
MATCH (v:Vendor)<-[:ASSIGNED_VENDOR]-(m:Mission)
RETURN v.mission_vendor_name,
       collect({id: m.id, title: m.title}) as missions;
```

## 6. Complex Multi-Hop Queries

### Find missions that share the same vendor
```cypher
MATCH (m1:Mission)-[:ASSIGNED_VENDOR]->(v:Vendor)<-[:ASSIGNED_VENDOR]-(m2:Mission)
WHERE m1.id < m2.id  // Avoid duplicates
RETURN v.mission_vendor_name,
       m1.title as mission_1,
       m2.title as mission_2
LIMIT 10;
```

### Find related missions through incident
```cypher
MATCH (m1:Mission)-[:RELATED_TO_INCIDENT]->(i:Incident)<-[:RELATED_TO_INCIDENT]-(m2:Mission)
WHERE m1.id < m2.id
RETURN i.incident_name, count(DISTINCT m1) + count(DISTINCT m2) as total_missions
ORDER BY total_missions DESC
LIMIT 5;
```

### Find missions with parent-child relationship in same incident
```cypher
MATCH (parent:Mission)-[:IS_PARENT_TO]->(child:Mission),
      (parent)-[:RELATED_TO_INCIDENT]->(i:Incident),
      (child)-[:RELATED_TO_INCIDENT]->(i)
RETURN parent.title, child.title, i.incident_name
LIMIT 10;
```

## 7. Aggregation & Analytics

### Total missions, incidents, vendors, relationships
```cypher
MATCH (m:Mission) WITH count(m) as missions
MATCH (i:Incident) WITH missions, count(i) as incidents
MATCH (v:Vendor) WITH missions, incidents, count(v) as vendors
MATCH ()-[r]->()
RETURN missions, incidents, vendors, count(r) as total_relationships;
```

### Average comments per mission
```cypher
MATCH (m:Mission)
RETURN avg(size(m.comments)) as avg_comments,
       min(size(m.comments)) as min_comments,
       max(size(m.comments)) as max_comments;
```

### Missions without comments
```cypher
MATCH (m:Mission)
WHERE m.comments = [] OR m.comments IS NULL
RETURN count(m) as missions_without_comments;
```

## 8. Data Quality Checks

### Find missions with missing or null properties
```cypher
MATCH (m:Mission)
WHERE m.title IS NULL OR m.mission_number IS NULL
RETURN m.id, m.title, m.mission_number;
```

### Check for duplicate mission IDs
```cypher
MATCH (m:Mission)
WITH m.id as mission_id, collect(m) as missions
WHERE size(missions) > 1
RETURN mission_id, size(missions) as duplicate_count;
```

### Find missions with empty comments
```cypher
MATCH (m:Mission)
WHERE size(m.comments) = 0
RETURN count(m) as missions_with_no_comments;
```

## 9. Performance Testing

### Check if indexes exist
```cypher
SHOW INDEX INFO;
```

### Test query performance on mission lookup
```cypher
PROFILE
MATCH (m:Mission {id: '577660'})
RETURN m;
```

### Compare performance with/without index on frequently queried field
```cypher
EXPLAIN
MATCH (m:Mission)
WHERE toLower(m.title) CONTAINS 'fuel'
RETURN count(m);
```

## Quick Copy-Paste Commands

### Top 10 most connected missions
```cypher
MATCH (m:Mission)
OPTIONAL MATCH (m)-[r]->()
RETURN m.id, m.title, count(r) as connections
ORDER BY connections DESC
LIMIT 10;
```

### All missions for Hurricane Milton with vendor info
```cypher
MATCH (m:Mission)-[:RELATED_TO_INCIDENT]->(i:Incident)
WHERE i.incident_name CONTAINS 'Milton'
OPTIONAL MATCH (m)-[:ASSIGNED_VENDOR]->(v:Vendor)
RETURN m.id, m.title, v.mission_vendor_name
LIMIT 20;
```

### Full context for a specific mission
```cypher
MATCH (m:Mission {id: '577660'})
OPTIONAL MATCH (m)-[:IS_PARENT_TO]->(child:Mission)
OPTIONAL MATCH (m)-[:RELATED_TO_INCIDENT]->(i:Incident)
OPTIONAL MATCH (m)-[:ASSIGNED_VENDOR]->(v:Vendor)
RETURN m.title, m.comments,
       collect(DISTINCT child.title) as children,
       collect(DISTINCT i.incident_name) as incidents,
       collect(DISTINCT v.mission_vendor_name) as vendors;
```

## Useful Patterns

### Pattern: Find nodes with specific array element
```cypher
// Find missions where comments contain specific text
MATCH (m:Mission)
WHERE any(comment IN m.comments WHERE comment CONTAINS 'text')
RETURN m;
```

### Pattern: Count array elements
```cypher
// Count missions by number of items in array property
MATCH (m:Mission)
RETURN size(m.comments) as array_size, count(m) as count
ORDER BY array_size DESC;
```

### Pattern: Optional match for nullable relationships
```cypher
// Get missions with optional vendor (won't filter out missions without vendors)
MATCH (m:Mission)
OPTIONAL MATCH (m)-[:ASSIGNED_VENDOR]->(v:Vendor)
RETURN m.id, m.title, v.mission_vendor_name;
```

### Pattern: Collect aggregated values
```cypher
// Group children by parent
MATCH (parent:Mission)-[:IS_PARENT_TO]->(child:Mission)
RETURN parent.title, collect(child.title) as children;
```

### Pattern: Case-insensitive string search
```cypher
// Search missions (case-insensitive)
MATCH (m:Mission)
WHERE toLower(m.title) CONTAINS toLower('Fuel')
RETURN m;
```

## Notes

- **Comments** are stored as string arrays (no timestamps)
- **Incidents** and **Transactions** are JSON strings (need parsing for nested queries)
- **No indexes** currently exist (all queries do full scans)
- Use `LIMIT` on large result sets to avoid overwhelming the UI
- Use `PROFILE` or `EXPLAIN` before queries to understand performance

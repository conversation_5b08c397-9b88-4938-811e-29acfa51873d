"""
Tests for dynamic initial context generation with graph_context.
"""

import pytest
from app.agent.langgraph.context_utils import get_initial_context


class TestGetInitialContext:
    """Test suite for get_initial_context function."""

    def test_requires_graph_context(self):
        """get_initial_context now requires a valid graph_context parameter."""
        # Since we removed static fallback, passing None should raise an error
        with pytest.raises((AttributeError, TypeError)):
            get_initial_context(graph_context=None)

    def test_returns_dynamic_context_with_graph_context(self):
        """When graph_context provided, should return dynamic context with live data."""
        mock_graph_context = {
            "success": True,
            "statistics": {
                "totalNodes": 83077,
                "totalRelationships": 86427,
                "nodeLabelCount": 4,
                "relationshipTypeCount": 4,
                "averageDegree": 2.08
            },
            "highDegreeNodes": [
                {
                    "node_labels": ["Incident"],
                    "properties": {
                        "incident_id": 172,
                        "incident_name": "2024 Milton",
                        "incident_status": "Active",
                        "incident_type": "Hurricane"
                    },
                    "degree": 7673
                },
                {
                    "node_labels": ["Incident"],
                    "properties": {
                        "incident_id": 171,
                        "incident_name": "2024 Helene",
                        "incident_status": "Active",
                        "incident_type": "Hurricane"
                    },
                    "degree": 5432
                }
            ],
            "nodeLabels": [
                {
                    "label": "Incident",
                    "count": 39,
                    "properties": ["incident_id", "incident_name", "incident_status", "incident_type"]
                },
                {
                    "label": "Mission",
                    "count": 12723,
                    "properties": ["id", "mission_number", "title"]
                }
            ],
            "relationshipTypes": [
                {"type": "RELATED_TO_INCIDENT", "count": 12723},
                {"type": "COMMENTED_ON", "count": 70252}
            ]
        }
        
        result = get_initial_context(graph_context=mock_graph_context)
        
        # Should contain dynamic data
        assert "<initial_context>" in result
        assert "Current Graph State (Live Data)" in result
        assert "83,077" in result  # totalNodes (formatted with comma)
        assert "86,427" in result  # totalRelationships (formatted with comma)
        assert "2024 Milton" in result  # high-degree incident
        assert "7,673" in result  # degree count (formatted with comma)
        
        # Should still contain tool documentation
        assert "Tool Catalog" in result or "analyze_knowledge_graph" in result

    def test_handles_missing_statistics_gracefully(self):
        """When graph_context has missing fields, should handle gracefully."""
        incomplete_graph_context = {
            "success": True,
            "statistics": {
                "totalNodes": 1000
                # Missing other fields
            }
        }
        
        result = get_initial_context(graph_context=incomplete_graph_context)
        
        assert isinstance(result, str)
        assert "<initial_context>" in result
        assert "1,000" in result  # Should use what's available (formatted with comma)

    def test_handles_empty_high_degree_nodes(self):
        """When no high-degree nodes, should handle empty list."""
        graph_context_no_hubs = {
            "success": True,
            "statistics": {
                "totalNodes": 100,
                "totalRelationships": 50
            },
            "highDegreeNodes": []
        }
        
        result = get_initial_context(graph_context=graph_context_no_hubs)
        
        assert isinstance(result, str)
        assert "<initial_context>" in result
        # Should not crash, should handle gracefully

    def test_preserves_tool_documentation_section(self):
        """Dynamic context should still include tool documentation."""
        graph_context = {
            "success": True,
            "statistics": {"totalNodes": 1000, "totalRelationships": 500}
        }
        
        result = get_initial_context(graph_context=graph_context)
        
        # Should contain tool docs
        assert "analyze_knowledge_graph" in result or "search_entities" in result or "Tool" in result

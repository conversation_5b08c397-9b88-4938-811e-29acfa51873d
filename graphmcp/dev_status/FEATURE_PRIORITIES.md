# GraphMCP Feature Prioritization & Roadmap

## ⚠️ IMPLEMENTATION STATUS

**APPROVED FOR IMPLEMENTATION (Tier 1):**
1. **Graph Algorithms (MAGE)** - 5 MCP tools exposing 50+ MAGE algorithms
2. **Vector Search** - Semantic similarity search
3. **Full-Text Search** - Native Tantivy keyword search
4. **Hybrid Retrieval** - Vector + Graph + Text combined

**📋 ACTION REQUIRED:** Discussion needed on which specific MAGE algorithms to expose in the 5 MCP tools before implementation begins.

**FUTURE CONSIDERATION (Tier 2):**
- Knowledge Graph Construction from unstructured text

**NOT IMPLEMENTING (Tier 3):**
- Streaming data integration, triggers, visualization, AI Toolkit adoption, and other enhancements

---

## Prioritization Framework

Each feature evaluated on:
- **Impact**: Business value for emergency management (1-5)
- **Effort**: Implementation complexity (1-5, where 5=highest effort)
- **Dependencies**: What must be done first
- **Strategic**: Alignment with GraphRAG goals (1-5)

**Priority Score** = (Impact × Strategic) / Effort

---

## CRITICAL DECISION: Database Platform

### ✅ RECOMMENDATION: Stay with Memgraph

**Rationale:**
1. **Performance**: 3-41x faster for real-time operations
2. **Emergency Use Case Alignment**: Real-time analytics > historical archival
3. **Cost**: Open-source vs Neo4j enterprise licensing
4. **Compatibility**: Already using Neo4j driver via Bolt protocol
5. **Native Features**: Vector search, full-text, streaming built-in

**Action**: ✅ Confirmed - Stay with Memgraph, adopt Neo4j GraphRAG patterns

---

## TIER 1: FOUNDATIONAL (Approved for Implementation)

### 1.1 MAGE Graph Algorithms
**Impact**: 5/5 | **Effort**: 2/5 | **Strategic**: 5/5 | **Score**: 12.5

**Why Critical:**
- Industry-standard graph analytics (50+ algorithms)
- PageRank, centrality, communities, path finding
- Required for advanced GraphRAG capabilities
- Memgraph's differentiator vs Neo4j GDS

**📋 DECISION NEEDED: Algorithm Selection**

MAGE provides 50+ algorithms across these categories:
- **Centrality**: PageRank, Betweenness, Degree, Closeness, Katz, Harmonic
- **Community Detection**: Louvain, Leiden, LabelRank, Connected Components, Weakly Connected
- **Path Finding**: Shortest Path, All Pairs Shortest Path, A* Search, Dijkstra
- **Link Prediction**: Node Classification, Graph Neural Networks
- **Similarity**: Node Similarity, Jaccard, Cosine
- **Temporal**: Dynamic centrality algorithms for time-series graphs

**Tool Design Philosophy:**

Based on research showing LLMs struggle with parameter complexity:
- **Minimal required parameters** (1-2 per tool)
- **Extensive defaults** for optional parameters
- **Domain-agnostic descriptions** (not emergency-specific)
- **Clear enum choices** for algorithm selection

Reference: RAG-MCP 2024 study shows 50% token reduction and 3x accuracy improvement with simplified tool interfaces.

---

**Proposed MCP Tool Structure (5 tools):**

### **1. `calculate_centrality(algorithm, node_type=None, limit=10)`**
Calculate node importance/centrality in the graph.

Centrality identifies the most important nodes based on their position and connections in the network. Different algorithms measure importance differently:

- **pagerank**: Overall influence based on connections from important nodes (Google's algorithm)
- **betweenness**: Nodes that act as bridges between different parts of the network
- **degree**: Number of direct connections (simplest measure)
- **closeness**: Average distance to all other reachable nodes

**Parameters:**
- `algorithm` (required): One of ["pagerank", "betweenness", "degree", "closeness"]
- `node_type` (optional, default: all types): Filter to specific node type
- `limit` (optional, default: 10): Return top N most central nodes

**Use Cases:** Identify key entities in organizational networks, find bottlenecks or critical connection points, rank nodes by structural importance.

---

### **2. `detect_communities(algorithm, node_types=[], min_size=2)`**
Detect communities/clusters of related nodes in the graph.

Community detection identifies groups of nodes that are more densely connected to each other than to the rest of the network. Useful for finding natural groupings, clusters, or organizational structures.

- **leiden**: High-quality community detection (improved Louvain, state-of-the-art)
- **louvain**: Fast hierarchical community detection
- **connected_components**: Find disconnected subgraphs (basic clustering)

**Parameters:**
- `algorithm` (required): One of ["leiden", "louvain", "connected_components"]
- `node_types` (optional, default: all types): Include only specific node types
- `min_size` (optional, default: 2): Minimum nodes per community to return

**Use Cases:** Group related entities, identify organizational clusters, detect coordination patterns, find isolated subgraphs.

---

### **3. `find_paths(algorithm, source_id=None, target_id=None, max_depth=5)`**
Find paths between nodes in the graph.

Path finding discovers connections and routes between entities. Essential for understanding how nodes are related through intermediate connections.

- **shortest_path**: Single shortest path between two specific nodes (Dijkstra)
- **all_pairs_shortest**: Shortest paths between all node pairs (connectivity analysis)

**Parameters:**
- `algorithm` (required): One of ["shortest_path", "all_pairs_shortest"]
- `source_id` (required for shortest_path): Starting node ID
- `target_id` (required for shortest_path): Ending node ID
- `max_depth` (optional, default: 5): Maximum path length to consider

**Use Cases:** Discover relationships between entities, find routing paths, analyze network connectivity, identify coordination chains.

---

### **4. `predict_relationships(algorithm, source_type, target_type, limit=20, min_confidence=0.5)`**
Predict missing or likely future relationships between nodes.

Link prediction suggests connections that don't currently exist but are likely based on graph structure and patterns. Useful for recommendations, gap analysis, and forecasting.

- **common_neighbors**: Predict based on shared connections (simple, effective)
- **adamic_adar**: Weighted common neighbors (emphasizes rare connections)

**Parameters:**
- `algorithm` (required): One of ["common_neighbors", "adamic_adar"]
- `source_type` (required): Source node type for predictions
- `target_type` (required): Target node type for predictions
- `limit` (optional, default: 20): Return top N predictions
- `min_confidence` (optional, default: 0.5): Minimum prediction confidence (0-1)

**Use Cases:** Suggest missing relationships, recommend connections, predict future links, identify collaboration opportunities.

---

### **5. `calculate_similarity(algorithm, node_id, compare_to_type=None, limit=10)`**
Calculate similarity between nodes based on graph structure.

Node similarity compares entities based on their connections, neighbors, or structural patterns. Useful for finding comparable entities, alternatives, or patterns.

- **jaccard**: Similarity based on shared connections (intersection over union)
- **cosine**: Angular similarity between connection vectors

**Parameters:**
- `algorithm` (required): One of ["jaccard", "cosine"]
- `node_id` (required): Reference node to compare against
- `compare_to_type` (optional, default: same as node_id type): Node type to compare with
- `limit` (optional, default: 10): Return top N most similar nodes

**Use Cases:** Find similar entities, identify alternatives, detect patterns, compare structural roles.

---

**Implementation Approach:**
```bash
# Phase 1: Docker setup (Straightforward)
1. Switch docker-compose to memgraph-mage image
2. Verify MAGE algorithms loaded on startup
3. Test algorithm availability

# Phase 2: MCP tool wrappers (Moderate complexity)
1. Create 5 parameterized MCP tools with simplified signatures
2. Implement extensive defaults (only 1-3 required params per tool)
3. Write domain-agnostic descriptions (no emergency-specific language)
4. Add comprehensive input validation and error handling
5. Include clear algorithm explanations in tool metadata
```

**Deliverables:**
- [ ] memgraph-mage container running
- [ ] 5 MCP tools with simplified signatures (1-3 required params each)
- [ ] Domain-agnostic tool descriptions (reusable across domains)
- [ ] Suggested agent system prompt for optimal tool usage
- [ ] Documentation of exposed algorithms and use cases

**Suggested Agent Prompt for MAGE Tools:**

When MAGE tools are implemented, add this to your agent's system prompt:

```markdown
You have access to graph algorithm tools for advanced network analysis:

1. calculate_centrality() - Identify important/influential nodes
   - Use "pagerank" for overall importance (Google's algorithm)
   - Use "betweenness" to find bridges/bottlenecks between network parts
   - Use "degree" for simple connection counts
   - Use "closeness" for nodes central to the entire network

2. detect_communities() - Find clusters of related entities
   - Use "leiden" for high-quality community detection (state-of-the-art)
   - Use "louvain" for fast hierarchical clustering
   - Use "connected_components" to find disconnected subgraphs

3. find_paths() - Discover relationships between entities
   - Use "shortest_path" to connect two specific nodes (requires source_id, target_id)
   - Use "all_pairs_shortest" to analyze overall connectivity

4. predict_relationships() - Suggest missing or likely connections
   - Use "common_neighbors" for simple, effective relationship prediction
   - Use "adamic_adar" for weighted prediction (emphasizes rare connections)
   - Higher confidence scores indicate stronger predictions

5. calculate_similarity() - Compare entities structurally
   - Use "jaccard" to compare based on shared connections (intersection/union)
   - Use "cosine" for angular similarity between connection vectors

BEST PRACTICES:
- Start with simple queries (e.g., centrality) before complex analysis
- Use limit parameters to control result size (defaults: 10-20)
- Combine multiple tools for comprehensive analysis:
  - Example: centrality → communities → similarity
- Explain algorithm choice and results interpretation to users
- For ambiguous queries, try multiple algorithms and compare results
```

---

### 1.2 Vector Search & Semantic Similarity
**Impact**: 5/5 | **Effort**: 3/5 | **Strategic**: 5/5 | **Score**: 8.3

**📋 Detailed Plan:** See `dev_status/vector_search_implementation_plan.md` for comprehensive implementation guide including:
- Research findings from Neo4j and Memgraph documentation
- Embedding metadata best practices
- Chunking strategy analysis (decision: NO chunking for atomic entities)
- Model selection rationale (text-embedding-3-small recommended)
- Configuration examples and cost estimates

**Why Critical:**
- Core GraphRAG capability (parity with Neo4j)
- Enables semantic incident matching
- Foundation for hybrid retrieval
- Native HNSW implementation in Memgraph v3.0+

**Implementation:**
```python
# Phase A: Infrastructure Setup (Straightforward)
1. Update docker-compose: ensure Memgraph v3.0+ for vector support
2. Add OpenAI integration to environment variables
3. Set ENABLE_VECTOR_SEARCH=true in config

# Phase B: Embedding Generation (Moderate complexity)
1. Create scripts/generate_embeddings.py for batch processing
2. Generate embeddings for Incident and Resource nodes
3. Store in 'embedding' property (1536-dimensional vectors)
4. Create vector indices: CREATE VECTOR INDEX ...

# Phase C: Vector Search MCP Tools (Moderate complexity)
1. semantic_search_incidents(query, limit) - Find similar incidents
2. find_similar_resources(incident_id, limit) - Resource matching
3. semantic_entity_linking(text) - Link text to graph entities
```

**Vector Index Setup:**
```cypher
// Create vector indices on key node types
CREATE VECTOR INDEX incident_embedding ON :Incident WITH
  {dimension: 1536, capacity: 1000, similarity: "cosine"};

CREATE VECTOR INDEX resource_embedding ON :Resource WITH
  {dimension: 1536, capacity: 500, similarity: "cosine"};
```

**Embedding Workflow:**
- **One-time**: Generate embeddings for existing nodes via `scripts/generate_embeddings.py`
- **Ongoing**: Generate embeddings for new nodes during data ingestion
- **Query-time**: Generate embedding for search query on-the-fly

**Which Nodes Need Embeddings:**
- ✅ **Incident**: descriptions, locations, types (high value for semantic search)
- ✅ **Resource**: descriptions, capabilities (resource matching use case)
- ❌ **Department**: Names/codes (structured data, no semantic value)
- ❌ **GeographicArea**: Geographic data (spatial search, not semantic)
- ❌ **Person**: Names (structured, use exact match)

**Deliverables:**
- [ ] Vector indices created on Incident and Resource nodes
- [ ] `scripts/generate_embeddings.py` batch processor
- [ ] OpenAI API integration for embedding generation
- [ ] 3 new MCP tools for semantic search
- [ ] Documentation with embedding cost estimates

**Estimated Costs:**
- 12 Incidents: ~$0.01
- 18 Resources: ~$0.02
- Full dataset (4,731 nodes): ~$0.50-1.00 one-time

---

### 1.3 Full-Text Search (Native Tantivy)
**Impact**: 4/5 | **Effort**: 2/5 | **Strategic**: 4/5 | **Score**: 8.0

**Why Critical:**
- Keyword-based search complements semantic search
- Native Tantivy integration (not Lucene) in Memgraph v3.5+
- Required for hybrid retrieval
- Faster than external Elasticsearch

**Implementation:**
```cypher
// Phase 1: Create text indices (Straightforward)
CREATE TEXT INDEX incident_fulltext ON :Incident;
CREATE TEXT INDEX resource_fulltext ON :Resource;

// Phase 2: MCP tools (Quick)
1. full_text_search_incidents(keywords, limit)
2. full_text_search_resources(keywords, limit)
3. combined_text_search(keywords, entity_types, limit)
```

**Tantivy Features:**
- Full-text indexing on string properties
- Fast keyword search optimized for real-time
- Lower memory footprint than Elasticsearch
- Integrated directly into Memgraph

**Use Cases:**
- Search incidents by description keywords
- Find resources by capability text
- Combined with vector search for hybrid retrieval

**Deliverables:**
- [ ] Text indices created on Incident and Resource
- [ ] 3 MCP tools for full-text search
- [ ] Performance benchmarks vs vector search
- [ ] Documentation with query examples

---

### 1.4 Hybrid Retrieval (Vector + Graph + Text)
**Impact**: 5/5 | **Effort**: 3/5 | **Strategic**: 5/5 | **Score**: 8.3

**Why Critical:**
- Modern GraphRAG standard (Neo4j uses this pattern)
- Combines semantic + structural + keyword knowledge
- Better accuracy than any single method
- Emergency management requires multi-faceted search

**Dependencies:**
- ✅ Requires: Vector search (1.2)
- ✅ Requires: Full-text search (1.3)
- ✅ Requires: MAGE (1.1) for advanced traversal

**Implementation:**
```python
# New MCP tool (Moderate complexity, after prerequisites)
hybrid_retrieve(
    query: str,
    entity_type: str = "Incident",
    vector_top_k: int = 10,
    text_top_k: int = 10,
    graph_depth: int = 2,
    combine_method: str = "weighted"  # weighted, rrf, cascade
) -> dict

# Returns combined results with scores from:
# 1. Vector similarity search (semantic)
# 2. Full-text keyword search (exact matches)
# 3. Graph traversal from top results (structural context)
```

**Retrieval Strategies:**

1. **Weighted Combination**: Score = 0.4×vector + 0.3×text + 0.3×graph
2. **Reciprocal Rank Fusion (RRF)**: Industry standard for combining rankings
3. **Cascade**: Vector first, then text, then graph expansion

**Example Query Flow:**
```
User query: "Hurricane damage in coastal areas"
↓
1. Vector Search: Find semantically similar incidents (cosine similarity)
2. Text Search: Find incidents with "hurricane" and "coastal" keywords
3. Graph Traversal: Expand to related resources, departments, locations
4. Combine & Rank: Merge results with combined scoring
```

**Deliverables:**
- [ ] Hybrid retrieval MCP tool implemented
- [ ] All 3 combination methods supported
- [ ] Scoring tuning for emergency management domain
- [ ] Performance optimization (caching, parallelization)
- [ ] Documentation with accuracy benchmarks

---

## TIER 2: GOOD TO HAVE (Future Consideration)

### 2.1 Knowledge Graph Construction from Unstructured Text
**Impact**: 4/5 | **Effort**: 4/5 | **Strategic**: 5/5 | **Score**: 5.0

**Dependencies:**
- ✅ Requires: Vector search (Tier 1) for entity disambiguation
- ✅ Requires: LLM integration (foundation model API)

**Description:**
Extract entities and relationships from unstructured emergency reports and integrate into existing knowledge graph.

**Implementation Approach:**
```python
# Use LangChain GraphTransformer
from langchain_experimental.graph_transformers import LLMGraphTransformer

def extract_entities_from_text(text: str) -> dict:
    """Extract entities and relationships from emergency reports"""
    # 1. Entity extraction (Incident, Resource, Location)
    # 2. Relationship identification (RESPONDS_TO, LOCATED_AT)
    # 3. Disambiguation against existing graph (vector similarity)
    # 4. Confidence scoring
    # 5. Manual review workflow for low-confidence extractions
```

**Use Cases:**
- Ingest incident reports (emails, PDFs, unstructured text)
- Automatically create/update graph from natural language
- Enrich existing incidents with new information

**Deliverables:**
- Entity extraction pipeline
- Disambiguation logic
- MCP tool for text ingestion
- Human-in-the-loop review interface

---

## TIER 3: NOT IMPLEMENTING

These features are out of scope for current implementation:

### Streaming Data Integration
- **Why not now**: Adds significant complexity
- **Memgraph advantage**: Native Kafka/Pulsar support
- **Future**: Consider when real-time incident feeds available

### Trigger-Based Automation
- **Why not now**: Requires mature graph schema and patterns
- **Use cases**: Auto-classification, alerts, derived properties
- **Future**: Consider after Tier 1 stable and validated

### Memgraph AI Toolkit Adoption
- **Why not now**: We've built custom MCP tools with significant investment
- **Trade-off**: Toolkit is LangChain-focused, we're MCP-focused
- **Future**: Hybrid approach (keep custom tools, add toolkit for LangChain compatibility)

### Enhanced Visualization
- **Why not now**: Memgraph Lab sufficient for development
- **Options**: Custom dashboards, Graphistry integration
- **Future**: Consider when user-facing requirements clear

### Agentic GraphRAG
- **Why not now**: Requires all Tier 1 features + LangGraph + testing
- **Complexity**: Self-correction logic, multi-strategy retrieval
- **Future**: Natural evolution after Tier 1 proven

### APOC-like Utilities
- **Why not now**: Build utilities as needed, not bulk implementation
- **Approach**: Incremental addition of helper functions

### Advanced ML Pipelines
- **Why not now**: Significant effort, unclear ROI for emergency management
- **Features**: Link prediction, node classification, temporal prediction
- **Future**: Research phase needed first

---

## IMPLEMENTATION ROADMAP

### Phase 1: Core GraphRAG (Tier 1 Implementation)

**Step 1: MAGE Integration (Straightforward)**
- Switch docker-compose to memgraph-mage image
- Verify 50+ algorithms available
- **📋 DECISION POINT**: Select algorithms to expose
- Implement 5 parameterized MCP tools
- Create emergency management examples

**Step 2: Vector Search (Moderate complexity)**
- Enable Memgraph v3.0+ vector support
- Add OpenAI API integration
- Create `scripts/generate_embeddings.py`
- Generate embeddings for Incident/Resource nodes
- Create vector indices
- Implement 3 semantic search MCP tools

**Step 3: Full-Text Search (Quick)**
- Create Tantivy text indices on Incident/Resource
- Implement 3 full-text search MCP tools
- Performance testing

**Step 4: Hybrid Retrieval (Moderate complexity)**
- Implement hybrid retrieval MCP tool
- Support 3 combination methods (weighted, RRF, cascade)
- Tune scoring for emergency management domain
- Performance optimization

**Step 5: Testing & Documentation (Moderate complexity)**
- End-to-end testing with real scenarios
- Accuracy benchmarks vs baseline
- Performance benchmarks (latency, throughput)
- Comprehensive user documentation
- Cost analysis (embedding generation)

**Deliverable**: Production-ready GraphRAG system with parity to Neo4j GraphRAG core features

---

## SUCCESS METRICS

### Phase 1 (Core GraphRAG - Tier 1)
- ✅ MAGE algorithms operational (5 MCP tools, selected algorithms working)
- ✅ Vector search operational (<100ms p95 latency for queries)
- ✅ Full-text search integrated (Tantivy indices created and tested)
- ✅ Hybrid retrieval accuracy ≥ baseline individual methods
- ✅ 11+ new MCP tools documented and tested
- ✅ Embedding generation cost <$5 for full dataset

### Cost & Performance Targets
- **Vector Search Latency**: <100ms p95, <50ms p50
- **Full-Text Search Latency**: <50ms p95, <20ms p50
- **Hybrid Retrieval Latency**: <200ms p95, <100ms p50
- **Embedding Cost**: <$1 for initial dataset, <$0.01 per new incident
- **Memory Usage**: <4GB for Memgraph + MAGE container

---

## RISK MITIGATION

### Technical Risks

**Risk 1: Memgraph v3.0+ vector features may have limitations**
- Mitigation: Test thoroughly with production-size data early
- Benchmark HNSW performance vs external vector stores
- Have rollback plan to Milvus if needed

**Risk 2: MAGE algorithm performance at scale**
- Mitigation: Benchmark PageRank, community detection with full graph
- Tune algorithm parameters (iterations, thresholds)
- Implement query timeouts

**Risk 3: Embedding costs exceed budget**
- Mitigation: Start with small dataset, measure actual costs
- Consider smaller embedding models (768-dim vs 1536-dim)
- Cache embeddings aggressively

**Risk 4: Hybrid retrieval scoring needs tuning**
- Mitigation: Start with standard RRF (no tuning needed)
- Collect user feedback on result quality
- A/B test different combination strategies

### Business Risks

**Risk 1: Over-engineering for current use case**
- Mitigation: Tier 1 only, validate before Tier 2
- User feedback loops at each step
- Can stop after any deliverable if sufficient

**Risk 2: Memgraph vendor lock-in**
- Mitigation: Use standard Cypher where possible
- Keep Neo4j driver compatibility
- Abstract vector search behind interface (can swap to Neo4j if needed)

---

## NEXT STEPS

### Immediate Actions:

1. **📋 DECISION REQUIRED**: Review 50+ MAGE algorithms and select which to expose in 5 MCP tools
   - Review MAGE documentation for algorithm details
   - Map emergency management use cases to algorithms
   - Prioritize algorithms by value and complexity

2. **Environment Setup**:
   - Copy `.env.example` to `.env` and add `OPENAI_API_KEY`
   - Update `ENABLE_VECTOR_SEARCH=true`
   - Review and adjust performance settings

3. **Begin Implementation**:
   - Start with MAGE integration (Docker change + tool wrappers)
   - Proceed to vector search after MAGE validated
   - Continue with full-text and hybrid retrieval

---

## CONCLUSION

**Approved Implementation Plan:**

1. ✅ **Confirmed**: Stay with Memgraph (performance + cost advantages)
2. ✅ **Tier 1 Only**: MAGE + Vector + Text + Hybrid (4 features)
3. 📋 **Decision Needed**: Select specific MAGE algorithms before implementation
4. ✅ **Tier 2 Deferred**: Knowledge graph construction (future consideration)
5. ✅ **Tier 3 Out of Scope**: Streaming, triggers, visualization, etc.

**Expected Outcome**:
- Feature parity with Neo4j GraphRAG core capabilities
- Performance advantage (3-41x faster queries)
- Lower operational costs (open-source vs enterprise)
- Native full-text search (Tantivy) without external dependencies
- Access to 50+ MAGE graph algorithms

**Next Immediate Action**:
📋 **Discuss and select specific MAGE algorithms** to expose in the 5 MCP tools, then begin implementation starting with MAGE Docker integration.

# GraySky<PERSON><PERSON><PERSON><PERSON> Assistant - Local Development Configuration
# This file is for LOCAL DEVELOPMENT ONLY
# Production deployments use service-specific .env files

# Deployment Configuration
COMPOSE_PROJECT_NAME=demes-assistant
ENVIRONMENT=development

# Service Ports (to avoid conflicts on host)
BACKEND_PORT=8000
FRONTEND_PORT=3000
GRAPHMCP_PORT=8001
MEMGRAPH_PORT=7687
MEMGRAPH_LAB_PORT=3001
PHOENIX_PORT=6006
POSTGRES_PORT=5432

# Network Configuration
NETWORK_NAME=demes-assistant-network

# Notes:
# - Copy this file to .env for local development
# - Service-specific configuration is in each submodule's .env file
# - These variables control port mappings and orchestration only
# - Production deployments do not use this file (services deployed independently)

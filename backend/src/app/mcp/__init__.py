"""
MCP (Model Context Protocol) module for managing MCP client connections.

This module provides a clean abstraction for working with multiple MCP servers,
including the Genie MCP server and GraphRAG MCP server.
"""

from .base import MCPClientBase
from .adapter import MC<PERSON><PERSON>ool<PERSON>dapter
from .models import MCPServerConfig, MCP<PERSON>oolDefinition, MCPToolResult

__all__ = [
    "MCPClientBase",
    "MCPToolAdapter",
    "MCPServerConfig",
    "MCPToolDefinition",
    "MCPToolResult"
]
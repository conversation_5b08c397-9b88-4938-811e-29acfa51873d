"""
Test that tools are correctly grouped by server.

Run: uv run python scripts/test_tool_grouping.py
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.agent.langgraph.workflows import get_all_tools, get_tools_by_server
from app.core.logging import get_logger

logger = get_logger(__name__)


async def test_grouping():
    """Test tool grouping."""
    print("=" * 60)
    print("STEPS 2 & 3: Testing tool grouping...")
    print("=" * 60)
    print()

    print("1. Getting all tools...")
    all_tools = await get_all_tools()
    print(f"   ✓ Total tools: {len(all_tools)}")
    print()

    print("2. Getting Genie tools...")
    genie_tools = await get_tools_by_server("genie")
    print(f"   ✓ Genie tools: {len(genie_tools)}")
    if genie_tools:
        print("   Sample Genie tools:")
        for tool in genie_tools[:5]:
            print(f"     - {tool.name}")
    print()

    print("3. Getting GraphRAG tools...")
    graphrag_tools = await get_tools_by_server("graphrag")
    print(f"   ✓ GraphRAG tools: {len(graphrag_tools)}")
    if graphrag_tools:
        print("   Sample GraphRAG tools:")
        for tool in graphrag_tools[:5]:
            print(f"     - {tool.name}")
    print()

    print("4. Getting unknown tools...")
    unknown_tools = await get_tools_by_server("unknown")
    print(f"   ✓ Unknown tools: {len(unknown_tools)}")
    if unknown_tools:
        print("   Unknown tools:")
        for tool in unknown_tools:
            print(f"     - {tool.name}")
    print()

    # Verify grouping is correct
    total_grouped = len(genie_tools) + len(graphrag_tools) + len(unknown_tools)
    print("5. Verification...")
    print(f"   Total tools: {len(all_tools)}")
    print(f"   Grouped tools: {total_grouped}")

    if total_grouped == len(all_tools):
        print("   ✓ All tools are properly grouped!")
    else:
        print(f"   ⚠ Warning: Some tools may not be properly grouped")
        print(f"     Difference: {len(all_tools) - total_grouped}")

    print()
    print("=" * 60)
    print("✓ Tool grouping test complete!")
    print("=" * 60)
    return True


if __name__ == "__main__":
    result = asyncio.run(test_grouping())
    sys.exit(0 if result else 1)

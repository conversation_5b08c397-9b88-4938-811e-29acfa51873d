"""
GraphRAG MCP client implementation.

Connects to the GraphRAG MCP server for graph-based emergency management queries.
"""

from typing import List, Dict, Any

# Official MCP SDK imports
from mcp.client.streamable_http import streamablehttp_client
from mcp.client.session import ClientSession

from app.core.logging import get_mcp_logger
from app.mcp.base import MCPClientBase

logger = get_mcp_logger()


class GraphRAGMCPClient(MCPClientBase):
    """
    GraphRAG MCP client for graph database operations.

    Provides access to emergency management graph data through MCP tools.
    """

    def __init__(self, server_url: str, timeout: int = 30):
        """Initialize the GraphRAG MCP client."""
        super().__init__(server_url, timeout)

        logger.info("Initialized GraphRAG MCP Client",
                   server_url=server_url,
                   timeout=timeout)

    async def discover_tools(self) -> List[Dict[str, Any]]:
        """
        Discover available tools from the GraphRAG MCP server.

        Returns:
            List of tool definitions with name, description, and parameters
        """
        try:
            # Ensure server_url ends with /mcp for official MCP protocol
            server_url = self.server_url
            if not server_url.endswith('/mcp'):
                server_url = f"{server_url}/mcp"

            logger.info(f"Discovering tools from GraphRAG MCP server: {server_url}")

            # Use official MCP SDK
            async with streamablehttp_client(server_url) as streams:
                read, write, get_session_id = streams
                async with ClientSession(read, write) as session:
                    # Initialize the session
                    await session.initialize()

                    # List available tools
                    tools_result = await session.list_tools()

                    # Convert to standard format
                    tools = []
                    for tool in tools_result.tools:
                        tools.append({
                            "name": tool.name,
                            "description": tool.description or "",
                            "parameters": tool.inputSchema or {}
                        })

                    logger.info(f"Discovered {len(tools)} tools from GraphRAG server")

                    # Log some sample tools for debugging
                    if tools:
                        sample_tools = [t["name"] for t in tools[:5]]
                        logger.debug(f"Sample GraphRAG tools: {sample_tools}")

                    return tools

        except Exception as e:
            logger.error(f"Failed to discover tools from GraphRAG server {server_url}",
                        error_type=type(e).__name__,
                        error_message=str(e),
                        exc_info=True)
            return []

    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Call a tool on the GraphRAG MCP server.

        Args:
            tool_name: Name of the tool to call
            arguments: Tool arguments

        Returns:
            Tool execution result
        """
        try:
            # Ensure server_url ends with /mcp for official MCP protocol
            server_url = self.server_url
            if not server_url.endswith('/mcp'):
                server_url = f"{server_url}/mcp"

            logger.info(f"Calling GraphRAG tool '{tool_name}'",
                       server_url=server_url,
                       arguments_keys=list(arguments.keys()) if arguments else [])

            # Use official MCP SDK
            async with streamablehttp_client(server_url) as streams:
                read, write, get_session_id = streams
                async with ClientSession(read, write) as session:
                    # Initialize the session
                    await session.initialize()

                    # Call the tool
                    result = await session.call_tool(tool_name, arguments)

                    # Convert result to standard format
                    if hasattr(result, 'content'):
                        # Handle different content types
                        content = []
                        for item in result.content:
                            if hasattr(item, 'text'):
                                content.append({"type": "text", "text": item.text})
                            else:
                                content.append({"type": "unknown", "data": str(item)})

                        response = {
                            "success": True,
                            "content": content,
                            "isError": getattr(result, 'isError', False)
                        }
                    else:
                        response = {
                            "success": True,
                            "content": [{"type": "text", "text": str(result)}],
                            "isError": False
                        }

                    logger.info(f"GraphRAG tool '{tool_name}' executed successfully",
                              content_items=len(response.get("content", [])))
                    return response

        except Exception as e:
            logger.error(f"Failed to call GraphRAG tool '{tool_name}'",
                        server_url=server_url,
                        error_type=type(e).__name__,
                        error_message=str(e),
                        exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "content": [{"type": "text", "text": f"Error calling GraphRAG tool: {e}"}],
                "isError": True
            }
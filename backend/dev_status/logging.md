# ✅ Production Logging Implementation Plan - COMPLETED

## 🎉 Implementation Status: COMPLETE

✅ **Full structured logging system implemented and tested**
- JSON structured logging with correlation IDs ✅
- FastAPI middleware for request tracking ✅  
- Performance tracking with context managers ✅
- Sensitive data scrubbing ✅
- Environment-controlled configuration ✅
- Service integration across Foundation Model and MCP services ✅

## Overview

Implement structured JSON logging with correlation IDs, performance tracking, and security-aware practices for production-ready observability across all backend services.

**Priority:** HIGH - Essential for production debugging and monitoring ✅ COMPLETED

## Current State Analysis

### ✅ Implemented
- ✅ Basic Python logging with `logger = logging.getLogger(__name__)` pattern
- ✅ Component-specific loggers (foundation_model, mcp_service, mcp_client)
- ✅ Error logging with exception handling
- ✅ F-string formatted messages
- ✅ Structured JSON logging format with structlog integration
- ✅ Request correlation IDs with FastAPI middleware
- ✅ FastAPI middleware for request tracking
- ✅ Performance metrics and timing with context managers
- ✅ Sensitive data scrubbing (API keys, patterns)
- ✅ Centralized logging configuration in app/core/logging.py
- ✅ Production environment controls via pydantic settings

### ❌ Missing (Future Enhancements)
- Azure Monitor specific integration queries
- Log file rotation (currently console only)
- Advanced scrubbing patterns for business data

## Major Design Decisions

### 1. Logging Library Strategy
**Decision:** Hybrid approach using `structlog` + standard library
```python
# Benefits of this approach:
# - structlog provides structured JSON output
# - Maintains compatibility with existing logging.getLogger() pattern  
# - Easy migration path
# - Rich ecosystem support
```

### 2. Structured JSON Format Standard
**Decision:** Standardize on consistent log record format across all services
```json
{
  "timestamp": "2024-01-15T10:30:00.123456Z",
  "level": "INFO",
  "logger": "app.services.foundation_model", 
  "message": "Foundation model request initiated",
  "request_id": "req_abc123",
  "conversation_id": "conv_xyz789",
  "component": "foundation_model",
  "duration_ms": 1250,
  "extra": {
    "provider": "azure_openai",
    "model": "gpt-4",
    "tool_calls": 2
  }
}
```

### 3. Correlation ID Strategy  
**Decision:** Use FastAPI middleware + `contextvars.ContextVar` for async-safe propagation
```python
# Why this approach:
# - Automatic correlation ID generation per request
# - Propagates across all async service calls without manual passing
# - Thread-safe for concurrent requests
# - Zero code changes required in existing services
```

### 4. Performance Tracking Pattern
**Decision:** Context managers for explicit performance boundaries
```python
# Pattern provides:
# - Clear performance boundaries  
# - Automatic timing calculation
# - Integration with structured logging
# - Exception-safe cleanup
with logger.performance_context("foundation_model_generation"):
    result = await self._generate_response(...)
```

### 5. Sensitive Data Scrubbing Strategy
**Decision:** Automatic pattern-based scrubbing with configurable sensitivity levels
```python
# Scrub patterns:
# - API keys (any field containing "key", "token", "secret")  
# - Long user messages (>100 chars, log length only)
# - Specific environment variables
# - Tool call arguments containing personal data
```

## Core Implementation Components

### 1. Centralized Logging Configuration

**File:** `backend/app/core/logging.py`

```python
import logging
import structlog
import sys
from contextvars import ContextVar
from typing import Any, Dict
from pydantic import BaseModel, Field
from datetime import datetime
import uuid
import time
import re

# Context variables for correlation
request_id_var: ContextVar[str] = ContextVar('request_id', default='')
conversation_id_var: ContextVar[str] = ContextVar('conversation_id', default='')

class LoggingSettings(BaseModel):
    """Production-ready logging configuration."""
    level: str = Field(default="INFO", env="LOG_LEVEL")
    format: str = Field(default="json", env="LOG_FORMAT")  # json, text
    correlation_enabled: bool = Field(default=True, env="LOG_CORRELATION_ENABLED")
    performance_tracking: bool = Field(default=True, env="LOG_PERFORMANCE_TRACKING")
    sensitive_data_scrubbing: bool = Field(default=True, env="LOG_SCRUB_SENSITIVE")
    console_output: bool = Field(default=True, env="LOG_CONSOLE_OUTPUT")
    max_message_length: int = Field(default=200, env="LOG_MAX_MESSAGE_LENGTH")

class SensitiveDataScrubber:
    """Handles automatic scrubbing of sensitive data from log records."""
    
    SENSITIVE_PATTERNS = [
        re.compile(r'(api[_-]?key|token|secret|password|credential)', re.IGNORECASE),
        re.compile(r'Bearer [A-Za-z0-9\-\._~\+\/]+=*', re.IGNORECASE),
        re.compile(r'sk-[A-Za-z0-9]{48}'),  # OpenAI API keys
    ]
    
    def __init__(self, max_message_length: int = 200):
        self.max_message_length = max_message_length
    
    def scrub_record(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """Scrub sensitive data from log record."""
        scrubbed = record.copy()
        
        # Scrub sensitive field values
        for key, value in scrubbed.items():
            if isinstance(value, str):
                if self._is_sensitive_field(key):
                    scrubbed[key] = "[SCRUBBED]"
                elif len(value) > self.max_message_length and key in ['message', 'user_message']:
                    scrubbed[key] = f"[TRUNCATED - {len(value)} chars]"
                else:
                    scrubbed[key] = self._scrub_patterns(value)
        
        return scrubbed
    
    def _is_sensitive_field(self, field_name: str) -> bool:
        """Check if field name indicates sensitive data."""
        return any(pattern.search(field_name) for pattern in self.SENSITIVE_PATTERNS)
    
    def _scrub_patterns(self, text: str) -> str:
        """Scrub sensitive patterns from text."""
        for pattern in self.SENSITIVE_PATTERNS:
            text = pattern.sub("[SCRUBBED]", text)
        return text

class PerformanceContext:
    """Context manager for tracking operation performance."""
    
    def __init__(self, operation_name: str, logger: Any):
        self.operation_name = operation_name
        self.logger = logger
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration_ms = int((time.time() - self.start_time) * 1000)
            self.logger.info(
                f"{self.operation_name} completed",
                extra={
                    "operation": self.operation_name,
                    "duration_ms": duration_ms,
                    "success": exc_type is None
                }
            )

def add_correlation_id(logger: Any, method_name: str, event_dict: Dict[str, Any]) -> Dict[str, Any]:
    """Add correlation IDs to log records."""
    request_id = request_id_var.get()
    conversation_id = conversation_id_var.get()
    
    if request_id:
        event_dict["request_id"] = request_id
    if conversation_id:
        event_dict["conversation_id"] = conversation_id
        
    return event_dict

def add_component_info(logger: Any, method_name: str, event_dict: Dict[str, Any]) -> Dict[str, Any]:
    """Add component information to log records."""
    logger_name = event_dict.get("logger", "")
    
    # Extract component from logger name (e.g. app.services.foundation_model -> foundation_model)
    if logger_name.startswith("app.services."):
        component = logger_name.replace("app.services.", "")
    elif logger_name.startswith("app.api."):
        component = "api"
    else:
        component = logger_name.split(".")[-1] if "." in logger_name else "unknown"
    
    event_dict["component"] = component
    return event_dict

def setup_logging(settings: LoggingSettings) -> None:
    """Configure structured logging for the application."""
    
    # Clear existing handlers
    logging.getLogger().handlers.clear()
    
    # Configure structlog processors
    processors = [
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        add_correlation_id,
        add_component_info,
        structlog.processors.TimeStamper(fmt="iso"),
    ]
    
    if settings.sensitive_data_scrubbing:
        scrubber = SensitiveDataScrubber(settings.max_message_length)
        processors.append(lambda _, __, event_dict: scrubber.scrub_record(event_dict))
    
    if settings.format == "json":
        processors.append(structlog.processors.JSONRenderer())
    else:
        processors.append(structlog.dev.ConsoleRenderer())
    
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=structlog.stdlib.LoggerFactory(),
        cache_logger_on_first_use=True,
    )
    
    # Configure standard library logging
    handler = logging.StreamHandler(sys.stdout)
    handler.setLevel(getattr(logging, settings.level))
    
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.level))
    root_logger.addHandler(handler)

# Enhanced logger that includes performance tracking
class EnhancedLogger:
    """Enhanced logger with performance tracking capabilities."""
    
    def __init__(self, name: str):
        self._logger = structlog.get_logger(name)
    
    def performance_context(self, operation_name: str) -> PerformanceContext:
        """Create a performance tracking context manager."""
        return PerformanceContext(operation_name, self._logger)
    
    def __getattr__(self, name: str):
        """Delegate all other methods to the underlying logger."""
        return getattr(self._logger, name)

def get_logger(name: str) -> EnhancedLogger:
    """Get an enhanced logger instance."""
    return EnhancedLogger(name)
```

### 2. FastAPI Logging Middleware

**File:** `backend/app/middleware/logging.py`

```python
import time
import uuid
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from app.core.logging import request_id_var, conversation_id_var, get_logger

logger = get_logger(__name__)

class LoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for automatic request logging with correlation IDs."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Generate request ID and extract conversation ID
        request_id = str(uuid.uuid4())
        conversation_id = request.headers.get("X-Conversation-ID", "")
        
        # Set context variables for correlation
        request_id_token = request_id_var.set(request_id)
        conversation_id_token = conversation_id_var.set(conversation_id)
        
        start_time = time.time()
        
        # Log request initiation
        logger.info(
            "Request started",
            extra={
                "method": request.method,
                "path": request.url.path,
                "user_agent": request.headers.get("User-Agent", ""),
                "client_ip": self._get_client_ip(request)
            }
        )
        
        try:
            # Process request
            response = await call_next(request)
            
            # Calculate duration
            duration_ms = int((time.time() - start_time) * 1000)
            
            # Log successful completion
            logger.info(
                "Request completed",
                extra={
                    "status_code": response.status_code,
                    "duration_ms": duration_ms,
                    "response_size": response.headers.get("content-length", 0),
                    "success": 200 <= response.status_code < 400
                }
            )
            
            # Add correlation ID to response headers for client tracking
            response.headers["X-Request-ID"] = request_id
            if conversation_id:
                response.headers["X-Conversation-ID"] = conversation_id
            
            return response
            
        except Exception as e:
            # Calculate duration even for errors
            duration_ms = int((time.time() - start_time) * 1000)
            
            # Log request failure
            logger.error(
                "Request failed",
                extra={
                    "error": str(e),
                    "error_type": type(e).__name__,
                    "duration_ms": duration_ms
                },
                exc_info=True
            )
            raise
            
        finally:
            # Clean up context variables
            request_id_var.reset(request_id_token)
            conversation_id_var.reset(conversation_id_token)
    
    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP handling proxy headers."""
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        return request.client.host if request.client else "unknown"
```

### 3. Service Integration Examples

**Foundation Model Service Integration:**

```python
# backend/app/services/foundation_model.py - Updated logging
from app.core.logging import get_logger

logger = get_logger(__name__)

class FoundationModelService:
    async def generate_response_with_tools(self, messages, conversation_id):
        logger.info(
            "Foundation model request initiated", 
            extra={
                "provider": "azure_openai",
                "model": self.deployment_name,
                "message_count": len(messages),
                "has_mcp": self.mcp_service is not None
            }
        )
        
        try:
            with logger.performance_context("foundation_model_generation"):
                # Existing generation logic...
                async for message in self._generate_with_tools(messages):
                    yield message
                    
            logger.info(
                "Foundation model request completed",
                extra={
                    "success": True,
                    "stream_type": "multi_type"
                }
            )
            
        except Exception as e:
            logger.error(
                "Foundation model request failed",
                extra={
                    "error": str(e),
                    "error_type": type(e).__name__,
                    "model": self.deployment_name
                },
                exc_info=True
            )
            raise
```

**MCP Service Integration:**

```python
# backend/app/services/mcp_service.py - Updated logging
from app.core.logging import get_logger

logger = get_logger(__name__)

class MCPService:
    async def call_tool_with_attribution(self, tool_name: str, arguments: Dict[str, Any]):
        logger.info(
            "MCP tool execution started",
            extra={
                "tool": tool_name,
                "server": "genie",
                "args_provided": list(arguments.keys()) if arguments else []
            }
        )
        
        try:
            with logger.performance_context("mcp_tool_execution"):
                # Existing tool execution logic...
                result = await self.client.call_tool(settings.genie_mcp_url, tool_name, arguments)
                
                # Generate attribution message...
                yield StreamMessage(StreamType.SOURCES, {...})
                
            logger.info(
                "MCP tool execution completed", 
                extra={
                    "tool": tool_name,
                    "success": True,
                    "response_size": len(str(result)) if result else 0
                }
            )
            
        except Exception as e:
            logger.error(
                "MCP tool execution failed",
                extra={
                    "tool": tool_name,
                    "error": str(e),
                    "error_type": type(e).__name__
                },
                exc_info=True
            )
            raise
```

### 4. Configuration Integration

**Update:** `backend/app/core/config.py`

```python
from app.core.logging import LoggingSettings

class Settings(BaseSettings):
    # ... existing settings ...
    
    # Logging Configuration
    logging: LoggingSettings = LoggingSettings()
    
    class Config:
        env_file = ".env"
        env_nested_delimiter = "__"  # Enables LOG__LEVEL syntax
```

**Update:** `backend/app/main.py`

```python
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.api.api_v1.api import api_router
from app.core.config import settings
from app.core.logging import setup_logging
from app.middleware.logging import LoggingMiddleware

# Configure logging first
setup_logging(settings.logging)

app = FastAPI(
    title=settings.app_name,
    version=settings.version,
    openapi_url=f"{settings.api_v1_str}/openapi.json"
)

# Add logging middleware (first in chain for complete request tracking)
app.add_middleware(LoggingMiddleware)

# Existing CORS middleware...
if settings.backend_cors_origins:
    app.add_middleware(CORSMiddleware, ...)

# Include API router
app.include_router(api_router, prefix=settings.api_v1_str)
```

## Environment Variables

```env
# Logging Configuration
LOG_LEVEL=INFO                    # DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_FORMAT=json                   # json, text (text for dev, json for prod)
LOG_CORRELATION_ENABLED=true     # Enable request correlation IDs
LOG_PERFORMANCE_TRACKING=true    # Enable performance timing
LOG_SCRUB_SENSITIVE=true         # Enable sensitive data scrubbing  
LOG_CONSOLE_OUTPUT=true          # Output to console (vs file)
LOG_MAX_MESSAGE_LENGTH=200       # Max length before truncating user messages

# Development vs Production Examples:
# Development (.env.dev):
LOG_LEVEL=DEBUG
LOG_FORMAT=text
LOG_SCRUB_SENSITIVE=false

# Production (.env.prod):
LOG_LEVEL=INFO  
LOG_FORMAT=json
LOG_SCRUB_SENSITIVE=true
```

## Complete Implementation Plan

### Prerequisites & Dependencies

**Required Python packages to add to `requirements.txt`:**
```txt
structlog>=24.1.0
```

**Install command:**
```bash
pip install structlog>=24.1.0
```

### ✅ Phase 1: Core Infrastructure Implementation (COMPLETED)

#### ✅ Step 1.1: Create Core Logging Module
**File:** `backend/app/core/logging.py` (✅ COMPLETED - full implementation)

**Key Components to Implement:**
- `LoggingSettings` Pydantic class with all environment variables
- `SensitiveDataScrubber` class with pattern matching and field scrubbing
- `PerformanceContext` context manager class
- `EnhancedLogger` wrapper class with performance tracking
- Structured logging processors (correlation, component info, scrubbing)
- `setup_logging()` function to configure structlog + standard library
- Context variables: `request_id_var`, `conversation_id_var`

#### ✅ Step 1.2: Create Logging Middleware
**File:** `backend/app/middleware/__init__.py` (✅ COMPLETED)
**File:** `backend/app/middleware/correlation.py` (✅ COMPLETED - FastAPI correlation middleware)

**Key Components to Implement:**
- `LoggingMiddleware` class extending `BaseHTTPMiddleware`
- Request ID generation with UUID
- Conversation ID extraction from `X-Conversation-ID` header
- Context variable setting/cleanup with tokens
- Request timing and response logging
- Client IP extraction with proxy header support
- Error handling with structured logging

#### ✅ Step 1.3: Update Configuration System
**File:** `backend/app/core/config.py` (✅ COMPLETED - logging settings integrated)

**Changes to make:**
```python
# Add this import
from app.core.logging import LoggingSettings

# Modify Settings class
class Settings(BaseSettings):
    # ... existing settings ...
    
    # ADD THIS: Logging Configuration
    logging: LoggingSettings = LoggingSettings()
    
    class Config:
        env_file = ".env"
        env_nested_delimiter = "__"  # CHANGE: Add this line for nested env vars
```

#### ✅ Step 1.4: Update Main Application
**File:** `backend/app/main.py` (✅ COMPLETED - logging initialization and middleware added)

**Changes to make:**
```python
# ADD these imports
from app.core.logging import setup_logging
from app.middleware.logging import LoggingMiddleware

# MODIFY: Add logging setup before FastAPI app creation
setup_logging(settings.logging)

app = FastAPI(
    title=settings.app_name,
    version=settings.version,
    openapi_url=f"{settings.api_v1_str}/openapi.json"
)

# ADD: Logging middleware (FIRST in middleware chain)
app.add_middleware(LoggingMiddleware)

# EXISTING: CORS middleware comes after logging
if settings.backend_cors_origins:
    app.add_middleware(CORSMiddleware, ...)
```

### ✅ Phase 2: Service Integration Implementation (COMPLETED)

#### ✅ Step 2.1: Update Foundation Model Service
**File:** `backend/app/services/foundation_model.py` (✅ COMPLETED - structured logging integrated)

**Changes to make:**
```python
# CHANGE import
from app.core.logging import get_logger  # REPLACE: import logging

logger = get_logger(__name__)  # REPLACE: logging.getLogger(__name__)

# UPDATE: generate_response_with_tools method
async def generate_response_with_tools(self, messages, conversation_id):
    logger.info(
        "Foundation model request initiated", 
        extra={
            "provider": "azure_openai",
            "model": self.deployment_name,
            "message_count": len(messages),
            "has_mcp": self.mcp_service is not None
        }
    )
    
    try:
        with logger.performance_context("foundation_model_generation"):
            # EXISTING generation logic...
            async for message in self._existing_generation_logic(messages, conversation_id):
                yield message
                
        logger.info(
            "Foundation model request completed",
            extra={"success": True, "stream_type": "multi_type"}
        )
        
    except Exception as e:
        logger.error(
            "Foundation model request failed",
            extra={
                "error": str(e),
                "error_type": type(e).__name__,
                "model": self.deployment_name
            },
            exc_info=True
        )
        raise

# REMOVE: All existing logger.info(f"...") calls - replace with structured versions
# REMOVE: All existing logger.error(f"...") calls - replace with structured versions
```

#### ✅ Step 2.2: Update MCP Service
**File:** `backend/app/services/mcp_service.py` (✅ COMPLETED - structured logging and performance tracking)

**Changes to make:**
```python
# CHANGE import  
from app.core.logging import get_logger  # REPLACE: import logging

logger = get_logger(__name__)  # REPLACE: logging.getLogger(__name__)

# UPDATE: Major methods with structured logging
async def initialize(self):
    if self._initialized:
        return
        
    try:
        if not settings.genie_mcp_url:
            logger.warning(
                "MCP functionality disabled",
                extra={"reason": "no_mcp_url_configured"}
            )
            return
            
        logger.info("MCP service initialization started")
        
        with logger.performance_context("mcp_initialization"):
            # EXISTING initialization logic...
            
        if self.tools_available:
            logger.info(
                "MCP service initialized successfully",
                extra={
                    "tools_discovered": len(available_tools),
                    "server": "genie"
                }
            )
        else:
            logger.warning("MCP service initialized but no tools discovered")
            
    except Exception as e:
        logger.error(
            "MCP initialization failed",
            extra={"error": str(e), "error_type": type(e).__name__},
            exc_info=True
        )

# UPDATE: call_tool_with_attribution method
async def call_tool_with_attribution(self, tool_name: str, arguments: Dict[str, Any]):
    logger.info(
        "MCP tool execution started",
        extra={
            "tool": tool_name,
            "server": "genie",
            "args_provided": list(arguments.keys()) if arguments else []
        }
    )
    
    try:
        with logger.performance_context("mcp_tool_execution"):
            # EXISTING tool execution logic...
            
        logger.info(
            "MCP tool execution completed", 
            extra={
                "tool": tool_name,
                "success": True,
                "response_size": len(str(result)) if result else 0
            }
        )
        
    except Exception as e:
        logger.error(
            "MCP tool execution failed",
            extra={
                "tool": tool_name,
                "error": str(e),
                "error_type": type(e).__name__
            },
            exc_info=True
        )

# REPLACE: All f-string logging calls with structured equivalents
```

#### ✅ Step 2.3: Update MCP Client Service  
**File:** `backend/app/services/mcp_client.py` (✅ COMPLETED - basic structured logging integration)

**Changes to make:**
```python
# CHANGE import
from app.core.logging import get_logger  # REPLACE: import logging

logger = get_logger(__name__)  # REPLACE: logging.getLogger(__name__)

# UPDATE: All methods with structured logging following same pattern as MCP service
# REPLACE: All f-string logging with structured extra fields
# ADD: Performance context managers for expensive operations (discover_tools, call_tool)
```

### ✅ Phase 3: Production Features & Testing (COMPLETED)

#### ✅ Step 3.1: Environment Configuration (✅ COMPLETED)
**Create development environment file:** `.env.dev`
```env
# Development Logging Configuration
LOG_LEVEL=DEBUG
LOG_FORMAT=text
LOG_CORRELATION_ENABLED=true
LOG_PERFORMANCE_TRACKING=true
LOG_SCRUB_SENSITIVE=false
LOG_MAX_MESSAGE_LENGTH=1000
LOG_CONSOLE_OUTPUT=true
```

**Update production environment:** `.env` or deployment environment
```env
# Production Logging Configuration  
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_CORRELATION_ENABLED=true
LOG_PERFORMANCE_TRACKING=true
LOG_SCRUB_SENSITIVE=true
LOG_MAX_MESSAGE_LENGTH=200
LOG_CONSOLE_OUTPUT=true
```

#### ✅ Step 3.2: Testing Implementation (✅ COMPLETED)

**Test structured logging output:**
```bash
# Test development format
LOG_FORMAT=text uvicorn app.main:app --reload

# Test production format
LOG_FORMAT=json uvicorn app.main:app --reload
```

**Test correlation ID propagation:**
```bash
# Send request with conversation ID
curl -N -X POST http://localhost:8000/api/v1/chat/stream \
  -H "Content-Type: application/json" \
  -H "X-Conversation-ID: test-conversation-123" \
  -d '{"message": "What are emergency incidents?", "conversation_id": "test-conversation-123"}'

# Verify all log entries include same request_id and conversation_id
```

**Test sensitive data scrubbing:**
```bash
# Test with scrubbing enabled
LOG_SCRUB_SENSITIVE=true uvicorn app.main:app --reload
# Make requests and verify API keys in logs show [SCRUBBED]

# Test with scrubbing disabled  
LOG_SCRUB_SENSITIVE=false uvicorn app.main:app --reload
# Make requests and verify full data visible for debugging
```

**Test performance tracking:**
```bash
# Make requests and verify log entries include:
# - duration_ms fields for operations
# - operation names in performance context logs
# - success/failure indicators
```

#### ✅ Step 3.3: Validation Checklist (✅ COMPLETED)

Verify these outputs in logs:

**✅ JSON Format (Production):**
```json
{"timestamp": "2024-01-15T10:30:00Z", "level": "INFO", "logger": "app.services.foundation_model", "message": "Foundation model request initiated", "request_id": "req_abc123", "conversation_id": "conv_xyz", "component": "foundation_model", "provider": "azure_openai"}
```

**✅ Text Format (Development):**
```
2024-01-15 10:30:00 [INFO    ] Foundation model request initiated [app.services.foundation_model] request_id=req_abc123 conversation_id=conv_xyz provider=azure_openai
```

**✅ Correlation ID Propagation:**
- Same `request_id` appears in middleware, foundation_model, mcp_service logs
- `conversation_id` from client header appears in all service logs
- Response headers include `X-Request-ID` and `X-Conversation-ID`

**✅ Performance Tracking:**
```json
{"message": "foundation_model_generation completed", "operation": "foundation_model_generation", "duration_ms": 1250, "success": true}
```

**✅ Sensitive Data Scrubbing:**
- API keys show as `[SCRUBBED]` in logs when scrubbing enabled
- Long user messages show as `[TRUNCATED - 450 chars]` when over limit
- Full data visible when scrubbing disabled for debugging

**✅ Error Handling:**
```json
{"level": "ERROR", "message": "Foundation model request failed", "error": "Connection timeout", "error_type": "TimeoutError", "component": "foundation_model", "exc_info": "Full stack trace here"}
```

### ✅ Phase 4: Documentation Updates (COMPLETED)

#### ✅ Step 4.1: Update Main README
**File:** `README.md` (✅ COMPLETED - README_additions section ready for integration)

#### ✅ Step 4.2: Update CLAUDE.md  
**File:** `CLAUDE.md` (✅ COMPLETED - claude_additions section ready for integration)

### Deployment Considerations

**Azure Container Apps deployment:**
```yaml
# Container environment variables
environment:
  - name: LOG_LEVEL
    value: INFO
  - name: LOG_FORMAT
    value: json
  - name: LOG_SCRUB_SENSITIVE
    value: true
```

**Local development:**
```bash
# Use development settings
cp .env.dev .env
uvicorn app.main:app --reload
```

### Troubleshooting Common Issues

**Issue: Logs not appearing**
- Check `LOG_LEVEL` environment variable
- Verify `setup_logging(settings.logging)` called before FastAPI app creation
- Check console output with `LOG_CONSOLE_OUTPUT=true`

**Issue: Correlation IDs not propagating**
- Verify `LoggingMiddleware` is first in middleware chain
- Check context variables are properly set/reset in middleware
- Verify services use `get_logger(__name__)` not `logging.getLogger()`

**Issue: Sensitive data not scrubbed**
- Check `LOG_SCRUB_SENSITIVE=true` environment variable
- Verify scrubber is added to structlog processors
- Test with known sensitive patterns (API keys, Bearer tokens)

**Issue: Performance impact**
- Monitor response time impact (should be <5ms overhead)
- Consider reducing `LOG_LEVEL` to `WARNING` in high-traffic production
- Verify async operations aren't being blocked by logging calls

This complete implementation plan provides everything needed to implement production-ready structured logging from scratch, including all file modifications, testing procedures, and deployment considerations.

## Validation & Testing

### Manual Testing
```bash
# Test structured logging output
uvicorn app.main:app --reload

# Make requests and verify log format
curl -H "X-Conversation-ID: test-123" http://localhost:8000/api/v1/chat/stream

# Expected JSON output:
{"timestamp": "2024-01-15T10:30:00Z", "level": "INFO", "message": "Request started", "request_id": "abc-123", ...}
```

### Key Validation Points
- ✅ All logs output in consistent JSON format
- ✅ Request correlation IDs appear in all service logs within a request
- ✅ Sensitive data (API keys) are scrubbed from logs
- ✅ Performance timing appears for major operations
- ✅ Exception logging includes full stack traces
- ✅ Log levels work correctly (INFO in prod, DEBUG in dev)

## Production Benefits

### Observability
- **Request tracking:** Follow requests across all services via correlation ID
- **Performance monitoring:** Identify slow operations with detailed timing
- **Error debugging:** Rich context for production issue resolution

### Security  
- **Data protection:** Automatic scrubbing of sensitive information
- **Audit trail:** Complete record of system operations
- **Compliance:** Structured logs support compliance requirements

### Operations
- **Log aggregation:** JSON format works with ELK, Splunk, CloudWatch
- **Alerting:** Structured fields enable precise alert conditions
- **Metrics:** Performance data enables service monitoring dashboards

This implementation transforms our basic logging into a production-ready observability system while maintaining backward compatibility and requiring minimal code changes to existing services.

---

## README_additions

*This section contains documentation that should be added to the main README.md file after logging implementation is complete.*

### Logging & Observability

The backend implements production-ready structured logging with correlation IDs, performance tracking, and security-aware practices for comprehensive observability.

#### Features

**🔗 Request Correlation**
- Automatic correlation IDs for tracking requests across all services
- Conversation ID support for multi-turn chat session tracking  
- Response headers include correlation IDs for client-side tracking

**📊 Performance Monitoring** 
- Automatic timing for all HTTP requests
- Service-level performance tracking with context managers
- Operation-specific duration logging (Foundation Model calls, MCP tool execution)

**🔒 Security-Aware Logging**
- Automatic scrubbing of API keys, tokens, and credentials
- User message truncation for privacy (configurable length)
- Environment-controlled sensitivity levels (dev vs production)

**📋 Structured Format**
- JSON output for production log aggregation (ELK, Splunk, CloudWatch)
- Human-readable text format for local development  
- Consistent field naming across all services

#### Component Loggers

The application uses component-specific loggers following the pattern:

```python
from app.core.logging import get_logger

logger = get_logger(__name__)  # Creates enhanced logger with performance tracking
```

**Available Loggers:**
- `app.services.foundation_model` - Foundation Model service operations
- `app.services.mcp_service` - MCP tool execution and attribution
- `app.services.mcp_client` - MCP server communication
- `app.api` - API endpoint operations  
- `app.middleware.logging` - Request/response tracking

#### Performance Tracking Usage

Use performance context managers for operation timing:

```python
# Automatic timing with structured logging
with logger.performance_context("foundation_model_generation"):
    result = await self.generate_response(messages)

# Outputs: {"operation": "foundation_model_generation", "duration_ms": 1250, "success": true}
```

#### Configuration

Control logging behavior via environment variables:

```env
# Logging Level & Format
LOG_LEVEL=INFO                    # DEBUG, INFO, WARNING, ERROR, CRITICAL  
LOG_FORMAT=json                   # json (production), text (development)

# Correlation & Performance
LOG_CORRELATION_ENABLED=true     # Enable request correlation IDs
LOG_PERFORMANCE_TRACKING=true    # Enable operation timing

# Security & Privacy  
LOG_SCRUB_SENSITIVE=true         # Scrub API keys and credentials
LOG_MAX_MESSAGE_LENGTH=200       # Truncate user messages after N chars
```

#### Development vs Production

**Development Configuration (.env.dev):**
```env
LOG_LEVEL=DEBUG
LOG_FORMAT=text                   # Human-readable output
LOG_SCRUB_SENSITIVE=false        # See full data for debugging
LOG_MAX_MESSAGE_LENGTH=1000      # Allow longer messages
```

**Production Configuration (.env.prod):**
```env
LOG_LEVEL=INFO
LOG_FORMAT=json                  # Structured for log aggregation
LOG_SCRUB_SENSITIVE=true        # Always scrub sensitive data
LOG_MAX_MESSAGE_LENGTH=200      # Truncate for privacy
```

#### Correlation ID Usage

**Client Integration:**
Send conversation IDs in requests to track multi-turn conversations:

```javascript
// Frontend: Send conversation ID header
fetch('/api/v1/chat/stream', {
  headers: {
    'X-Conversation-ID': conversationId,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({message, conversation_id: conversationId})
});
```

**Response Headers:**
The API returns correlation IDs in response headers for client tracking:

```http
HTTP/1.1 200 OK
X-Request-ID: req_abc123def456      # Unique per request
X-Conversation-ID: conv_xyz789      # Echoed from client
```

**Log Correlation:**
All log entries within a request include correlation fields:

```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "request_id": "req_abc123def456",
  "conversation_id": "conv_xyz789", 
  "component": "foundation_model",
  "message": "Tool execution completed"
}
```

#### Azure Deployment

**Azure Container Apps / App Service:**
```yaml
# Azure deployment - logs automatically flow to Azure Monitor
environment:
  - name: LOG_LEVEL
    value: INFO
  - name: LOG_FORMAT  
    value: json
  - name: LOG_SCRUB_SENSITIVE
    value: true
```

**Azure Monitor Integration:**
- JSON logs automatically parsed by Azure Monitor
- Correlation IDs enable cross-service tracing
- Performance metrics available in Application Insights
- Set up alerts based on error rates or performance thresholds

**Log Analytics Queries:**
```kusto
# Find all logs for a specific request
ContainerAppConsoleLogs_CL  
| where request_id_s == "req_abc123def456"
| order by TimeGenerated asc

# Performance monitoring  
ContainerAppConsoleLogs_CL
| where component_s == "foundation_model"
| where duration_ms_d > 2000  
| summarize avg(duration_ms_d) by bin(TimeGenerated, 5m)
```

#### Local Development

**Viewing Logs:**
```bash
# Development server with text format
LOG_FORMAT=text uvicorn app.main:app --reload

# JSON format for testing log parsing
LOG_FORMAT=json uvicorn app.main:app --reload

# Debug level for detailed logging
LOG_LEVEL=DEBUG uvicorn app.main:app --reload
```

#### How to Confirm Logging is Working

**Step 1: Basic Logging Test**
```bash
# Start the server with JSON logging
LOG_FORMAT=json uvicorn app.main:app --reload

# You should see structured JSON logs in the console:
{"event": "Application startup complete", "level": "info", "timestamp": "2025-01-15T10:30:00.123456Z"}
```

**Step 2: Test Health Endpoint with Correlation**
```bash
# Make a request to the health endpoint
curl -v http://localhost:8000/health

# Check the logs - you should see:
# 1. Request started log with request_id
# 2. Health check accessed log 
# 3. Request completed log with duration_ms
# 4. All logs should have the same request_id

# Example output:
{"event": "Request started", "request_id": "abc-123", "method": "GET", "path": "/health", "level": "info"}
{"event": "Health check accessed", "request_id": "abc-123", "level": "info"}  
{"event": "Request completed", "request_id": "abc-123", "status_code": 200, "duration_seconds": 0.005, "level": "info"}
```

**Step 3: Test Correlation ID Propagation**
```bash
# Send request with custom conversation ID
curl -H "X-Conversation-ID: test-conversation-123" http://localhost:8000/health

# Verify logs show correlation_id field:
{"event": "Request started", "correlation_id": "test-conversation-123", "request_id": "def-456", "level": "info"}
```

**Step 4: Test Sensitive Data Scrubbing**
```python
# Create a test script to verify scrubbing works:
import sys
sys.path.append('.')
from app.core.logging import setup_logging, get_logger, set_correlation_id
from app.core.config import settings

setup_logging(settings.logging)
logger = get_logger('test')

# Test with sensitive data - should be scrubbed in output
logger.info("API call made", api_key="sk-1234567890abcdef", token="bearer-token-123")

# Expected output (scrubbed):
{"api_key": "[REDACTED]", "token": "[REDACTED]", "event": "API call made", "level": "info"}
```

**Step 5: Test Text Format (Development Mode)**
```bash
# Switch to human-readable format for development
LOG_FORMAT=text uvicorn app.main:app --reload

# Make a request - should see readable format:
# 2025-01-15 10:30:00 [INFO    ] Request started [correlation] request_id=abc-123 method=GET path=/health
```

**Verification Checklist:**
- ✅ Logs appear in console when server starts
- ✅ All logs have timestamp, level, and event fields
- ✅ Each HTTP request generates request_id that appears across all related logs
- ✅ Custom X-Conversation-ID header appears as correlation_id in logs
- ✅ Response headers include X-Correlation-ID 
- ✅ JSON format is valid and parseable
- ✅ Text format is human-readable for development
- ✅ Sensitive data is automatically scrubbed when LOG_SCRUB_SENSITIVE=true

**Log Output Examples:**

Text format (development):
```
2024-01-15 10:30:00 [INFO    ] Request started [app.middleware.logging] request_id=req_abc123 method=POST path=/api/v1/chat/stream
2024-01-15 10:30:01 [INFO    ] Foundation model request initiated [app.services.foundation_model] request_id=req_abc123 provider=azure_openai
2024-01-15 10:30:02 [INFO    ] Foundation model request completed [app.services.foundation_model] request_id=req_abc123 duration_ms=1250
```

JSON format (production):
```json
{"timestamp": "2024-01-15T10:30:00Z", "level": "INFO", "message": "Request started", "request_id": "req_abc123", "component": "api", "method": "POST"}
{"timestamp": "2024-01-15T10:30:01Z", "level": "INFO", "message": "Foundation model request initiated", "request_id": "req_abc123", "component": "foundation_model", "provider": "azure_openai"}
```

**Debugging Tips:**
- Use `X-Conversation-ID` header to trace multi-turn conversations
- Search logs by `request_id` to see complete request flow
- Enable DEBUG level to see detailed service interactions
- Disable scrubbing temporarily (`LOG_SCRUB_SENSITIVE=false`) to debug data issues

---

## claude_additions  

*This section contains notes that should be added to CLAUDE.md to guide future development with consistent logging patterns.*

### Logging Guidelines for Backend Development

The codebase implements structured logging with correlation IDs and performance tracking. When developing backend services, follow these established patterns:

#### Logger Initialization

**ALWAYS use the enhanced logger pattern:**
```python
from app.core.logging import get_logger

logger = get_logger(__name__)  # NOT logging.getLogger(__name__)
```

**Why:** The enhanced logger includes performance tracking capabilities and integrates with the structured logging system.

#### Structured Logging Pattern

**Use structured logging with extra fields instead of f-strings:**

✅ **Correct:**
```python
logger.info(
    "Foundation model request initiated",
    extra={
        "provider": self.provider,
        "model": self.model_name,
        "message_count": len(messages),
        "has_tools": bool(available_tools)
    }
)
```

❌ **Avoid:**
```python
logger.info(f"Foundation model request initiated with {len(messages)} messages using {self.provider}")
```

**Why:** Structured fields enable precise log querying, alerting, and dashboard creation in production.

#### Performance Tracking

**Use performance context managers for significant operations:**
```python
# Wrap expensive operations with performance tracking
with logger.performance_context("foundation_model_generation"):
    result = await self._expensive_operation()

# The context manager automatically logs duration and success/failure
```

**Apply to these operation types:**
- Foundation Model API calls
- MCP tool execution  
- Database operations (when implemented)
- External API calls
- File I/O operations
- Complex computation operations >100ms

#### Error Logging Standards

**Always include structured error information:**
```python
try:
    result = await self.risky_operation()
except Exception as e:
    logger.error(
        "Operation failed with detailed context",
        extra={
            "error": str(e),
            "error_type": type(e).__name__,
            "operation": "specific_operation_name",
            "input_params": {"param1": value1}  # Be careful with sensitive data
        },
        exc_info=True  # Include full stack trace
    )
    raise  # Re-raise unless handling gracefully
```

#### Service Integration Pattern

**When creating new services, integrate logging at these key points:**

1. **Service initialization:**
```python
def __init__(self):
    logger.info(
        "Service initialized", 
        extra={"service": self.__class__.__name__, "config": "basic_config_info"}
    )
```

2. **Major operation start/completion:**
```python
async def major_operation(self, params):
    logger.info("Operation started", extra={"operation": "major_operation", "param_summary": "..."})
    
    try:
        with logger.performance_context("major_operation"):
            result = await self._do_work()
            
        logger.info("Operation completed", extra={"success": True, "result_summary": "..."})
        return result
    except Exception as e:
        logger.error("Operation failed", extra={...}, exc_info=True)
        raise
```

3. **External service calls:**
```python
async def call_external_service(self):
    logger.info("External service call initiated", extra={"service": "service_name", "endpoint": "endpoint"})
    
    with logger.performance_context("external_service_call"):
        response = await self.client.call()
        
    logger.info("External service call completed", extra={"status_code": response.status, "response_size": len(response.data)})
```

#### Sensitive Data Handling

**Be aware that sensitive data scrubbing is automatic but not perfect:**

- API keys, tokens, secrets are automatically scrubbed
- Long user messages are truncated to prevent log spam
- **Manual consideration required for:** custom credentials, PII in structured fields, business-sensitive data

**When logging user input or external data:**
```python
# Log data size/type instead of content for sensitive fields
logger.info(
    "User message processed",
    extra={
        "message_length": len(user_message),
        "message_type": "user_input",
        "contains_attachments": bool(attachments)
        # Don't include: "message_content": user_message
    }
)
```

#### Correlation ID Usage

**Correlation IDs are automatically available - no manual propagation needed:**

```python
# Correlation IDs automatically included in all log entries within a request
# No need to manually pass request_id or conversation_id between services

async def my_service_method(self):
    logger.info("Processing request")  # Automatically includes request_id and conversation_id
    
    result = await self.another_service.call_method()  # This call's logs also have correlation IDs
    return result
```

#### Development vs Production Considerations

**Write logs that work in both environments:**

- Use structured fields that are meaningful in JSON (production) and readable in text (development)
- Avoid excessive DEBUG level logging that could impact performance
- Include enough context for production debugging without log spam

**Example of environment-friendly logging:**
```python
# Good: Structured and informative in both text and JSON formats
logger.info(
    "Configuration loaded successfully",
    extra={
        "config_source": ".env",
        "providers_enabled": ["azure_openai", "mcp"], 
        "debug_mode": settings.debug
    }
)

# Avoid: Too verbose for production, not structured for analysis
logger.debug(f"Loaded config: {settings.dict()}")  # Don't log entire config objects
```

#### New Service Checklist

When creating new backend services, ensure:

- [ ] Uses `from app.core.logging import get_logger`
- [ ] Includes structured logging at initialization, major operations, and errors
- [ ] Uses performance context managers for expensive operations  
- [ ] Follows error logging pattern with structured fields and `exc_info=True`
- [ ] Considers sensitive data implications in logged fields
- [ ] Tests logging output in both development (text) and production (JSON) formats

#### Integration with FastAPI

**FastAPI endpoints automatically have request correlation - focus on business logic logging:**

```python
@router.post("/my-endpoint")
async def my_endpoint(request: MyRequest):
    # Request/response logging handled by middleware - don't duplicate
    
    # Focus on business logic logging:
    logger.info(
        "Business operation started",
        extra={
            "operation": "specific_business_function",
            "input_summary": {"field_count": len(request.fields)}
        }
    )
    
    # Use services with their own logging
    result = await my_service.process(request)
    
    return result  # Response logging handled by middleware
```

These patterns ensure consistent, production-ready logging across all backend development while enabling powerful observability and debugging capabilities.
"""
Test timestamp-based queries to ensure agent uses execute_cypher with ORDER BY.

TDD approach:
1. Write test that captures desired behavior
2. Run test to see it fail
3. Update prompt/workflow to fix
4. Run test to see it pass
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from langchain_core.messages import HumanMessage, AIMessage


@pytest.mark.asyncio
async def test_recent_missions_query_uses_execute_cypher_with_order_by():
    """
    Test that queries for 'most recent' missions use execute_cypher with ORDER BY.
    
    Expected behavior:
    - Agent receives "Provide a list of the 5 most recently updated missions"
    - Agent calls get_schema to find timestamp fields
    - <PERSON> calls execute_cypher with ORDER BY updated_at DESC LIMIT 5
    - Agent returns missions ordered by updated_at timestamp
    """
    from app.agent.langgraph.workflows import get_graph
    
    # Create test input
    test_query = "Provide a list of the 5 most recently updated missions."
    
    # Mock the MCP tool calls to track what's called
    execute_cypher_called = False
    execute_cypher_query = None
    
    async def mock_execute_cypher(query: str):
        nonlocal execute_cypher_called, execute_cypher_query
        execute_cypher_called = True
        execute_cypher_query = query
        
        # Return mock data that matches expected structure
        return {
            "success": True,
            "results": [
                {"m.id": "642768", "m.title": "One (1) 275KW Generator for FDC-EOC and FHP-TRCC", "updated_at_str": "2025-07-26T09:48:49.000000+00:00[Etc/UTC]"},
                {"m.id": "641175", "m.title": "Generator Technician needed for tower trailer", "updated_at_str": "2025-07-25T08:10:15.000000+00:00[Etc/UTC]"},
                {"m.id": "642792", "m.title": "IT Requests PO for Auburndale Network Components", "updated_at_str": "2025-07-23T13:16:54.000000+00:00[Etc/UTC]"},
                {"m.id": "637540", "m.title": "IT Requests PO for Data Domain Deployment", "updated_at_str": "2025-07-23T12:00:40.000000+00:00[Etc/UTC]"},
                {"m.id": "642770", "m.title": "Finance Payables", "updated_at_str": "2025-07-23T08:20:27.000000+00:00[Etc/UTC]"}
            ]
        }
    
    # Get workflow graph
    graph = await get_graph()
    
    # Run workflow
    result = await graph.ainvoke({
        "messages": [HumanMessage(content=test_query)]
    })
    
    # Assertions
    assert execute_cypher_called, "execute_cypher tool was not called"
    assert execute_cypher_query is not None, "No Cypher query was captured"
    
    # Verify query uses ORDER BY with timestamp field
    query_lower = execute_cypher_query.lower()
    assert "order by" in query_lower, f"Query missing ORDER BY: {execute_cypher_query}"
    assert "updated_at" in query_lower, f"Query doesn't use updated_at field: {execute_cypher_query}"
    assert "desc" in query_lower, f"Query doesn't sort descending: {execute_cypher_query}"
    assert "limit 5" in query_lower, f"Query doesn't limit to 5: {execute_cypher_query}"
    
    # Verify response mentions correct missions
    final_message = result["messages"][-1].content
    assert "642768" in final_message or "275KW Generator" in final_message, \
        f"Response doesn't mention most recent mission: {final_message}"


@pytest.mark.asyncio
async def test_timestamp_query_pattern_recognition():
    """
    Test that agent recognizes various timestamp-based query patterns.
    
    Queries that should trigger timestamp ordering:
    - "most recent", "latest", "newest"
    - "last N hours/days"
    - "recently updated"
    """
    from app.agent.langgraph.workflows import get_graph
    
    timestamp_queries = [
        "Show me the latest missions",
        "What are the most recent incidents?",
        "List missions updated in the last 24 hours",
        "Show recently updated fuel transactions"
    ]
    
    graph = await get_graph()
    
    for query in timestamp_queries:
        # Track if execute_cypher is called
        cypher_called = False
        
        # This is a simplified test - in real implementation,
        # we'd need to mock the MCP adapter to track calls
        result = await graph.ainvoke({
            "messages": [HumanMessage(content=query)]
        })
        
        # For now, just verify the workflow completes
        assert result is not None
        assert "messages" in result
        
        # TODO: Add proper mocking to verify execute_cypher is called
        # with ORDER BY for each of these queries


@pytest.mark.asyncio  
async def test_schema_inspection_before_timestamp_query():
    """
    Test that agent calls get_schema to discover timestamp fields
    before executing timestamp-based queries.
    
    Expected flow:
    1. User asks for "most recent" 
    2. Agent calls get_schema
    3. Agent identifies updated_at/created_at fields
    4. Agent uses those fields in execute_cypher ORDER BY
    """
    from app.agent.langgraph.workflows import get_graph
    
    test_query = "Show the 3 most recently created incidents"
    
    # Track tool call order
    tool_calls = []
    
    async def track_tool_call(tool_name: str):
        tool_calls.append(tool_name)
    
    # TODO: Implement proper tool call tracking via MCP adapter mocking
    
    graph = await get_graph()
    result = await graph.ainvoke({
        "messages": [HumanMessage(content=test_query)]
    })
    
    # Verify workflow completes
    assert result is not None
    
    # TODO: Verify get_schema was called before execute_cypher
    # assert "get_schema" in tool_calls
    # assert "execute_cypher" in tool_calls
    # schema_idx = tool_calls.index("get_schema")
    # cypher_idx = tool_calls.index("execute_cypher")
    # assert schema_idx < cypher_idx, "get_schema should be called before execute_cypher"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

# GraySkyGen<PERSON><PERSON> Assistant - Local Development Orchestration
# This file is for LOCAL DEVELOPMENT ONLY
# Production deployments use individual service docker-compose files

services:
  # ===== External Services (Dev Only) =====

  # Memgraph Database
  memgraph:
    extends:
      file: ./memgraph/docker-compose.yml
      service: memgraph
    container_name: memgraph-db
    networks:
      - assistant-network

  # Memgraph Lab UI
  memgraph-lab:
    extends:
      file: ./memgraph/docker-compose.yml
      service: memgraph-lab
    container_name: memgraph-lab
    networks:
      - assistant-network
    depends_on:
      - memgraph

  # Phoenix Observability Server
  phoenix:
    extends:
      file: ./phoenix/docker-compose.yml
      service: phoenix
    container_name: phoenix-server
    networks:
      - assistant-network
    depends_on:
      - postgres

  # Phoenix PostgreSQL Database
  postgres:
    extends:
      file: ./phoenix/docker-compose.yml
      service: postgres
    container_name: phoenix-postgres
    networks:
      - assistant-network

  # ===== Application Services =====

  # GraphRAG MCP Server
  graphrag-mcp:
    build:
      context: ./graphmcp
      dockerfile: Dockerfile
    container_name: graphrag-mcp
    ports:
      - "${GRAPHMCP_PORT:-8001}:8001"
    environment:
      # MCP Configuration
      MCP_TRANSPORT: streamable-http
      HTTP_PORT: 8001
      HOST: 0.0.0.0

      # Server Configuration
      SERVER_NAME: GraphRAG-Emergency-Management
      LOG_LEVEL: INFO

      # Connect to local memgraph via service name
      MEMGRAPH_HOST: memgraph-db
      MEMGRAPH_PORT: 7687
      MEMGRAPH_USER: ""
      MEMGRAPH_PASSWORD: ""
      MEMGRAPH_DATABASE: memgraph
      MEMGRAPH_SSL: "false"

      # Performance Settings
      CACHE_TTL_SECONDS: 300
      MAX_TRAVERSAL_DEPTH: 5
      DEFAULT_LIMIT: 100
      MAX_LIMIT: 1000
      QUERY_TIMEOUT_SECONDS: 30
      CONNECTION_POOL_SIZE: 10

    env_file:
      - ./graphmcp/.env
    volumes:
      # Mount source code for development (auto-reload)
      - ./graphmcp/src:/app/src:ro
      - ./graphmcp/main.py:/app/main.py:ro
    networks:
      - assistant-network
    depends_on:
      - memgraph
    restart: unless-stopped

  # TODO - fix
  # Temporarily Disabled - due to caching issues backend should be instantiated separately
  # Caching issue - workflows.py attempts to cache tools at module load time, but graphrag-mcp may not be ready yet
  # TODO: Add health check to graphrag-mcp and wait for it to be ready before caching tools and / or other fixes
  # Until then, manually start backend after graphrag-mcp is ready using uv run uvicorn app.main:app --reload from backend/src/
  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: backend
    ports:
      - "${BACKEND_PORT:-8000}:8000"
    environment:
      # Development settings
      DEBUG: "true"
      LOG_FORMAT: text
      LOG_LEVEL: DEBUG

      # Connect to services via container names
      PHOENIX_ENABLED: "true"
      PHOENIX_ENDPOINT: http://phoenix-server:6006
      GRAPHRAG_MCP_URL: http://graphrag-mcp:8001/mcp

    env_file:
      - ./backend/src/.env
    volumes:
      - ./backend/src:/app
      - /app/.venv
      - /app/__pycache__
    networks:
      - assistant-network
    extra_hosts:
      - "host.docker.internal:host-gateway"
    depends_on:
      - memgraph
      - phoenix
      - graphrag-mcp
    restart: unless-stopped

  # Frontend
  frontend:
    build:
      context: ./front-end
      dockerfile: Dockerfile
      target: development
    container_name: frontend
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    environment:
      NODE_ENV: development
      WATCHPACK_POLLING: "true"

      # Connect to backend via host (browser-accessible)
      NEXT_PUBLIC_API_URL: http://localhost:${BACKEND_PORT:-8000}

    volumes:
      - ./front-end:/app
      - /app/node_modules
      - /app/.next
    networks:
      - assistant-network
    depends_on:
      - backend
    restart: unless-stopped

# ===== Networks =====

networks:
  assistant-network:
    name: demes-assistant-network
    driver: bridge

  # Default network referenced by extended services  
  default:
    driver: bridge
  
# ===== Volumes =====
# Volumes from extended services must be explicitly defined
volumes:
  memgraph_data:
    name: memgraph_data
    driver: local

  postgres_data:
    name: postgres_data
    driver: local

# ===== Usage =====
# Start all services:     docker-compose up -d
# View logs:              docker-compose logs -f
# Stop all services:      docker-compose down
# Clean everything:       docker-compose down -v
#
# Or use the Makefile:
#   make up      - Start all services
#   make down    - Stop all services
#   make logs    - View logs
#   make test    - Run health checks
#   make clean   - Remove everything
#
# Access Services:
#   Frontend:       http://localhost:3000
#   Backend:        http://localhost:8000
#   GraphMCP:       http://localhost:8001
#   Phoenix UI:     http://localhost:6006
#   Memgraph Lab:   http://localhost:3001
#   Memgraph Bolt:  bolt://localhost:7687

import type { Metadata } from 'next';
import './globals.css';
import './chat-layout.css';
import { Inter } from 'next/font/google';
import { cn } from '@/lib/utils';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Digital State RespondER - API Testing Interface',
  description: 'Modern API testing interface for Digital State RespondER streaming capabilities',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={cn(inter.className, 'min-h-screen bg-gray-50 font-sans antialiased relative')}>
        {children}
      </body>
    </html>
  );
}
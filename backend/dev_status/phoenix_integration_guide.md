# Arize Phoenix Integration Guide - Telemetry-Only Implementation

## 📋 Outstanding TODOs

### High Priority
- [ ] **Ensure correlation IDs match the same correlation IDs used for logging** (confirm that this is a good choice)
  - Verify that Phoenix traces use the same correlation IDs as structured logging
  - Check if correlation ID propagation works correctly across OpenTelemetry spans
  - Confirm correlation ID appears in both log entries and Phoenix trace attributes
  - Validate that correlation IDs enable cross-system debugging (logs ↔ traces)

- [ ] **Be able to track MCP response** 
  - Add custom spans for MCP tool execution (tool discovery, execution, results)
  - Capture MCP response times, success/failure rates, and tool-specific metrics
  - Include MCP server URL, tool names, and response sizes in span attributes
  - Trace end-to-end MCP request flow from Foundation Model to tool execution

### Medium Priority
- [ ] **Performance impact assessment**
  - Measure overhead of Phoenix tracing on application performance
  - Test with high-volume chat streams to ensure <3% latency impact
  - Optimize span sampling if needed for production workloads

- [ ] **Production monitoring setup**
  - Configure alerts for trace ingestion failures
  - Set up dashboards for key Phoenix metrics (response times, error rates, token usage)
  - Document production troubleshooting procedures

## Overview

This document tracks the complete implementation of Arize Phoenix observability integration for the GraySkyGenAI Assistant. The implementation follows a **telemetry-only approach** where the application sends traces to an external Phoenix instance but does not manage Phoenix startup/shutdown.

## Architecture

```mermaid
graph TB
    subgraph "Development Environment"
        Docker[Phoenix Docker Container<br/>arizephoenix/phoenix:latest<br/>Port 6006/4317]
        SQLite[(SQLite Database<br/>phoenix-dev-data volume)]
        Docker --> SQLite
    end
    
    subgraph "FastAPI Application"
        App[GraySkyGenAI Assistant<br/>Port 8000]
        Phoenix_Config[phoenix_config.py<br/>Telemetry Setup]
        Foundation[Foundation Model Service<br/>Custom Tracing]
        
        App --> Phoenix_Config
        App --> Foundation
    end
    
    subgraph "Azure Production Environment"
        ACI[Azure Container Instance<br/>Phoenix Server]
        AzureDB[(PostgreSQL<br/>Persistent Storage)]
        AppService[Azure App Service<br/>FastAPI Application]
        
        ACI --> AzureDB
        AppService --> ACI
    end
    
    subgraph "Phoenix Cloud (Optional)"
        ArizeCloud[Arize Phoenix Cloud<br/>Managed Service]
    end
    
    %% Telemetry Connections
    App -.->|OTLP Traces<br/>Port 4317| Docker
    Foundation -.->|Custom Spans| Docker
    AppService -.->|OTLP Traces<br/>HTTPS| ACI
    AppService -.->|OTLP Traces<br/>HTTPS| ArizeCloud
    
    %% UI Access
    Docker -.->|Phoenix UI<br/>Port 6006| Browser[Developer Browser]
    ACI -.->|Phoenix UI<br/>Port 6006| ProdBrowser[Production Dashboard]
    ArizeCloud -.->|Web UI<br/>HTTPS| CloudBrowser[Cloud Dashboard]
    
    classDef container fill:#e1f5fe
    classDef service fill:#f3e5f5
    classDef storage fill:#e8f5e8
    classDef external fill:#fff3e0
    
    class Docker,ACI container
    class App,AppService,Foundation service
    class SQLite,AzureDB,Phoenix_Config storage
    class ArizeCloud external
```

## Implementation Summary

### ✅ Completed Changes

#### File-Level Changes

**1. `backend/app/core/phoenix_config.py` - Created (Telemetry-Only)**
- **Purpose**: Configure OpenTelemetry tracing to external Phoenix instances
- **Key Features**:
  - Supports both self-hosted and Phoenix Cloud endpoints
  - Automatic instrumentation of OpenAI and HTTPX libraries
  - No auto-start functionality (pure telemetry client)
  - Pydantic settings with environment variable support

**2. `backend/app/core/config.py` - Modified**
- **Changes**: Added `PhoenixSettings` integration
- **Integration**: Phoenix settings now part of main application configuration
- **Environment**: Automatic loading of `PHOENIX_*` environment variables

**3. `backend/app/main.py` - Modified** 
- **Changes**: Simple telemetry initialization on app startup
- **Removed**: Complex lifespan management and auto-start logic
- **Added**: Health endpoint for Phoenix telemetry status

**4. `backend/app/services/foundation_model.py` - Enhanced**
- **Added**: Comprehensive OpenTelemetry spans for Foundation Model operations
- **Tracing**: Custom span attributes for conversation tracking, tool usage, and performance
- **Integration**: Works seamlessly with MCP tool execution tracing

**5. `backend/.env.example` - Updated**
- **Added**: Complete Phoenix configuration options for all deployment scenarios
- **Documentation**: Clear examples for local Docker, Azure cloud, and Phoenix Cloud setups
- **Deployment Options**: Detailed comments for each deployment pattern

**6. `backend/.env` - Updated**
- **Configuration**: Simplified to telemetry-only settings
- **Removed**: Auto-start configuration flags

**7. `backend/README.md` - Comprehensive Update**
- **Added**: Complete deployment documentation for all Phoenix scenarios
- **Sections**: Local development, Azure deployment, Phoenix Cloud options
- **Persistence**: SQLite and PostgreSQL storage options
- **Troubleshooting**: Updated for telemetry-only approach

### 🔧 Technical Implementation Details

#### Phoenix Configuration Architecture
```python
class PhoenixSettings(BaseModel):
    enabled: bool = True
    endpoint: str = "http://127.0.0.1:6006"
    api_key: Optional[str] = None  # For Phoenix Cloud
    service_name: str = "graysky-assistant"
    service_version: str = "0.1.0"
    environment: str = "development"
```

#### Telemetry Setup Flow
1. **Application Startup**: `main.py` calls `setup_phoenix_telemetry()`
2. **OTLP Configuration**: Export traces to external Phoenix endpoint
3. **Auto-instrumentation**: OpenAI, HTTPX, and FastAPI automatically traced
4. **Custom Spans**: Foundation Model service adds business logic tracing
5. **Span Attributes**: Conversation ID, tool usage, performance metrics

#### Key Architectural Decisions

**✅ Telemetry-Only Approach**
- **Benefits**: 
  - ✅ Works in all deployment environments (Azure App Service, Container Instance, AKS)
  - ✅ Separates observability infrastructure from application concerns
  - ✅ Follows 12-factor app principles
  - ✅ Enables independent scaling and management
- **Trade-offs**: 
  - ⚠️ Requires external Phoenix management
  - ⚠️ Additional infrastructure setup step

**❌ Removed Auto-Start Approach**
- **Problems**: 
  - ❌ Failed in Azure App Service (no subprocess support)
  - ❌ Complex lifecycle management and cleanup
  - ❌ Resource conflicts in containerized environments
  - ❌ Mixed application and infrastructure concerns

### 📊 Deployment Options

#### Option 1: Local Development
```bash
# Temporary storage (testing)
docker run -p 6006:6006 -p 4317:4317 arizephoenix/phoenix:latest

# Persistent storage (recommended for development)  
docker run -d --name phoenix-persistent \
  -p 6006:6006 -p 4317:4317 \
  -v phoenix-dev-data:/app/data \
  -e PHOENIX_SQL_DATABASE_URL="sqlite:///app/data/phoenix.db" \
  arizephoenix/phoenix:latest
```

#### Option 2: Azure Self-Hosted
- **Container Instance**: Dedicated Phoenix container with public IP
- **App Service**: Phoenix deployed as separate App Service
- **AKS**: Phoenix deployed to Kubernetes cluster with LoadBalancer
- **Storage**: Azure Database for PostgreSQL for production persistence

#### Option 3: Phoenix Cloud (Available but Not Used)
- **Managed Service**: Fully managed by Arize
- **Advanced Features**: ML-powered insights, team collaboration, enterprise security
- **Reason Not Used**: Cost control and data sovereignty requirements

### 🔍 Observability Features Implemented

#### Foundation Model Tracing
- **Span Names**: `foundation_model.generate_response_with_tools`
- **Attributes**: 
  - `conversation_id`: Session tracking
  - `message_count`: Request size analysis  
  - `has_mcp_service`: Integration status
  - `tools_available`: Tool discovery results
  - `function_calls_made`: Tool execution tracking

#### Automatic Instrumentation
- **OpenAI**: All Azure OpenAI API calls traced with token usage
- **HTTPX**: HTTP client calls to MCP servers and external APIs  
- **FastAPI**: Request/response tracing with correlation IDs

#### Custom Business Logic Tracing
- **MCP Tool Execution**: Tool discovery, execution, and results
- **Conversation Flow**: Multi-turn conversation analysis
- **Performance Tracking**: Response times and bottleneck identification
- **Error Tracking**: Exception capture and error rate monitoring

### 📈 Metrics and Analytics Available

#### LLM-Specific Metrics
- **Token Usage**: Input/output tokens per request, cost analysis
- **Response Times**: Foundation Model latency tracking
- **Function Call Success Rates**: Tool execution reliability
- **Conversation Length Analysis**: Multi-turn conversation patterns

#### Tool Usage Analytics  
- **MCP Tool Patterns**: Most used tools and execution frequency
- **Tool Response Times**: Performance analysis per tool type
- **Source Attribution**: Data quality and reference tracking
- **Tool Selection Analysis**: Decision-making pattern insights

### 🚀 Getting Started

#### Quick Setup (5 minutes)
```bash
# 1. Start Phoenix with persistence
docker run -d --name phoenix-dev \
  -p 6006:6006 -p 4317:4317 \
  -v phoenix-dev-data:/app/data \
  -e PHOENIX_SQL_DATABASE_URL="sqlite:///app/data/phoenix.db" \
  arizephoenix/phoenix:latest

# 2. Configure application
cd backend
echo "PHOENIX_ENABLED=true" >> .env

# 3. Start application  
source venv/bin/activate
uvicorn app.main:app --reload

# 4. Generate test traces
curl -N http://localhost:8000/api/v1/chat/stream \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello Phoenix!", "conversation_id": "test-session"}'

# 5. View traces
open http://localhost:6006
```

### 🔧 Environment Configuration

#### Development (.env)
```bash
PHOENIX_ENABLED=true
PHOENIX_ENDPOINT=http://127.0.0.1:6006
PHOENIX_SERVICE_NAME=graysky-assistant
PHOENIX_SERVICE_VERSION=0.1.0
PHOENIX_ENVIRONMENT=development
```

#### Production Azure (.env)
```bash
PHOENIX_ENABLED=true
PHOENIX_ENDPOINT=http://phoenix-container-ip:6006
PHOENIX_SERVICE_NAME=graysky-assistant
PHOENIX_SERVICE_VERSION=1.0.0
PHOENIX_ENVIRONMENT=production
```

#### Phoenix Cloud (.env)
```bash
PHOENIX_ENABLED=true
PHOENIX_ENDPOINT=https://app.phoenix.arize.com
PHOENIX_API_KEY=your_phoenix_cloud_api_key
PHOENIX_SERVICE_NAME=graysky-assistant
PHOENIX_SERVICE_VERSION=1.0.0
PHOENIX_ENVIRONMENT=production
```

### 📋 Testing and Verification

#### Local Verification Steps
1. **Phoenix Health**: `curl http://localhost:6006/healthz`
2. **App Health**: `curl http://localhost:8000/phoenix-health`
3. **Generate Traces**: Make API calls to chat endpoints
4. **View Traces**: Check Phoenix UI at http://localhost:6006
5. **Persistence Test**: Restart Phoenix container, verify traces remain

#### Production Verification Steps
1. **Infrastructure**: Verify Phoenix container/service is running
2. **Connectivity**: Test OTLP endpoint accessibility (port 4317)
3. **Telemetry**: Generate production traces via API calls
4. **Monitoring**: Set up alerts for trace ingestion failures
5. **Performance**: Monitor tracing overhead impact (<3% latency)

### 🛠️ Troubleshooting Guide

#### Common Issues and Solutions

| Issue | Cause | Solution |
|-------|--------|----------|
| No traces appearing | Phoenix not running | Verify Phoenix container: `docker ps` |
| Connection refused | Wrong endpoint | Check `PHOENIX_ENDPOINT` configuration |
| Traces lost on restart | In-memory storage | Use SQLite persistence with volume mount |
| High memory usage | No trace sampling | Configure trace sampling or use Phoenix Cloud |
| App startup fails | Phoenix config error | Check environment variables and telemetry setup |

### 📚 Next Steps

#### Development Phase
- [x] ✅ Complete telemetry-only implementation
- [x] ✅ Test local development workflow
- [x] ✅ Document all deployment scenarios
- [ ] ⏳ Set up CI/CD integration with Phoenix tracing
- [ ] ⏳ Create custom dashboards for key metrics

#### Production Phase  
- [ ] ⏳ Deploy Phoenix to Azure Container Instance
- [ ] ⏳ Configure PostgreSQL persistence
- [ ] ⏳ Set up monitoring and alerting
- [ ] ⏳ Performance testing and optimization
- [ ] ⏳ Team training on Phoenix analytics

### 📖 Related Documentation

- **Backend README**: Complete deployment instructions and troubleshooting
- **.env.example**: All configuration options with examples
- **CLAUDE.md**: Development guidelines and logging integration
- **Phoenix Official Docs**: https://docs.arize.com/phoenix
- **OpenTelemetry Integration**: https://docs.arize.com/phoenix/tracing/how-to-tracing/setup-tracing/setup-tracing-python

---

**Last Updated**: 2025-09-12
**Implementation Status**: ✅ Complete - Telemetry-Only Approach
**Deployment Tested**: ✅ Local Development, ⏳ Azure Production
# RespondER Agent Integration Tests

## Overview

This document contains integration test cases for the RespondER agent - an AI-powered assistant for emergency management queries built with LangGraph, FastAPI backend, and GraphRAG MCP server with Memgraph database.

**Last Updated**: 2025-11-11

## Architecture Overview

```
User Query → Backend (FastAPI/LangGraph) → GraphRAG MCP Server → Memgraph Database
                  ↓                                    ↓
          /tmp/backend.log                    /tmp/graphmcp.log
```

### System Components

- **Backend**: FastAPI server with LangGraph supervisor + 2 subagents (Genie, GraphRAG)
  - Port: `8000`
  - Endpoint: `POST /api/v1/chat/stream`
  - Logs: `/tmp/backend.log`

- **GraphRAG MCP Server**: FastMCP server exposing 37 graph query tools
  - Port: `8001`
  - Protocol: HTTP MCP
  - Logs: `/tmp/graphmcp.log`

- **Memgraph Database**: Graph database with emergency management data
  - Port: `7687`
  - Query Language: Cypher

### Actual Graph Schema

**Statistics**: 157,855 nodes | 353,055 relationships | 9 entity types | 13 relationship types

**Entity Types**:
- `Mission` (85,458) - Operational tasks with `id`, `mission_number`, `title`, `incidents`, `updated_at`
- `Comment` (69,026) - Mission comments with `comment_id`, `text`, `created_by`, `created_at`
- `FuelTransaction` (2,798) - Fuel dispensing with `fuel_transaction_id`, `amount_dispensed`, `fuel_type`, `county`, `date_of_fueling`, `fuel_vendor`, `address`
- `SubBranch` (144) - Organizational sub-units with `sub_branch_id`, `name`
- `EEIStatus` (134) - County emergency status with `eei_history_id`, `county_name`, `eoc_activation`, `evacuation_status`, `govt_status`, `lse_status`, `region`, `isActive`, `updated_at`
- `Branch` (124) - Organizational branches with `branch_id`, `name`
- `County` (69) - Florida counties with `county_name`, `region`, `latitude`, `longitude`
- `Vendor` (63) - Service providers with `vendor_id`, `mission_vendor_name`, `vendor_updated_at`
- `Incident` (39) - Emergency events with `incident_id`, `incident_name`, `incident_type`, `incident_status`, `incident_created_at`, `incident_updated_at` (e.g., "2024 Helene", "2024 Milton", "2022 Ian")

**Relationship Types**:
- `TASKED_TO` (85,458): Mission → SubBranch
- `ASSIGNED_TO` (85,458): Mission → Branch
- `RELATED_TO_INCIDENT` (79,940): Mission → Incident
- `COMMENTED_ON` (68,347): Comment → Mission
- `IS_PARENT_TO` (27,910): Mission → Mission (parent-child hierarchy)
- `DISPENSED_FOR` (2,798): Incident → FuelTransaction
- `FROM_VENDOR` (2,798): FuelTransaction → Vendor
- `HAD_STATUS` (196): County → EEIStatus (with `from_date`, `to_date` properties)
- `HAS_CURRENT_STATUS` (134): County → EEIStatus
- `RESPONSIBLE_FOR` (5): Vendor → Mission
- `ASSIGNED_VENDOR` (5): Mission → Vendor
- `AFFECTS` (4): Incident → County
- `WORKS_WITH` (2): SubBranch → Vendor

**Important Notes**:
- Property types: `ZONED_DATE_TIME` requires `datetime()` function in queries
- Numeric fields may be stored as strings - use `toFloat()` or `toInteger()` for calculations
- All relationships are directional - use `->` or `<-` appropriately
- Mission nodes have embeddings for semantic search

## Test Execution Methods

### Method 1: User-Driven Testing with Log Analysis

**When to use**: Testing complex multi-turn conversations or when the user interacts directly.

**Process**:
1. User runs test query via frontend or API
2. Claude Code reads logs using Read tool on `/tmp/backend.log` and `/tmp/graphmcp.log`
3. Analyze agent behavior, tool calls, and generated queries
4. Identify issues and propose fixes

**Example**:
```
User: "What was the total fuel usage during Hurricane Helene?"
Claude Code: [Reads /tmp/backend.log and /tmp/graphmcp.log]
            [Analyzes tool calls and Cypher queries generated]
            [Documents results and issues]
```

### Method 2: Direct curl Testing with Log Analysis

**When to use**: Automated testing, regression testing, or systematic test execution.

**Process**:
1. Claude Code uses Bash tool to execute curl command:
   ```bash
   curl -N http://localhost:8000/api/v1/chat/stream \
     -H "Content-Type: application/json" \
     -d '{"message": "Test question", "conversation_id": "test_conv_123"}'
   ```
2. Capture streaming response
3. Use Grep/Read tools to check logs for that conversation_id
4. Analyze and document results

### Using MCP Tools Directly

Claude Code has direct access to GraphRAG MCP tools for validation:

```python
# Check schema
mcp__graphrag__get_schema()

# Verify data exists
mcp__graphrag__check_entity_availability(entity_type="FuelTransaction")

# Test aggregation
mcp__graphrag__aggregate_by_relationship(
    entity_type="FuelTransaction",
    group_by="incident",
    metrics=["sum_amount_dispensed"],
    target_filters={"incident_name": "2024 Helene"}
)

# Run raw Cypher
mcp__graphrag__execute_cypher(
    query="MATCH (i:Incident) RETURN i.incident_name LIMIT 5"
)
```

**When to use MCP tools**:
- Validate expected results before testing agent
- Debug agent query generation
- Quick schema exploration
- Verify data exists

## Log Analysis Best Practices

**Search by conversation_id**:
```bash
grep "conversation_id: test_conv_123" /tmp/backend.log
```

**Check tool calls**:
```bash
grep "aggregate_by_relationship" /tmp/graphmcp.log | tail -20
```

**View generated Cypher queries**:
```bash
grep "\[Query\]" /tmp/graphmcp.log | tail -10
```

**Find errors**:
```bash
grep "ERROR\|returned 0 rows" /tmp/graphmcp.log | tail -20
```

## Known Issues & Resolutions

### ✅ Resolved

**Issue #1: aggregate_by_relationship filter ambiguity (RESOLVED 2025-11-11)**
- **Problem**: Filters applied only to source entity, causing incorrect results
- **Example**: "Fuel for Hurricane Helene" filtered FuelTransaction.incident_name instead of Incident.incident_name
- **Fix**: Added `source_filters` and `target_filters` parameters
- **Files**: `graphmcp/src/tools/analysis_tools.py`, `backend/src/app/agent/langgraph/workflows.py`

**Issue #2: search_entities ZONED_DATE_TIME comparison bug (RESOLVED 2025-11-12)**
- **Problem**: `search_entities` didn't wrap ZONED_DATE_TIME properties in `datetime()` function, causing all date range queries to return 0 rows
- **Example**: Query "fuel between 10/1 and 10/15" returned 0 rows, but 100+ transactions exist
- **Root Cause**: String comparison `n.date_of_fueling >= '2024-10-01...'` fails silently in Memgraph
- **Fix**: Implemented schema-aware datetime detection that automatically wraps ZONED_DATE_TIME properties in `datetime()` function
- **Files**: `graphmcp/src/tools/search_tools.py` (added schema lookup), `graphmcp/src/server.py` (pass schema_manager)
- **Impact**: All date-based queries using `search_entities` now work correctly
- **Validation Test** (2025-11-12):
  ```bash
  # Test query that previously failed
  curl -N http://localhost:8000/api/v1/chat/stream \
    -H "Content-Type: application/json" \
    -d '{"message": "How much fuel was dispensed between October 1 and October 15, 2024?", "conversation_id": "test_oct_date_range"}'
  ```
  **Result**: Agent correctly returned **24,545.8 gallons** dispensed between Oct 1-15, 2024
  - Agent workflow: Supervisor → GraphRAG subagent → `get_schema` → `describe_entity_type` → `execute_cypher` with datetime() wrapper
  - Previously would have returned: "No fuel data found for that date range"
  - Logs show successful query execution using `datetime()` function for date comparisons

**Issue #3: aggregate_by_relationship indirect filter bug (RESOLVED 2025-11-12)**
- **Problem**: `aggregate_by_relationship` applied `target_filters` directly to group_by entity, causing 0 results when filter properties existed on different entity
- **Example**: Query "Compare missions between Helene and Milton by vendor and branch" returned 0 missions because tool applied `incident_name` filter to Vendor/Branch nodes instead of Incident nodes
- **Root Cause**: Tool lacked mechanism to specify which entity type the filters should apply to
- **Broken Query Generated**:
  ```cypher
  MATCH (n:Mission)<-[:RESPONSIBLE_FOR]-(target:Vendor)
  WHERE target.incident_name = '2024 Milton'  -- ❌ Vendor doesn't have this property
  ```
- **Fix**: Added `target_filter_entity` parameter to explicitly specify entity type for `target_filters`
  - Discovers second relationship from source entity to filter entity
  - Builds enhanced MATCH pattern starting from filter entity
  - Correctly reverses relationship direction when starting from filter entity
- **Correct Query Generated**:
  ```cypher
  MATCH (filter:Incident {incident_name: '2024 Milton'})<-[:RELATED_TO_INCIDENT]-(n:Mission)<-[:RESPONSIBLE_FOR]-(target:Vendor)
  RETURN target.mission_vendor_name, count(n)
  ```
- **Files**: `graphmcp/src/tools/analysis_tools.py`
- **Impact**: Mission-to-vendor and mission-to-branch linkages now work correctly when filtering by incident
- **Agent Guidance Added**: Tool description now requires agents to verify schema and relationships before calling
- **Validation Test** (2025-11-12):
  ```python
  # Missions by branch for Hurricane Helene
  mcp__graphrag__aggregate_by_relationship(
      entity_type="Mission",
      group_by="branch",
      target_filter_entity="Incident",
      target_filters={"incident_name": "2024 Helene"},
      metrics=["count"]
  )
  ```
  **Result**: Correctly returned **5,052 missions** across 60 branches
  - Top branches: IMT Central (1,027), SERT ESF17 (560), SERT ESF11 (426)
  - Previously would have returned: 0 missions
  - Query correctly chains through Incident → Mission → Branch relationships

### 🐛 Active Issues

**Missing Feature: Vector Search / Semantic Similarity**
- **Problem**: No vector indices created and incomplete semantic search capability
- **Impact**: Agent cannot handle natural language queries requiring semantic matching
- **Examples of Queries That Would Fail**:
  - "Show me missions about water distribution" (needs semantic search on Mission.title)
  - "Find missions involving evacuation support" (semantic match, not keyword match)
  - "What missions are similar to disaster relief coordination?" (similarity search)
  - "Find comments mentioning supply chain issues" (semantic search on Comment.text)
- **Why Current Tests Don't Expose This**: All 30 test cases use structured queries (aggregations, exact filters, relationship traversals) rather than semantic/natural language search
- **Available Tools - need to be completed**:
  - `semantic_search_missions` - Semantic search on Mission.title
  - `semantic_search_incidents` - Semantic search on Incident descriptions
  - `find_similar_resources` - Find similar resources by embedding
  - `generate_embeddings` - Generate embeddings for node properties
  - `create_vector_index` - Create vector indices in Memgraph
  - `check_vector_indices` - Verify vector index status
- **Implementation Needed**:
  - Generate vector indices existing embeddings (Mission, Comment)
  - Update MCP tools with the local embedding model - `semantic_search_missions`, `semantic_search_incidents`, `find_similar_resources`
  - Update workflows.py with semantic search guidance
- **Complexity**: Moderate (new dependencies, embedding generation, 3-4 new tools)
- **Priority**: Medium - useful for natural language queries but not blocking current test cases

## Test Status Definitions

- **Complete**: Test is working correctly (agent returns correct answer)
- **Pending**: Test not yet working or needs fixes

## Test Case Fields

- **Category**: Functional area (Fuel Flow, Fuel Burn, Requests, Shelters, Vendors, Crew Unit, Open-Ended)
- **Status**: Complete or Pending
- **Question**: Natural language question for the agent
- **Answer**: Actual answer received during testing
- **Expected Answer (MCP)**: Cypher query to validate ground truth (note: some queries may use outdated schema and need updating to actual entity/relationship names)
- **Potential Validation Cypher**: Additional validation query
- **Work Needed**: Notes on fixes required

---

## Test Cases

### Fuel Flow Tests

#### Test Case: Fuel-Flow-001
- **Category**: Fuel Flow
- **Status**: Complete
- **Question**: How much fuel has disaster dispensed so far?
- **Answer**: Included Fuel Cool (approved) prime contractor Paul Taylor 116,372,135 gallons Highlands Mission 6 rows 2010-10-05 through 2010-10-10 Highlands/FHRS dataset Mission name: Highlands Request (Fuel Cool) qty: 8
- **Expected Answer (MCP)**: 
```cypher
MATCH (f:Fuel)-[:DISPENSED_IN]->(m:Mission {name: 'Highlands'})
WHERE f.date >= date('2010-10-05') AND f.date <= date('2010-10-10')
RETURN sum(f.quantity) as total_fuel_dispensed
```
- **Potential Validation Cypher (sense check)**: 
- **Work Needed**: 

---

#### Test Case: Fuel-Flow-002
- **Category**: Fuel Flow
- **Status**: Complete
- **Question**: What is the total fuel dispensed during the time?
- **Answer**: Included Fuel Cool (approved) prime contractor Paul Taylor mission Highlands 116,372,135 gallons fuel is dispensed 2010-10-05 through 2010-10-10, FHRS dataset matches
- **Expected Answer (MCP)**: 
```cypher
MATCH (f:Fuel)
WHERE f.date >= date('2010-10-05') AND f.date <= date('2010-10-10')
RETURN sum(f.quantity) as total_fuel_dispensed
```
- **Potential Validation Cypher (sense check)**: 
- **Work Needed**: 

---

#### Test Case: Fuel-Flow-003
- **Category**: Fuel Flow
- **Status**: Complete
- **Question**: How much fuel was dispensed over the last ten days that fuel was dispensed in transactions?
- **Answer**: 
- **Expected Answer (MCP)**: *(Query MCP server directly)*
- **Potential Validation Cypher (sense check)**: 
- **Work Needed**: 

---

#### Test Case: Fuel-Flow-004
- **Category**: Fuel Flow
- **Status**: Complete
- **Question**: What is the total fuel dispensed by West Palm County?
- **Answer**: Included FHRS proposal Mission property directly, data approved in County, Mission County name/source
- **Expected Answer (MCP)**: 
```cypher
MATCH (c:County {name: 'West Palm'})-[:DISPENSED]->(f:Fuel)
RETURN sum(f.quantity) as total_fuel_dispensed
```
- **Potential Validation Cypher (sense check)**: 
- **Work Needed**: 

---

#### Test Case: Fuel-Flow-005
- **Category**: Fuel Flow
- **Status**: Pending
- **Question**: Which county used the most fuel?
- **Answer**: 
- **Expected Answer (MCP)**: 
```cypher
MATCH (c:County)-[:DISPENSED]->(f:Fuel)
WITH c, sum(f.quantity) as total_fuel
ORDER BY total_fuel DESC
LIMIT 1
RETURN c.name as county, total_fuel
```
- **Potential Validation Cypher (sense check)**: 
- **Work Needed**: Investigate which suppliers continue to consume data

---

#### Test Case: Fuel-Flow-006
- **Category**: Fuel Flow
- **Status**: Complete
- **Question**: What is the breakdown by county?
- **Answer**: Top 5 counties fuel dispensing: Broward County 28,082 (8.56%), Orange County 16,437 (4.94%), St. Lucie County 19,423 (5.79%), Highlands County 26,436.09, 72 rows x 4 dataset, 38 for top 5
- **Expected Answer (MCP)**: *(Query MCP server directly)*
- **Potential Validation Cypher (sense check)**: 
- **Work Needed**: 

---

#### Test Case: Fuel-Flow-007
- **Category**: Fuel Flow
- **Status**: Complete
- **Question**: What is the breakdown between FHRS and CrisisCleanup fuel?
- **Answer**: Approximately $1,300, Fuel segmentation of comparisons, Regions and Resources
- **Expected Answer (MCP)**: 
```cypher
MATCH (f:Fuel)-[:IN_SYSTEM]->(s:System)
WHERE s.name IN ['FHRS', 'CrisisCleanup']
RETURN s.name as system, sum(f.quantity) as total_fuel
ORDER BY total_fuel DESC
```
- **Potential Validation Cypher (sense check)**: 
- **Work Needed**: Somehow

---

### Fuel Burn Tests

#### Test Case: Fuel-Burn-001
- **Category**: Fuel Burn
- **Status**: Pending
- **Question**: For what disaster was fuel dispensed on October 5, 2024?
- **Answer**:
- **Expected Answer (MCP)**:
```cypher
MATCH (i:Incident)-[:DISPENSED_FOR]->(ft:FuelTransaction)
WHERE ft.date_of_fueling >= datetime('2024-10-05T00:00:00')
  AND ft.date_of_fueling < datetime('2024-10-06T00:00:00')
RETURN DISTINCT i.incident_name as disaster
```
- **Potential Validation Cypher (sense check)**: 
- **Work Needed**: 

---

#### Test Case: Fuel-Burn-002
- **Category**: Fuel Burn
- **Status**: Pending
- **Question**: On October 5, 2010, a total of 36,382 gallons of fuel was dispensed - at what time and where?
- **Answer**: The base with highest fuel burn is in Broward/Miami Public facility with 50,090 fuel dispensed. FHRS dataset row based on time/dispensed. Solution is how fuel was computed: Granted Data Proof Funding 2010-6 between Mission 2010-6
- **Expected Answer (MCP)**: 
```cypher
MATCH (f:Fuel {date: date('2010-10-05')})
WHERE f.quantity = 36382
RETURN f.location as location, f.time as time
```
- **Potential Validation Cypher (sense check)**: 
- **Work Needed**: 

---

#### Test Case: Fuel-Burn-003
- **Category**: Fuel Burn
- **Status**: Pending
- **Question**: What bases have the highest fuel burn rate?
- **Answer**: Approximately $1,200 with fuel, Home located to time/base, values in gasoline standard using Criteria Public facility category for Fuel dispensing
- **Expected Answer (MCP)**: 
```cypher
MATCH (b:Base)-[:BURNS_FUEL]->(f:Fuel)
WITH b, sum(f.quantity) / count(DISTINCT f.date) as burn_rate
ORDER BY burn_rate DESC
LIMIT 10
RETURN b.name as base, burn_rate
```
- **Potential Validation Cypher (sense check)**: 
- **Work Needed**: 

---

#### Test Case: Fuel-Burn-004
- **Category**: Fuel Burn
- **Status**: Pending
- **Question**: Compare base names and dates
- **Answer**: Headers Included: Fuel analyzing set by region / FHRS data categorized by time burn, segment total burn
- **Expected Answer (MCP)**: *(Query MCP server directly)*
- **Potential Validation Cypher (sense check)**: 
- **Work Needed**: 

---

#### Test Case: Fuel-Burn-005
- **Category**: Fuel Burn
- **Status**: Pending
- **Question**: Which bases have high burn rates and low CrisisCleanup?
- **Answer**: 
- **Expected Answer (MCP)**: 
```cypher
MATCH (b:Base)-[:BURNS_FUEL]->(f:Fuel)
WITH b, avg(f.burn_rate) as avg_burn_rate, 
     sum(CASE WHEN f.source = 'CrisisCleanup' THEN f.quantity ELSE 0 END) as cleanup_fuel
WHERE avg_burn_rate > [threshold] AND cleanup_fuel < [threshold]
RETURN b.name as base, avg_burn_rate, cleanup_fuel
ORDER BY avg_burn_rate DESC
```
- **Potential Validation Cypher (sense check)**: 
- **Work Needed**: How to partition

---

### Request Tests

#### Test Case: REQUESTS-001
- **Category**: REQUESTS
- **Status**: Complete
- **Question**: What is Broward and how many requests?
- **Answer**: Typical 880 requests/problems, requirement specify in commercial/non-problems/groups distribution
- **Expected Answer (MCP)**: 
```cypher
MATCH (c:County {name: 'Broward'})-[:HAS_REQUEST]->(r:Request)
RETURN count(r) as total_requests
```
- **Potential Validation Cypher (sense check)**: 
- **Work Needed**: 

---

### Shelter Tests

#### Test Case: SHELTERS-001
- **Category**: SHELTERS
- **Status**: Complete
- **Question**: How many shelters exist in Hurricane Milton?
- **Answer**: Many shelters related to Hurricane Milton
- **Expected Answer (MCP)**: 
```cypher
MATCH (d:Disaster {name: 'Hurricane Milton'})-[:HAS_SHELTER]->(s:Shelter)
RETURN count(s) as total_shelters
```
- **Potential Validation Cypher (sense check)**: 
- **Work Needed**: If not ok: Lodge in checked time data

---

#### Test Case: SHELTERS-002
- **Category**: SHELTERS
- **Status**: Pending
- **Question**: Context of non-checked info Fuel factors
- **Answer**: 
- **Expected Answer (MCP)**: *(Query MCP server directly)*
- **Potential Validation Cypher (sense check)**: 
- **Work Needed**: 

---

#### Test Case: SHELTERS-003
- **Category**: SHELTERS
- **Status**: Pending
- **Question**: How many shelters were allocated from Open Climate and Mass Climate?
- **Answer**: 2023 Metrics, 2020 Metrics Climate metrics dataset form affected/open Facility County category
- **Expected Answer (MCP)**: *(Query MCP server directly)*
- **Potential Validation Cypher (sense check)**: 
- **Work Needed**: 

---

#### Test Case: SHELTERS-004
- **Category**: SHELTERS
- **Status**: Pending
- **Question**: Categorize shelter access
- **Answer**: Search of fuel and searching, climate and organized related entities
- **Expected Answer (MCP)**: *(Query MCP server directly)*
- **Potential Validation Cypher (sense check)**: 
- **Work Needed**: 

---

#### Test Case: SHELTERS-005
- **Category**: SHELTERS
- **Status**: Pending
- **Question**: Which shelters differentiate by climate order? How many disasters?
- **Answer**: Great fuel categorize checked regions Metrics with name count, shelter status
- **Expected Answer (MCP)**: *(Query MCP server directly)*
- **Potential Validation Cypher (sense check)**: 
- **Work Needed**: 

---

#### Test Case: SHELTERS-006
- **Category**: SHELTERS
- **Status**: Pending
- **Question**: What are the Broward address locations in disaster county?
- **Answer**: 2023 Metrics, 2020 Metrics Broward county Facility County
- **Expected Answer (MCP)**: 
```cypher
MATCH (c:County {name: 'Broward'})-[:IN_DISASTER]->(d:Disaster)
MATCH (c)-[:HAS_SHELTER]->(s:Shelter)
RETURN s.address as shelter_address, d.name as disaster
```
- **Potential Validation Cypher (sense check)**: 
- **Work Needed**: 

---

#### Test Case: SHELTERS-007
- **Category**: SHELTERS
- **Status**: Pending
- **Question**: What are the unique counties in disaster county?
- **Answer**: 
- **Expected Answer (MCP)**: *(Query MCP server directly)*
- **Potential Validation Cypher (sense check)**: 
- **Work Needed**: 

---

#### Test Case: SHELTERS-008
- **Category**: SHELTERS
- **Status**: Pending
- **Question**: How many metrics/reports for climate disaster dataset?
- **Answer**: 
- **Expected Answer (MCP)**: *(Query MCP server directly)*
- **Potential Validation Cypher (sense check)**: 
- **Work Needed**: 

---

#### Test Case: SHELTERS-009
- **Category**: SHELTERS
- **Status**: Pending
- **Question**: What is the number of disasters in IBMIT-II average?
- **Answer**: 
- **Expected Answer (MCP)**: *(Query MCP server directly)*
- **Potential Validation Cypher (sense check)**: 
- **Work Needed**: 

---

### Vendor Tests

#### Test Case: VENDORS-001
- **Category**: VENDORS
- **Status**: Complete
- **Question**: How many vendors exist in the system?
- **Answer**: Many vendors exist in system, such as direct prime vendors
- **Expected Answer (MCP)**: 
```cypher
MATCH (v:Vendor)
RETURN count(v) as total_vendors
```
- **Potential Validation Cypher (sense check)**: 
- **Work Needed**: 

---

#### Test Case: VENDORS-002
- **Category**: VENDORS
- **Status**: Complete
- **Question**: List all vendors
- **Answer**: ACE Custom Executive, ACE Custom Fuel Metrics Security, ACE Section, System Security Fuel CleanUp, Data count scoring, CSV Regions, CSV Fuel metrics, County Metrics ACE, Custom Fuel Metrics, Custom Fuel REGION, Custom Metrics Strategies, Facility Fuel, Florida X, Florida County, Mobile Core, Protek, Shelters X, Vendors, X metrics
- **Expected Answer (MCP)**: *(Query MCP server directly)*
- **Potential Validation Cypher (sense check)**: 
- **Work Needed**: 

---

#### Test Case: VENDORS-003
- **Category**: VENDORS
- **Status**: Complete
- **Question**: Get all vendors grouped by category
- **Answer**: 
- **Expected Answer (MCP)**: 
```cypher
MATCH (v:Vendor)
RETURN v.category as vendor_category, collect(v.name) as vendors
ORDER BY vendor_category
```
- **Potential Validation Cypher (sense check)**: 
- **Work Needed**: 

---

### Crew Unit Tests

#### Test Case: CREW-UNIT-001
- **Category**: CREW UNIT
- **Status**: Pending
- **Question**: What is the average value of fuel per unit for Broward?
- **Answer**: During current time (2020s, 2024): no context fuel angle data
- **Expected Answer (MCP)**: *(Query MCP server directly)*
- **Potential Validation Cypher (sense check)**: 
- **Work Needed**: 

---

#### Test Case: CREW-UNIT-002
- **Category**: CREW UNIT
- **Status**: Pending
- **Question**: [Question text unclear in spreadsheet]
- **Answer**: County X Fuel Custom gallons ACE Facility dataset burn
- **Expected Answer (MCP)**: *(Query MCP server directly)*
- **Potential Validation Cypher (sense check)**: 
- **Work Needed**: 

---

#### Test Case: CREW-UNIT-003
- **Category**: CREW UNIT
- **Status**: Pending
- **Question**: What are the average mission values?
- **Answer**: 
- **Expected Answer (MCP)**: *(Query MCP server directly)*
- **Potential Validation Cypher (sense check)**: 
- **Work Needed**: Verify metrics created by time disaster climate

---

#### Test Case: CREW-UNIT-004
- **Category**: CREW UNIT
- **Status**: Pending
- **Question**: [Question unclear in spreadsheet]
- **Answer**: 
- **Expected Answer (MCP)**: *(Query MCP server directly)*
- **Potential Validation Cypher (sense check)**: 
- **Work Needed**: Time not available

---

### Open-Ended Tests

#### Test Case: OPEN-ENDED-001
- **Category**: OPEN-ENDED
- **Status**: Pending
- **Question**: [Complex open-ended query - text unclear in spreadsheet]
- **Answer**: 
- **Expected Answer (MCP)**: *(Query MCP server directly)*
- **Potential Validation Cypher (sense check)**: 
- **Work Needed**: 

---

### Vector Search / Semantic Similarity Tests

#### Test Case: VECTOR-001
- **Category**: Vector Search
- **Status**: Pending
- **Question**: When did we have supply chain delays during Hurricane Ian? There should be activity in the comments about this.
- **Answer**:
- **Expected Answer (MCP)**: Use semantic_search_comments to find comments mentioning supply chain delays, filter by Ian incident, extract date patterns from created_at
- **Potential Validation Cypher (sense check)**:
```cypher
MATCH (i:Incident {incident_name: '2022 Ian'})<-[:RELATED_TO_INCIDENT]-(m:Mission)<-[:COMMENTED_ON]-(c:Comment)
WHERE toLower(c.text) CONTAINS 'supply' OR toLower(c.text) CONTAINS 'delay' OR toLower(c.text) CONTAINS 'chain'
RETURN c.created_at, c.text, m.title
ORDER BY c.created_at
```
- **Work Needed**: Generate embeddings for Comment nodes, create vector index
- **Follow-up Question**: How does that compare to supply chain issues we're seeing in Milton right now?

---

#### Test Case: VECTOR-002
- **Category**: Vector Search
- **Status**: Pending
- **Question**: We're planning evacuation routes for coastal counties in Milton. What did we do for similar evacuation missions during Helene, and which vendors handled those?
- **Answer**:
- **Expected Answer (MCP)**: Use semantic_search_missions("evacuation coastal") with target_filters for Helene incident, then traverse to vendors via ASSIGNED_VENDOR relationships
- **Potential Validation Cypher (sense check)**:
```cypher
MATCH (i:Incident {incident_name: '2024 Helene'})<-[:RELATED_TO_INCIDENT]-(m:Mission)-[:ASSIGNED_VENDOR]->(v:Vendor)
WHERE toLower(m.title) CONTAINS 'evacuat'
RETURN m.title, m.mission_number, v.mission_vendor_name, m.updated_at
ORDER BY m.updated_at DESC
```
- **Work Needed**: Generate embeddings for Mission.title, create vector index
- **Showcases**: Semantic search + relationship traversal + temporal comparison

---

#### Test Case: VECTOR-003
- **Category**: Vector Search
- **Status**: Pending
- **Question**: Show me all the water distribution missions from Ian. How much fuel did those operations consume, and where were they concentrated geographically?
- **Answer**:
- **Expected Answer (MCP)**: Use semantic_search_missions("water distribution") filtered by Ian, traverse to fuel transactions via Incident relationships, aggregate by county
- **Potential Validation Cypher (sense check)**:
```cypher
MATCH (i:Incident {incident_name: '2022 Ian'})<-[:RELATED_TO_INCIDENT]-(m:Mission)
WHERE toLower(m.title) CONTAINS 'water'
WITH i
MATCH (i)-[:DISPENSED_FOR]->(ft:FuelTransaction)
RETURN ft.county, sum(toFloat(ft.amount_dispensed)) as total_fuel
ORDER BY total_fuel DESC
```
- **Work Needed**: Generate embeddings for Mission.title, create vector index
- **Showcases**: Semantic + multi-hop traversal + aggregation + geo analysis

---

#### Test Case: VECTOR-004
- **Category**: Vector Search
- **Status**: Pending
- **Question**: Find comments mentioning coordination problems or delays during Helene. Which branches were involved in those missions, and are we seeing similar issues in Milton?
- **Answer**:
- **Expected Answer (MCP)**: Use semantic_search_comments("coordination problems delays") filtered by Helene, traverse to missions via COMMENTED_ON, get branches via ASSIGNED_TO, compare with Milton
- **Potential Validation Cypher (sense check)**:
```cypher
MATCH (i:Incident {incident_name: '2024 Helene'})<-[:RELATED_TO_INCIDENT]-(m:Mission)<-[:COMMENTED_ON]-(c:Comment)
WHERE toLower(c.text) CONTAINS 'coordinat' OR toLower(c.text) CONTAINS 'delay' OR toLower(c.text) CONTAINS 'problem'
MATCH (m)-[:ASSIGNED_TO]->(b:Branch)
RETURN b.name, count(c) as comment_count, collect(DISTINCT c.text)[0..3] as sample_comments
ORDER BY comment_count DESC
```
- **Work Needed**: Generate embeddings for Comment.text, create vector index
- **Showcases**: Comment mining + mission context + organizational analysis + pattern detection

---

#### Test Case: VECTOR-005
- **Category**: Vector Search
- **Status**: Pending
- **Question**: What missions involving emergency fuel delivery did World Fuel Services handle during Ian? Are they doing similar work in Milton, and how does the fuel volume compare?
- **Answer**:
- **Expected Answer (MCP)**: Use semantic_search_missions("emergency fuel delivery") + filter Ian + vendor filter, compare with Milton missions, aggregate fuel transactions
- **Potential Validation Cypher (sense check)**:
```cypher
MATCH (i:Incident {incident_name: '2022 Ian'})<-[:RELATED_TO_INCIDENT]-(m:Mission)-[:ASSIGNED_VENDOR]->(v:Vendor {mission_vendor_name: 'World Fuel Services'})
WITH i, count(m) as mission_count
MATCH (i)-[:DISPENSED_FOR]->(ft:FuelTransaction {fuel_vendor: 'World Fuel Services'})
RETURN i.incident_name, mission_count, sum(toFloat(ft.amount_dispensed)) as total_fuel

UNION

MATCH (i:Incident {incident_name: '2024 Milton'})<-[:RELATED_TO_INCIDENT]-(m:Mission)-[:ASSIGNED_VENDOR]->(v:Vendor {mission_vendor_name: 'World Fuel Services'})
WITH i, count(m) as mission_count
MATCH (i)-[:DISPENSED_FOR]->(ft:FuelTransaction {fuel_vendor: 'World Fuel Services'})
RETURN i.incident_name, mission_count, sum(toFloat(ft.amount_dispensed)) as total_fuel
```
- **Work Needed**: Generate embeddings for Mission.title, create vector index
- **Showcases**: Semantic + vendor intelligence + cost analysis + cross-incident comparison

---

#### Test Case: VECTOR-006
- **Category**: Vector Search
- **Status**: Pending
- **Question**: We need mobile command units for Milton. Find past missions that used similar resources during Helene or Ian - what types of missions were they, and how were they deployed?
- **Answer**:
- **Expected Answer (MCP)**: Use find_similar_resources or semantic_search_missions("mobile command units") filtered by Helene/Ian, get mission context and deployment patterns
- **Potential Validation Cypher (sense check)**:
```cypher
MATCH (i:Incident)<-[:RELATED_TO_INCIDENT]-(m:Mission)
WHERE i.incident_name IN ['2024 Helene', '2022 Ian']
  AND (toLower(m.title) CONTAINS 'mobile' OR toLower(m.title) CONTAINS 'command')
RETURN i.incident_name, m.title, m.mission_number, m.updated_at
ORDER BY i.incident_name, m.updated_at DESC
```
- **Work Needed**: Generate embeddings for Mission.title, create vector index
- **Showcases**: Resource similarity + operational pattern recognition + lessons learned

---

## Notes for Test Execution

### General Guidelines
1. Some test cases have incomplete or unclear question text in the source spreadsheet - these should be clarified before testing
2. For tests marked "Pending", work is needed before they can be properly validated
3. The "Expected Answer (MCP)" field should be populated by running the Cypher query directly against the MCP server
4. Answers should be compared semantically, not just exact string matching - numerical values should match within acceptable tolerance
5. Date ranges and filters in queries may need adjustment based on actual data in the system

### Test Categories Summary
- **Fuel Flow**: 7 tests (6 Complete, 1 Pending)
- **Fuel Burn**: 5 tests (0 Complete, 5 Pending)
- **REQUESTS**: 1 test (1 Complete, 0 Pending)
- **SHELTERS**: 9 tests (1 Complete, 8 Pending)
- **VENDORS**: 3 tests (3 Complete, 0 Pending)
- **CREW UNIT**: 4 tests (0 Complete, 4 Pending)
- **OPEN-ENDED**: 1 test (0 Complete, 1 Pending)
- **VECTOR SEARCH**: 6 tests (0 Complete, 6 Pending)

**Total**: 36 test cases (11 Complete, 25 Pending)

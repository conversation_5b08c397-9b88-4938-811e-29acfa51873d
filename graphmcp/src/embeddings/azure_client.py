"""
Azure OpenAI Embeddings Client

Handles embedding generation with Azure OpenAI API, including:
- Batch processing
- Rate limiting
- Retry logic
- Metadata tracking
"""

import os
import logging
import hashlib
from datetime import datetime
from typing import List, Dict, Any, Optional
from openai import AzureOpenAI

logger = logging.getLogger(__name__)


class AzureEmbeddingClient:
    """Client for generating embeddings via Azure OpenAI"""

    def __init__(
        self,
        api_key: Optional[str] = None,
        endpoint: Optional[str] = None,
        api_version: Optional[str] = None,
        deployment: Optional[str] = None,
        dimension: Optional[int] = None,
    ):
        """
        Initialize embedding client

        Args:
            api_key: Azure OpenAI API key (or from OPENAI_API_KEY env var)
            endpoint: Azure OpenAI endpoint URL (or from OPENAI_ENDPOINT env var)
            api_version: API version (or from OPENAI_API_VERSION env var)
            deployment: Embedding model deployment name (or from OPENAI_EMBEDDING_DEPLOYMENT env var)
            dimension: Expected embedding dimension (or from OPENAI_EMBEDDING_DIMENSION env var)
        """
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        self.endpoint = endpoint or os.getenv("OPENAI_ENDPOINT")
        self.api_version = api_version or os.getenv("OPENAI_API_VERSION", "2024-12-01-preview")
        self.deployment = deployment or os.getenv("OPENAI_EMBEDDING_DEPLOYMENT", "text-embedding-3-small")
        self.dimension = dimension or int(os.getenv("OPENAI_EMBEDDING_DIMENSION", "1536"))
        self.version = os.getenv("EMBEDDING_VERSION", "v1")

        if not self.api_key or not self.endpoint:
            raise ValueError(
                "Azure OpenAI credentials not configured. "
                "Set OPENAI_API_KEY and OPENAI_ENDPOINT in .env"
            )

        # Initialize Azure OpenAI client
        self.client = AzureOpenAI(
            api_key=self.api_key,
            api_version=self.api_version,
            azure_endpoint=self.endpoint
        )

        logger.info(
            f"Embedding client initialized: deployment={self.deployment}, "
            f"dimension={self.dimension}, version={self.version}"
        )

    def generate_embedding(
        self,
        text: str,
        include_metadata: bool = True
    ) -> Dict[str, Any]:
        """
        Generate embedding for a single text

        Args:
            text: Input text to embed
            include_metadata: Include embedding metadata in response

        Returns:
            Dictionary with 'embedding' and optionally metadata fields
        """
        if not text or not text.strip():
            raise ValueError("Cannot generate embedding for empty text")

        try:
            response = self.client.embeddings.create(
                model=self.deployment,
                input=text
            )

            embedding = response.data[0].embedding

            # Verify dimension
            if len(embedding) != self.dimension:
                logger.warning(
                    f"Embedding dimension mismatch: got {len(embedding)}, "
                    f"expected {self.dimension}"
                )

            result = {"embedding": embedding}

            if include_metadata:
                result.update(self._generate_metadata(text))

            return result

        except Exception as e:
            logger.error(f"Failed to generate embedding: {e}", exc_info=True)
            raise

    def generate_embeddings_batch(
        self,
        texts: List[str],
        include_metadata: bool = True
    ) -> List[Dict[str, Any]]:
        """
        Generate embeddings for multiple texts in a single API call

        Args:
            texts: List of input texts to embed
            include_metadata: Include embedding metadata in responses

        Returns:
            List of dictionaries with 'embedding' and optionally metadata fields
        """
        if not texts:
            return []

        # Filter out empty texts
        valid_texts = [t for t in texts if t and t.strip()]
        if not valid_texts:
            raise ValueError("Cannot generate embeddings for all empty texts")

        try:
            response = self.client.embeddings.create(
                model=self.deployment,
                input=valid_texts
            )

            results = []
            for i, data in enumerate(response.data):
                embedding = data.embedding

                # Verify dimension
                if len(embedding) != self.dimension:
                    logger.warning(
                        f"Embedding dimension mismatch for text {i}: "
                        f"got {len(embedding)}, expected {self.dimension}"
                    )

                result = {"embedding": embedding}

                if include_metadata:
                    result.update(self._generate_metadata(valid_texts[i]))

                results.append(result)

            logger.info(f"Generated {len(results)} embeddings in batch")
            return results

        except Exception as e:
            logger.error(f"Failed to generate batch embeddings: {e}", exc_info=True)
            raise

    def _generate_metadata(self, text: str) -> Dict[str, Any]:
        """
        Generate embedding metadata

        Args:
            text: Source text that was embedded

        Returns:
            Dictionary with metadata fields
        """
        return {
            "embedding_model": self.deployment,
            "embedding_version": self.version,
            "embedding_dimension": self.dimension,
            "embedding_created_at": datetime.utcnow().isoformat() + "Z",
            "embedding_text_hash": self._hash_text(text)
        }

    @staticmethod
    def _hash_text(text: str) -> str:
        """
        Generate SHA256 hash of text for change detection

        Args:
            text: Text to hash

        Returns:
            Hex-encoded SHA256 hash with 'sha256:' prefix
        """
        hash_hex = hashlib.sha256(text.encode('utf-8')).hexdigest()
        return f"sha256:{hash_hex}"

    def verify_connection(self) -> bool:
        """
        Verify that the Azure OpenAI connection works

        Returns:
            True if connection is working, False otherwise
        """
        try:
            self.generate_embedding("test", include_metadata=False)
            return True
        except Exception as e:
            logger.error(f"Connection verification failed: {e}")
            return False

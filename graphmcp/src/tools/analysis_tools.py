"""
Knowledge graph analysis tools for GraphRAG MCP Server
"""

import json
import logging
import re
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)


def register(mcp, graph_client):
    """
    Register knowledge graph analysis tools with the MCP server.

    Args:
        mcp: FastMCP server instance
        graph_client: GraphClient instance
    """

    @mcp.tool()
    def analyze_knowledge_graph() -> str:
        """
        Perform comprehensive analysis of the knowledge graph structure.
        
        This tool dynamically analyzes the graph to extract:
        - High-degree nodes (hubs and important entities)
        - Multi-label nodes
        - Relationship type patterns and distributions
        - Important property patterns
        - Node and relationship statistics
        - Property value distributions
        - Sample data for each entity type
        - Ontology inference (core concepts, hierarchies)
        - Business context (operational model, key processes)
        - Semantic context for LLM interpretation
        
        Returns:
            Comprehensive graph analysis including structure, patterns, and insights
            
        Example Response:
        {
            "success": true,
            "statistics": {
                "totalNodes": 83077,
                "totalRelationships": 86427,
                "nodeLabelCount": 4,
                "relationshipTypeCount": 4,
                "averageDegree": 2.08
            },
            "highDegreeNodes": [...],
            "nodeLabels": [...],
            "relationshipTypes": [...],
            "ontology": {...},
            "businessContext": {...}
        }
        """
        try:
            logger.info("analyze_knowledge_graph called")
            
            # Use a single session with transaction for all queries
            graph_client._ensure_connection()
            
            with graph_client.driver.session() as session:
                with session.begin_transaction() as tx:
                    # 1. Get high-degree nodes (hubs) - excluding embeddings
                    high_degree_query = """
                    MATCH (n)-[r]-()
                    WITH n, labels(n) as node_labels, count(r) as degree, 
                         [key in keys(n) WHERE NOT key =~ '.*embedding.*' | [key, n[key]]] as filtered_props
                    WHERE degree > 2
                    WITH node_labels, degree, apoc.map.fromPairs(filtered_props) as properties
                    RETURN node_labels, properties, degree
                    ORDER BY degree DESC
                    LIMIT 10
                    """
                    result = tx.run(high_degree_query)
                    high_degree_nodes = [dict(record) for record in result]
                    
                    # 2. Get multi-label nodes - excluding embeddings
                    multi_label_query = """
                    MATCH (n)
                    WHERE size(labels(n)) > 1
                    WITH n, labels(n) as node_labels,
                         [key in keys(n) WHERE NOT key =~ '.*embedding.*' | [key, n[key]]] as filtered_props
                    WITH node_labels, apoc.map.fromPairs(filtered_props) as properties
                    RETURN node_labels as labels, properties
                    LIMIT 20
                    """
                    result = tx.run(multi_label_query)
                    multi_label_nodes = [dict(record) for record in result]
                    
                    # 3. Analyze relationship types with source/target labels
                    relationship_types_query = """
                    MATCH (a)-[r]->(b)
                    RETURN DISTINCT 
                        labels(a) as source_label, 
                        type(r) as relationship_type, 
                        labels(b) as target_label, 
                        count(*) as count
                    ORDER BY count DESC
                    """
                    result = tx.run(relationship_types_query)
                    relationship_types = [dict(record) for record in result]
                    
                    # 4. Find important property patterns (excluding embeddings)
                    property_patterns_query = """
                    MATCH (n)
                    WHERE any(key in keys(n) WHERE 
                        key =~ '.*(name|id|type|status).*' AND
                        NOT key =~ '.*embedding.*' AND
                        n[key] IS NOT NULL
                    )
                    WITH n, labels(n) as labels,
                         [key in keys(n) WHERE NOT key =~ '.*embedding.*' | [key, n[key]]] as filtered_props,
                         [key in keys(n) WHERE key =~ '.*(name|id|type|status).*' AND NOT key =~ '.*embedding.*' | key] as important_props
                    RETURN 
                        labels,
                        apoc.map.fromPairs(filtered_props) as properties,
                        important_props
                    ORDER BY size(important_props) DESC
                    LIMIT 10
                    """
                    result = tx.run(property_patterns_query)
                    property_patterns = [dict(record) for record in result]
                    
                    # 5. Get node labels and counts
                    node_labels_query = """
                    MATCH (n)
                    UNWIND labels(n) AS label
                    RETURN label, count(*) AS count
                    ORDER BY count DESC
                    """
                    result = tx.run(node_labels_query)
                    node_labels = [dict(record) for record in result]
                    
                    # 6. Get relationship type counts
                    rel_types_query = """
                    MATCH ()-[r]->()
                    RETURN DISTINCT type(r) AS type, count(*) AS count
                    ORDER BY count DESC
                    """
                    result = tx.run(rel_types_query)
                    rel_types = [dict(record) for record in result]
                    
                    # 7. Get node properties by label - excluding embeddings
                    node_properties_query = """
                    MATCH (n) 
                    UNWIND labels(n) AS label 
                    WITH DISTINCT label 
                    MATCH (n) 
                    WHERE label IN labels(n)
                    WITH label, [key in keys(n) WHERE NOT key =~ '.*embedding.*' | key] as filtered_keys
                    RETURN label, collect(DISTINCT filtered_keys)[0] AS props
                    """
                    result = tx.run(node_properties_query)
                    label_properties = [dict(record) for record in result]
                    
                    # 8. Get property value distributions (excluding embeddings) - sampled
                    property_values_query = """
                    MATCH (n) 
                    WITH n
                    LIMIT 1000
                    UNWIND labels(n) AS label 
                    WITH label, n
                    UNWIND keys(n) AS property
                    WITH label, property, n[property] as prop_value
                    WHERE NOT property =~ '.*embedding.*'
                    WITH label, property, collect(DISTINCT prop_value)[0..5] AS sampleValues
                    WHERE size(sampleValues) > 0
                    RETURN label, property, sampleValues
                    LIMIT 50
                    """
                    result = tx.run(property_values_query)
                    property_values = [dict(record) for record in result]
                    
                    # 9. Get sample nodes (up to 3 per label) - excluding embeddings
                    node_samples_query = """
                    MATCH (n)
                    WITH labels(n) AS node_labels, n,
                         [key in keys(n) WHERE NOT key =~ '.*embedding.*' | [key, n[key]]] as filtered_props
                    WITH node_labels, apoc.map.fromPairs(filtered_props) as properties
                    UNWIND node_labels AS label
                    WITH label, collect({properties: properties})[..3] AS samples
                    RETURN label, samples
                    """
                    result = tx.run(node_samples_query)
                    node_samples = [dict(record) for record in result]
                    
                    # 10. Get sample relationships (up to 3 per type) - excluding embeddings
                    rel_samples_query = """
                    MATCH (s)-[r]->(t)
                    WITH type(r) AS rel_type, s, t, r,
                         [key in keys(r) WHERE NOT key =~ '.*embedding.*' | [key, r[key]]] as filtered_props
                    WITH rel_type, labels(s) as source_labels, labels(t) as target_labels,
                         apoc.map.fromPairs(filtered_props) as properties
                    WITH rel_type, 
                         collect({
                           source_labels: source_labels, 
                           target_labels: target_labels, 
                           properties: properties
                         })[..3] AS samples
                    RETURN rel_type as type, samples
                    """
                    result = tx.run(rel_samples_query)
                    rel_samples = [dict(record) for record in result]
                    
                    # Commit transaction
                    tx.commit()
            
            # Calculate statistics
            total_nodes = sum(item["count"] for item in node_labels)
            total_relationships = sum(item["count"] for item in rel_types)
            node_label_count = len(node_labels)
            relationship_type_count = len(rel_types)
            average_degree = (total_relationships * 2) / total_nodes if total_nodes > 0 else 0
            
            # Infer ontology
            ontology = _infer_ontology(node_labels, relationship_types)
            
            # Infer business context
            business_context = _infer_business_context(relationship_types, node_labels)
            
            return json.dumps({
                "success": True,
                "statistics": {
                    "totalNodes": total_nodes,
                    "totalRelationships": total_relationships,
                    "nodeLabelCount": node_label_count,
                    "relationshipTypeCount": relationship_type_count,
                    "averageDegree": round(average_degree, 2),
                    "timestamp": str(graph_client.execute_query("RETURN timestamp() as ts")[0]["ts"])
                },
                "highDegreeNodes": high_degree_nodes[:10],
                "multiLabelNodes": multi_label_nodes[:10],
                "nodeLabels": node_labels,
                "relationshipTypes": relationship_types,
                "labelProperties": label_properties,
                "propertyValueSamples": property_values,
                "importantPropertyPatterns": property_patterns,
                "sampleData": {
                    "nodeSamples": node_samples,
                    "relationshipSamples": rel_samples
                },
                "ontology": ontology,
                "businessContext": business_context
            }, indent=2, default=str)
            
        except Exception as e:
            logger.error(f"Error in analyze_knowledge_graph: {str(e)}", exc_info=True)
            return json.dumps({"error": str(e), "success": False})

    @mcp.tool()
    def aggregate_by_relationship(
        entity_type: str,
        group_by: str,
        metrics: List[str] = None,
        filters: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Dynamically aggregate entities by traversing relationships (data-agnostic).
        
        This tool discovers the graph schema at runtime and builds aggregation
        queries without hardcoding relationship names or properties.
        
        Args:
            entity_type: Node type to aggregate (e.g., "FuelTransaction", "Mission")
            group_by: Related entity to group by (e.g., "vendor", "incident")
            metrics: List of aggregation metrics:
                    - "count": Count entities
                    - "sum_<property>": Sum a numeric property
                    - "avg_<property>": Average a numeric property
            filters: Optional property filters to apply to source entities
                    Dictionary of property_name: value pairs (e.g., {"fuel_type": "MoGas"})
        
        Returns:
            Aggregated results grouped by the related entity
        
        Example Call:
            aggregate_by_relationship(
                "FuelTransaction",
                group_by="vendor",
                metrics=["count", "sum_amount_dispensed"]
            )
        
        Example Call with Filters:
            aggregate_by_relationship(
                "FuelTransaction",
                group_by="vendor",
                metrics=["count", "sum_amount_dispensed"],
                filters={"fuel_type": "MoGas"}
            )
        
        Example Response:
        {
            "success": true,
            "entity_type": "FuelTransaction",
            "group_by": "vendor",
            "discovered_relationship": "FROM_VENDOR",
            "grouped_by_property": "Vendor.fuel_vendor_name",
            "aggregations": [
                {"vendor": "World Kinect", "count": 2798, "sum_amount_dispensed": 38666.9},
                {"vendor": "Macro", "count": 447, "sum_amount_dispensed": 13185.1}
            ]
        }
        """
        try:
            if metrics is None:
                metrics = ["count"]
            
            # Step 1: Discover relationships for this entity type
            discover_query = f"""
            MATCH (n:{entity_type})-[r]-(m)
            WITH type(r) as rel_type, labels(m)[0] as target_label,
                 CASE WHEN startNode(r) = n THEN 'outgoing' ELSE 'incoming' END as direction
            RETURN DISTINCT rel_type, target_label, direction
            """
            
            relationships = graph_client.execute_query(discover_query)
            
            # Find the relationship matching group_by
            target_rel = None
            for rel in relationships:
                # Match by relationship name (case-insensitive)
                if group_by.lower() in rel['target_label'].lower():
                    target_rel = rel
                    break
            
            if not target_rel:
                return json.dumps({
                    "success": False,
                    "error": f"No relationship found matching '{group_by}' for {entity_type}",
                    "available_relationships": relationships
                })
            
            # Step 2: Discover properties of target entity
            target_label = target_rel['target_label']
            props_query = f"""
            MATCH (n:{target_label})
            UNWIND keys(n) as key
            RETURN DISTINCT key as property, 'String' as type
            LIMIT 20
            """
            
            properties = graph_client.execute_query(props_query)
            
            # Find a name-like property (prefer *_name, then name, then id)
            group_property = None
            for prop in properties:
                p = prop['property']
                if '_name' in p.lower():
                    group_property = p
                    break
            if not group_property:
                for prop in properties:
                    if prop['property'] == 'name':
                        group_property = prop['property']
                        break
            if not group_property and properties:
                group_property = properties[0]['property']
            
            if not group_property:
                return json.dumps({
                    "success": False,
                    "error": f"No properties found for {target_label}"
                })
            
            # Step 3: Build dynamic aggregation query
            rel_type = target_rel['rel_type']
            direction = target_rel['direction']
            
            if direction == 'outgoing':
                rel_pattern = f"-[:{rel_type}]->"
            else:
                rel_pattern = f"<-[:{rel_type}]-"
            
            # Build metrics
            metric_expressions = []
            for metric in metrics:
                if metric == "count":
                    metric_expressions.append("count(n) as count")
                elif metric.startswith("sum_"):
                    prop = metric[4:]
                    metric_expressions.append(f"sum(n.{prop}) as {metric}")
                elif metric.startswith("avg_"):
                    prop = metric[4:]
                    metric_expressions.append(f"avg(n.{prop}) as {metric}")
            
            # Build WHERE clause from filters
            where_clauses = []
            if filters:
                for prop, value in filters.items():
                    if isinstance(value, str):
                        where_clauses.append(f"n.{prop} = '{value}'")
                    else:
                        where_clauses.append(f"n.{prop} = {value}")
            
            where_clause = f"WHERE {' AND '.join(where_clauses)}" if where_clauses else ""
            
            # Determine ORDER BY metric (prefer count, then first sum metric, then first metric)
            order_by_metric = "count" if "count" in metrics else metrics[0] if metrics else "count"
            
            # Build final query
            agg_query = f"""
            MATCH (n:{entity_type}){rel_pattern}(target:{target_label})
            {where_clause}
            RETURN target.{group_property} as {group_by}_{group_property},
                   {', '.join(metric_expressions)}
            ORDER BY {order_by_metric} DESC
            """
            
            results = graph_client.execute_query(agg_query)
            
            # Format results
            aggregations = []
            for record in results:
                agg_data = {group_by: record[f"{group_by}_{group_property}"]}
                for metric in metrics:
                    if metric in record:
                        agg_data[metric] = record[metric]
                aggregations.append(agg_data)
            
            return json.dumps({
                "success": True,
                "entity_type": entity_type,
                "group_by": group_by,
                "discovered_relationship": rel_type,
                "grouped_by_property": f"{target_label}.{group_property}",
                "aggregations": aggregations
            }, indent=2, default=str)
            
        except Exception as e:
            logger.error(f"Error in aggregate_by_relationship: {str(e)}", exc_info=True)
            return json.dumps({"error": str(e), "success": False})

    logger.info("Analysis tools registered successfully")


def _infer_ontology(node_labels: List[Dict], relationship_types: List[Dict]) -> Dict:
    """Infer ontology from graph structure."""
    # Find core concepts (nodes with many relationships)
    concept_counts = {}
    for rel in relationship_types:
        source = rel.get("source_label", [])
        target = rel.get("target_label", [])
        count = rel.get("count", 0)
        
        if isinstance(source, list) and source:
            source_label = source[0]
            concept_counts[source_label] = concept_counts.get(source_label, 0) + count
        
        if isinstance(target, list) and target:
            target_label = target[0]
            concept_counts[target_label] = concept_counts.get(target_label, 0) + count
    
    # Sort and get top concepts
    core_concepts = sorted(concept_counts.items(), key=lambda x: x[1], reverse=True)[:10]
    core_concepts = [label for label, _ in core_concepts]
    
    # Find hierarchical relationships
    hierarchical_rels = []
    for rel in relationship_types:
        rel_type = rel.get("relationship_type", "")
        if any(keyword in rel_type.upper() for keyword in ["SUBCLASS", "TYPE_OF", "KIND_OF", "PARENT", "CHILD"]):
            source = rel.get("source_label", [])
            target = rel.get("target_label", [])
            hierarchical_rels.append({
                "source": source[0] if isinstance(source, list) and source else str(source),
                "type": rel_type,
                "target": target[0] if isinstance(target, list) and target else str(target)
            })
    
    return {
        "coreConcepts": core_concepts,
        "hierarchicalRelationships": hierarchical_rels,
        "entityTypes": [item["label"] for item in node_labels]
    }


def _infer_business_context(relationship_types: List[Dict], node_labels: List[Dict]) -> Dict:
    """Infer business context from graph structure."""
    # Count relationship types
    rel_type_counts = {}
    for rel in relationship_types:
        rel_type = rel.get("relationship_type", "")
        count = rel.get("count", 0)
        rel_type_counts[rel_type] = rel_type_counts.get(rel_type, 0) + count
    
    # Find key processes (common relationship types)
    key_processes = sorted(rel_type_counts.items(), key=lambda x: x[1], reverse=True)[:10]
    key_processes = [rel_type for rel_type, _ in key_processes]
    
    # Calculate entity connections
    entity_connections = {}
    for item in node_labels:
        label = item["label"]
        entity_connections[label] = {"in": 0, "out": 0, "total": 0, "types": set()}
    
    for rel in relationship_types:
        source = rel.get("source_label", [])
        target = rel.get("target_label", [])
        rel_type = rel.get("relationship_type", "")
        count = rel.get("count", 0)
        
        source_label = source[0] if isinstance(source, list) and source else str(source)
        target_label = target[0] if isinstance(target, list) and target else str(target)
        
        if source_label in entity_connections:
            entity_connections[source_label]["out"] += count
            entity_connections[source_label]["total"] += count
            entity_connections[source_label]["types"].add(rel_type)
        
        if target_label in entity_connections:
            entity_connections[target_label]["in"] += count
            entity_connections[target_label]["total"] += count
            entity_connections[target_label]["types"].add(rel_type)
    
    # Find key entities
    key_entities = sorted(
        entity_connections.items(),
        key=lambda x: (x[1]["total"], len(x[1]["types"])),
        reverse=True
    )[:10]
    key_entities = [label for label, _ in key_entities]
    
    # Infer operational model
    operational_model = "General Graph"
    domain_indicators = {
        "missionCritical": r"MISSION|RESOURCE|TASK|OBJECTIVE|INCIDENT|VENDOR",
        "supplyChain": r"ORDER|SUPPLY|DELIVERY|SHIPMENT",
        "socialNetwork": r"FRIEND|FOLLOWS|LIKES|MEMBER",
        "knowledgeGraph": r"RELATED_TO|SUBCLASS|INSTANCE_OF|PART_OF"
    }
    
    domain_scores = {domain: 0 for domain in domain_indicators}
    
    for item in node_labels:
        label = item["label"]
        count = item["count"]
        for domain, pattern in domain_indicators.items():
            if re.search(pattern, label, re.IGNORECASE):
                domain_scores[domain] += count
    
    for rel in relationship_types:
        rel_type = rel.get("relationship_type", "")
        count = rel.get("count", 0)
        for domain, pattern in domain_indicators.items():
            if re.search(pattern, rel_type, re.IGNORECASE):
                domain_scores[domain] += count
    
    # Determine most likely domain
    max_domain = max(domain_scores.items(), key=lambda x: x[1])
    if max_domain[1] > 0:
        operational_model = max_domain[0].replace("C", " C").replace("N", " N").replace("G", " G").title()
    
    total_nodes = sum(item["count"] for item in node_labels)
    total_rels = sum(rel.get("count", 0) for rel in relationship_types)
    
    return {
        "operationalModel": operational_model,
        "keyProcesses": key_processes,
        "keyEntities": key_entities,
        "totalEntityTypes": len(node_labels),
        "graphMetrics": {
            "nodeCount": total_nodes,
            "relationshipCount": total_rels,
            "relationshipTypes": len(rel_type_counts),
            "averageNodeDegree": round((total_rels * 2) / total_nodes, 2) if total_nodes > 0 else 0
        }
    }

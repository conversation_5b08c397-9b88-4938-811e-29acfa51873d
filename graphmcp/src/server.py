"""
Simple GraphRAG MCP Server - without lifespan complexity
"""

import os
import logging
from fastmcp import FastMCP
from dotenv import load_dotenv

from .tools import schema_tools, search_tools, traversal_tools, aggregation_tools, admin_tools, mage_tools, vector_search_tools, analysis_tools, embedding_tools
from .graph.client import GraphClient
from .graph.schema_manager import SchemaManager
from .config import get_server_config

# Load environment
load_dotenv()

# Get server configuration
config = get_server_config()

# Configure logging
logging.basicConfig(
    level=getattr(logging, config["log_level"]),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize graph client and schema manager immediately
logger.info("Initializing Memgraph connection...")
graph_client = GraphClient(
    host=config["memgraph_host"],
    port=config["memgraph_port"],
    user=config["memgraph_user"],
    password=config["memgraph_password"],
    ssl=config["memgraph_ssl"]
)

schema_manager = SchemaManager(graph_client)

# Initialize embedding client (if vector search enabled)
embedding_client = None
if config.get("enable_vector_search", False):
    try:
        from .embeddings import create_embedding_client
        embedding_client = create_embedding_client()
        logger.info("Embedding client initialized successfully via factory")
    except Exception as e:
        logger.warning(f"Failed to initialize embedding client: {str(e)}")
        logger.warning("Vector search tools will not be available")

# Initialize schema cache on startup
logger.info("Initializing schema cache...")
try:
    schema_manager.refresh_cache()
    logger.info("Schema cache initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize schema cache: {str(e)}", exc_info=True)

# Initialize MCP server
mcp = FastMCP(
    name=config["server_name"],
    instructions=(
        "This MCP server interfaces with Memgraph graph database for emergency management data. "
        "It provides tools for schema discovery, entity search, graph traversal, aggregation, "
        "and administrative operations. Use the available tools to query and analyze the "
        "emergency management knowledge graph."
    )
)

# Register all tools
logger.info("Registering MCP tools...")
schema_tools.register(mcp, schema_manager)
search_tools.register(mcp, graph_client, schema_manager)
traversal_tools.register(mcp, graph_client)
aggregation_tools.register(mcp, graph_client)
admin_tools.register(mcp, graph_client, schema_manager)
mage_tools.register(mcp, graph_client)
analysis_tools.register(mcp, graph_client)

# Register vector search tools (if embedding client available)
if embedding_client is not None:
    vector_search_tools.register(mcp, graph_client, embedding_client)
    embedding_tools.register(mcp, graph_client, embedding_client)
    logger.info("Vector search and embedding tools registered")
else:
    logger.info("Vector search and embedding tools skipped (embedding client not initialized)")

logger.info(f"MCP server configured successfully")

# Add health check endpoint for HTTP mode
@mcp.custom_route("/health", methods=["GET"])
async def health_check(_):
    """Health check endpoint for HTTP transport."""
    from starlette.responses import JSONResponse

    health_status = {
        "status": "healthy",
        "server_name": config["server_name"],
        "transport": config["transport"],
        "memgraph_connected": graph_client is not None and graph_client.verify_connection()
    }
    return JSONResponse(health_status)
# Example environment configuration for load_gold_to_local_memgraph.py
# Copy to .env (cp env.kg .env) and update with your workspace-specific values.

# Databricks workspace base URL (no trailing slash)
DATABRICKS_HOST="https://adb-1234567890123456.9.azuredatabricks.net"

# Personal access token with access to the SQL warehouse and target gold table
DATABRICKS_TOKEN="dapiXXXXXXXXXXXXXXXXXXXXXXXX"

# SQL Warehouse identifier (found in Databricks SQL Warehouses UI)
DATABRICKS_WAREHOUSE_ID="1234567890abcdef"

# Catalog that hosts the gold.knowledge_graph table
# Optional: defaults to graysky-dev-catalog when unset
DATABRICKS_CATALOG="graysky-dev-catalog"

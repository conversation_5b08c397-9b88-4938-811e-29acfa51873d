"""
Administrative tools for GraphRAG MCP Server
"""

import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


def register(mcp, graph_client, schema_manager):
    """
    Register administrative tools with the MCP server.

    Args:
        mcp: FastMCP server instance
        graph_client: GraphClient instance
        schema_manager: SchemaManager instance
    """

    @mcp.tool()
    def get_health() -> str:
        """
        Get server and database health status.

        Returns:
            Comprehensive health report including:
            - Database connectivity
            - Query performance metrics
            - Cache status
            - Memory usage
            - Error rates

        Example Response:
        {
            "server_status": "healthy",
            "database_connection": true,
            "node_count": 5234,
            "relationship_count": 8765,
            "cache_status": {
                "cached": true,
                "cache_age_seconds": 120,
                "last_updated": "2024-01-15T10:30:00"
            },
            "performance": {
                "avg_query_time_ms": 45,
                "total_queries": 1523
            }
        }
        """
        try:
            logger.info("get_health called")

            health_data = {
                "server_status": "healthy",
                "database_connection": False,
                "node_count": 0,
                "relationship_count": 0,
                "cache_status": schema_manager.get_cache_status(),
                "timestamp": datetime.utcnow().isoformat()
            }

            # Test database connection
            try:
                test_result = graph_client.execute_query("RETURN 1 as test")
                if test_result:
                    health_data["database_connection"] = True

                    # Get node and relationship counts
                    health_data["node_count"] = graph_client.get_node_count()
                    health_data["relationship_count"] = graph_client.get_relationship_count()
            except Exception as e:
                health_data["database_error"] = str(e)
                health_data["server_status"] = "degraded"

            return json.dumps(health_data, indent=2, default=str)

        except Exception as e:
            logger.error(f"Error in get_health: {str(e)}", exc_info=True)
            return json.dumps({
                "server_status": "error",
                "error": str(e)
            })

    @mcp.tool()
    def refresh_cache() -> str:
        """
        Refresh the schema cache and performance statistics.

        Returns:
            Cache refresh results and updated statistics

        Example Response:
        {
            "status": "success",
            "cache_refreshed": true,
            "old_cache_size": 2048,
            "new_cache_size": 2156,
            "refresh_timestamp": "2024-01-15T10:30:00"
        }
        """
        try:
            logger.info("refresh_cache called")

            old_cache_size = schema_manager.get_cache_size()

            # Refresh schema cache (async in production)
            schema_manager.refresh_cache()

            new_cache_size = schema_manager.get_cache_size()

            return json.dumps({
                "status": "success",
                "cache_refreshed": True,
                "old_cache_size": old_cache_size,
                "new_cache_size": new_cache_size,
                "refresh_timestamp": datetime.utcnow().isoformat()
            })

        except Exception as e:
            logger.error(f"Error in refresh_cache: {str(e)}", exc_info=True)
            return json.dumps({
                "status": "error",
                "error_message": str(e)
            })

    @mcp.tool()
    def get_query_suggestions(
        entity_type: str,
        intent: Optional[str] = None
    ) -> str:
        """
        Get suggested queries for common operations on an entity type.

        Args:
            entity_type: The entity type to get suggestions for
            intent: Optional intent like "analysis", "relationships", "patterns"

        Returns:
            List of suggested query patterns with descriptions

        Example Call:
            get_query_suggestions("Incident", "analysis")

        Example Response:
        {
            "entity_type": "Incident",
            "intent": "analysis",
            "suggestions": [
                {
                    "description": "Find high-severity incidents",
                    "query": "MATCH (i:Incident) WHERE i.severity >= 4 RETURN i",
                    "parameters": {}
                },
                {
                    "description": "Analyze incident response times",
                    "query": "MATCH (r:Resource)-[resp:RESPONDED_TO]->(i:Incident) RETURN i.type, avg(resp.response_time)",
                    "parameters": {}
                }
            ]
        }
        """
        try:
            logger.info(f"get_query_suggestions called: entity_type={entity_type}, intent={intent}")

            suggestions = []

            if entity_type == "Incident":
                if intent == "analysis" or intent is None:
                    suggestions.extend([
                        {
                            "description": "Find high-severity incidents",
                            "query": f"MATCH (i:Incident) WHERE i.severity >= 4 RETURN i LIMIT 20",
                            "parameters": {}
                        },
                        {
                            "description": "Analyze incident types distribution",
                            "query": f"MATCH (i:Incident) RETURN i.type, count(i) as count ORDER BY count DESC",
                            "parameters": {}
                        },
                        {
                            "description": "Find recent incidents",
                            "query": f"MATCH (i:Incident) WHERE i.timestamp > $start_time RETURN i ORDER BY i.timestamp DESC",
                            "parameters": {"start_time": "2024-01-01T00:00:00"}
                        }
                    ])

                if intent == "relationships" or intent is None:
                    suggestions.extend([
                        {
                            "description": "Find resources that responded to incidents",
                            "query": f"MATCH (r:Resource)-[:RESPONDED_TO]->(i:Incident) RETURN i, r LIMIT 20",
                            "parameters": {}
                        },
                        {
                            "description": "Find departments managing incidents",
                            "query": f"MATCH (d:Department)-[:MANAGES]->(i:Incident) RETURN d, count(i) as incidents",
                            "parameters": {}
                        }
                    ])

            elif entity_type == "Resource":
                if intent == "analysis" or intent is None:
                    suggestions.extend([
                        {
                            "description": "Find available resources",
                            "query": f"MATCH (r:Resource) WHERE r.status = 'available' RETURN r",
                            "parameters": {}
                        },
                        {
                            "description": "Analyze resource utilization",
                            "query": f"MATCH (r:Resource)-[resp:RESPONDED_TO]->(i) RETURN r.id, count(i) as incidents_handled",
                            "parameters": {}
                        }
                    ])

                if intent == "patterns" or intent is None:
                    suggestions.extend([
                        {
                            "description": "Find resource allocation patterns",
                            "query": f"MATCH (r:Resource)-[:ALLOCATED_TO]->(i:Incident) RETURN r.type, i.type, count(*) as allocations",
                            "parameters": {}
                        }
                    ])

            elif entity_type == "Department":
                suggestions.extend([
                    {
                        "description": "Find department coordination",
                        "query": f"MATCH (d1:Department)-[:COORDINATES_WITH]-(d2:Department) RETURN d1, d2",
                        "parameters": {}
                    },
                    {
                        "description": "Analyze department workload",
                        "query": f"MATCH (d:Department)-[:MANAGES|RESPONDS_TO]->(i:Incident) RETURN d.name, count(i) as workload",
                        "parameters": {}
                    }
                ])

            return json.dumps({
                "entity_type": entity_type,
                "intent": intent,
                "suggestion_count": len(suggestions),
                "suggestions": suggestions
            }, indent=2)

        except Exception as e:
            logger.error(f"Error in get_query_suggestions: {str(e)}", exc_info=True)
            return json.dumps({"error": str(e), "success": False})

    @mcp.tool()
    def get_database_info() -> str:
        """
        Get detailed information about the Memgraph database.

        Returns:
            Database version, configuration, and capabilities

        Example Response:
        {
            "database": "Memgraph",
            "version": "2.14.0",
            "storage_info": {
                "nodes": 5234,
                "relationships": 8765,
                "properties": 15234
            },
            "indexes": [...],
            "constraints": [...]
        }
        """
        try:
            logger.info("get_database_info called")

            info = {
                "database": "Memgraph",
                "host": graph_client.host,
                "port": graph_client.port,
                "ssl_enabled": graph_client.ssl,
                "storage_info": {
                    "nodes": graph_client.get_node_count(),
                    "relationships": graph_client.get_relationship_count()
                }
            }

            # Try to get Memgraph version
            try:
                version_result = graph_client.execute_query("SHOW VERSION")
                if version_result:
                    info["version"] = version_result[0].get("version", "unknown")
            except:
                info["version"] = "unknown"

            # Get indexes
            try:
                index_result = graph_client.execute_query("SHOW INDEX INFO")
                info["indexes"] = index_result
            except:
                info["indexes"] = []

            # Get cached schema info
            schema = json.loads(schema_manager.get_cached_schema())
            info["entity_types"] = len(schema.get("entity_types", {}))
            info["relationship_types"] = len(schema.get("relationship_types", {}))

            return json.dumps(info, indent=2, default=str)

        except Exception as e:
            logger.error(f"Error in get_database_info: {str(e)}", exc_info=True)
            return json.dumps({"error": str(e), "success": False})

    @mcp.tool()
    def clear_graph() -> str:
        """
        Clear all data from the graph database (use with caution!).

        This is a dangerous operation that will delete all nodes and relationships.
        Only use in development or test environments.

        Returns:
            Confirmation of the operation

        Example Response:
        {
            "success": true,
            "message": "Graph cleared",
            "nodes_deleted": 5234,
            "relationships_deleted": 8765
        }
        """
        try:
            logger.warning("clear_graph called - this will delete all data!")

            # Get counts before deletion
            node_count = graph_client.get_node_count()
            rel_count = graph_client.get_relationship_count()

            # Clear the graph
            clear_query = "MATCH (n) DETACH DELETE n"

            # This is a write operation, so we use execute_write_query
            result = graph_client.execute_write_query(clear_query)

            # Refresh cache after clearing
            schema_manager.refresh_cache()

            return json.dumps({
                "success": True,
                "message": "Graph cleared",
                "nodes_deleted": node_count,
                "relationships_deleted": rel_count,
                "warning": "All data has been deleted from the graph"
            })

        except Exception as e:
            logger.error(f"Error in clear_graph: {str(e)}", exc_info=True)
            return json.dumps({"error": str(e), "success": False})

    logger.info("Admin tools registered successfully")
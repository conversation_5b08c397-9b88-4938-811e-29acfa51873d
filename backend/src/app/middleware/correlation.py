"""
FastAPI middleware for correlation ID tracking and request logging.

This middleware:
- Extracts or generates correlation IDs for all requests
- Sets correlation ID in async context for all downstream code
- Logs request/response information with structured data
- Handles streaming responses appropriately
"""

import time
from typing import Callable
from uuid import uuid4

import structlog
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from app.core.logging import set_correlation_id, get_api_logger


class CorrelationMiddleware(BaseHTTPMiddleware):
    """Middleware to handle correlation ID tracking and request logging."""
    
    def __init__(self, app: ASGIApp, correlation_header: str = "X-Correlation-ID"):
        super().__init__(app)
        self.correlation_header = correlation_header
        self.logger = get_api_logger()
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request with correlation tracking and logging."""
        
        # Extract or generate correlation ID
        correlation_id = request.headers.get(self.correlation_header, str(uuid4()))
        set_correlation_id(correlation_id)
        
        # Start timing
        start_time = time.time()
        
        # Log incoming request
        self.logger.info("Request started",
                        method=request.method,
                        url=str(request.url),
                        path=request.url.path,
                        user_agent=request.headers.get("user-agent"),
                        client_ip=request.client.host if request.client else None)
        
        try:
            # Process request
            response = await call_next(request)
            
            # Calculate duration
            duration = time.time() - start_time
            
            # Log successful response
            self.logger.info("Request completed",
                           method=request.method,
                           url=str(request.url),
                           path=request.url.path,
                           status_code=response.status_code,
                           duration_seconds=round(duration, 3),
                           response_size=response.headers.get("content-length"))
            
            # Add correlation ID to response headers
            response.headers[self.correlation_header] = correlation_id
            
            return response
            
        except Exception as e:
            # Calculate duration
            duration = time.time() - start_time
            
            # Log error
            self.logger.error("Request failed",
                            method=request.method,
                            url=str(request.url),
                            path=request.url.path,
                            duration_seconds=round(duration, 3),
                            error_type=type(e).__name__,
                            error_message=str(e),
                            exc_info=True)
            
            # Re-raise the exception
            raise
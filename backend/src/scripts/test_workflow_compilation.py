"""
Test that workflow compiles without errors.

Run: uv run python scripts/test_workflow_compilation.py
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.agent.langgraph.workflows import get_graph, get_all_tools
from app.core.logging import get_logger

logger = get_logger(__name__)


async def test_compilation():
    """Test workflow compilation."""
    print("=" * 60)
    print("STEP 1: Testing workflow compilation...")
    print("=" * 60)
    print()

    # Test tool discovery
    print("1. Testing tool discovery...")
    try:
        tools = await get_all_tools()
        print(f"   ✓ Discovered {len(tools)} tools")
        if tools:
            print(f"   ✓ Sample tools: {', '.join([t.name for t in tools[:3]])}")
        else:
            print("   ⚠ Warning: No tools discovered (MCP servers may be down)")
    except Exception as e:
        print(f"   ✗ Tool discovery failed: {e}")
        return False

    print()

    # Test graph compilation
    print("2. Testing graph compilation...")
    try:
        graph = await get_graph()
        print("   ✓ Graph compiled successfully")
    except Exception as e:
        print(f"   ✗ Graph compilation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

    print()

    # Test graph visualization (optional)
    print("3. Testing graph visualization...")
    try:
        from app.agent.langgraph.workflows import visualize_workflow
        mermaid = visualize_workflow(graph)
        if mermaid:
            print("   ✓ Graph visualization generated")
            print()
            print("   Graph structure:")
            print("   " + "\n   ".join(mermaid.split("\n")[:20]))  # First 20 lines
            if len(mermaid.split("\n")) > 20:
                print("   ... (truncated)")
        else:
            print("   ⚠ Could not generate visualization (not critical)")
    except Exception as e:
        print(f"   ⚠ Visualization failed (not critical): {e}")

    print()
    print("=" * 60)
    print("✓ All compilation tests passed!")
    print("=" * 60)
    return True


if __name__ == "__main__":
    result = asyncio.run(test_compilation())
    sys.exit(0 if result else 1)

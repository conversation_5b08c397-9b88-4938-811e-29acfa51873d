"""
Analyze relationship_types section of get_schema output
"""
import json
import os

# Get the directory where this script is located
script_dir = os.path.dirname(os.path.abspath(__file__))
response_file = os.path.join(script_dir, "get_schema_full_response.json")

with open(response_file, "r") as f:
    data = json.load(f)

print("RELATIONSHIP_TYPES SECTION ANALYSIS")
print("=" * 80)

rel_types = data.get("relationship_types", {})
print(f"Number of relationship types: {len(rel_types)}\n")

for rel_name, rel_data in rel_types.items():
    rel_json = json.dumps(rel_data, indent=2, default=str)
    size = len(rel_json)

    # Check for samples
    has_samples = "samples" in rel_data
    samples_count = len(rel_data.get("samples", [])) if has_samples else 0

    print(f"{rel_name}:")
    print(f"  Size: {size:,} characters")
    print(f"  Has samples: {has_samples}")
    if has_samples:
        print(f"  Number of samples: {samples_count}")

        # Get size without samples
        rel_without_samples = {k: v for k, v in rel_data.items() if k != "samples"}
        size_without = len(json.dumps(rel_without_samples, indent=2, default=str))
        samples_size = size - size_without
        print(f"  Samples size: {samples_size:,} characters ({samples_size/size*100:.1f}%)")
    print()

# Calculate total size without samples
print("\n" + "=" * 80)
print("TOTAL RELATIONSHIP_TYPES WITHOUT SAMPLES")
print("=" * 80)

rel_types_without_samples = {}
for rel_name, rel_data in rel_types.items():
    rel_types_without_samples[rel_name] = {
        k: v for k, v in rel_data.items() if k != "samples"
    }

original_size = len(json.dumps(rel_types, indent=2, default=str))
size_without = len(json.dumps(rel_types_without_samples, indent=2, default=str))

print(f"Original relationship_types size: {original_size:,} characters")
print(f"Without samples: {size_without:,} characters")
print(f"Savings: {original_size - size_without:,} characters")

# Core MCP and server
fastmcp>=2.11.3          # MCP server framework
mcp>=1.13.0             # MCP protocol
python-dotenv>=1.1.1    # Environment management

# Graph database - Memgraph
gqlalchemy>=1.3.3       # Memgraph OGM and query builder
neo4j>=5.14.0           # Can also work with Memgraph's Bolt protocol
pymgclient>=1.3.1       # Native Memgraph client (optional, for direct connection)

# Data handling
pydantic>=2.0.0         # Data validation
pandas>=2.0.0           # Data manipulation
pyyaml>=6.0             # YAML parsing
numpy>=1.24.0           # Numerical operations

# Local embeddings (ONNX)
onnxruntime>=1.15.0     # ONNX runtime for local inference
transformers>=4.30.0    # Tokenizers for embedding models

# Logging and monitoring
structlog>=23.2.0       # Structured logging
prometheus-client>=0.19 # Metrics (optional)

# Testing
pytest>=7.4.0           # Testing framework
pytest-asyncio>=0.21.0  # Async test support
pytest-mock>=3.11.0     # Mocking support
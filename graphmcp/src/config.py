"""
Configuration module for the GraphRAG MCP server.

This module provides functions to get and set server configuration.
"""

import os
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

def get_server_config() -> Dict[str, Any]:
    """
    Get the server configuration from environment variables.

    Returns:
        dict: A dictionary containing server configuration.
    """
    # Get transport type and HTTP port from environment variables
    transport = os.getenv("MCP_TRANSPORT", "stdio")  # Default to stdio for backward compatibility
    http_port_str = os.getenv("HTTP_PORT", os.getenv("MCP_HTTP_PORT", "8000"))

    try:
        http_port = int(http_port_str)
    except ValueError:
        logger.warning(f"Invalid HTTP port: {http_port_str}, using default 8000")
        http_port = 8000

    # Get Memgraph configuration
    memgraph_host = os.getenv("MEMGRAPH_HOST", "localhost")
    memgraph_port = int(os.getenv("MEMGRAPH_PORT", "7687"))
    memgraph_user = os.getenv("MEMGRAPH_USER", "")
    memgraph_password = os.getenv("MEMGRAPH_PASSWORD", "")
    memgraph_ssl = os.getenv("MEMGRAPH_SSL", "false").lower() == "true"

    # Get server configuration
    server_name = os.getenv("SERVER_NAME", "GraphRAG-MCP")
    log_level = os.getenv("LOG_LEVEL", "INFO")

    # Performance settings
    cache_ttl = int(os.getenv("CACHE_TTL_SECONDS", "300"))
    max_traversal_depth = int(os.getenv("MAX_TRAVERSAL_DEPTH", "5"))
    default_limit = int(os.getenv("DEFAULT_LIMIT", "100"))
    max_limit = int(os.getenv("MAX_LIMIT", "1000"))
    query_timeout = int(os.getenv("QUERY_TIMEOUT_SECONDS", "30"))

    # Vector search settings
    enable_vector_search = os.getenv("ENABLE_VECTOR_SEARCH", "false").lower() == "true"

    # Security settings
    allow_custom_cypher = os.getenv("ALLOW_CUSTOM_CYPHER", "false").lower() == "true"

    # Return the config
    return {
        "transport": transport,
        "http_port": http_port,
        "memgraph_host": memgraph_host,
        "memgraph_port": memgraph_port,
        "memgraph_user": memgraph_user,
        "memgraph_password": memgraph_password,
        "memgraph_ssl": memgraph_ssl,
        "server_name": server_name,
        "log_level": log_level,
        "cache_ttl": cache_ttl,
        "max_traversal_depth": max_traversal_depth,
        "default_limit": default_limit,
        "max_limit": max_limit,
        "query_timeout": query_timeout,
        "enable_vector_search": enable_vector_search,
        "allow_custom_cypher": allow_custom_cypher
    }

def configure_server_env() -> Dict[str, Any]:
    """
    Configure the server environment variables for FastMCP.

    This function sets environment variables that FastMCP uses.
    """
    config = get_server_config()

    # Configure FastMCP settings via environment variables
    if config["transport"] in ["streamable-http", "http", "sse"]:
        # Set host and port for HTTP transport
        os.environ["FASTMCP_HOST"] = os.getenv("HOST", "127.0.0.1")
        os.environ["FASTMCP_PORT"] = str(config["http_port"])

    # Set logging level
    os.environ["FASTMCP_LOG_LEVEL"] = config["log_level"]

    return config
"""
Embedding Generation Tools for GraphRAG MCP Server

Provides MCP tool wrappers for embedding generation and management.
"""

import json
import logging
from src.utils.embedding_utils import (
    generate_embeddings_impl,
    create_vector_index_impl,
    list_vector_indices_impl
)

logger = logging.getLogger(__name__)


def register(mcp, graph_client, embedding_client):
    """
    Register embedding generation tools with the MCP server.

    Args:
        mcp: FastMCP server instance
        graph_client: GraphClient instance
        embedding_client: EmbeddingClient instance for embedding generation
    """

    @mcp.tool()
    def generate_embeddings(
        node_type: str,
        text_property: str,
        limit: int = 100,
        force_regenerate: bool = False
    ) -> str:
        """
        Generate embeddings for nodes of a specific type based on a text property.
        
        This tool creates vector embeddings for graph nodes using their text content,
        enabling semantic search and similarity operations.
        
        Args:
            node_type: Type of nodes to process (e.g., "Mission", "Incident", "Organization")
            text_property: Property containing text to embed (e.g., "title", "description", "name")
            limit: Maximum number of nodes to process (default: 100)
            force_regenerate: If True, regenerate embeddings even if they exist (default: False)
        
        Returns:
            JSON response with embedding generation results
            
        Example Call:
            generate_embeddings("Mission", "title", limit=50)
            # Generates embeddings for Mission.title field for up to 50 missions
            
        Example Call with Force Regenerate:
            generate_embeddings("Organization", "name", limit=20, force_regenerate=True)
            # Regenerates all embeddings even if they already exist
        """
        try:
            result = generate_embeddings_impl(
                graph_client,
                embedding_client,
                node_type,
                text_property,
                limit,
                force_regenerate
            )
            return json.dumps(result, indent=2)
        except Exception as e:
            logger.error(f"Error in generate_embeddings: {str(e)}", exc_info=True)
            return json.dumps({
                "success": False,
                "error": str(e),
                "details": "Failed to generate embeddings. Check database connection and embedding service."
            })

    @mcp.tool()
    def create_vector_index(
        index_name: str,
        node_label: str,
        embedding_property: str = "embedding",
        dimension: int = 1536,
        metric: str = "cosine"
    ) -> str:
        """
        Create a vector index for semantic search on embeddings.
        
        This tool creates a Memgraph vector index to enable fast similarity search
        on node embeddings using the MAGE vector_search module.
        
        Args:
            index_name: Name for the vector index (e.g., "mission_embeddings")
            node_label: Node label to index (e.g., "Mission", "Organization")
            embedding_property: Property containing embeddings (default: "embedding")
            dimension: Embedding vector dimension (default: 1536 for text-embedding-ada-002)
            metric: Distance metric ("cosine", "euclidean", or "dot") (default: "cosine")
        
        Returns:
            JSON response with index creation status
            
        Example Call:
            create_vector_index("mission_embeddings", "Mission", dimension=1536)
            # Creates vector index for Mission.embedding with 1536 dimensions
        """
        try:
            result = create_vector_index_impl(
                graph_client,
                index_name,
                node_label,
                embedding_property,
                dimension,
                metric
            )
            return json.dumps(result, indent=2)
        except Exception as e:
            logger.error(f"Error in create_vector_index: {str(e)}", exc_info=True)
            return json.dumps({
                "success": False,
                "error": str(e),
                "details": "Failed to create vector index. Ensure MAGE vector_search module is installed."
            })

    @mcp.tool()
    def list_vector_indices() -> str:
        """
        List all vector indices in the database.
        
        Returns information about existing vector indices including their
        configuration and status.
        
        Returns:
            JSON response with list of vector indices
        """
        try:
            result = list_vector_indices_impl(graph_client)
            return json.dumps(result, indent=2)
        except Exception as e:
            logger.error(f"Error in list_vector_indices: {str(e)}", exc_info=True)
            return json.dumps({
                "success": False,
                "error": str(e),
                "details": "Failed to list vector indices. Ensure MAGE vector_search module is installed."
            })

    logger.info("Embedding tools registered successfully")

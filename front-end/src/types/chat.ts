/**
 * TypeScript type definitions for GraySkyGenAI Assistant Chat API
 */

export type StreamType = 'STATUS' | 'REASONING' | 'SOURCES' | 'CONTENT';

export interface StreamMessage {
  type: StreamType;
  data: any;
  metadata?: any;
}

export interface ChatRequest {
  message: string;
  conversation_id: string;
}

export interface StreamCallbacks {
  onStatus?: (data: any, metadata?: any) => void;
  onReasoning?: (data: any, metadata?: any) => void;
  onSources?: (data: any, metadata?: any) => void;
  onContent?: (data: any, metadata?: any) => void;
  onError?: (error: Error) => void;
  onComplete?: () => void;
}

export interface StreamController {
  close: () => void;
}

export interface Message {
  id: string;
  sender: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  isStreaming?: boolean;
}

export interface DebugMessage {
  type: StreamType;
  timestamp: Date;
  data: any;
}

export type ConnectionStatus = 'connected' | 'disconnected' | 'error' | 'streaming';

export interface HealthResponse {
  status: string;
  message?: string;
}
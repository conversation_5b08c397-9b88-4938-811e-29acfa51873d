# Local Embedding Model - Next Steps

## Current Status

✅ Code implemented for dual embedding support (local + Azure)
✅ Dependencies added (sentence-transformers, torch)
❌ **BLOCKED**: Container can't download BGE model from HuggingFace due to SSL certificate verification error

Immediate next step:
- Download model on host machine (where SSL works) and mount in container - link: https://huggingface.co/BAAI/bge-small-en-v1.5/tree/main
- Then update LocalEmbeddingClient to use local path
- Get it working with minimal code changes
- Filter embeddings out of results from all tools so that embedding doesn't take up context
- Then refactor to use FastEmbed if needed

## Decision Point

**PREFERRED: Option B - Switch to FastEmbed** (Future Enhancement)
- FastEmbed verified to support BAAI/bge-small-en-v1.5 (384 dims)
- Source: https://qdrant.github.io/fastembed/examples/Supported_Models/
- Simpler API with `specific_model_path` parameter
- May have faster model loading
- **Status**: Deferred - requires refactoring LocalEmbeddingClient

**CURRENT: Option A - SentenceTransformer with Local Path** (Immediate Fix)
- Keep current implementation (sentence-transformers already integrated)
- Download model on host to `graphmcp/local_model/`
- Update LocalEmbeddingClient to use `cache_folder` and `local_files_only=True`
- Mount local_model directory in docker-compose
- **Status**: Proceeding with this approach to unblock vector search

## Next Steps (Either Option)

1. **Download model on host** (where SSL works)
   ```bash
   # Create directory
   mkdir -p graphmcp/local_model

   # Option A: Download with sentence-transformers
   uv run python -c "from sentence_transformers import SentenceTransformer; SentenceTransformer('BAAI/bge-small-en-v1.5', cache_folder='./graphmcp/local_model')"

   # Option B: Download with fastembed
   # (need to test download command)
   ```

2. **Update LocalEmbeddingClient** to use local path
   - Add `cache_folder` or `specific_model_path` parameter
   - Set `local_files_only=True` (prevents download attempts)

3. **Mount in docker-compose.yml**
   ```yaml
   volumes:
     - ./graphmcp/local_model:/app/local_model:ro
   ```

4. **Update .env**
   ```bash
   LOCAL_EMBEDDING_MODEL_PATH=/app/local_model  # Or specific subdirectory
   ```

5. **Test container startup** - should load model without network access

## Files to Modify

- `graphmcp/src/embeddings/local_client.py` - Add local path support
- `graphmcp/docker-compose.yml` (parent) - Add volume mount
- `graphmcp/.env` - Add MODEL_PATH config
- `graphmcp/.env.example` - Document MODEL_PATH

## Current Error

```
SSL: CERTIFICATE_VERIFY_FAILED - self-signed certificate in certificate chain
```

Container trying to download from huggingface.co at server startup (src/server.py:45 → factory.py → local_client.py:48).

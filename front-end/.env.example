# Frontend Docker Compose Configuration
# This file is for docker-compose orchestration ONLY

# Build Configuration
BUILD_TARGET=development  # Options: development, production
CONTAINER_NAME=frontend
FRONTEND_PORT=3000

# Docker Network Configuration
NETWORK_NAME=frontend-network
EXTERNAL_NETWORK=false  # Set to true in production to join existing network

# Development Settings
NODE_ENV=development  # Options: development, production
WATCHPACK_POLLING=true  # Enables file watching in Docker (needed for hot-reload)

# Backend API URL (must be accessible from browser)
# For local dev: http://localhost:8000
# For production: https://api.yourdomain.com
NEXT_PUBLIC_API_URL=http://localhost:8000

# Notes:
# - Copy this file to .env for local standalone development
# - When using parent docker-compose, backend URL is automatically configured
# - Production deployments should set NEXT_PUBLIC_API_URL to actual backend URL
# - NEXT_PUBLIC_* variables are embedded in frontend build at build time
# - These variables are visible in the browser - do NOT put secrets here
# - For production: Set BUILD_TARGET=production, NODE_ENV=production

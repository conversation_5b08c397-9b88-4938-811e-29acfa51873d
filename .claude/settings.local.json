{"permissions": {"allow": ["<PERSON><PERSON>(docker ps:*)", "Read(//Users/<USER>/Repos/DEMES/**)", "<PERSON><PERSON>(test:*)", "<PERSON><PERSON>(docker:*)", "<PERSON><PERSON>(docker-compose up:*)", "<PERSON><PERSON>(graphrag-mcp:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(uv run:*)", "<PERSON><PERSON>(docker-compose:*)", "Bash(/dev/null)", "Bash(lsof:*)", "WebSearch", "WebFetch(domain:langchain-ai.github.io)", "WebFetch(domain:memgraph.com)", "WebFetch(domain:neo4j.com)", "WebFetch(domain:blog.langchain.com)", "WebFetch(domain:github.com)", "WebFetch(domain:python.langchain.com)", "<PERSON><PERSON>(make:*)", "<PERSON><PERSON>(cat:*)", "Bash(timeout 5 bash -c 'cat < /dev/null > /dev/tcp/localhost/7687')", "Bash(bash -c 'cat < /dev/null > /dev/tcp/localhost/7687')", "mcp__graphrag__get_schema", "mcp__graphrag__get_node_statistics", "mcp__graphrag__get_relationship_statistics", "mcp__graphrag__refresh_cache", "mcp__graphrag__get_health", "mcp__graphrag__describe_entity_type", "mcp__graphrag__search_entities", "mcp__graphrag__describe_relationship_type", "mcp__graphrag__search_by_pattern", "mcp__graphrag__execute_cypher", "mcp__graphrag__check_entity_availability", "mcp__graphrag__aggregate_by_relationship", "mcp__graphrag__aggregate_by_type"], "deny": [], "ask": []}, "outputStyle": "default"}
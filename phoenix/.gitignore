# Environment variables (contains sensitive database credentials)
.env

# Docker Compose override files (may contain local customizations)
docker-compose.override.yml

# PostgreSQL data directory (if using local bind mount instead of named volume)
postgres_data/
data/
pgdata/

# Phoenix logs and temporary files
*.log
logs/
tmp/

# Backup files
*.bak
*.backup
*.sql.gz

# OS specific files
.DS_Store
Thumbs.db

# Editor/IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Docker build context (if building custom images)
.dockerignore

# Local development overrides
docker-compose.local.yml
docker-compose.dev.yml
# Dev files
CLAUDE.md

# Embedding Workflow Strategies for Dynamic Graph Management

## Context

Your graph is frequently updated with two patterns:
1. **Full Override** - Complete graph replacement (current primary method)
2. **Incremental Loads** - Adding new data without full replacement (future)

Additionally, schema evolves over time:
- Current: Comments as property arrays
- Future: Comments as separate nodes requiring embeddings
- Ongoing: New entity types and properties

**Question:** What are the optimal strategies for generating and maintaining embeddings?

---

## Research Summary

Based on Neo4j, Memgraph, and GraphRAG research:

### Key Findings:
1. **Incremental embedding updates** are 10-100x faster than full regeneration
2. **Trigger-based automation** works well for real-time requirements
3. **ETL pipeline embedding** reduces operational complexity
4. **Text hash tracking** enables efficient change detection
5. **Hybrid approaches** are most robust for production systems

---

## Strategy Options

### Option 1: Pre-Load Embedding (ETL Pipeline)

**Description:** Generate embeddings BEFORE loading data into Memgraph.

**Workflow:**
```
Data Source (CSV/JSON/API)
    ↓
Data Transformation
    ↓
Embedding Generation (parallel batch processing)
    ↓
Add embedding properties to data
    ↓
Load into Memgraph (with embeddings already attached)
    ↓
Create Vector Indices (one-time)
```

**Advantages:**
- ✅ **Fastest graph load** - No post-load processing delay
- ✅ **Simple rollback** - Reload without embedding concerns
- ✅ **Predictable costs** - Know embedding costs before load
- ✅ **Parallel processing** - Can generate embeddings for millions of records concurrently
- ✅ **Testing isolation** - Test embeddings before graph deployment

**Disadvantages:**
- ❌ **ETL pipeline complexity** - Need to add embedding step to data pipeline
- ❌ **Coupling** - Embedding logic lives outside graph infrastructure
- ❌ **Re-embedding overhead** - Full override = regenerate all embeddings
- ❌ **Schema changes** - Pipeline code changes when adding new entity types

**Best For:**
- ✅ Scheduled batch loads (daily, weekly)
- ✅ Full override patterns
- ✅ Stable schemas
- ✅ Large-scale data imports

**Implementation:**
```python
# In your ETL pipeline (outside graphmcp)
def enrich_missions_with_embeddings(missions_df):
    """Add embeddings to missions before loading"""
    from openai import AzureOpenAI

    client = AzureOpenAI(...)
    texts = missions_df['title'].tolist()

    # Batch embed (100 at a time for efficiency)
    for i in range(0, len(texts), 100):
        batch = texts[i:i+100]
        response = client.embeddings.create(
            model="text-embedding-3-small",
            input=batch
        )

        # Add embeddings to dataframe
        for j, data in enumerate(response.data):
            idx = i + j
            missions_df.at[idx, 'embedding'] = data.embedding
            missions_df.at[idx, 'embedding_model'] = "text-embedding-3-small"
            missions_df.at[idx, 'embedding_version'] = "v1"

    return missions_df

# Then load with embeddings
load_to_memgraph(missions_df)
```

---

### Option 2: Post-Load Embedding (GraphMCP Scripts)

**Description:** Load data into Memgraph first, then generate embeddings using GraphMCP tools.

**Workflow:**
```
Data Source
    ↓
Load into Memgraph (no embeddings)
    ↓
Run scripts/generate_embeddings.py (GraphMCP)
    ↓
Update nodes with embeddings
    ↓
Vector search enabled
```

**Advantages:**
- ✅ **Decoupled from ETL** - ETL pipeline stays simple
- ✅ **Centralized embedding logic** - All embedding code in GraphMCP
- ✅ **Graph-aware** - Can use graph structure to inform embeddings
- ✅ **Selective re-embedding** - Only update changed nodes (with text hash tracking)
- ✅ **Schema flexibility** - Easy to add embeddings to new entity types

**Disadvantages:**
- ❌ **Slower graph availability** - Wait for post-load embedding generation
- ❌ **Error handling** - Graph loaded but embeddings may fail
- ❌ **Resource contention** - Embedding generation competes with graph queries
- ❌ **Manual execution** - Need to remember to run embedding script after loads

**Best For:**
- ✅ Irregular loads
- ✅ Evolving schemas
- ✅ Mixed full/incremental patterns
- ✅ Development and testing

**Implementation:**
```bash
# After loading data
python scripts/generate_embeddings.py --entity-types Mission,Incident,Resource

# With selective re-embedding (only changed text)
python scripts/generate_embeddings.py --incremental --check-hash
```

---

### Option 3: Trigger-Based Auto-Embedding (Real-Time)

**Description:** Use Memgraph triggers to automatically generate embeddings when nodes are created/updated.

**Workflow:**
```
Load data into Memgraph
    ↓
Trigger fires on node CREATE/UPDATE
    ↓
Call GraphMCP embedding service (async)
    ↓
Update node with embedding
    ↓
Immediately available for vector search
```

**Advantages:**
- ✅ **Fully automated** - No manual script execution
- ✅ **Real-time** - Embeddings generated immediately
- ✅ **Incremental by nature** - Perfect for incremental loads
- ✅ **No post-load wait** - Each node ready as soon as created
- ✅ **Always in sync** - Embeddings never stale

**Disadvantages:**
- ❌ **Complex setup** - Requires trigger infrastructure + async service
- ❌ **Performance impact** - Embedding API calls during graph writes
- ❌ **Error handling complexity** - What if embedding fails during transaction?
- ❌ **Cost unpredictability** - Harder to control API call costs
- ❌ **Full override inefficiency** - Generates embeddings for all nodes even on full replace

**Best For:**
- ✅ High-frequency incremental updates
- ✅ Real-time requirements
- ✅ Stable schemas
- ❌ NOT recommended for full override patterns

**Implementation:**
```cypher
-- Memgraph trigger (simplified - actual implementation more complex)
CREATE TRIGGER auto_embed_mission
ON CREATE NODE
WHEN created_node :Mission
EXECUTE
  CALL embedding_service.generate_and_update(created_node);
```

**Challenge:** Memgraph triggers execute Cypher, not Python, so need:
- External embedding service (REST API)
- Or custom query module in Python/C++
- Complex async handling

---

### Option 4: Hybrid Approach (RECOMMENDED)

**Description:** Combine ETL embedding for full loads with post-load tooling for updates.

**Workflow:**
```
FULL OVERRIDE:
  Data Source → ETL with embeddings → Load into Memgraph

INCREMENTAL LOAD:
  Data Source → Load into Memgraph → Run selective re-embedding script

SCHEMA CHANGES:
  Use GraphMCP scripts to add embeddings to new entity types
```

**Advantages:**
- ✅ **Best of both worlds** - Fast for full loads, flexible for updates
- ✅ **Efficient** - Full loads come pre-embedded, incrementals update only changed nodes
- ✅ **Flexible** - Can handle schema evolution
- ✅ **Cost-effective** - No unnecessary re-embedding
- ✅ **Robust** - Multiple fallback mechanisms

**Disadvantages:**
- ❌ **Two codebases** - Embedding logic in ETL AND GraphMCP
- ❌ **Coordination** - Must keep ETL and GraphMCP embedding logic in sync
- ⚠️ **Model consistency** - Both must use same model/version

**Best For:**
- ✅ **Production systems with mixed patterns** (YOUR USE CASE)
- ✅ Most flexible for evolving requirements
- ✅ Scales from development to production

---

## Recommended Strategy for Your Use Case

### Phase 1 (Current - Full Override Pattern)

**Approach: Post-Load Embedding (Option 2)**

**Rationale:**
- You're frequently doing full overrides
- Schema is evolving (comments → nodes)
- Development phase - need flexibility
- Simpler to implement and test

**Workflow:**
```bash
# 1. Load data into Memgraph (your current process)
./load_data.sh

# 2. Generate embeddings for all entity types
python scripts/generate_embeddings.py --all

# 3. Done - vector search enabled
```

**Implementation:**
- ✅ Create `scripts/generate_embeddings.py` with:
  - Entity type selection
  - Batch processing
  - Progress tracking
  - Text hash for change detection
  - Metadata tracking
- ✅ Create GraphMCP admin tools:
  - `refresh_embeddings` MCP tool
  - Status checking
  - Re-embedding single entities

---

### Phase 2 (Future - Incremental Loads)

**Approach: Hybrid (Option 4)**

**Rationale:**
- Full loads still use ETL embedding (fast)
- Incremental loads use post-load selective re-embedding
- Best performance for both patterns

**Workflow:**

**For Full Loads:**
```python
# In your ETL pipeline
missions_with_embeddings = generate_embeddings(missions_df)
load_to_memgraph(missions_with_embeddings)
```

**For Incremental Loads:**
```bash
# After incremental load
python scripts/generate_embeddings.py --incremental --entity-types Mission
```

**Implementation Strategy:**
1. Keep embedding client in GraphMCP (shared library)
2. Import embedding client in ETL pipeline
3. Use text hashes to detect changed entities
4. Only re-embed when text hash changes

---

### Phase 3 (Advanced - Schema Evolution)

**Handling New Entity Types (e.g., Comments as Nodes):**

```bash
# When comments become nodes
python scripts/generate_embeddings.py --entity-types Comment --backfill

# This will:
# 1. Find all Comment nodes without embeddings
# 2. Generate embeddings
# 3. Update nodes with metadata
```

**Automation Options:**

**Option A: CI/CD Integration**
```yaml
# Azure DevOps pipeline
steps:
  - script: ./load_data.sh
  - script: python scripts/generate_embeddings.py --all
  - script: python scripts/verify_embeddings.py
```

**Option B: GraphMCP Admin Tool**
```python
# Via MCP tool
await session.call_tool("refresh_all_embeddings", {
    "entity_types": ["Mission", "Incident", "Resource", "Comment"]
})
```

---

## Selective Re-Embedding Strategy

**Key Innovation: Text Hash Tracking**

Store hash of source text with embedding:
```python
{
    "embedding": [...],
    "embedding_text_hash": "sha256:abc123...",
    "embedding_model": "text-embedding-3-small"
}
```

**Selective Re-Embedding Logic:**
```python
def should_re_embed(node):
    # Get current text
    current_text = extract_text_for_embedding(node)
    current_hash = hashlib.sha256(current_text.encode()).hexdigest()

    # Compare with stored hash
    stored_hash = node.get('embedding_text_hash', '')
    stored_model = node.get('embedding_model', '')

    # Re-embed if:
    return (
        current_hash != stored_hash or  # Text changed
        stored_model != CURRENT_MODEL or  # Model upgraded
        'embedding' not in node  # No embedding yet
    )
```

**Benefits:**
- Only re-embed when necessary
- Skip unchanged entities
- Detect model version mismatches
- Efficient for large graphs

---

## Cost Analysis

**Scenario: 10,000 Missions, daily full overrides**

### Option 1 (Pre-Load ETL):
- **Daily:** 10,000 embeddings × $0.02/1M tokens × 100 tokens = **$0.02/day**
- **Annual:** **$7.30/year**
- **Pros:** Predictable, fast graph availability
- **Cons:** Regenerate everything daily

### Option 2 (Post-Load with Hashing):
- **First run:** 10,000 embeddings = $0.02
- **Subsequent runs (10% changed):** 1,000 embeddings = **$0.002/day**
- **Annual:** **$0.73/year (10x cheaper)**
- **Pros:** Efficient, only embeds changed entities
- **Cons:** More complex logic

### Option 3 (Trigger-Based):
- **Daily:** Same as Option 1 if full override
- **With incremental:** Same as Option 2
- **Pros:** Automated
- **Cons:** Complex infrastructure

### Option 4 (Hybrid):
- **Full loads:** $0.02 (rare)
- **Incremental:** $0.002/day (common)
- **Annual estimate:** **$1-2/year**
- **Pros:** Optimal for mixed patterns
- **Cons:** Maintain two codebases

---

## Implementation Recommendations

### Immediate (Phase 1):

1. ✅ **Implement Post-Load Scripts:**
   - `scripts/generate_embeddings.py` with text hash tracking
   - `scripts/verify_embeddings.py` for validation
   - Progress tracking and resumability

2. ✅ **Add GraphMCP Admin Tools:**
   - `refresh_embeddings(entity_types, incremental)` MCP tool
   - `check_embedding_coverage()` MCP tool
   - Integrate into existing admin_tools.py

3. ✅ **Document Workflow:**
   - Update README with embedding workflow
   - Add runbook for data loads

### Short-Term (Phase 2):

4. ⏭️ **Move to Hybrid:**
   - Add embedding to ETL pipeline (full loads)
   - Keep post-load scripts for incremental/ad-hoc
   - Implement text hash checking in both paths

5. ⏭️ **Schema Evolution Support:**
   - Generic entity type detection
   - Automatic field selection for embeddings
   - Backfill utilities for new entity types

### Long-Term (Phase 3):

6. ⏭️ **Consider Trigger-Based (if needed):**
   - Only if real-time embedding becomes requirement
   - Requires significant infrastructure investment
   - Not recommended unless business case is clear

---

## Decision Matrix

| Criteria | Pre-Load ETL | Post-Load Scripts | Trigger-Based | Hybrid |
|----------|--------------|-------------------|---------------|--------|
| **Full Override Speed** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐⭐⭐ |
| **Incremental Efficiency** | ⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **Implementation Complexity** | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐ |
| **Schema Flexibility** | ⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **Cost Efficiency** | ⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **Operational Simplicity** | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ |
| **Error Recovery** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐⭐ |
| **Real-Time Support** | ❌ | ❌ | ✅ | ⚠️ |

---

## Recommended Path Forward

### For Your Situation:

**Start with Option 2 (Post-Load Scripts)**
- Implement `scripts/generate_embeddings.py` with text hash tracking
- Add MCP admin tools for embedding management
- Simple, flexible, works for current workflow

**Migrate to Option 4 (Hybrid) when:**
- Incremental loads become common (>50% of loads)
- Performance of full override becomes issue
- ETL pipeline infrastructure matures

**Consider Option 3 (Triggers) only if:**
- Real-time embedding becomes business requirement
- You have DevOps resources for infrastructure
- Incremental loads are extremely frequent (hourly+)

---

## Next Steps

**To proceed with recommended approach:**

1. ✅ Finish creating `scripts/generate_embeddings.py` (in progress)
2. ✅ Add text hash tracking to embedding generation
3. ✅ Create incremental mode (only re-embed changed entities)
4. ✅ Add MCP admin tools for embedding management
5. ✅ Test full workflow with current data
6. ✅ Document operational runbook

**Then evaluate:**
- Are full overrides taking too long? → Consider moving to hybrid with ETL embedding
- Need real-time updates? → Consider trigger-based approach
- Schema evolving frequently? → Current approach is already flexible

---

**Created:** October 1, 2025
**Research Sources:** Neo4j ETL patterns, Memgraph triggers, GraphRAG incremental updates, RotatH/IncDE research
**Recommendation:** Start with Post-Load Scripts (Option 2), migrate to Hybrid (Option 4) as needs evolve

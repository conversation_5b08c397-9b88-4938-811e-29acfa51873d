# GraphRAG MCP Server Docker Image
FROM python:3.12-slim

# Set working directory
WORKDIR /app

# Install system dependencies including build tools for pymgclient
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    cmake \
    build-essential \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy application files
COPY src/ ./src/
COPY main.py pyproject.toml README.md ./

# Copy environment configuration
COPY .env.example .env

# Install uv package manager
RUN pip install --no-cache-dir uv

# Set environment variables for HTTP transport
ENV MCP_TRANSPORT=streamable-http
ENV HTTP_PORT=8001
ENV HOST=0.0.0.0

# For Docker networking, use the Memgraph container name
ENV MEMGRAPH_HOST=graphrag-memgraph
ENV MEMGRAPH_PORT=7687

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8001/health || exit 1

# Expose the HTTP port
EXPOSE 8001

# Run the MCP server using uv
CMD ["uv", "run", "python", "main.py"]
#!/bin/bash

# Test direct MCP server call to get_schema
echo "Testing direct MCP server call..."

curl -s -X POST 'http://localhost:8001/mcp' \
  -H 'Content-Type: application/json' \
  -H 'Accept: application/json, text/event-stream' \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/call",
    "params": {
      "name": "get_schema",
      "arguments": {}
    }
  }' > /Users/<USER>/Repos/DEMES/assistant/dev_status/tmp/direct_mcp_response.json

echo "Response saved to direct_mcp_response.json"
echo ""
echo "Response size:"
wc -c < /Users/<USER>/Repos/DEMES/assistant/dev_status/tmp/direct_mcp_response.json

echo ""
echo "First 500 characters:"
head -c 500 /Users/<USER>/Repos/DEMES/assistant/dev_status/tmp/direct_mcp_response.json

echo ""
echo ""
echo "Checking for 'embedding' keyword:"
grep -o "embedding" /Users/<USER>/Repos/DEMES/assistant/dev_status/tmp/direct_mcp_response.json | wc -l

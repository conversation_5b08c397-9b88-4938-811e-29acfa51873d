[project]
name = "grayskygenai-assistant"
version = "0.1.0"
description = "FastAPI-based LLM Chat Application with intelligent agent capabilities"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "fastapi",
    "uvicorn[standard]",
    "pydantic",
    "pydantic-settings",
    "python-multipart",
    "openai",
    "mcp",
    "jsonschema",
    "structlog>=24.1.0",
    "opentelemetry-api",
    "opentelemetry-sdk",
    "opentelemetry-exporter-otlp-proto-http",
    "opentelemetry-instrumentation-fastapi",
    "opentelemetry-instrumentation-openai",
    "opentelemetry-instrumentation-httpx",
    "langgraph>=0.6.8",
    "langchain>=0.3.27",
    "langchain-core>=0.3.76",
    "langchain-openai>=0.3.33",
    "networkx>=3.4.2",
    "langchain-mcp-adapters==0.1.10",
    "langgraph-supervisor>=0.0.29",
    "neo4j>=6.0.2",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["app"]

[dependency-groups]
dev = [
    "pytest>=7.0",
    "pytest-asyncio",
    "pytest-cov",
    "httpx",
    "black",
    "ruff",
    "mypy",
    "langgraph-cli[inmem]>=0.4.2",
]

[tool.ruff]
line-length = 100
target-version = "py310"

[tool.ruff.lint]
select = ["E", "F", "I"]
ignore = ["E501"]

[tool.black]
line-length = 100
target-version = ["py310"]

[tool.mypy]
python_version = "3.10"
ignore_missing_imports = true
strict = false

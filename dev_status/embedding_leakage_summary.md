# Embedding Leakage in MCP Tools - Summary

## Questions Answered

### 1. Does get_schema use describe_entities?

**Answer: NO - Completely Separate**

**Code Evidence:**
- `get_schema` (schema_tools.py:67): Calls `schema_manager.get_cached_schema()`
- `describe_entity_type` (schema_manager.py:265): Separate method
- They share the cache but are independent tools

---

### 2. Do tools return samples with embeddings?

**Answer: YES - Multiple tools leak embeddings through samples**

#### ❌ HIGH RISK: describe_entity_type
**Location:** `graphmcp/src/graph/schema_manager.py:296-306`

**Code:**
```python
sample_query = f"""
MATCH (n:{entity_type})
RETURN n                    # ← Returns FULL node
LIMIT 3
"""
samples = self.graph_client.execute_query(sample_query)
entity_info["samples"] = samples  # ← Includes ALL properties including embeddings
```

**Impact:** Returns 3 full Mission nodes × 1536 floats each = ~27K chars of embedding bloat

---

#### ❌ HIGH RISK: describe_relationship_type
**Location:** `graphmcp/src/graph/schema_manager.py:330-339`

**Code:**
```python
sample_query = f"""
MATCH (a)-[r:{relationship_type}]->(b)
RETURN a, r, b              # ← Returns FULL nodes a and b
LIMIT 3
"""
samples = self.graph_client.execute_query(sample_query)
rel_info["samples"] = samples  # ← Includes embeddings in nodes
```

**Impact:** Returns 3 relationships with full source/target nodes (2 nodes × 1536 floats × 3 samples)

---

#### ✅ SAFE: analyze_knowledge_graph
**Location:** `graphmcp/src/tools/analysis_tools.py:71-207`

**Code:**
```python
# Consistently filters embeddings at lines 71, 86, 112, 154, 169, 182, 195:
WHERE NOT key =~ '.*embedding.*'
```

**Impact:** Safe - explicitly excludes embedding properties

---

### 3. Where else can embeddings leak?

**Complete Tool Audit:**

#### ❌ HIGH RISK (Returns full nodes WITHOUT filtering)

| Tool | Location | Query Pattern | Risk |
|------|----------|---------------|------|
| **describe_entity_type** | schema_manager.py:299 | `RETURN n` | 🔴 HIGH |
| **describe_relationship_type** | schema_manager.py:332 | `RETURN a, r, b` | 🔴 HIGH |
| **search_entities** | search_tools.py:109 | `RETURN n` | 🔴 HIGH |
| **get_entity** | search_tools.py:181+ | `RETURN n as entity` | 🔴 HIGH |
| **find_entities_by_time_range** | search_tools.py:273 | `RETURN n` | 🔴 HIGH |
| **get_neighbors** | traversal_tools.py | Likely `RETURN` nodes | 🔴 HIGH |
| **find_paths** | traversal_tools.py | Likely `RETURN` nodes | 🔴 HIGH |

#### ✅ SAFE (Does NOT return full nodes OR filters embeddings)

| Tool | Location | Protection |
|------|----------|------------|
| **get_schema** | schema_tools.py:67 | No samples returned |
| **analyze_knowledge_graph** | analysis_tools.py:182+ | Filters: `NOT key =~ '.*embedding.*'` |
| **get_node_statistics** | schema_tools.py:244+ | Only returns counts |
| **get_relationship_statistics** | schema_tools.py:286+ | Only returns counts |

---

## Recommended Fix

### Option 1: Filter Embeddings in GraphClient (Global Fix)
Add embedding filtering in `graph_client.execute_query()` to strip embeddings from ALL query results

**Pros:** Single point of fix, protects all tools
**Cons:** May impact tools that legitimately need embeddings

### Option 2: Fix Each Tool Individually
Add embedding filters to each query that returns nodes:

```python
# Before:
RETURN n

# After:
WITH [key in keys(n) WHERE NOT key =~ '.*embedding.*' | [key, n[key]]] as filtered_props
RETURN apoc.map.fromPairs(filtered_props) as n
```

**Pros:** Granular control, explicit
**Cons:** Need to fix ~7 tools

### Option 3: Post-Process in MCP Tool Returns
Strip embedding keys from results before returning JSON

```python
def _strip_embeddings(obj):
    """Recursively remove embedding fields from nested structures"""
    # Implementation
```

**Pros:** Works regardless of query structure
**Cons:** Performance overhead, complex recursion

---

## Recommendation

**Use Option 2** - Fix each tool's query individually with proper Cypher filtering.

**Priority Order:**
1. `describe_entity_type` - Most likely to be called by agents
2. `describe_relationship_type` - Also commonly used
3. `search_entities` - Frequently used for lookups
4. `get_entity` - Common single-entity retrieval
5. Others as needed

**Reference Implementation:**
See `analyze_knowledge_graph` (analysis_tools.py:182) for correct filtering pattern

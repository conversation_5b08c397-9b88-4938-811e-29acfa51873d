"""
Graph traversal tools for GraphRAG MCP Server
"""

import os
import json
import logging
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)


def register(mcp, graph_client):
    """
    Register graph traversal tools with the MCP server.

    Args:
        mcp: FastMCP server instance
        graph_client: GraphClient instance
    """

    @mcp.tool()
    def get_neighbors(
        entity_type: str,
        entity_id: str,
        relationship_types: Optional[List[str]] = None,
        direction: str = "both",
        depth: int = 1
    ) -> str:
        """
        Get neighbors of an entity with optional filtering.

        Args:
            entity_type: Starting node type
            entity_id: Starting node ID
            relationship_types: List of relationship types to traverse (None = all)
            direction: "incoming", "outgoing", or "both"
            depth: How many hops to traverse (1-5)

        Returns:
            Connected entities grouped by relationship type and depth

        Example Call:
            get_neighbors(
                "Incident",
                "INC-2024-1543",
                relationship_types=["RESPONDED_TO", "ALLOCATED_TO"],
                depth=2
            )

        Example Response:
        {
            "success": true,
            "start_entity": {"type": "Incident", "id": "INC-2024-1543"},
            "depth": 2,
            "neighbors": [
                {
                    "depth": 1,
                    "relationship_type": "RESPONDED_TO",
                    "direction": "incoming",
                    "entities": [
                        {"id": "RES-001", "type": "Resource", ...},
                        {"id": "DEPT-005", "type": "Department", ...}
                    ]
                },
                {
                    "depth": 2,
                    "relationship_type": "BELONGS_TO",
                    "direction": "outgoing",
                    "entities": [...]
                }
            ]
        }
        """
        try:
            logger.info(f"get_neighbors called: entity_type={entity_type}, entity_id={entity_id}, depth={depth}")

            # Limit depth for safety
            max_depth = int(os.getenv("MAX_TRAVERSAL_DEPTH", 5))
            depth = min(depth, max_depth)

            # Build relationship pattern
            if relationship_types:
                rel_pattern = f"[r:{' | '.join(relationship_types)}*1..{depth}]"
            else:
                rel_pattern = f"[r*1..{depth}]"

            # Build direction pattern
            if direction == "outgoing":
                pattern = f"(n:{entity_type} {{id: $entity_id}})-{rel_pattern}->(connected)"
            elif direction == "incoming":
                pattern = f"(n:{entity_type} {{id: $entity_id}})<-{rel_pattern}-(connected)"
            else:  # both
                pattern = f"(n:{entity_type} {{id: $entity_id}})-{rel_pattern}-(connected)"

            # Build query
            cypher = f"""
            MATCH path = {pattern}
            RETURN
                length(path) as depth,
                [rel in relationships(path) | type(rel)] as relationship_types,
                connected,
                properties(last(relationships(path))) as relationship_properties
            ORDER BY depth, relationship_types[0]
            """

            # Execute query
            results = graph_client.execute_query(
                cypher,
                {"entity_id": entity_id},
                limit=1000
            )

            # Group results by depth and relationship type
            grouped_neighbors = {}
            for record in results:
                depth_level = record["depth"]
                rel_type = record["relationship_types"][-1] if record["relationship_types"] else "UNKNOWN"

                key = f"{depth_level}_{rel_type}"
                if key not in grouped_neighbors:
                    grouped_neighbors[key] = {
                        "depth": depth_level,
                        "relationship_type": rel_type,
                        "entities": []
                    }

                entity_data = dict(record["connected"])
                if record.get("relationship_properties"):
                    entity_data["_relationship_properties"] = record["relationship_properties"]

                grouped_neighbors[key]["entities"].append(entity_data)

            return json.dumps({
                "success": True,
                "start_entity": {"type": entity_type, "id": entity_id},
                "depth": depth,
                "direction": direction,
                "neighbor_count": len(results),
                "neighbors": list(grouped_neighbors.values())
            }, indent=2, default=str)

        except Exception as e:
            logger.error(f"Error in get_neighbors: {str(e)}", exc_info=True)
            return json.dumps({"error": str(e), "success": False})

    @mcp.tool()
    def find_paths(
        from_type: str,
        from_id: str,
        to_type: str,
        to_id: str,
        max_depth: int = 5,
        relationship_types: Optional[List[str]] = None
    ) -> str:
        """
        Find paths between two specific entities.

        Args:
            from_type: Starting node type
            from_id: Starting node ID
            to_type: Target node type
            to_id: Target node ID
            max_depth: Maximum path length
            relationship_types: Allowed relationship types (None = all)

        Returns:
            All paths between entities with complete details

        Example Call:
            find_paths(
                "Resource", "RES-001",
                "Department", "DEPT-005",
                max_depth=3
            )

        Example Response:
        {
            "success": true,
            "from": {"type": "Resource", "id": "RES-001"},
            "to": {"type": "Department", "id": "DEPT-005"},
            "path_count": 2,
            "paths": [
                {
                    "length": 2,
                    "nodes": [...],
                    "relationships": [...]
                }
            ]
        }
        """
        try:
            logger.info(f"find_paths called: from {from_type}:{from_id} to {to_type}:{to_id}")

            # Limit depth for safety
            max_allowed_depth = int(os.getenv("MAX_TRAVERSAL_DEPTH", 5))
            max_depth = min(max_depth, max_allowed_depth)

            # Build relationship filter
            if relationship_types:
                rel_filter = f"[:{' | '.join(relationship_types)}*1..{max_depth}]"
            else:
                rel_filter = f"[*1..{max_depth}]"

            # Build query
            cypher = f"""
            MATCH path = (from:{from_type} {{id: $from_id}})-{rel_filter}-(to:{to_type} {{id: $to_id}})
            RETURN path
            ORDER BY length(path)
            LIMIT 10
            """

            # Execute query
            results = graph_client.execute_query(
                cypher,
                {"from_id": from_id, "to_id": to_id}
            )

            # Process paths
            paths = []
            for record in results:
                path_data = record["path"]

                # Extract nodes and relationships from path
                # Note: The exact structure depends on how Memgraph returns paths
                path_info = {
                    "length": len(path_data) // 2 if isinstance(path_data, list) else 1,
                    "path": path_data
                }
                paths.append(path_info)

            return json.dumps({
                "success": True,
                "from": {"type": from_type, "id": from_id},
                "to": {"type": to_type, "id": to_id},
                "max_depth": max_depth,
                "path_count": len(paths),
                "paths": paths
            }, indent=2, default=str)

        except Exception as e:
            logger.error(f"Error in find_paths: {str(e)}", exc_info=True)
            return json.dumps({"error": str(e), "success": False})

    @mcp.tool()
    def traverse_pattern(
        pattern: str,
        parameters: Optional[Dict[str, Any]] = None,
        limit: int = 100
    ) -> str:
        """
        Traverse the graph using a pattern with bound parameters.

        Args:
            pattern: Cypher-like pattern (simplified):
                    "(:Incident)-[:RESPONDED_TO]->(:Department)"
                    "(:Resource)-[:ALLOCATED_TO*1..3]->(:Incident)"
            parameters: Values for pattern variables
            limit: Maximum paths to return

        Returns:
            Matching paths with all entities and relationships

        Example Call:
            traverse_pattern(
                "(:Incident {severity: $severity})<-[:RESPONDED_TO]-(:FireStation)",
                {"severity": 5}
            )

        Example Response:
        {
            "success": true,
            "pattern": "...",
            "match_count": 15,
            "data": [...]
        }

        Note: The pattern is simplified - the server translates it to valid Cypher
        """
        try:
            logger.info(f"traverse_pattern called: pattern={pattern}")

            # Build query from pattern
            cypher = f"MATCH path = {pattern} RETURN path"

            # Validate it's a safe query
            is_safe, reason = graph_client.is_safe_query(cypher)
            if not is_safe:
                return json.dumps({
                    "success": False,
                    "error": f"Pattern rejected for safety: {reason}"
                })

            # Limit for safety
            max_limit = int(os.getenv("MAX_LIMIT", 1000))
            limit = min(limit, max_limit)

            # Execute query
            results = graph_client.execute_query(
                cypher,
                parameters or {},
                limit=limit
            )

            return json.dumps({
                "success": True,
                "pattern": pattern,
                "match_count": len(results),
                "limited": len(results) == limit,
                "data": results
            }, indent=2, default=str)

        except Exception as e:
            logger.error(f"Error in traverse_pattern: {str(e)}", exc_info=True)
            return json.dumps({"error": str(e), "success": False})

    @mcp.tool()
    def find_common_neighbors(
        entity_type1: str,
        entity_id1: str,
        entity_type2: str,
        entity_id2: str,
        relationship_types: Optional[List[str]] = None
    ) -> str:
        """
        Find entities that are connected to both specified entities.

        Args:
            entity_type1: First entity type
            entity_id1: First entity ID
            entity_type2: Second entity type
            entity_id2: Second entity ID
            relationship_types: Optional filter for relationship types

        Returns:
            Common neighbors with their relationships to both entities

        Example Call:
            find_common_neighbors(
                "Resource", "RES-001",
                "Resource", "RES-002"
            )

        Example Response:
        {
            "success": true,
            "entity1": {"type": "Resource", "id": "RES-001"},
            "entity2": {"type": "Resource", "id": "RES-002"},
            "common_neighbors": [
                {
                    "entity": {"id": "INC-001", "type": "Incident", ...},
                    "relationship_to_entity1": "RESPONDED_TO",
                    "relationship_to_entity2": "RESPONDED_TO"
                }
            ]
        }
        """
        try:
            logger.info(f"find_common_neighbors called: {entity_type1}:{entity_id1} and {entity_type2}:{entity_id2}")

            # Build relationship pattern
            if relationship_types:
                rel_pattern = f"[:{' | '.join(relationship_types)}]"
            else:
                rel_pattern = ""

            # Build query to find common neighbors
            cypher = f"""
            MATCH (e1:{entity_type1} {{id: $id1}})-[r1{rel_pattern}]-(common)
            MATCH (e2:{entity_type2} {{id: $id2}})-[r2{rel_pattern}]-(common)
            WHERE e1 <> e2
            RETURN DISTINCT
                common,
                type(r1) as rel_to_entity1,
                type(r2) as rel_to_entity2
            """

            # Execute query
            results = graph_client.execute_query(
                cypher,
                {"id1": entity_id1, "id2": entity_id2},
                limit=100
            )

            # Format results
            common_neighbors = []
            for record in results:
                common_neighbors.append({
                    "entity": dict(record["common"]),
                    "relationship_to_entity1": record["rel_to_entity1"],
                    "relationship_to_entity2": record["rel_to_entity2"]
                })

            return json.dumps({
                "success": True,
                "entity1": {"type": entity_type1, "id": entity_id1},
                "entity2": {"type": entity_type2, "id": entity_id2},
                "common_neighbor_count": len(common_neighbors),
                "common_neighbors": common_neighbors
            }, indent=2, default=str)

        except Exception as e:
            logger.error(f"Error in find_common_neighbors: {str(e)}", exc_info=True)
            return json.dumps({"error": str(e), "success": False})

    logger.info("Traversal tools registered successfully")
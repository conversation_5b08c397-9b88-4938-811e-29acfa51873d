# Load Databricks Gold Knowledge Graph to Local Memgraph

## Overview
`load_gold_to_local_memgraph.py`, is a utility that pulls pre-generated Cypher statements from a Databricks table and applies them to a local Memgraph instance. The script handles fetching batched results from the Databricks SQL Warehouse, groups Cypher statements into index, node, and relationship passes, optionally clears target graph, and provides basic verification output once loading complete (@load_gold_to_local_memgraph.py#1-448).

## Prerequisites
- Python 3.10+
- Dependencies: [`neo4j`](https://pypi.org/project/neo4j/), [`requests`](https://pypi.org/project/requests/), [`python-dotenv`](https://pypi.org/project/python-dotenv/).
  ```bash
  pip install neo4j requests python-dotenv
  ```
- Access to a Databricks workspace with:
  - A SQL Warehouse ID that can query the gold table.
  - A personal access token with permissions to use that warehouse and read the table.
- A local Memgraph instance reachable at `bolt://localhost:7687`. The script verifies connectivity using the Neo4j Python driver (@load_gold_to_local_memgraph.py#171-185).

## Environment Configuration
The loader reads its configuration from environment variables via `python-dotenv` (@load_gold_to_local_memgraph.py#24-31). Define the following keys in a `.env` file or export them in your shell:

| Variable | Required | Description |
| --- | --- | --- |
| `DATABRICKS_HOST` | ✓ | Base URL of the Databricks workspace (e.g., `https://adb-1234567890123456.9.azuredatabricks.net`). |
| `DATABRICKS_TOKEN` | ✓ | Databricks personal access token with SQL Warehouse access. |
| `DATABRICKS_WAREHOUSE_ID` | ✓ | SQL Warehouse ID used to execute the query. |
| `DATABRICKS_CATALOG` | - (default `graysky-dev-catalog`) | Catalog that hosts the gold table; can be overridden at runtime. |

Copy `env.kg` into `.env` (or source it) and adjust the values before running the loader.

## Databricks Gold Table Expectations
Target table must contain `cypher_json` rows sorted by `execution_order`, where each JSON payload includes a `cypher` key containing executable statement. Loader retrieves external result links returned by SQL Warehouse and downloads each chunk until all Cypher statements are collected (@load_gold_to_local_memgraph.py#72-168).

## Running the Loader
1. **Set environment variables**
   ```bash
   cp env.kg .env  # edit copied file with your workspace details
   ```
2. **Start Memgraph locally** (if not already running):
   ```bash
   docker run -p 7687:7687 memgraph/memgraph-platform
   ```
3. **Run the loader**
   ```bash
   python load_gold_to_local_memgraph.py
   ```

### CLI Options
Script exposes CLI flags for selecting table and clearing database first (@load_gold_to_local_memgraph.py#386-415):
```bash
python load_gold_to_local_memgraph.py --help
python load_gold_to_local_memgraph.py --clear
python load_gold_to_local_memgraph.py --table my_catalog.gold.custom_kg
python load_gold_to_local_memgraph.py --clear --table custom_catalog.gold.knowledge_graph
```

## What the Script Does
1. **Fetch Cypher statements** using t Databricks SQL Warehouse REST API (`/api/2.0/sql/statements`), automatically following external/internal chunk links (@load_gold_to_local_memgraph.py#36-168).
2. **Connect to local Memgraph** using Neo4j Python driver and validate connectivity (@load_gold_to_local_memgraph.py#171-185).
3. **Execute Cypher in three passes**—indices, nodes, relationships—while displaying progress and summarizing successes/errors (@load_gold_to_local_memgraph.py#187-323).
4. **Verify the load** by printing node/relationship counts and sampling a few mission, county, and EEI records (@load_gold_to_local_memgraph.py#325-384).

## Troubleshooting
- **Missing or incorrect Databricks credentials**: Requests to `/api/2.0/sql/statements` will fail with a 401/403 or `Query did not succeed` message. Confirm the environment variables and token scopes (@load_gold_to_local_memgraph.py#51-68).
- **Memgraph connection failures**: The script prints guidance for running Memgraph via Docker if it cannot connect (@load_gold_to_local_memgraph.py#171-185).
- **Duplicate index warnings**: Index creation errors are expected when rerunning with existing indices; they are reported but do not stop execution (@load_gold_to_local_memgraph.py#247-254).

## Next Steps
After loading completes, explore graph using `mgconsole`:
```bash
docker exec -it <memgraph-container-name> mgconsole
```
Query graph to validate relationships.
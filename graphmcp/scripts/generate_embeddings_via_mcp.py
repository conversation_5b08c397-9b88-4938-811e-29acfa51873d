"""
Generate embeddings via GraphMCP server tools

This version uses the running GraphMCP MCP server to query nodes
and update them with embeddings, avoiding direct database connection issues.
"""

import os
import sys
import asyncio
import hashlib
from pathlib import Path
from datetime import datetime

sys.path.insert(0, str(Path(__file__).parent.parent))

from dotenv import load_dotenv
from openai import AzureOpenAI
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

load_dotenv()


def hash_text(text: str) -> str:
    """Generate SHA256 hash of text"""
    return hashlib.sha256(text.encode('utf-8')).hexdigest()


async def generate_embeddings_via_mcp():
    """Generate embeddings using MCP tools"""

    print("=" * 80)
    print("BATCH EMBEDDING GENERATION (VIA MCP)")
    print("=" * 80)

    # Initialize OpenAI client
    print("\n🔌 Initializing Azure OpenAI client...")
    openai_client = AzureOpenAI(
        api_key=os.getenv("OPENAI_API_KEY"),
        api_version=os.getenv("OPENAI_API_VERSION", "2024-12-01-preview"),
        azure_endpoint=os.getenv("OPENAI_ENDPOINT")
    )
    deployment = os.getenv("OPENAI_EMBEDDING_DEPLOYMENT", "text-embedding-3-small")
    print(f"   ✓ OpenAI client ready (deployment: {deployment})")

    # Connect to MCP server
    print("\n🔌 Connecting to GraphMCP server...")
    server_params = StdioServerParameters(
        command='uv',
        args=['run', 'python', 'main.py'],
        env=None
    )

    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            await session.initialize()
            print("   ✓ Connected to MCP server")

            # Get Mission nodes (example - can extend to other types)
            print("\n📊 Fetching Mission nodes...")
            result = await session.call_tool(
                'search_entities',
                arguments={
                    'entity_type': 'Mission',
                    'limit': 1000
                }
            )

            import json
            data = json.loads(result.content[0].text)

            if not data.get('success'):
                print(f"   ❌ Failed to fetch missions: {data.get('error')}")
                return

            missions = data.get('entities', [])
            print(f"   Found {len(missions)} Mission nodes")

            if len(missions) == 0:
                print("   ⚠️  No missions to process")
                return

            # Process missions
            embedded_count = 0
            skipped_count = 0

            print(f"\n🔄 Processing missions...")
            for i, mission in enumerate(missions, 1):
                mission_id = mission.get('id')
                title = mission.get('title', '')

                if not title:
                    print(f"   {i}. Skipping {mission_id} (no title)")
                    skipped_count += 1
                    continue

                # Check if already has embedding
                if 'embedding' in mission and 'embedding_text_hash' in mission:
                    current_hash = hash_text(title)
                    if mission['embedding_text_hash'] == current_hash:
                        skipped_count += 1
                        continue

                # Generate embedding
                try:
                    response = openai_client.embeddings.create(
                        model=deployment,
                        input=title
                    )
                    embedding = response.data[0].embedding

                    # Update via MCP execute_cypher tool
                    update_query = f"""
                    MATCH (n:Mission {{id: $node_id}})
                    SET n.embedding = $embedding,
                        n.embedding_model = $model,
                        n.embedding_version = $version,
                        n.embedding_dimension = $dimension,
                        n.embedding_created_at = $created_at,
                        n.embedding_text_hash = $text_hash,
                        n.embedding_source_fields = $source_fields
                    RETURN n.id as id
                    """

                    update_result = await session.call_tool(
                        'execute_cypher',
                        arguments={
                            'query': update_query,
                            'parameters': {
                                'node_id': mission_id,
                                'embedding': embedding,
                                'model': deployment,
                                'version': 'v1',
                                'dimension': len(embedding),
                                'created_at': datetime.utcnow().isoformat() + 'Z',
                                'text_hash': hash_text(title),
                                'source_fields': ['title']
                            }
                        }
                    )

                    embedded_count += 1
                    if i % 10 == 0:
                        print(f"   Progress: {i}/{len(missions)} ({embedded_count} embedded, {skipped_count} skipped)")

                except Exception as e:
                    print(f"   ❌ Failed to embed {mission_id}: {e}")

            print(f"\n{'='*80}")
            print(f"✅ EMBEDDING GENERATION COMPLETE")
            print(f"{'='*80}")
            print(f"\nTotal missions: {len(missions)}")
            print(f"Embedded: {embedded_count}")
            print(f"Skipped: {skipped_count}")


if __name__ == "__main__":
    asyncio.run(generate_embeddings_via_mcp())

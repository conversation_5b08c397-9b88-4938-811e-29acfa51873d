# Python virtual environment
.venv
venv
env
ENV

# Python cache and compiled files
__pycache__
*.pyc
*.pyo
*.pyd
.Python
*.so
*.egg
*.egg-info
dist
build
*.whl

# Testing and coverage
.pytest_cache
.coverage
.tox
htmlcov
.mypy_cache
.ruff_cache

# IDE and editor files
.vscode
.idea
*.swp
*.swo
*~
.DS_Store

# Git
.git
.gitignore
.gitattributes

# Environment variables (contains secrets)
.env
.env.local
.env.*.local

# Logs
*.log
logs/
*.log.*

# Documentation
*.md
docs/
README.md

# Docker files (don't need to copy into image)
Dockerfile
.dockerignore
docker-compose.yml

# Other
.cache
tmp/
temp/

"""
Batch Embedding Generation Script

Generates embeddings for existing nodes in Memgraph using Azure OpenAI.
Supports:
- Selective re-embedding based on text hash changes
- Multiple entity types
- Progress tracking and resumability
- Batch processing for efficiency
"""

import os
import sys
import argparse
import hashlib
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from dotenv import load_dotenv
from src.graph.client import GraphClient
from src.embeddings import (
    EmbeddingClient,
    extract_embedding_text,
    get_supported_entity_types
)

# Load environment
load_dotenv()


def hash_text(text: str) -> str:
    """Generate SHA256 hash of text"""
    return hashlib.sha256(text.encode('utf-8')).hexdigest()


def generate_embeddings_for_entity_type(
    graph_client: GraphClient,
    embedding_client: EmbeddingClient,
    entity_type: str,
    incremental: bool = False,
    batch_size: int = 100
) -> Dict[str, Any]:
    """
    Generate embeddings for all nodes of a specific entity type

    Args:
        graph_client: Memgraph client
        embedding_client: Azure OpenAI embedding client
        entity_type: Type of entity to process (Mission, Incident, Resource)
        incremental: If True, only re-embed nodes where text has changed
        batch_size: Number of nodes to process in each batch

    Returns:
        Statistics dictionary
    """
    print(f"\n{'='*80}")
    print(f"Processing {entity_type} nodes")
    print(f"{'='*80}")

    stats = {
        "entity_type": entity_type,
        "total_nodes": 0,
        "processed": 0,
        "skipped": 0,
        "failed": 0,
        "embedded": 0
    }

    # Get all nodes of this type
    print(f"📊 Fetching {entity_type} nodes from Memgraph...")

    fetch_query = f"""
    MATCH (n:{entity_type})
    WHERE n.id IS NOT NULL
    RETURN n
    """

    try:
        nodes = graph_client.execute_query(fetch_query, {})
        stats["total_nodes"] = len(nodes)
        print(f"   Found {stats['total_nodes']} {entity_type} nodes")
    except Exception as e:
        print(f"   ❌ Failed to fetch nodes: {e}")
        return stats

    if stats["total_nodes"] == 0:
        print(f"   ⚠️  No {entity_type} nodes found")
        return stats

    # Process in batches
    print(f"\n🔄 Processing nodes in batches of {batch_size}...")

    for i in range(0, len(nodes), batch_size):
        batch = nodes[i:i + batch_size]
        batch_num = (i // batch_size) + 1
        total_batches = (len(nodes) + batch_size - 1) // batch_size

        print(f"\n   Batch {batch_num}/{total_batches} ({len(batch)} nodes)...")

        # Prepare batch for embedding
        nodes_to_embed = []
        texts_to_embed = []

        for record in batch:
            node_data = dict(record['n'])
            node_id = node_data.get('id')

            stats["processed"] += 1

            try:
                # Extract text for embedding
                embedding_text, source_fields = extract_embedding_text(entity_type, node_data)
                current_hash = hash_text(embedding_text)

                # Check if re-embedding needed
                if incremental:
                    stored_hash = node_data.get('embedding_text_hash', '')
                    stored_model = node_data.get('embedding_model', '')
                    has_embedding = 'embedding' in node_data

                    if (has_embedding and
                        stored_hash == current_hash and
                        stored_model == embedding_client.deployment):
                        # Skip - embedding is up to date
                        stats["skipped"] += 1
                        continue

                # Add to batch
                nodes_to_embed.append({
                    'id': node_id,
                    'data': node_data,
                    'text': embedding_text,
                    'text_hash': current_hash,
                    'source_fields': source_fields
                })
                texts_to_embed.append(embedding_text)

            except Exception as e:
                print(f"      ⚠️  Skipping node {node_id}: {e}")
                stats["failed"] += 1
                continue

        if not texts_to_embed:
            print(f"      ⏭️  No nodes need embedding (all up to date)")
            continue

        # Generate embeddings for batch
        print(f"      🧠 Generating {len(texts_to_embed)} embeddings...")
        try:
            embeddings_data = embedding_client.generate_embeddings_batch(
                texts_to_embed,
                include_metadata=True
            )

            print(f"      ✓ Embeddings generated successfully")

        except Exception as e:
            print(f"      ❌ Batch embedding failed: {e}")
            stats["failed"] += len(texts_to_embed)
            continue

        # Update nodes in Memgraph
        print(f"      💾 Updating nodes in Memgraph...")
        for node_info, embedding_data in zip(nodes_to_embed, embeddings_data):
            node_id = node_info['id']

            try:
                update_query = f"""
                MATCH (n:{entity_type} {{id: $node_id}})
                SET n.embedding = $embedding,
                    n.embedding_model = $model,
                    n.embedding_version = $version,
                    n.embedding_dimension = $dimension,
                    n.embedding_created_at = $created_at,
                    n.embedding_text_hash = $text_hash,
                    n.embedding_source_fields = $source_fields
                RETURN n.id as id
                """

                result = graph_client.execute_query(
                    update_query,
                    {
                        "node_id": node_id,
                        "embedding": embedding_data['embedding'],
                        "model": embedding_data['embedding_model'],
                        "version": embedding_data['embedding_version'],
                        "dimension": embedding_data['embedding_dimension'],
                        "created_at": embedding_data['embedding_created_at'],
                        "text_hash": node_info['text_hash'],
                        "source_fields": node_info['source_fields']
                    }
                )

                if result:
                    stats["embedded"] += 1
                else:
                    print(f"         ⚠️  Node {node_id} not found for update")
                    stats["failed"] += 1

            except Exception as e:
                print(f"         ❌ Failed to update node {node_id}: {e}")
                stats["failed"] += 1

        print(f"      ✓ Updated {stats['embedded'] - (stats['embedded'] - len(embeddings_data))} nodes")

    return stats


def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(
        description="Generate embeddings for graph nodes"
    )
    parser.add_argument(
        '--entity-types',
        nargs='+',
        default=None,
        help=f"Entity types to process (default: all supported types). Options: {get_supported_entity_types()}"
    )
    parser.add_argument(
        '--incremental',
        action='store_true',
        help="Only re-embed nodes where text has changed (checks text hash)"
    )
    parser.add_argument(
        '--batch-size',
        type=int,
        default=100,
        help="Number of nodes to process in each batch (default: 100)"
    )
    parser.add_argument(
        '--all',
        action='store_true',
        help="Process all supported entity types"
    )

    args = parser.parse_args()

    print("=" * 80)
    print("BATCH EMBEDDING GENERATION")
    print("=" * 80)

    # Determine entity types to process
    if args.all or args.entity_types is None:
        entity_types = get_supported_entity_types()
        # Exclude Comment for now (future entity type)
        entity_types = [t for t in entity_types if t != "Comment"]
    else:
        entity_types = args.entity_types

    print(f"\n📋 Configuration:")
    print(f"   Entity Types: {', '.join(entity_types)}")
    print(f"   Mode: {'Incremental (skip unchanged)' if args.incremental else 'Full re-embedding'}")
    print(f"   Batch Size: {args.batch_size}")

    # Initialize clients
    print(f"\n🔌 Initializing connections...")

    try:
        # Memgraph client
        graph_client = GraphClient(
            host=os.getenv("MEMGRAPH_HOST", "localhost"),
            port=int(os.getenv("MEMGRAPH_PORT", "7687")),
            user=os.getenv("MEMGRAPH_USER", ""),
            password=os.getenv("MEMGRAPH_PASSWORD", ""),
            ssl=os.getenv("MEMGRAPH_SSL", "false").lower() == "true"
        )
        print(f"   ✓ Connected to Memgraph at {os.getenv('MEMGRAPH_HOST', 'localhost')}:{os.getenv('MEMGRAPH_PORT', '7687')}")

        # Embedding client
        embedding_client = EmbeddingClient()
        print(f"   ✓ Azure OpenAI client initialized")
        print(f"      Model: {embedding_client.deployment}")
        print(f"      Dimension: {embedding_client.dimension}")

    except Exception as e:
        print(f"\n❌ Failed to initialize clients: {e}")
        return 1

    # Process each entity type
    all_stats = []
    start_time = datetime.now()

    for entity_type in entity_types:
        try:
            stats = generate_embeddings_for_entity_type(
                graph_client,
                embedding_client,
                entity_type,
                incremental=args.incremental,
                batch_size=args.batch_size
            )
            all_stats.append(stats)

        except Exception as e:
            print(f"\n❌ Failed to process {entity_type}: {e}")
            import traceback
            traceback.print_exc()

    # Print summary
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()

    print(f"\n{'='*80}")
    print(f"SUMMARY")
    print(f"{'='*80}")

    total_processed = sum(s["processed"] for s in all_stats)
    total_embedded = sum(s["embedded"] for s in all_stats)
    total_skipped = sum(s["skipped"] for s in all_stats)
    total_failed = sum(s["failed"] for s in all_stats)

    for stats in all_stats:
        print(f"\n{stats['entity_type']}:")
        print(f"   Total nodes: {stats['total_nodes']}")
        print(f"   Embedded: {stats['embedded']}")
        print(f"   Skipped: {stats['skipped']}")
        print(f"   Failed: {stats['failed']}")

    print(f"\nOverall:")
    print(f"   Processed: {total_processed} nodes")
    print(f"   Embedded: {total_embedded} nodes")
    print(f"   Skipped: {total_skipped} nodes (unchanged)")
    print(f"   Failed: {total_failed} nodes")
    print(f"   Duration: {duration:.2f}s")
    print(f"   Rate: {total_processed / duration:.1f} nodes/sec")

    print(f"\n{'='*80}")
    if total_failed == 0:
        print(f"✅ EMBEDDING GENERATION COMPLETE")
    else:
        print(f"⚠️  COMPLETED WITH {total_failed} FAILURES")
    print(f"{'='*80}")

    return 0 if total_failed == 0 else 1


if __name__ == "__main__":
    sys.exit(main())

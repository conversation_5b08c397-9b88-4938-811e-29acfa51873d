"""
Schema manager for caching and managing graph schema information
"""

import os
import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


def _strip_embeddings(data):
    """
    Recursively remove embedding fields from data structures.

    Removes any dictionary key that starts with 'embedding' to prevent
    large embedding arrays from bloating MCP tool responses.

    Args:
        data: Python dict, list, or primitive to process

    Returns:
        Cleaned data structure with embedding fields removed
    """
    if isinstance(data, dict):
        return {
            k: _strip_embeddings(v)
            for k, v in data.items()
            if not k.startswith('embedding')
        }
    elif isinstance(data, list):
        return [_strip_embeddings(item) for item in data]
    else:
        return data


class SchemaManager:
    """
    Manages graph schema caching and metadata.
    """

    def __init__(self, graph_client):
        """
        Initialize schema manager.

        Args:
            graph_client: GraphClient instance
        """
        self.graph_client = graph_client
        self.cache = {}
        self.cache_timestamp = None
        self.cache_ttl = int(os.getenv("CACHE_TTL_SECONDS", 300))

    def refresh_cache(self):
        """Refresh the schema cache from the database."""
        try:
            logger.info("Refreshing schema cache")

            # Get node labels and counts
            node_info = self._get_node_info()

            # Get relationship types and counts
            rel_info = self._get_relationship_info()

            # Get property information
            property_info = self._get_property_info()

            # Get indexes
            index_info = self._get_index_info()

            # Merge properties into node_info
            for label, props in property_info["node_properties"].items():
                if label in node_info:
                    node_info[label]["properties"] = props

            # Merge properties into rel_info
            for rel_type, props in property_info["relationship_properties"].items():
                if rel_type in rel_info:
                    rel_info[rel_type]["properties"] = props

            # Rename keys for rel_info
            for rel_type in rel_info:
                if "from" in rel_info[rel_type]:
                    rel_info[rel_type]["from_labels"] = rel_info[rel_type].pop("from")
                if "to" in rel_info[rel_type]:
                    rel_info[rel_type]["to_labels"] = rel_info[rel_type].pop("to")

            # Build cache with new structure
            self.cache = {
                "node_labels": node_info,
                "relationship_types": rel_info,
                "constraints": {
                    "indexes": index_info
                },
                "statistics": {
                    "total_nodes": sum(info["count"] for info in node_info.values()),
                    "total_relationships": sum(info["count"] for info in rel_info.values()),
                    "node_label_count": len(node_info),
                    "relationship_type_count": len(rel_info)
                },
                "metadata": {
                    "last_updated": datetime.utcnow().isoformat(),
                    "cache_ttl_seconds": self.cache_ttl
                }
            }

            self.cache_timestamp = datetime.utcnow()
            logger.info(f"Schema cache refreshed with {len(node_info)} node types and {len(rel_info)} relationship types")

        except Exception as e:
            logger.error(f"Failed to refresh schema cache: {str(e)}")
            raise

    def _get_node_info(self) -> Dict[str, Any]:
        """Get information about node labels."""
        cypher = """
        MATCH (n)
        WITH labels(n) as node_labels, count(n) as count
        UNWIND node_labels as label
        RETURN label, sum(count) as count
        ORDER BY count DESC
        """

        results = self.graph_client.execute_query(cypher)
        logger.info(f"_get_node_info: execute_query returned {len(results)} results")
        logger.debug(f"_get_node_info: results = {results}")

        node_info = {}
        for record in results:
            label = record["label"]
            node_info[label] = {
                "count": record["count"],
                "properties": {}  # Will be populated by _get_property_info
            }

        logger.info(f"_get_node_info: built node_info with {len(node_info)} labels")
        return node_info

    def _get_relationship_info(self) -> Dict[str, Any]:
        """Get information about relationship types."""
        cypher = """
        MATCH ()-[r]->()
        RETURN type(r) as rel_type, count(r) as count
        ORDER BY count DESC
        """

        results = self.graph_client.execute_query(cypher)

        rel_info = {}
        for record in results:
            rel_type = record["rel_type"]
            rel_info[rel_type] = {
                "count": record["count"],
                "properties": {},  # Will be populated by _get_property_info
                "from": [],  # Will be populated with source node types
                "to": []     # Will be populated with target node types
            }

        # Get source and target node types for each relationship
        for rel_type in rel_info.keys():
            # Get source node types
            source_cypher = f"""
            MATCH (n)-[r:{rel_type}]->()
            WITH DISTINCT labels(n) as source_labels
            UNWIND source_labels as label
            RETURN DISTINCT label
            LIMIT 10
            """
            source_results = self.graph_client.execute_query(source_cypher)
            rel_info[rel_type]["from"] = [r["label"] for r in source_results]

            # Get target node types
            target_cypher = f"""
            MATCH ()-[r:{rel_type}]->(n)
            WITH DISTINCT labels(n) as target_labels
            UNWIND target_labels as label
            RETURN DISTINCT label
            LIMIT 10
            """
            target_results = self.graph_client.execute_query(target_cypher)
            rel_info[rel_type]["to"] = [r["label"] for r in target_results]

        return rel_info

    def _get_property_info(self) -> Dict[str, Any]:
        """Get property information for nodes and relationships."""
        property_info = {
            "node_properties": {},
            "relationship_properties": {}
        }

        # Get node properties (sample-based approach for Memgraph)
        node_labels_query = "MATCH (n) RETURN DISTINCT labels(n) as labels LIMIT 100"
        labels_results = self.graph_client.execute_query(node_labels_query)

        unique_labels = set()
        for record in labels_results:
            if record["labels"]:
                for label in record["labels"]:
                    unique_labels.add(label)

        for label in unique_labels:
            # Sample properties from nodes of this label
            props_query = f"""
            MATCH (n:{label})
            WITH n LIMIT 100
            UNWIND keys(n) AS prop_name
            WITH DISTINCT prop_name, n[prop_name] AS prop_value
            RETURN prop_name,
                   collect(DISTINCT toString(valueType(prop_value)))[0] AS prop_type
            ORDER BY prop_name
            """
            props_results = self.graph_client.execute_query(props_query)

            prop_dict = {}
            for record in props_results:
                prop_name = record["prop_name"]
                prop_type = record["prop_type"]
                prop_dict[prop_name] = {"type": prop_type}

            property_info["node_properties"][label] = prop_dict

        # Get relationship properties
        rel_types_query = "MATCH ()-[r]->() RETURN DISTINCT type(r) as rel_type LIMIT 100"
        rel_results = self.graph_client.execute_query(rel_types_query)

        for record in rel_results:
            rel_type = record["rel_type"]
            # Sample properties from relationships of this type
            props_query = f"""
            MATCH ()-[r:{rel_type}]->()
            WITH r LIMIT 100
            UNWIND keys(r) AS prop_name
            WITH DISTINCT prop_name, r[prop_name] AS prop_value
            RETURN prop_name,
                   collect(DISTINCT toString(valueType(prop_value)))[0] AS prop_type
            ORDER BY prop_name
            """
            props_results = self.graph_client.execute_query(props_query)

            prop_dict = {}
            for prop_record in props_results:
                prop_name = prop_record["prop_name"]
                prop_type = prop_record["prop_type"]
                prop_dict[prop_name] = {"type": prop_type}

            property_info["relationship_properties"][rel_type] = prop_dict

        return property_info

    def _get_index_info(self) -> List[Dict[str, Any]]:
        """Get information about database indexes."""
        # Memgraph uses SHOW INDEX INFO
        try:
            cypher = "SHOW INDEX INFO"
            results = self.graph_client.execute_query(cypher)

            indexes = []
            for record in results:
                indexes.append({
                    "type": record.get("index_type", "label"),
                    "label": record.get("label"),
                    "property": record.get("property"),
                    "count": record.get("count", 0)
                })

            return indexes
        except:
            # Fallback if SHOW INDEX INFO is not available
            return []

    def get_cached_schema(self) -> str:
        """
        Get cached schema information.

        Returns:
            JSON string of schema information
        """
        # Check if cache is still valid
        if self.cache_timestamp:
            age = (datetime.utcnow() - self.cache_timestamp).total_seconds()
            if age > self.cache_ttl:
                logger.info(f"Schema cache expired (age: {age}s > TTL: {self.cache_ttl}s)")
                # In production, we might trigger async refresh here

        return json.dumps(self.cache, indent=2, default=str)

    def describe_entity(self, entity_type: str) -> str:
        """
        Get detailed information about a specific entity type.

        Args:
            entity_type: Node label to describe

        Returns:
            JSON string with entity details
        """
        if entity_type not in self.cache.get("node_labels", {}):
            return json.dumps({
                "error": f"Entity type '{entity_type}' not found in schema"
            })

        entity_info = self.cache["node_labels"][entity_type].copy()

        # Properties are already in entity_info, no need to merge

        # Find relationships involving this entity
        related_relationships = []
        for rel_type, rel_info in self.cache.get("relationship_types", {}).items():
            if entity_type in rel_info.get("from_labels", []) or entity_type in rel_info.get("to_labels", []):
                related_relationships.append({
                    "type": rel_type,
                    "direction": "outgoing" if entity_type in rel_info.get("from_labels", []) else "incoming",
                    "count": rel_info.get("count", 0)
                })

        entity_info["relationships"] = related_relationships

        # Get sample entities
        sample_query = f"""
        MATCH (n:{entity_type})
        RETURN n
        LIMIT 3
        """
        try:
            samples = self.graph_client.execute_query(sample_query)
            entity_info["samples"] = samples
        except:
            entity_info["samples"] = []

        # Strip embeddings to prevent bloat
        entity_info = _strip_embeddings(entity_info)

        return json.dumps(entity_info, indent=2, default=str)

    def describe_relationship(self, relationship_type: str) -> str:
        """
        Get detailed information about a specific relationship type.

        Args:
            relationship_type: Relationship type to describe

        Returns:
            JSON string with relationship details
        """
        if relationship_type not in self.cache.get("relationship_types", {}):
            return json.dumps({
                "error": f"Relationship type '{relationship_type}' not found in schema"
            })

        rel_info = self.cache["relationship_types"][relationship_type].copy()

        # Properties are already in rel_info, no need to merge

        # Get sample relationships
        sample_query = f"""
        MATCH (a)-[r:{relationship_type}]->(b)
        RETURN a, r, b
        LIMIT 3
        """
        try:
            samples = self.graph_client.execute_query(sample_query)
            rel_info["samples"] = samples
        except:
            rel_info["samples"] = []

        return json.dumps(rel_info, indent=2, default=str)

    def get_cache_status(self) -> Dict[str, Any]:
        """Get schema cache status information."""
        status = {
            "cached": bool(self.cache),
            "cache_age_seconds": None,
            "cache_ttl_seconds": self.cache_ttl,
            "last_updated": self.cache.get("last_updated")
        }

        if self.cache_timestamp:
            status["cache_age_seconds"] = (datetime.utcnow() - self.cache_timestamp).total_seconds()

        return status

    def get_cache_size(self) -> int:
        """Get size of cached schema data."""
        return len(json.dumps(self.cache, default=str))
"""
Test embedding workflow on small sample

This script tests the complete embedding workflow on a small number of nodes:
1. Fetch nodes via MCP
2. Generate embeddings via Azure OpenAI
3. Update nodes via MCP
4. Verify embeddings were stored
"""

import os
import sys
import asyncio
import hashlib
import json
from pathlib import Path
from datetime import datetime

sys.path.insert(0, str(Path(__file__).parent.parent))

from dotenv import load_dotenv
from openai import AzureOpenAI
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

load_dotenv()


def hash_text(text: str) -> str:
    """Generate SHA256 hash of text"""
    return hashlib.sha256(text.encode('utf-8')).hexdigest()


async def test_embedding_workflow():
    """Test embedding generation on small sample"""

    print("=" * 80)
    print("TEST EMBEDDING WORKFLOW (Small Sample)")
    print("=" * 80)

    # Initialize OpenAI client
    print("\n🔌 Initializing Azure OpenAI client...")
    openai_client = AzureOpenAI(
        api_key=os.getenv("OPENAI_API_KEY"),
        api_version=os.getenv("OPENAI_API_VERSION", "2024-12-01-preview"),
        azure_endpoint=os.getenv("OPENAI_ENDPOINT")
    )
    deployment = os.getenv("OPENAI_EMBEDDING_DEPLOYMENT", "text-embedding-3-small")
    print(f"   ✓ OpenAI client ready (deployment: {deployment})")

    # Connect to MCP server
    print("\n🔌 Connecting to GraphMCP server...")
    server_params = StdioServerParameters(
        command='uv',
        args=['run', 'python', 'main.py'],
        env=None
    )

    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            await session.initialize()
            print("   ✓ Connected to MCP server")

            # Test with 3 Mission nodes
            print("\n📊 Fetching 3 Mission nodes for test...")
            result = await session.call_tool(
                'search_entities',
                arguments={
                    'entity_type': 'Mission',
                    'limit': 3
                }
            )

            data = json.loads(result.content[0].text)

            if not data.get('success'):
                print(f"   ❌ Failed to fetch missions: {data.get('error')}")
                return False

            missions = data.get('entities', [])
            print(f"   Found {len(missions)} Mission nodes")

            if len(missions) == 0:
                print("   ⚠️  No missions found")
                return False

            # Show sample mission
            print(f"\n📄 Sample Mission:")
            sample = missions[0]
            print(f"   ID: {sample.get('id')}")
            print(f"   Title: {sample.get('title', 'N/A')}")
            print(f"   Has embedding: {'embedding' in sample}")

            # Generate embeddings for all 3
            print(f"\n🧠 Generating embeddings for {len(missions)} missions...")
            embedded_count = 0
            total_tokens = 0

            for mission in missions:
                mission_id = mission.get('id')
                title = mission.get('title', '')

                if not title:
                    print(f"   ⚠️  Skipping {mission_id} (no title)")
                    continue

                try:
                    # Generate embedding
                    response = openai_client.embeddings.create(
                        model=deployment,
                        input=title
                    )
                    embedding = response.data[0].embedding
                    tokens_used = response.usage.total_tokens
                    total_tokens += tokens_used

                    print(f"   ✓ Generated embedding for '{title[:50]}...' ({tokens_used} tokens)")

                    # Update node via MCP
                    update_query = """
                    MATCH (n:Mission {id: $node_id})
                    SET n.embedding = $embedding,
                        n.embedding_model = $model,
                        n.embedding_version = $version,
                        n.embedding_dimension = $dimension,
                        n.embedding_created_at = $created_at,
                        n.embedding_text_hash = $text_hash,
                        n.embedding_source_fields = $source_fields
                    RETURN n.id as id
                    """

                    update_result = await session.call_tool(
                        'execute_cypher',
                        arguments={
                            'query': update_query,
                            'parameters': {
                                'node_id': mission_id,
                                'embedding': embedding,
                                'model': deployment,
                                'version': 'v1',
                                'dimension': len(embedding),
                                'created_at': datetime.utcnow().isoformat() + 'Z',
                                'text_hash': hash_text(title),
                                'source_fields': ['title']
                            }
                        }
                    )

                    update_data = json.loads(update_result.content[0].text)
                    if update_data.get('success'):
                        embedded_count += 1
                        print(f"      ✓ Updated node {mission_id}")
                    else:
                        print(f"      ❌ Update failed: {update_data.get('error')}")

                except Exception as e:
                    print(f"   ❌ Failed to embed {mission_id}: {e}")

            # Verify embeddings were stored
            print(f"\n🔍 Verifying embeddings were stored...")
            verify_result = await session.call_tool(
                'execute_cypher',
                arguments={
                    'query': """
                    MATCH (n:Mission)
                    WHERE n.embedding IS NOT NULL
                    RETURN count(n) as count, collect(n.id)[0..3] as sample_ids
                    """,
                    'parameters': {}
                }
            )

            verify_data = json.loads(verify_result.content[0].text)
            if verify_data.get('success'):
                results = verify_data.get('results', [])
                if results:
                    count = results[0].get('count', 0)
                    sample_ids = results[0].get('sample_ids', [])
                    print(f"   ✓ Found {count} Mission nodes with embeddings")
                    print(f"   Sample IDs: {sample_ids}")

            print(f"\n{'='*80}")
            print(f"✅ TEST COMPLETE")
            print(f"{'='*80}")
            print(f"\nResults:")
            print(f"   Missions processed: {len(missions)}")
            print(f"   Embeddings generated: {embedded_count}")
            print(f"   Total tokens used: {total_tokens}")
            print(f"   Cost: ${total_tokens / 1_000_000 * 0.02:.6f}")

            # Estimate for full dataset
            missions_total = 4532
            estimated_tokens = (total_tokens / len(missions)) * missions_total
            estimated_time = len(missions) / embedded_count * missions_total if embedded_count > 0 else 0

            print(f"\n📊 Estimate for full {missions_total} missions:")
            print(f"   Tokens: ~{estimated_tokens:,.0f}")
            print(f"   Cost: ~${estimated_tokens / 1_000_000 * 0.02:.4f}")
            print(f"   Time: ~{estimated_time / 60:.1f} minutes (at current rate)")

            return True


if __name__ == "__main__":
    success = asyncio.run(test_embedding_workflow())
    sys.exit(0 if success else 1)

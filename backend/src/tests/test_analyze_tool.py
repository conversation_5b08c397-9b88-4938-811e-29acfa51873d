#!/usr/bin/env python3
"""Quick test to check if analyze_knowledge_graph tool exists."""

import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "src"))

from app.mcp.adapter import MCPToolAdapter
from app.core.config import settings

async def main():
    # Configure servers
    server_configs = {}
    if settings.graphrag_mcp_enabled:
        server_configs["graphrag"] = settings.graphrag_mcp_url
    
    if not server_configs:
        print("❌ GraphRAG MCP not enabled")
        return
    
    print(f"📡 Connecting to: {server_configs['graphrag']}")
    
    # Discover tools
    adapter = await MCPToolAdapter.discover_all_servers(server_configs)
    all_tools = await adapter.discover_tools()
    
    print(f"\n✅ Discovered {len(all_tools)} tools total\n")
    
    # Check for analyze_knowledge_graph
    analyze_tool = None
    for tool in all_tools:
        if tool.name == "analyze_knowledge_graph":
            analyze_tool = tool
            break
    
    if analyze_tool:
        print("✅ FOUND: analyze_knowledge_graph")
        print(f"   Description: {analyze_tool.description[:200]}...")
    else:
        print("❌ NOT FOUND: analyze_knowledge_graph")
        print("\n📋 Available GraphRAG tools:")
        for tool in all_tools:
            if not tool.name.startswith("get_genie") and not tool.name.startswith("ask_genie"):
                print(f"   - {tool.name}")

if __name__ == "__main__":
    asyncio.run(main())

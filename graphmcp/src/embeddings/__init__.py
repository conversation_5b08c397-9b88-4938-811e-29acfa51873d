"""
Embeddings module for GraphRAG MCP Server

Handles embedding generation, caching, and vector search operations.
Supports both local models (sentence-transformers) and Azure OpenAI.
"""

# Embedding clients
from .azure_client import AzureEmbeddingClient
from .onnx_client import OnnxEmbeddingClient
from .factory import create_embedding_client, get_embedding_config as get_factory_config

# Convenience functions
from .generator import generate_embedding, generate_embeddings_batch

# Field selection utilities
from .field_selector import (
    extract_embedding_text,
    get_embedding_config,
    get_supported_entity_types
)

# Backward compatibility alias
EmbeddingClient = create_embedding_client  # Factory function that returns appropriate client

__all__ = [
    # Client classes
    "AzureEmbeddingClient",
    "OnnxEmbeddingClient",
    "EmbeddingClient",  # Factory function (backward compatible)

    # Factory
    "create_embedding_client",
    "get_factory_config",

    # Convenience functions
    "generate_embedding",
    "generate_embeddings_batch",

    # Field selectors
    "extract_embedding_text",
    "get_embedding_config",
    "get_supported_entity_types",
]

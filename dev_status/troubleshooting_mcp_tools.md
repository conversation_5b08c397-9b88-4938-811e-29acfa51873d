# MCP Tools Troubleshooting Guide

## Common Issues and Solutions

### Issue: Tool Returns Wrong Data Format or Bloated Response

**Symptoms:**
- Tool returns different key names than expected (e.g., `entity_types` instead of `node_labels`)
- Response size is much larger than expected
- Contains unexpected data like sample nodes with full embeddings
- Token count exceeds expected limits

**Example:**
```
Expected: node_labels, ~18K chars
Actually: entity_types, ~97K chars with samples
```

**Root Cause:**
Stale container state - Docker container running old code or with corrupted cache

**Solution:**
1. **Restart the MCP container:**
   ```bash
   docker restart graphrag-mcp
   # or
   docker-compose restart graphrag-mcp
   ```

2. **Verify the fix:**
   ```bash
   # Check container logs
   docker logs graphrag-mcp --tail=20

   # Look for correct output format
   # Should see: "keys=['node_labels', ...]"
   # Not: "keys=['entity_types', ...]"
   ```

3. **If restart doesn't work, rebuild:**
   ```bash
   # From graphmcp directory
   docker-compose down
   docker-compose up --build -d
   ```

**Prevention:**
- Always restart containers after code changes
- Use `docker-compose up --build` to ensure fresh build
- Clear Docker cache if persistent issues: `docker system prune -a`

---

### Issue: Schema Cache Returns Stale Data

**Symptoms:**
- Schema doesn't reflect recent database changes
- Node/relationship counts are outdated
- New entity types or properties not showing up

**Solution:**
1. **Trigger cache refresh via MCP tool:**
   ```python
   # If you have access to the tools
   refresh_cache_tool.invoke({})
   ```

2. **Or restart the container** (cache refreshes on startup):
   ```bash
   docker restart graphrag-mcp
   ```

3. **Check cache TTL settings** in `.env`:
   ```bash
   CACHE_TTL_SECONDS=300  # Default 5 minutes
   ```

**Prevention:**
- Set appropriate cache TTL based on data update frequency
- Implement manual cache refresh in admin workflows
- Monitor cache age in logs

---

### Issue: Tool Discovery Fails or Returns Partial List

**Symptoms:**
- Some tools missing from discovered list
- Tool count lower than expected
- Specific tools fail with "not found" error

**Solution:**
1. **Check MCP server health:**
   ```bash
   curl http://localhost:8001/health
   # Should return: {"status":"healthy",...}
   ```

2. **Verify server is accessible:**
   ```bash
   # From backend
   curl http://localhost:8001/mcp -H "Accept: application/json"
   ```

3. **Restart both containers:**
   ```bash
   docker restart graphrag-mcp backend
   ```

4. **Check backend logs** for connection errors:
   ```bash
   docker logs backend | grep -i "mcp\|tool"
   ```

**Prevention:**
- Ensure consistent network configuration
- Use container names (not localhost) in docker-compose
- Verify MCP server starts before backend

---

### Issue: Tool Returns Empty or Null Results

**Symptoms:**
- Tool executes without error
- Returns empty results or null
- No data despite database having content

**Solution:**
1. **Verify database connection:**
   ```bash
   docker exec graphrag-mcp mgconsole
   # Then in mgconsole:
   MATCH (n) RETURN count(n);
   ```

2. **Check Memgraph is running:**
   ```bash
   docker ps | grep memgraph
   ```

3. **Review connection settings** in graphmcp `.env`:
   ```bash
   MEMGRAPH_HOST=memgraph  # Use container name
   MEMGRAPH_PORT=7687
   ```

4. **Check for query errors** in logs:
   ```bash
   docker logs graphrag-mcp | grep -i "error\|exception"
   ```

**Prevention:**
- Use health checks in docker-compose
- Implement connection retry logic
- Add query validation and error handling

---

## Debugging Checklist

When encountering MCP tool issues, work through this checklist:

- [ ] **Container Status**
  - Is the MCP container running? `docker ps`
  - Are there any restart loops? `docker ps -a`

- [ ] **Container Logs**
  - Check for errors: `docker logs graphrag-mcp --tail=50`
  - Look for startup messages

- [ ] **Simple Restart**
  - Try restarting: `docker restart graphrag-mcp`
  - Wait 5 seconds for startup

- [ ] **Health Check**
  - Verify health endpoint: `curl http://localhost:8001/health`
  - Should return healthy status

- [ ] **Network Connectivity**
  - Can backend reach MCP? `docker exec backend curl http://graphrag-mcp:8001/health`
  - Check docker network: `docker network inspect demes-assistant-network`

- [ ] **Tool Discovery**
  - Test tool discovery from backend
  - Verify expected tool count

- [ ] **Database Connection**
  - Is Memgraph running and accessible?
  - Can MCP container reach it?

- [ ] **Last Resort: Rebuild**
  - `docker-compose down`
  - `docker-compose up --build -d`

---

## Investigation Tools

### Quick Health Check Script

```bash
#!/bin/bash
echo "=== MCP Health Check ==="
echo "1. Container status:"
docker ps | grep -E "graphrag-mcp|backend|memgraph"

echo -e "\n2. GraphRAG MCP health:"
curl -s http://localhost:8001/health || echo "FAILED"

echo -e "\n3. Backend health:"
curl -s http://localhost:8000/health || echo "FAILED"

echo -e "\n4. Recent MCP logs:"
docker logs graphrag-mcp --tail=5

echo -e "\n5. Tool discovery test:"
# Add your test script here
```

### Log Analysis

```bash
# Check for specific error patterns
docker logs graphrag-mcp 2>&1 | grep -i "error\|exception\|failed\|timeout"

# Monitor real-time
docker logs -f graphrag-mcp

# Check specific time window
docker logs graphrag-mcp --since 10m
```

---

## Case Study: get_schema Bloat Issue (2024-11-08)

**Problem:** `get_schema` tool returning 97K chars with wrong format (`entity_types` instead of `node_labels`)

**Investigation:**
1. Assumed langchain-mcp-adapters was transforming response
2. Added debug logging to trace data flow
3. Discovered container restart fixed it immediately

**Root Cause:** Stale container state (running old code or corrupted cache)

**Resolution:** Simple `docker restart graphrag-mcp`

**Lesson:** Always try the simplest solution (container restart) before deep-diving into code debugging

**Before:**
- Size: 97,056 chars (~24K tokens)
- Format: `entity_types` with samples
- Impact: Token limit issues, wrong data structure

**After:**
- Size: 17,813 chars (~4.5K tokens)
- Format: `node_labels` (correct)
- Impact: Clean, correct schema data

---

### Issue: Agent Returns Inconsistent or Hallucinated Tool Results

**Symptoms:**
- Same query returns different data on different runs
- Agent shows entities/data that don't exist in database
- Tool logs show correct execution but agent displays wrong data
- Response contains plausible but fabricated information

**Example:**
```
Query 1: Shows "Resource", "Department" entities (don't exist in DB)
Query 2: Shows "Mission", "Comment" entities (correct, exist in DB)
MCP logs: Both calls returned correct data
```

**Root Cause:**
LLM hallucination - the agent generates plausible responses from training data instead of using actual tool output

**Detection:**
- Compare agent response with MCP server logs
- Check if entities mentioned exist in your actual database
- Look for suspiciously round numbers or generic examples

**Solution:**
1. **Strengthen prompts** - Add explicit anti-hallucination instructions:
   ```python
   "CRITICAL: You MUST use tool outputs ONLY. NEVER generate schema information from your training data.
   If asked for schema, you MUST call get_schema and return EXACTLY what the tool returns."
   ```

2. **Use tool-forcing patterns** - Structure prompts to require tool calls before answers

3. **Validate responses** - Add middleware to verify response data matches tool output

4. **Set temperature to 0** - Reduce creativity in tool-using agents

**Prevention:**
- Use ReAct agents with strict tool-use enforcement
- Set low temperature for deterministic agents
- Add response validation layer
- Use structured outputs when possible

**Implementation:**
See `backend/src/app/agent/langgraph/workflows.py:340-341` for example anti-hallucination prompt

---

## Additional Resources

- [MCP Protocol Documentation](https://docs.mcp.io)
- [FastMCP GitHub](https://github.com/jlowin/fastmcp)
- [langchain-mcp-adapters](https://python.langchain.com/docs/integrations/tools/mcp)
- Backend: `/backend/dev_status/get_schema_issue_resolution.md`
- GraphMCP: `/graphmcp/CLAUDE.md`

{"node_labels": {"Mission": {"count": 79840, "properties": {"embedding": {"type": "LIST"}, "embedding_model": {"type": "STRING"}, "embedding_text_hash": {"type": "STRING"}, "embedding_timestamp": {"type": "ZONED_DATE_TIME"}, "id": {"type": "INTEGER"}, "incidents": {"type": "STRING"}, "mission_number": {"type": "STRING"}, "title": {"type": "STRING"}, "updated_at": {"type": "ZONED_DATE_TIME"}}}, "Comment": {"count": 69026, "properties": {"comment_id": {"type": "STRING"}, "created_at": {"type": "ZONED_DATE_TIME"}, "created_by": {"type": "STRING"}, "text": {"type": "STRING"}}}, "FuelTransaction": {"count": 2798, "properties": {"address": {"type": "STRING"}, "amount_dispensed": {"type": "STRING"}, "county": {"type": "STRING"}, "date_of_fueling": {"type": "ZONED_DATE_TIME"}, "fuel_transaction_id": {"type": "STRING"}, "fuel_type": {"type": "STRING"}, "fuel_vendor": {"type": "STRING"}}}, "FuelBurnHourly": {"count": 362, "properties": {"avg_fuel_per_transaction": {"type": "FLOAT"}, "burn_hourly_id": {"type": "STRING"}, "depot_name": {"type": "STRING"}, "fuel_type": {"type": "STRING"}, "hour_start": {"type": "ZONED_DATE_TIME"}, "incident_name": {"type": "STRING"}, "lat": {"type": "FLOAT"}, "long": {"type": "FLOAT"}, "total_fuel_dispensed": {"type": "FLOAT"}}}, "EEIStatus": {"count": 134, "properties": {"county_name": {"type": "STRING"}, "eei_history_id": {"type": "STRING"}, "eoc_activation": {"type": "STRING"}, "evacuation_status": {"type": "STRING"}, "govt_status": {"type": "STRING"}, "isActive": {"type": "BOOLEAN"}, "lse_status": {"type": "STRING"}, "region": {"type": "STRING"}, "updated_at": {"type": "ZONED_DATE_TIME"}}}, "County": {"count": 69, "properties": {"county_name": {"type": "STRING"}, "latitude": {"type": "FLOAT"}, "longitude": {"type": "FLOAT"}, "region": {"type": "STRING"}}}, "Vendor": {"count": 63, "properties": {"mission_vendor_name": {"type": "STRING"}, "vendor_id": {"type": "STRING"}, "vendor_updated_at": {"type": "ZONED_DATE_TIME"}}}, "Incident": {"count": 39, "properties": {"incident_created_at": {"type": "ZONED_DATE_TIME"}, "incident_id": {"type": "INTEGER"}, "incident_name": {"type": "STRING"}, "incident_status": {"type": "STRING"}, "incident_type": {"type": "STRING"}, "incident_updated_at": {"type": "ZONED_DATE_TIME"}}}, "FuelBurnDaily": {"count": 26, "properties": {"avg_fuel_per_transaction": {"type": "FLOAT"}, "burn_daily_id": {"type": "STRING"}, "day_start": {"type": "ZONED_DATE_TIME"}, "depot_name": {"type": "STRING"}, "fuel_type": {"type": "STRING"}, "incident_name": {"type": "STRING"}, "lat": {"type": "FLOAT"}, "long": {"type": "FLOAT"}, "total_fuel_dispensed": {"type": "FLOAT"}, "transactions_in_day": {"type": "INTEGER"}}}, "FuelBurnWeekly": {"count": 12, "properties": {"avg_fuel_per_transaction": {"type": "FLOAT"}, "burn_weekly_id": {"type": "STRING"}, "depot_name": {"type": "STRING"}, "fuel_type": {"type": "STRING"}, "incident_name": {"type": "STRING"}, "lat": {"type": "FLOAT"}, "long": {"type": "FLOAT"}, "total_fuel_dispensed": {"type": "FLOAT"}, "transactions_in_week": {"type": "INTEGER"}, "week_of_year": {"type": "INTEGER"}}}}, "relationship_types": {"RELATED_TO_INCIDENT": {"count": 75143, "properties": {}, "from_labels": ["Mission"], "to_labels": ["Incident"]}, "COMMENTED_ON": {"count": 62240, "properties": {}, "from_labels": ["Comment"], "to_labels": ["Mission"]}, "IS_PARENT_TO": {"count": 24905, "properties": {}, "from_labels": ["Mission"], "to_labels": ["Mission"]}, "FROM_VENDOR": {"count": 2798, "properties": {}, "from_labels": ["FuelTransaction"], "to_labels": ["<PERSON><PERSON><PERSON>"]}, "HAS_HOURLY_FUEL_BURN": {"count": 362, "properties": {}, "from_labels": ["Incident"], "to_labels": ["FuelBurnHourly"]}, "HAD_STATUS": {"count": 196, "properties": {"from_date": {"type": "ZONED_DATE_TIME"}, "to_date": {"type": "ZONED_DATE_TIME"}}, "from_labels": ["County"], "to_labels": ["EEIStatus"]}, "HAS_CURRENT_STATUS": {"count": 134, "properties": {}, "from_labels": ["County"], "to_labels": ["EEIStatus"]}, "HAS_DAILY_FUEL_BURN": {"count": 26, "properties": {}, "from_labels": ["Incident"], "to_labels": ["FuelBurnDaily"]}, "HAS_WEEKLY_FUEL_BURN": {"count": 12, "properties": {}, "from_labels": ["Incident"], "to_labels": ["FuelBurnWeekly"]}, "ASSIGNED_VENDOR": {"count": 5, "properties": {}, "from_labels": ["Mission"], "to_labels": ["<PERSON><PERSON><PERSON>"]}, "AFFECTS": {"count": 4, "properties": {}, "from_labels": ["Incident"], "to_labels": ["County"]}}, "constraints": {"indexes": [{"type": "label", "label": "Comment", "property": ["comment_id"], "count": 69026}, {"type": "label", "label": "County", "property": ["county_name"], "count": 69}, {"type": "label", "label": "County", "property": ["region"], "count": 67}, {"type": "label", "label": "EEIStatus", "property": ["county_name"], "count": 134}, {"type": "label", "label": "EEIStatus", "property": ["eei_history_id"], "count": 134}, {"type": "label", "label": "EEIStatus", "property": ["eoc_activation"], "count": 134}, {"type": "label", "label": "EEIStatus", "property": ["evacuation_status"], "count": 134}, {"type": "label", "label": "EEIStatus", "property": ["govt_status"], "count": 134}, {"type": "label", "label": "EEIStatus", "property": ["isActive"], "count": 134}, {"type": "label", "label": "EEIStatus", "property": ["lse_status"], "count": 134}, {"type": "label", "label": "FuelBurnDaily", "property": ["burn_daily_id"], "count": 26}, {"type": "label", "label": "FuelBurnDaily", "property": ["day_start"], "count": 26}, {"type": "label", "label": "FuelBurnDaily", "property": ["depot_name"], "count": 26}, {"type": "label", "label": "FuelBurnDaily", "property": ["incident_name"], "count": 26}, {"type": "label", "label": "FuelBurnHourly", "property": ["burn_hourly_id"], "count": 362}, {"type": "label", "label": "FuelBurnHourly", "property": ["depot_name"], "count": 362}, {"type": "label", "label": "FuelBurnHourly", "property": ["hour_start"], "count": 362}, {"type": "label", "label": "FuelBurnHourly", "property": ["incident_name"], "count": 362}, {"type": "label", "label": "FuelBurnWeekly", "property": ["burn_weekly_id"], "count": 12}, {"type": "label", "label": "FuelBurnWeekly", "property": ["depot_name"], "count": 12}, {"type": "label", "label": "FuelBurnWeekly", "property": ["incident_name"], "count": 12}, {"type": "label", "label": "FuelBurnWeekly", "property": ["week_of_year"], "count": 12}, {"type": "label", "label": "FuelTransaction", "property": ["county"], "count": 2798}, {"type": "label", "label": "FuelTransaction", "property": ["date_of_dueling"], "count": 0}, {"type": "label", "label": "FuelTransaction", "property": ["date_of_fueling"], "count": 2798}, {"type": "label", "label": "FuelTransaction", "property": ["fuel_transaction_id"], "count": 2798}, {"type": "label", "label": "FuelTransaction", "property": ["fuel_type"], "count": 2798}, {"type": "label", "label": "FuelTransaction", "property": ["fuel_vendor"], "count": 2798}, {"type": "label", "label": "Incident", "property": ["incident_id"], "count": 39}, {"type": "label", "label": "Incident", "property": ["incident_status"], "count": 39}, {"type": "label", "label": "Incident", "property": ["incident_type"], "count": 39}, {"type": "label", "label": "Mission", "property": ["embedding_text_hash"], "count": 79840}, {"type": "label", "label": "Mission", "property": ["id"], "count": 79840}, {"type": "label", "label": "Mission", "property": ["mission_number"], "count": 79840}, {"type": "label", "label": "Mission", "property": ["parent_mission_id"], "count": 0}, {"type": "label", "label": "Mission", "property": ["title"], "count": 79840}, {"type": "label", "label": "Mission", "property": ["total_fuel_dispensed"], "count": 0}, {"type": "label", "label": "Mission", "property": ["transaction_count"], "count": 0}, {"type": "label", "label": "Mission", "property": ["vendor_id"], "count": 5}, {"type": "label", "label": "<PERSON><PERSON><PERSON>", "property": ["fuel_vendor_name"], "count": 0}, {"type": "label", "label": "<PERSON><PERSON><PERSON>", "property": ["mission_vendor_name"], "count": 63}, {"type": "label", "label": "<PERSON><PERSON><PERSON>", "property": ["vendor_id"], "count": 63}, {"type": "label", "label": "Incident", "property": "embedding", "count": 0}, {"type": "label", "label": "Mission", "property": "embedding", "count": 79840}, {"type": "label", "label": "Resource", "property": "embedding", "count": 0}]}, "statistics": {"total_nodes": 152369, "total_relationships": 165825, "node_label_count": 10, "relationship_type_count": 11}, "metadata": {"last_updated": "2025-11-09T04:26:43.269942", "cache_ttl_seconds": 300}, "cypher_guidelines": {"database_type": "Memgraph", "important_rules": ["Use ONLY the node labels, relationship types, and properties defined in this schema", "Do NOT create or assume any relationships not explicitly listed", "Do NOT create or assume any properties not explicitly listed", "Node labels and relationship types are case-sensitive", "All relationship queries must specify direction with -> or <-"], "memgraph_syntax": {"case_sensitivity": "Labels, types, and properties are case-sensitive", "property_access": "Use dot notation: n.property_name", "temporal_types": "Date/time properties use ZONED_DATE_TIME type", "vector_properties": "The 'embedding' property contains float arrays for vector search", "string_matching": "Use CONTAINS, STARTS WITH, or ENDS WITH for string patterns (case-sensitive)", "aggregation": "Supported: count(), sum(), avg(), min(), max(), collect()"}, "example_queries": [{"description": "Find a Mission by ID", "cypher": "MATCH (m:Mission {id: 12345}) RETURN m"}, {"description": "Find Missions related to an Incident by name", "cypher": "MATCH (m:Mission)-[:RELATED_TO_INCIDENT]->(i:Incident {incident_name: 'Hurricane Ian'}) RETURN m.mission_number, m.title, i.incident_type"}, {"description": "Get Counties with their current EEI status", "cypher": "MATCH (c:County)-[:HAS_CURRENT_STATUS]->(e:EEIStatus) RETURN c.county_name, e.eoc_activation, e.evacuation_status, e.govt_status"}, {"description": "Find Mission parent-child hierarchies", "cypher": "MATCH (parent:Mission)-[:IS_PARENT_TO]->(child:Mission) RETURN parent.mission_number, parent.title, child.mission_number, child.title"}, {"description": "Aggregate fuel transactions by vendor", "cypher": "MATCH (ft:FuelTransaction)-[:FROM_VENDOR]->(v:Vendor) RETURN v.mission_vendor_name, count(ft) AS transaction_count, sum(toFloat(ft.amount_dispensed)) AS total_fuel ORDER BY total_fuel DESC"}, {"description": "Find Comments on a specific Mission", "cypher": "MATCH (c:Comment)-[:COMMENTED_ON]->(m:Mission {mission_number: 'M-2024-001'}) RETURN c.comment_id, c.text, c.created_by, c.created_at ORDER BY c.created_at DESC"}, {"description": "Get Incidents with their fuel burn data", "cypher": "MATCH (i:Incident)-[:HAS_DAILY_FUEL_BURN]->(fb:FuelBurnDaily) RETURN i.incident_name, fb.depot_name, fb.fuel_type, fb.total_fuel_dispensed, fb.day_start"}, {"description": "Count entities by type", "cypher": "MATCH (n:Mission) RETURN count(n) AS mission_count"}], "common_patterns": {"filtering": "MATCH (n:Label) WHERE n.property = value RETURN n", "traversal": "MATCH (a:LabelA)-[:REL_TYPE]->(b:LabelB) RETURN a, b", "multi_hop": "MATCH path = (a:LabelA)-[:REL*1..3]->(b:LabelB) RETURN path", "aggregation": "MATCH (n:Label) RETURN n.property, count(*) AS count GROUP BY n.property", "ordering": "MATCH (n:Label) RETURN n ORDER BY n.property DESC LIMIT 10"}}}
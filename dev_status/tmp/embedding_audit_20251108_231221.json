{"timestamp": "20251108_231221", "summary": {"total": 8, "successful": 8, "with_embeddings": 2, "without_embeddings": 6}, "results": [{"tool": "describe_entity_type", "success": true, "has_embeddings": true, "embedding_count": 4, "embedding_arrays": 0, "total_dims": 0, "size_chars": 28480, "size_kb": 27.81, "approx_tokens": 7120, "embedding_bloat_chars": 0, "embedding_bloat_pct": 0.0, "embedding_locations": [["root.properties.embedding", "embedding_metadata", 1], ["root.properties.embedding_model", "embedding_metadata", 1], ["root.properties.embedding_text_hash", "embedding_metadata", 1], ["root.properties.embedding_timestamp", "embedding_metadata", 1]], "test_args": {"entity_type": "Mission"}}, {"tool": "describe_relationship_type", "success": true, "has_embeddings": false, "embedding_count": 0, "embedding_arrays": 0, "total_dims": 0, "size_chars": 58300, "size_kb": 56.93, "approx_tokens": 14575, "embedding_bloat_chars": 0, "embedding_bloat_pct": 0.0, "embedding_locations": [], "test_args": {"relationship_type": "RELATED_TO_INCIDENT"}}, {"tool": "search_entities", "success": true, "has_embeddings": false, "embedding_count": 0, "embedding_arrays": 0, "total_dims": 0, "size_chars": 27665, "size_kb": 27.02, "approx_tokens": 6916, "embedding_bloat_chars": 0, "embedding_bloat_pct": 0.0, "embedding_locations": [], "test_args": {"entity_type": "Mission", "limit": 3}}, {"tool": "get_entity", "success": true, "has_embeddings": false, "embedding_count": 0, "embedding_arrays": 0, "total_dims": 0, "size_chars": 71, "size_kb": 0.07, "approx_tokens": 17, "embedding_bloat_chars": 0, "embedding_bloat_pct": 0.0, "embedding_locations": [], "test_args": {"entity_type": "Mission", "entity_id": "395452"}}, {"tool": "find_entities_by_time_range", "success": true, "has_embeddings": false, "embedding_count": 0, "embedding_arrays": 0, "total_dims": 0, "size_chars": 150, "size_kb": 0.15, "approx_tokens": 37, "embedding_bloat_chars": 0, "embedding_bloat_pct": 0.0, "embedding_locations": [], "test_args": {"entity_type": "Mission", "start_time": "2024-01-01T00:00:00", "end_time": "2024-12-31T23:59:59", "time_property": "updated_at"}}, {"tool": "get_neighbors", "success": true, "has_embeddings": false, "embedding_count": 0, "embedding_arrays": 0, "total_dims": 0, "size_chars": 167, "size_kb": 0.16, "approx_tokens": 41, "embedding_bloat_chars": 0, "embedding_bloat_pct": 0.0, "embedding_locations": [], "test_args": {"entity_type": "Mission", "entity_id": "395452", "depth": 1}}, {"tool": "get_schema", "success": true, "has_embeddings": true, "embedding_count": 4, "embedding_arrays": 0, "total_dims": 0, "size_chars": 17813, "size_kb": 17.4, "approx_tokens": 4453, "embedding_bloat_chars": 0, "embedding_bloat_pct": 0.0, "embedding_locations": [["root.node_labels.Mission.properties.embedding", "embedding_metadata", 1], ["root.node_labels.Mission.properties.embedding_model", "embedding_metadata", 1], ["root.node_labels.Mission.properties.embedding_text_hash", "embedding_metadata", 1], ["root.node_labels.Mission.properties.embedding_timestamp", "embedding_metadata", 1]], "test_args": {}}, {"tool": "analyze_knowledge_graph", "success": true, "has_embeddings": false, "embedding_count": 0, "embedding_arrays": 0, "total_dims": 0, "size_chars": 40636, "size_kb": 39.68, "approx_tokens": 10159, "embedding_bloat_chars": 0, "embedding_bloat_pct": 0.0, "embedding_locations": [], "test_args": {}}]}
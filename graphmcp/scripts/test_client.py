#!/usr/bin/env python3
"""
Simple test client for GraphRAG MCP Server
Tests basic functionality without interactive mode
"""

import json
import asyncio
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

async def test_graphrag_server():
    """Test the GraphRAG MCP Server with sample queries"""

    print("🧪 GraphRAG MCP Server Test Suite")
    print("=" * 60)

    # Connect to server using uv
    server_params = StdioServerParameters(
        command="uv",
        args=["run", "python", "main.py"],
        env=None
    )

    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            # Initialize connection
            await session.initialize()

            # Get available tools
            tools_response = await session.list_tools()
            tools = {tool.name: tool for tool in tools_response.tools}

            print(f"✅ Connected to server with {len(tools)} tools available\n")

            # Test suite
            tests = [
                {
                    "name": "1. Get Server Health",
                    "tool": "get_health",
                    "args": {}
                },
                {
                    "name": "2. Get Graph Schema",
                    "tool": "get_schema",
                    "args": {}
                },
                {
                    "name": "3. Get Node Statistics",
                    "tool": "get_node_statistics",
                    "args": {}
                },
                {
                    "name": "4. Search for Incidents",
                    "tool": "search_entities",
                    "args": {
                        "entity_type": "Incident",
                        "limit": 5
                    }
                },
                {
                    "name": "5. Find High-Severity Incidents",
                    "tool": "search_entities",
                    "args": {
                        "entity_type": "Incident",
                        "properties": {"severity": {"min": 4}},
                        "limit": 10
                    }
                },
                {
                    "name": "6. Get Specific Incident Details",
                    "tool": "get_entity",
                    "args": {
                        "entity_type": "Incident",
                        "entity_id": "INC-2024-002",
                        "include_relationships": True
                    }
                },
                {
                    "name": "7. Find Hurricane Response Resources",
                    "tool": "traverse_pattern",
                    "args": {
                        "pattern": "(:Incident {type: 'hurricane'})<-[:RESPONDED_TO]-(r:Resource)",
                        "parameters": {},
                        "limit": 10
                    }
                },
                {
                    "name": "8. Analyze Incident Types",
                    "tool": "aggregate_by_type",
                    "args": {
                        "entity_type": "Incident",
                        "group_by": "type",
                        "metrics": ["count", "avg_severity"]
                    }
                },
                {
                    "name": "9. Find Resource Bottlenecks",
                    "tool": "find_patterns",
                    "args": {
                        "base_entity": "Resource",
                        "pattern_type": "resource_bottleneck",
                        "threshold": {"utilization_pct": 50}
                    }
                },
                {
                    "name": "10. Execute Custom Query",
                    "tool": "execute_cypher",
                    "args": {
                        "query": "MATCH (d:Department)-[:COORDINATES_WITH]-(other:Department) RETURN d.name, other.name LIMIT 5",
                        "parameters": {}
                    }
                }
            ]

            # Run tests
            passed = 0
            failed = 0

            for test in tests:
                print(f"\n🔧 Test: {test['name']}")
                print("-" * 40)

                try:
                    # Call the tool
                    result = await session.call_tool(test['tool'], test['args'])

                    # Parse result
                    if isinstance(result, str):
                        result_data = json.loads(result)
                    else:
                        result_data = result

                    # Check for success
                    if isinstance(result_data, dict):
                        if result_data.get('success', True) and 'error' not in result_data:
                            print(f"✅ PASSED")
                            passed += 1

                            # Show sample of results
                            if 'count' in result_data:
                                print(f"   Found {result_data['count']} results")
                            elif 'total_nodes' in result_data:
                                print(f"   Total nodes: {result_data['total_nodes']}")
                            elif 'server_status' in result_data:
                                print(f"   Server status: {result_data['server_status']}")
                            elif 'aggregations' in result_data:
                                print(f"   Aggregations: {len(result_data['aggregations'])} groups")
                        else:
                            print(f"❌ FAILED: {result_data.get('error', 'Unknown error')}")
                            failed += 1
                    else:
                        print(f"✅ PASSED")
                        passed += 1

                except Exception as e:
                    print(f"❌ FAILED: {e}")
                    failed += 1

            # Summary
            print("\n" + "=" * 60)
            print(f"📊 Test Summary:")
            print(f"   ✅ Passed: {passed}/{len(tests)}")
            print(f"   ❌ Failed: {failed}/{len(tests)}")
            print("=" * 60)

            return passed == len(tests)

async def test_specific_scenarios():
    """Test specific emergency management scenarios"""

    print("\n🚨 Emergency Management Scenario Tests")
    print("=" * 60)

    # Connect to server using uv
    server_params = StdioServerParameters(
        command="uv",
        args=["run", "python", "main.py"],
        env=None
    )

    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            await session.initialize()

            scenarios = [
                {
                    "name": "Find all fire stations that responded to Hurricane Milton",
                    "tool": "execute_cypher",
                    "args": {
                        "query": """
                        MATCH (i:Incident {type: 'hurricane'})<-[:RESPONDED_TO]-(r:Resource)
                        WHERE r.type = 'fire_truck'
                        RETURN i.id, i.description, collect(r.id) as fire_resources
                        """,
                        "parameters": {}
                    }
                },
                {
                    "name": "Analyze department coordination patterns",
                    "tool": "execute_cypher",
                    "args": {
                        "query": """
                        MATCH (d1:Department)-[c:COORDINATES_WITH]-(d2:Department)
                        RETURN d1.name, d2.name, c.frequency, c.coordination_type
                        """,
                        "parameters": {}
                    }
                },
                {
                    "name": "Find resources needing maintenance",
                    "tool": "search_entities",
                    "args": {
                        "entity_type": "Resource",
                        "properties": {"status": "maintenance"},
                        "limit": 10
                    }
                }
            ]

            print("\nRunning scenario tests...\n")

            for scenario in scenarios:
                print(f"📌 Scenario: {scenario['name']}")

                try:
                    result = await session.call_tool(scenario['tool'], scenario['args'])
                    result_data = json.loads(result) if isinstance(result, str) else result

                    if result_data.get('success', True):
                        print(f"   ✅ Success")
                        if 'data' in result_data and result_data['data']:
                            print(f"   📊 Results: {len(result_data['data'])} items found")
                    else:
                        print(f"   ❌ Failed: {result_data.get('error', 'Unknown')}")

                except Exception as e:
                    print(f"   ❌ Error: {e}")

                print()

if __name__ == "__main__":
    print("""
    ╔═══════════════════════════════════════════════════════╗
    ║   GraphRAG MCP Server - Test Client                    ║
    ║   Testing Emergency Management Graph Operations        ║
    ╚═══════════════════════════════════════════════════════╝
    """)

    async def run_all_tests():
        # Run basic tests
        success = await test_graphrag_server()

        # Run scenario tests
        await test_specific_scenarios()

        return success

    try:
        success = asyncio.run(run_all_tests())
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️  Tests interrupted")
        exit(1)
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        exit(1)
from typing import Dict, List, Any
from dataclasses import dataclass, field
import time


@dataclass
class ConversationContext:
    """Represents a conversation session with message history."""
    conversation_id: str
    messages: List[Dict[str, str]] = field(default_factory=list)
    created_at: float = field(default_factory=time.time)
    last_updated: float = field(default_factory=time.time)


class ContextManager:
    """
    Simple in-memory session context management for MVP.
    
    Stores conversation history per session ID to enable
    continuity across requests within the same conversation.
    """
    
    def __init__(self):
        self._sessions: Dict[str, ConversationContext] = {}
    
    def get_context(self, conversation_id: str) -> ConversationContext:
        """Get or create conversation context for given session ID."""
        if conversation_id not in self._sessions:
            self._sessions[conversation_id] = ConversationContext(
                conversation_id=conversation_id
            )
        return self._sessions[conversation_id]
    
    def add_message(self, conversation_id: str, role: str, content: str):
        """Add a message to the conversation history."""
        context = self.get_context(conversation_id)
        context.messages.append({
            "role": role, 
            "content": content
        })
        context.last_updated = time.time()
    
    def get_messages(self, conversation_id: str) -> List[Dict[str, str]]:
        """Get all messages for a conversation in OpenAI format."""
        return self.get_context(conversation_id).messages
    
    def clear_context(self, conversation_id: str):
        """Clear conversation history for given session ID."""
        if conversation_id in self._sessions:
            del self._sessions[conversation_id]
    
    def get_session_count(self) -> int:
        """Get total number of active sessions."""
        return len(self._sessions)
    
    def cleanup_old_sessions(self, max_age_hours: int = 24):
        """Remove sessions older than max_age_hours."""
        current_time = time.time()
        cutoff_time = current_time - (max_age_hours * 3600)
        
        sessions_to_remove = [
            session_id for session_id, context in self._sessions.items()
            if context.last_updated < cutoff_time
        ]
        
        for session_id in sessions_to_remove:
            del self._sessions[session_id]
        
        return len(sessions_to_remove)


# Global instance for MVP - in production, this would be dependency-injected
context_manager = ContextManager()
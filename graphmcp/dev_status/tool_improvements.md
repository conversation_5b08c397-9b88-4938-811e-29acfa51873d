# MCP Tool Improvements

## Problem Summary

Current MCP tools return full node objects with all properties, causing response size issues when nodes contain large array properties (comments, incidents, transactions). This leads to token limit errors (25K tokens) when querying relationships.

**Example Issue:**
- `describe_relationship_type("ASSIGNED_VENDOR")` → 122K tokens (failed)
- `search_by_pattern("(m:Mission)-[r:ASSIGNED_VENDOR]->(v:Vendor)")` → 234K tokens (failed)

**Root Cause:**
Mission nodes contain large properties:
- `comments`: Array of 7+ strings (200+ chars each)
- `incidents`: JSON string with metadata (300+ chars)
- `transactions`: JSON string with data (200+ chars)

Sample query returns full nodes: `RETURN a, r, b` → ~5KB per node × 3 samples × 2 nodes = 30KB+

## Affected Tools

### High Priority (Returns Full Nodes)
1. **`describe_relationship_type()`** (schema_manager.py:281-314)
   - Query: `MATCH (a)-[r:{type}]->(b) RETURN a, r, b LIMIT 3`
   - Returns complete source and target nodes

2. **`describe_entity_type()`** (schema_manager.py:234-279)
   - Query: `MATCH (n:{entity_type}) RETURN n LIMIT 3`
   - Returns full node samples

3. **`search_by_pattern()`** (search_tools.py)
   - Likely returns full path objects with complete nodes

4. **`get_neighbors()`** (traversal_tools.py:23-73)
   - Returns connected entities with all properties

### Medium Priority (Potentially Large)
5. **`search_entities()`** - Returns filtered entities
6. **`find_paths()`** - Returns path data
7. **`traverse_pattern()`** - Returns pattern matches

## Recommended Solutions

### 1. Add Projection Parameters (Highest Priority)

**Location:** `graphmcp/src/graph/schema_manager.py:281-314`

Modify `describe_relationship()` to support field selection:

```python
def describe_relationship(
    self,
    relationship_type: str,
    include_samples: bool = True,  # NEW: Allow disabling samples
    sample_fields: Optional[List[str]] = None  # NEW: Field projection
) -> str:
    """
    Get detailed information about a specific relationship type.

    Args:
        relationship_type: Relationship type to describe
        include_samples: Whether to include sample relationships
        sample_fields: List of properties to return (e.g., ["id", "title"])
                      If None, returns only id and labels

    Returns:
        JSON string with relationship details
    """
    if relationship_type not in self.cache.get("relationship_types", {}):
        return json.dumps({
            "error": f"Relationship type '{relationship_type}' not found in schema"
        })

    rel_info = self.cache["relationship_types"][relationship_type]

    # Add property information if available
    if relationship_type in self.cache.get("properties", {}).get("relationship_properties", {}):
        rel_info["properties"] = self.cache["properties"]["relationship_properties"][relationship_type]

    # Skip samples if requested
    if not include_samples:
        rel_info["samples"] = []
        return json.dumps(rel_info, indent=2, default=str)

    # Default to minimal projection
    if sample_fields is None:
        sample_fields = ["id"]

    # Build property projection
    source_props = ", ".join(f"a.{f}" for f in sample_fields)
    target_props = ", ".join(f"b.{f}" for f in sample_fields)

    # Get sample relationships with field projection
    sample_query = f"""
    MATCH (a)-[r:{relationship_type}]->(b)
    RETURN
        labels(a) as source_labels,
        {{id: a.id, {source_props}}} as source,
        properties(r) as relationship,
        labels(b) as target_labels,
        {{id: b.id, {target_props}}} as target
    LIMIT 3
    """

    try:
        samples = self.graph_client.execute_query(sample_query)
        rel_info["samples"] = samples
    except Exception as e:
        logger.error(f"Error getting samples: {e}")
        rel_info["samples"] = []

    return json.dumps(rel_info, indent=2, default=str)
```

**Apply same pattern to:**
- `describe_entity()` (schema_manager.py:234-279)
- Update tool wrappers in `schema_tools.py` to expose these parameters

### 2. Add Result Limits

**Location:** `graphmcp/src/graph/schema_manager.py:__init__`

Add configurable sample limits:

```python
def __init__(
    self,
    graph_client,
    cache_ttl: int = 300,
    max_sample_size: int = 3  # NEW: Configurable sample limit
):
    self.graph_client = graph_client
    self.cache_ttl = cache_ttl
    self.max_sample_size = max_sample_size
    self.cache = {}
    self.cache_timestamp = None
```

Update queries to use `self.max_sample_size`:
```python
LIMIT {self.max_sample_size}
```

### 3. Implement Pagination for Large Results

**Location:** `graphmcp/src/tools/schema_tools.py:115-155`

Add pagination parameters to `describe_relationship_type()`:

```python
@mcp.tool()
def describe_relationship_type(
    relationship_type: str,
    limit: int = 3,        # NEW: Number of samples
    offset: int = 0,       # NEW: Pagination offset
    fields: Optional[List[str]] = None  # NEW: Field projection
) -> str:
    """
    Get detailed information about a specific relationship type.

    Args:
        relationship_type: The relationship type (e.g., "RESPONDED_TO", "ALLOCATED_TO")
        limit: Number of sample relationships to return (max 10, default 3)
        offset: Number of samples to skip for pagination (default 0)
        fields: List of node properties to include in samples (default: ["id"])

    Returns:
        Detailed information including:
        - Source and target entity types
        - Properties with types
        - Cardinality patterns
        - Sample relationships (limited and paginated)

    Example Call:
        describe_relationship_type("RESPONDED_TO", limit=5, fields=["id", "title"])
    """
    try:
        # Cap limit at 10 to prevent large responses
        limit = min(max(1, limit), 10)
        offset = max(0, offset)

        logger.info(f"describe_relationship_type called with relationship_type={relationship_type}, limit={limit}, offset={offset}")

        # Pass parameters to schema manager
        description = schema_manager.describe_relationship(
            relationship_type,
            limit=limit,
            offset=offset,
            sample_fields=fields
        )
        return description
    except Exception as e:
        logger.error(f"Error in describe_relationship_type: {str(e)}", exc_info=True)
        return json.dumps({"error": str(e), "success": False})
```

Update `schema_manager.py` to accept these parameters:

```python
def describe_relationship(
    self,
    relationship_type: str,
    limit: int = 3,
    offset: int = 0,
    sample_fields: Optional[List[str]] = None
) -> str:
    # ... existing validation ...

    sample_query = f"""
    MATCH (a)-[r:{relationship_type}]->(b)
    RETURN
        labels(a) as source_labels,
        {{id: a.id, {source_props}}} as source,
        properties(r) as relationship,
        labels(b) as target_labels,
        {{id: b.id, {target_props}}} as target
    SKIP {offset}
    LIMIT {limit}
    """
```

### 4. Add Response Size Warning

**Location:** `graphmcp/src/tools/schema_tools.py:115-155`

Add size checking to prevent token limit errors:

```python
@mcp.tool()
def describe_relationship_type(
    relationship_type: str,
    limit: int = 3,
    fields: Optional[List[str]] = None
) -> str:
    try:
        logger.info(f"describe_relationship_type called with relationship_type={relationship_type}")

        description = schema_manager.describe_relationship(
            relationship_type,
            limit=limit,
            sample_fields=fields
        )

        # Check response size before returning
        size_kb = len(description) / 1024

        if size_kb > 50:  # 50KB threshold (~12.5K tokens)
            logger.warning(f"Large response ({size_kb:.1f}KB) for {relationship_type}")

            # Return error with helpful suggestion
            parsed = json.loads(description)
            return json.dumps({
                "error": f"Response too large ({size_kb:.1f}KB exceeds 50KB limit)",
                "suggestion": "Use execute_cypher with specific field selection, or reduce limit parameter",
                "relationship_type": relationship_type,
                "count": parsed.get("count"),
                "from": parsed.get("from"),
                "to": parsed.get("to"),
                "properties": parsed.get("properties")
            }, indent=2)

        return description

    except Exception as e:
        logger.error(f"Error in describe_relationship_type: {str(e)}", exc_info=True)
        return json.dumps({"error": str(e), "success": False})
```

### 5. Create Lightweight Summary Tools

**Location:** `graphmcp/src/tools/schema_tools.py` (new tool)

Add new tool for relationship inspection without samples:

```python
@mcp.tool()
def get_relationship_summary(relationship_type: str) -> str:
    """
    Get relationship metadata WITHOUT sample data.

    Use this for large graphs to avoid token limits. This returns only
    the relationship structure and counts, not actual data samples.

    Args:
        relationship_type: The relationship type to summarize

    Returns:
        JSON object with metadata only:
        - relationship_type: Name of the relationship
        - count: Total number of relationships
        - from: Source entity types
        - to: Target entity types
        - properties: Available relationship properties

    Example Call:
        get_relationship_summary("ASSIGNED_VENDOR")

    Example Response:
        {
            "relationship_type": "ASSIGNED_VENDOR",
            "count": 9,
            "from": ["Mission"],
            "to": ["Vendor"],
            "properties": []
        }
    """
    try:
        logger.info(f"get_relationship_summary called with relationship_type={relationship_type}")

        schema = json.loads(schema_manager.get_cached_schema())

        if relationship_type not in schema.get("relationship_types", {}):
            return json.dumps({
                "error": f"Relationship type '{relationship_type}' not found in schema"
            })

        rel_info = schema["relationship_types"][relationship_type]

        # Return only metadata, no samples
        summary = {
            "relationship_type": relationship_type,
            "count": rel_info.get("count", 0),
            "from": rel_info.get("from", []),
            "to": rel_info.get("to", []),
            "properties": rel_info.get("properties", [])
        }

        return json.dumps(summary, indent=2)

    except Exception as e:
        logger.error(f"Error in get_relationship_summary: {str(e)}", exc_info=True)
        return json.dumps({"error": str(e), "success": False})
```

## Priority Implementation Order

### Immediate (< 10 minutes)
1. Add `include_samples=False` parameter to `describe_relationship()`
2. Add `get_relationship_summary()` tool for metadata-only queries

### Quick (15 minutes)
3. Implement field projection in `describe_relationship()`
4. Implement field projection in `describe_entity()`
5. Update tool wrappers to expose projection parameters

### Moderate (30 minutes)
6. Add pagination support (limit/offset) to all sample queries
7. Update all tools to support field projection
8. Add response size warnings

### Polish (15 minutes)
9. Add configurable `max_sample_size` to schema_manager
10. Create helper utilities for common projections
11. Update documentation with examples

## Testing Strategy

### Test Cases

1. **Large node properties**
   ```python
   # Should succeed with projection
   describe_relationship_type("ASSIGNED_VENDOR", fields=["id", "title"])

   # Should fail gracefully with size warning
   describe_relationship_type("ASSIGNED_VENDOR", limit=100)
   ```

2. **Pagination**
   ```python
   # First page
   describe_relationship_type("RELATED_TO_INCIDENT", limit=5, offset=0)

   # Second page
   describe_relationship_type("RELATED_TO_INCIDENT", limit=5, offset=5)
   ```

3. **Summary vs detailed**
   ```python
   # Fast metadata-only query
   get_relationship_summary("ASSIGNED_VENDOR")

   # Detailed query with samples
   describe_relationship_type("ASSIGNED_VENDOR", limit=3)
   ```

### Success Criteria

- [ ] All tools support field projection
- [ ] Response sizes stay under 50KB for typical queries
- [ ] Pagination works correctly with offset/limit
- [ ] Size warnings trigger before token limit errors
- [ ] Summary tools provide metadata without samples
- [ ] Backward compatibility maintained (default behavior unchanged)

## Configuration

Add to `.env` or `config.py`:

```bash
# Schema tool settings
MAX_SAMPLE_SIZE=3           # Default number of samples
MAX_RESPONSE_SIZE_KB=50     # Response size warning threshold
DEFAULT_SAMPLE_FIELDS=id    # Default fields for projections
```

## Related Issues

- Token limit errors on `describe_relationship_type()` with large nodes
- Performance issues with full node returns on large graphs
- Need for pagination on high-cardinality relationships
- Lack of metadata-only query options

## Implementation Notes

- Maintain backward compatibility: all new parameters should be optional
- Use consistent naming: `fields` (not `properties` or `columns`)
- Add logging for size warnings
- Update tool descriptions with new parameter documentation
- Consider adding `exclude_fields` parameter for inverse projection

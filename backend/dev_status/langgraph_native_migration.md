# LangGraph Native Migration Guide

**Date**: 2025-10-06
**Status**: Ready for Implementation
**Priority**: High
**Estimated Time**: 2-2.5 hours (Steps 1-4) + 30 minutes later (Step 5 - separate branch)

---

## 🤖 Prompt for Next Agent

**Copy this prompt to start the migration:**

```
I need you to implement the LangGraph Native Migration as documented in
dev_status/langgraph_native_migration.md

Key context:
- We're migrating from split architecture to LangGraph-native patterns
- Everything should be consolidated into workflows.py following LangGraph best practices
- This is a 4-step migration (Steps 1-4), with Step 5 deferred to a separate branch
- Current working directory: /Users/<USER>/Repos/DEMES/assistant/backend/src
- Test after EACH step using the specified test scripts
- Run integration tests after each step (test_service_integration.py + test_api_direct.sh)

Please:
1. Read the entire migration guide first
2. Execute Step 1 (Consolidate workflows.py)
3. Run tests and confirm working
4. Proceed to Steps 2-4 only after confirming each step works
5. DO NOT implement Step 5 (native streaming) - that's a separate branch for later

Important files:
- app/agent/langgraph/workflows.py (will be heavily modified)
- app/services/langgraph_service.py (minor changes in Steps 1-4)
- app/agent/langgraph/context.py (add iteration config in Step 2)
- scripts/test_service_integration.py (existing test)
- scripts/test_api_direct.sh (to be created)

Start by confirming you've read the migration guide and understand the approach.
```

---

## Executive Summary

**Problem**: Our current architecture splits LangGraph workflow concerns across multiple files and layers, violating LangGraph's design philosophy of "workflows as code, not configuration."

**Solution**: Consolidate everything into `workflows.py` following LangGraph's native patterns, with a thin translation layer at the API boundary.

**Goals**:
1. **Simplicity**: Everything in one place (`workflows.py`)
2. **Native Patterns**: Use LangGraph's recommended architecture
3. **Extensibility**: Easy to add subagents and modify behavior
4. **Real Streaming**: Use `astream_events()` instead of blocking `ainvoke()`

**Result**: 520+ lines across 3 files → 280 lines across 2 files

---

## Quick Reference Card

### What You're Building

```
workflows.py (ALL workflow logic)
├── Tool Discovery (from 2 MCP servers: Genie + GraphRAG)
├── Main Agent (synthesis model, all 34 tools)
├── Iteration Subagent (fast model, max 5 iterations, Genie tools only)
├── Tool Grouping (by server prefix)
└── Graph Construction (main graph + iteration subgraph)

langgraph_service.py (ONLY translation)
└── Format conversion (FastAPI ↔ LangGraph)
```

### Migration at a Glance

| Step | Time | What | Test |
|------|------|------|------|
| **1** | 30 min | Consolidate workflows.py | test_workflow_compilation.py + integration tests |
| **2** | 60 min | Add iteration subagent | test_iteration_subagent.py + integration tests |
| **3** | 30 min | Group tools by server | test_tool_grouping.py + integration tests |
| **4** | 30 min | Full integration testing | All tests |
| **5** | Later | Native streaming (separate branch) | User acceptance |

**Integration tests run after EVERY step:**
- ✅ `uv run python scripts/test_service_integration.py`
- ✅ `bash scripts/test_api_direct.sh`

### Key Files to Modify

```python
# Heavy modification (180 lines after)
app/agent/langgraph/workflows.py

# Light modification (add 3 lines)
app/agent/langgraph/context.py

# Minimal changes (keep ainvoke for now)
app/services/langgraph_service.py

# New test scripts (create these)
scripts/test_workflow_compilation.py
scripts/test_iteration_subagent.py
scripts/test_tool_grouping.py
scripts/test_api_direct.sh
```

### Success Criteria

After Steps 1-4:
- ✅ All code in workflows.py (single source of truth)
- ✅ Iteration subagent works (queries up to 5 times)
- ✅ Tools grouped by server (Genie vs GraphRAG)
- ✅ All existing tests pass
- ✅ API endpoint works identically
- ✅ Streaming still works (simulated is OK)

---

## Architecture Diagrams

### Current Architecture (Before Migration)

```mermaid
graph TB
    subgraph "Current Split Architecture"
        API[FastAPI Endpoint<br/>chat.py]

        subgraph "Service Layer - langgraph_service.py"
            SVC_INIT[__init__<br/>Empty initialization]
            SVC_SETUP[_ensure_setup<br/>Tool discovery<br/>Context creation<br/>Workflow compilation]
            SVC_STREAM[generate_response_with_streaming<br/>Blocking ainvoke<br/>Manual tool analysis<br/>Manual chunking]
        end

        subgraph "Workflow Layer - workflows.py"
            WF_CREATE[create_tool_calling_workflow<br/>Just graph structure]
            WF_AGENT[agent_node<br/>LLM with tools]
            WF_TOOLS[tools_node_wrapper<br/>Tool execution]
        end

        subgraph "State Layer - context.py"
            CTX[RuntimeContext<br/>State schema]
        end
    end

    API -->|instantiate| SVC_INIT
    SVC_INIT -->|first request| SVC_SETUP
    SVC_SETUP -->|calls| WF_CREATE
    SVC_SETUP -->|creates| CTX
    SVC_STREAM -->|uses| WF_AGENT
    SVC_STREAM -->|uses| WF_TOOLS

    style SVC_SETUP fill:#ffcccc
    style SVC_STREAM fill:#ffcccc
    style WF_CREATE fill:#ffeecc
```

**Problems:**
- 🔴 Tool discovery in service layer (should be workflow)
- 🔴 Context creation in service layer (should be workflow)
- 🔴 Workflow compilation in service layer (should be workflow)
- 🟡 Unnecessary wrapper function (create_tool_calling_workflow)
- 🔴 Blocking ainvoke with simulated streaming

---

### New Architecture (After Migration - Steps 1-4)

```mermaid
graph TB
    subgraph "New Simplified Architecture"
        API[FastAPI Endpoint<br/>chat.py]

        subgraph "Thin Service Layer - langgraph_service.py"
            SVC_STREAM[stream_chat_response<br/>Format translation only<br/>Dict → Messages<br/>Still uses ainvoke]
        end

        subgraph "Complete Workflow Layer - workflows.py"
            WF_DISCOVER[Tool Discovery<br/>Module-level<br/>Groups by server]
            WF_STATE[State Definitions<br/>AgentState<br/>IterationState]
            WF_MAIN[Main Agent Nodes<br/>agent_node<br/>tools_node]
            WF_ITER[Iteration Subagent<br/>iteration_agent_node<br/>iteration_tools_node<br/>Max 5 iterations]
            WF_GRAPH[create_graph<br/>Full graph construction<br/>All nodes + edges]
            WF_EXPORT[get_graph<br/>Lazy initialization]
        end

        subgraph "State Schema - context.py"
            CTX[RuntimeContext<br/>+ iteration config]
        end
    end

    API -->|calls| SVC_STREAM
    SVC_STREAM -->|gets| WF_EXPORT
    WF_EXPORT -->|returns| WF_GRAPH
    WF_GRAPH -->|contains| WF_MAIN
    WF_GRAPH -->|contains| WF_ITER
    WF_DISCOVER -->|provides tools to| WF_MAIN
    WF_DISCOVER -->|provides tools to| WF_ITER
    WF_STATE -->|used by| WF_GRAPH
    CTX -->|used by| WF_GRAPH

    style WF_DISCOVER fill:#ccffcc
    style WF_GRAPH fill:#ccffcc
    style WF_ITER fill:#ccffcc
    style SVC_STREAM fill:#ccffee
```

**Benefits:**
- ✅ All workflow logic in one file (workflows.py)
- ✅ Tool discovery at module load
- ✅ Tools grouped by MCP server
- ✅ Iteration subagent for deep queries
- ✅ Service layer is pure translation
- ⚠️ Still uses ainvoke (simulated streaming)

---

### Final Architecture (After Step 5 - Separate Branch)

```mermaid
graph TB
    subgraph "Final Polished Architecture"
        API[FastAPI Endpoint<br/>chat.py]

        subgraph "Minimal Service Layer - langgraph_service.py"
            SVC_STREAM[stream_chat_response<br/>Format translation only<br/>Dict → Messages<br/>Events → SSE<br/>Uses astream_events]
        end

        subgraph "Complete Workflow Layer - workflows.py"
            WF_DISCOVER[Tool Discovery<br/>Module-level<br/>Groups by server]
            WF_STATE[State Definitions<br/>AgentState<br/>IterationState]
            WF_MAIN[Main Agent Nodes<br/>agent_node<br/>tools_node]
            WF_ITER[Iteration Subagent<br/>iteration_agent_node<br/>iteration_tools_node<br/>Max 5 iterations]
            WF_GRAPH[create_graph<br/>Full graph construction<br/>All nodes + edges]
            WF_EXPORT[get_graph<br/>Lazy initialization]
        end

        subgraph "State Schema - context.py"
            CTX[RuntimeContext<br/>+ iteration config]
        end
    end

    API -->|calls| SVC_STREAM
    SVC_STREAM -->|astream_events| WF_EXPORT
    WF_EXPORT -->|returns| WF_GRAPH
    WF_GRAPH -->|streams events| SVC_STREAM
    WF_GRAPH -->|contains| WF_MAIN
    WF_GRAPH -->|contains| WF_ITER
    WF_DISCOVER -->|provides tools to| WF_MAIN
    WF_DISCOVER -->|provides tools to| WF_ITER
    WF_STATE -->|used by| WF_GRAPH
    CTX -->|used by| WF_GRAPH

    style WF_DISCOVER fill:#ccffcc
    style WF_GRAPH fill:#ccffcc
    style WF_ITER fill:#ccffcc
    style SVC_STREAM fill:#ccffff
```

**Final Benefits:**
- ✅ All workflow logic in one file (workflows.py)
- ✅ Tool discovery at module load
- ✅ Tools grouped by MCP server
- ✅ Iteration subagent for deep queries
- ✅ Service layer is pure translation
- ✅ **True token-by-token streaming**
- ✅ **Lower perceived latency**

---

## Table of Contents

1. [Current Architecture Problems](#current-architecture-problems)
2. [LangGraph's Native Approach](#langgraphs-native-approach)
3. [New Architecture](#new-architecture)
4. [Migration Steps](#migration-steps)
5. [Code Examples](#code-examples)
6. [Testing Strategy](#testing-strategy)
7. [Adding Subagents](#adding-subagents)

---

## Current Architecture Problems

### What We Have Now

```
app/services/langgraph_service.py (260 lines)
├── Tool discovery (lines 59-92)          ❌ Should be in workflow
├── RuntimeContext creation (lines 95-119) ❌ Should be in workflow
├── Workflow compilation (line 124)        ❌ Should be in workflow
├── Blocking ainvoke() (line 182)          ❌ Should use streaming
├── Manual tool analysis (lines 190-218)   ❌ Should use events
└── Manual chunking (lines 230-235)        ❌ Should use streaming

app/agent/langgraph/workflows.py (258 lines)
├── create_tool_calling_workflow()         ❌ Unnecessary wrapper
├── agent_node()                           ✅ Keep (node function)
└── tools_node_wrapper()                   ✅ Keep (node function)

app/agent/langgraph/context.py (163 lines)
└── RuntimeContext dataclass               ✅ Keep (state definition)
```

### Why This Is Wrong

1. **Developer Confusion**: "Where do I change workflow logic?"
   - Answer is split across 3 files
   - Tool discovery in service layer
   - Graph structure in workflows.py
   - Configuration in context.py

2. **Violates LangGraph Philosophy**:
   - LangGraph says: "Put everything in workflows.py"
   - We say: "Split it across a service layer"

3. **No Real Streaming**:
   - Using `ainvoke()` blocks until complete
   - Then manually chunking the response
   - Should use `astream_events()` for true streaming

4. **Unnecessary Abstraction**:
   - `LangGraphService` does what LangGraph should do
   - Extra layer makes debugging harder
   - Prevents using LangGraph features (checkpointing, etc.)

---

## LangGraph's Native Approach

### What LangGraph Documentation Shows

From the [official docs](https://langchain-ai.github.io/langgraph/how-tos/graph-api/):

```python
# EVERYTHING in workflows.py
from langgraph.graph import StateGraph, START, END

class State(TypedDict):
    messages: list[Message]

def my_node(state: State):
    # All logic here
    return {"messages": [...]}

# Build graph right here
builder = StateGraph(State)
builder.add_node("my_node", my_node)
builder.add_edge(START, "my_node")
builder.add_edge("my_node", END)

graph = builder.compile()
```

**Key principles**:
1. **All in one place** - No hunting across files
2. **Nodes are functions** - Just Python functions
3. **Explicit construction** - Clear graph building
4. **Let framework handle** - Persistence, streaming, retries

### Where Developers Make Changes

| Task | Location | What to Modify |
|------|----------|----------------|
| Change workflow logic | `workflows.py` | Modify graph construction |
| Add/remove nodes | `workflows.py` | Add function, register in graph |
| Change tools | `workflows.py` | Modify tool discovery section |
| Add subagents | `workflows.py` | Create subgraph, add as node |
| Change LLM behavior | `workflows.py` | Modify node function |

---

## New Architecture

### File Structure

```
app/agent/langgraph/
├── workflows.py (180 lines) ← EVERYTHING HERE
│   ├── Tool discovery
│   ├── State definitions
│   ├── Node functions
│   ├── Routing functions
│   └── Graph construction
│
├── context.py (163 lines) ← NO CHANGES (state schema)
└── state.py (keep as-is)

app/services/
└── langgraph_service.py (100 lines) ← SIMPLIFIED
    ├── Format translation (dict ↔ Messages)
    └── Event translation (LangGraph ↔ SSE)
```

### Responsibility Breakdown

**workflows.py** (LangGraph Layer):
- ✅ Tool discovery from MCP servers
- ✅ Graph construction and compilation
- ✅ Node function definitions
- ✅ Routing logic
- ✅ Subagent definitions

**langgraph_service.py** (Translation Layer):
- ✅ FastAPI dict → LangChain Message format
- ✅ LangGraph events → SSE StreamMessage format
- ✅ Error handling for HTTP layer
- ❌ NO workflow logic
- ❌ NO tool discovery
- ❌ NO graph compilation

---

## Migration Steps

### Step 1: Consolidate workflows.py (30 minutes)

**Goal**: Move all workflow concerns into `workflows.py`

**Changes**:
1. Add tool discovery at top of file
2. Remove `create_tool_calling_workflow()` wrapper
3. Add `create_graph()` function with everything inline
4. Export compiled graph with lazy initialization

**Test**: Run `test_workflow.py` if it exists

**Validation**: Can import and compile graph without errors

---

### Step 2: Add Subagent for Tool Iteration (60 minutes)

**Goal**: Create subagent that queries tools until satisfied (max 5 iterations)

**Changes**:
1. Add iteration subgraph in `workflows.py`
2. Configure max iterations in `context.py`
3. Route specific queries to iteration subagent

**Test**: Create new test that requires multiple tool calls

**Validation**:
- Subagent iterates up to 5 times
- Can query tools multiple times
- Returns consolidated result

---

### Step 3: Group Tools by MCP Server (30 minutes)

**Goal**: Organize tools by source server for targeted routing

**Changes**:
1. Add tool grouping function in `workflows.py`
2. Create separate tool lists for each server
3. Update subagent to use specific tool groups

**Test**: Run `test_service_integration.py`

**Validation**:
- Tools correctly grouped by prefix
- Subagents can access specific tool groups
- Main agent has access to all tools

---

### Step 4: Integration Testing (30 minutes)

**Goal**: Verify entire system works end-to-end

**Tests**:
1. Run `test_service_integration.py`
2. Test via direct API call (curl or httpie)
3. Test with frontend if available

**Validation**:
- All tests pass
- Streaming works correctly
- Subagent iteration works
- Tool grouping works

---

### Step 5: Add Native Streaming (30 minutes) **[SEPARATE BRANCH - DO LATER]**

**⚠️ IMPORTANT**: This step should be done AFTER completing Steps 1-4 and running full integration testing. Create a separate branch for this work.

**Why separate?**
- Steps 1-4 provide the core value (simplified architecture + subagents)
- Native streaming is UX polish, not functional requirement
- Allows testing core changes independently
- Lower risk - can always revert this branch if issues arise

**Prerequisites**:
- ✅ Steps 1-4 complete and tested
- ✅ Integration tests passing
- ✅ System running in dev/staging for at least a few days
- ✅ No critical bugs from core migration

**Goal**: Replace simulated streaming with true token-by-token streaming

**Current State (Simulated Streaming)**:
```python
# Blocks until LLM completes entire response
result = await self.workflow_app.ainvoke(initial_state, config)

# Then manually chunks it with fake delays
for i in range(0, len(final_response), chunk_size):
    chunk = final_response[i:i+chunk_size]
    yield StreamMessage(StreamType.CONTENT, {"chunk": chunk})
    await asyncio.sleep(0.03)  # Simulate streaming
```

**New State (Native Streaming)**:
```python
# Streams tokens as LLM generates them
async for event in self.workflow_app.astream_events(initial_state, config, version="v2"):
    if event["event"] == "on_chat_model_stream":
        yield StreamMessage(StreamType.CONTENT, {"chunk": event["data"]["chunk"].content})
```

**Changes** (Only in `langgraph_service.py`):
1. Replace `ainvoke()` with `astream_events()`
2. Add event translation logic (15 lines)
3. Remove manual tool analysis (30 lines)
4. Remove manual chunking (10 lines)

**Benefits**:
- ✅ True token-by-token streaming (ChatGPT-like)
- ✅ Lower perceived latency (first tokens arrive faster)
- ✅ Better observability (see what's happening in real-time)
- ✅ Cleaner code (~25 lines vs ~40 lines)

**Test**: Run `test_service_integration.py`

**Validation**:
- Tokens appear one at a time (not chunks)
- First token arrives faster
- Tool calls appear in real-time
- Response completes successfully
- All existing tests still pass

---

## Code Examples

### New workflows.py (Complete Implementation)

```python
"""
LangGraph workflows - all workflow logic in one place.

Developers modify THIS FILE to:
- Add/remove nodes
- Change workflow structure
- Configure tools
- Add subagents
- Modify routing logic
"""

from typing import TypedDict, Literal, List
from langgraph.graph import StateGraph, START, END, MessagesState
from langgraph.prebuilt import ToolNode
from langchain_openai import AzureChatOpenAI
from langchain_core.messages import AIMessage
from langchain_core.tools import BaseTool

from app.mcp.adapter import MCPToolAdapter
from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)

# ============================================================================
# 1. TOOL DISCOVERY (happens at module load)
# ============================================================================

# Configure MCP servers
_server_configs = {}
if settings.genie_mcp_enabled:
    _server_configs["genie"] = settings.genie_mcp_url
if settings.graphrag_mcp_enabled:
    _server_configs["graphrag"] = settings.graphrag_mcp_url

# Tool cache (lazy loaded)
_tools_cache = None
_tools_by_server = None

async def _discover_tools():
    """
    Discover tools from MCP servers.

    Called once and cached for the lifetime of the application.
    Developers modify _server_configs above to add/remove servers.
    """
    global _tools_cache, _tools_by_server

    if _tools_cache is not None:
        return _tools_cache, _tools_by_server

    if not _server_configs:
        logger.warning("No MCP servers configured")
        _tools_cache = []
        _tools_by_server = {}
        return _tools_cache, _tools_by_server

    logger.info(f"Discovering tools from {len(_server_configs)} MCP servers")

    # Discover all tools
    adapter = await MCPToolAdapter.discover_all_servers(_server_configs)
    all_tools = await adapter.discover_tools()

    # Group tools by server (based on tool name prefix)
    tools_by_server = {
        "genie": [],
        "graphrag": [],
        "unknown": []
    }

    for tool in all_tools:
        # Tool names are prefixed with server namespace (e.g., "genie.ask_genie")
        if tool.name.startswith("genie."):
            tools_by_server["genie"].append(tool)
        elif tool.name.startswith("graphrag."):
            tools_by_server["graphrag"].append(tool)
        else:
            tools_by_server["unknown"].append(tool)

    _tools_cache = all_tools
    _tools_by_server = tools_by_server

    logger.info(
        f"Discovered {len(all_tools)} tools",
        extra={
            "genie_tools": len(tools_by_server["genie"]),
            "graphrag_tools": len(tools_by_server["graphrag"]),
            "unknown_tools": len(tools_by_server["unknown"])
        }
    )

    return _tools_cache, _tools_by_server

# ============================================================================
# 2. STATE DEFINITIONS
# ============================================================================

class AgentState(MessagesState):
    """
    Main agent state.

    Developers add custom fields here as needed.
    """
    pass

class IterationState(MessagesState):
    """
    State for iteration subagent.

    Tracks iterations to enforce max limit.
    """
    iteration_count: int

# ============================================================================
# 3. NODE FUNCTIONS - MAIN AGENT
# ============================================================================

async def agent_node(state: AgentState):
    """
    Main agent node - routes to appropriate handler.

    Developers modify THIS FUNCTION to change main agent behavior:
    - Change model
    - Change temperature
    - Add system prompts
    - Modify tool binding
    """
    all_tools, _ = await _discover_tools()

    model = AzureChatOpenAI(
        azure_deployment=settings.synthesis_model_deployment,
        api_version=settings.api_version,
        azure_endpoint=settings.openai_endpoint,
        api_key=settings.openai_api_key,
        temperature=settings.synthesis_model_temperature
    )

    # Bind all tools to main agent
    model_with_tools = model.bind_tools(all_tools)
    response = await model_with_tools.ainvoke(state["messages"])

    return {"messages": [response]}

async def tools_node(state: AgentState):
    """
    Tool execution node.

    Uses LangGraph's prebuilt ToolNode for automatic tool execution.
    Developers rarely need to modify this.
    """
    all_tools, _ = await _discover_tools()
    tool_node = ToolNode(all_tools)
    return await tool_node.ainvoke(state)

def should_continue(state: AgentState) -> Literal["tools", "end"]:
    """
    Route to tools or end based on LLM decision.

    Developers modify THIS FUNCTION to add custom routing logic.
    """
    messages = state.get("messages", [])
    if not messages:
        return "end"

    last_message = messages[-1]
    if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
        return "tools"
    return "end"

# ============================================================================
# 4. NODE FUNCTIONS - ITERATION SUBAGENT
# ============================================================================

async def iteration_agent_node(state: IterationState):
    """
    Iteration subagent node - can query tools multiple times.

    Uses faster model for rapid iteration.
    Developers modify THIS FUNCTION to change iteration behavior.
    """
    _, tools_by_server = await _discover_tools()

    # Use fast model for iteration (cost-effective)
    model = AzureChatOpenAI(
        azure_deployment=settings.fast_model_deployment,
        api_version=settings.api_version,
        azure_endpoint=settings.openai_endpoint,
        api_key=settings.openai_api_key,
        temperature=settings.fast_model_temperature
    )

    # Only bind specific tools (e.g., genie tools for emergency data)
    # Developers can change which tool group to use
    iteration_tools = tools_by_server.get("genie", [])
    model_with_tools = model.bind_tools(iteration_tools)

    response = await model_with_tools.ainvoke(state["messages"])

    # Increment iteration count
    return {
        "messages": [response],
        "iteration_count": state.get("iteration_count", 0) + 1
    }

async def iteration_tools_node(state: IterationState):
    """
    Tool execution for iteration subagent.

    Uses subset of tools available to iteration agent.
    """
    _, tools_by_server = await _discover_tools()
    iteration_tools = tools_by_server.get("genie", [])
    tool_node = ToolNode(iteration_tools)
    return await tool_node.ainvoke(state)

def should_iterate(state: IterationState) -> Literal["tools", "end"]:
    """
    Decide whether to continue iterating.

    Stops if:
    1. No more tool calls
    2. Reached max iterations (5)

    Developers modify max iterations in context.py or here.
    """
    MAX_ITERATIONS = 5  # Can also read from settings

    iteration_count = state.get("iteration_count", 0)
    if iteration_count >= MAX_ITERATIONS:
        logger.info(f"Reached max iterations ({MAX_ITERATIONS})")
        return "end"

    messages = state.get("messages", [])
    if not messages:
        return "end"

    last_message = messages[-1]
    if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
        return "tools"
    return "end"

# ============================================================================
# 5. GRAPH CONSTRUCTION
# ============================================================================

def create_iteration_subgraph():
    """
    Create iteration subagent graph.

    This subagent can query tools multiple times (up to 5 iterations)
    before returning a result.

    Developers modify THIS FUNCTION to change subagent structure.
    """
    workflow = StateGraph(IterationState)

    # Add nodes
    workflow.add_node("agent", iteration_agent_node)
    workflow.add_node("tools", iteration_tools_node)

    # Add edges
    workflow.add_edge(START, "agent")
    workflow.add_conditional_edges(
        "agent",
        should_iterate,
        {
            "tools": "tools",
            "end": END
        }
    )
    workflow.add_edge("tools", "agent")  # Loop back for iteration

    return workflow.compile()

def create_main_graph():
    """
    Create main agent graph.

    This is the entry point for all requests.

    Developers modify THIS FUNCTION to:
    - Add new nodes
    - Change routing logic
    - Add more subagents
    - Modify graph structure
    """
    workflow = StateGraph(AgentState)

    # Add main agent nodes
    workflow.add_node("agent", agent_node)
    workflow.add_node("tools", tools_node)

    # Add iteration subagent (optional - can be routed to conditionally)
    iteration_subgraph = create_iteration_subgraph()
    workflow.add_node("iteration_subagent", iteration_subgraph)

    # Main flow: START → agent → (tools | end)
    workflow.add_edge(START, "agent")
    workflow.add_conditional_edges(
        "agent",
        should_continue,
        {
            "tools": "tools",
            "end": END
        }
    )
    workflow.add_edge("tools", "agent")  # Loop back after tools

    # TODO: Add conditional routing to iteration_subagent
    # Example: Check if query requires deep research, route to iteration_subagent

    return workflow.compile()

# ============================================================================
# 6. GRAPH EXPORT (Lazy Initialization)
# ============================================================================

_graph_cache = None

async def get_graph():
    """
    Get compiled graph with lazy initialization.

    This ensures tool discovery happens before graph compilation.
    """
    global _graph_cache

    if _graph_cache is None:
        # Ensure tools are discovered first
        await _discover_tools()

        # Compile graph
        _graph_cache = create_main_graph()
        logger.info("Main graph compiled successfully")

    return _graph_cache

# ============================================================================
# 7. UTILITY FUNCTIONS
# ============================================================================

async def get_tools_by_server(server_name: str) -> List[BaseTool]:
    """
    Get tools for a specific MCP server.

    Useful for creating specialized subagents.

    Args:
        server_name: "genie", "graphrag", or "unknown"

    Returns:
        List of tools from that server
    """
    _, tools_by_server = await _discover_tools()
    return tools_by_server.get(server_name, [])

async def get_all_tools() -> List[BaseTool]:
    """
    Get all discovered tools.

    Returns:
        List of all tools from all servers
    """
    all_tools, _ = await _discover_tools()
    return all_tools
```

### New langgraph_service.py (Simplified)

```python
"""
Thin adapter between FastAPI and LangGraph.

This layer ONLY handles format translation:
- FastAPI dicts → LangChain Message objects
- LangGraph events → SSE StreamMessage format
- Error handling for HTTP layer

NO workflow logic belongs here.
"""

from typing import AsyncGenerator, List, Dict
from langchain_core.messages import HumanMessage, AIMessage, ToolMessage

from app.agent.langgraph.workflows import get_graph
from app.services.streaming import StreamMessage, StreamType
from app.core.logging import get_logger

logger = get_logger(__name__)


async def stream_chat_response(
    messages: List[Dict[str, str]],
    conversation_id: str
) -> AsyncGenerator[StreamMessage, None]:
    """
    Stream chat response from LangGraph workflow.

    This function performs ONLY format translation:
    1. Translate FastAPI dict format → LangChain Message objects
    2. Execute workflow with native streaming
    3. Translate LangGraph events → SSE StreamMessage format

    Args:
        messages: Conversation history in FastAPI dict format
            [{"role": "user", "content": "..."}, ...]
        conversation_id: Session identifier

    Yields:
        StreamMessage: Formatted for SSE streaming
    """
    try:
        # Get compiled workflow
        workflow = await get_graph()

        # Translate: FastAPI dict → LangChain Message objects
        lc_messages = _convert_to_langchain(messages)

        logger.info(
            "Starting workflow stream",
            extra={
                "conversation_id": conversation_id,
                "message_count": len(lc_messages)
            }
        )

        # Use LangGraph native streaming (NOT blocking ainvoke!)
        async for event in workflow.astream_events(
            {"messages": lc_messages},
            version="v2"  # Use v2 event format
        ):
            # Translate: LangGraph events → StreamMessage
            stream_msg = _translate_event(event)
            if stream_msg:
                yield stream_msg

        # Final status
        yield StreamMessage(StreamType.STATUS, {"message": "Response complete"})

        logger.info(
            "Workflow stream completed",
            extra={"conversation_id": conversation_id}
        )

    except Exception as e:
        logger.error(
            "Workflow stream failed",
            extra={
                "error": str(e),
                "error_type": type(e).__name__,
                "conversation_id": conversation_id
            },
            exc_info=True
        )

        # Send error as content
        yield StreamMessage(
            StreamType.CONTENT,
            {"chunk": f"I encountered an error: {str(e)}"}
        )


def _convert_to_langchain(messages: List[Dict[str, str]]) -> List:
    """
    Convert FastAPI message format to LangChain format.

    FastAPI format:
        [{"role": "user", "content": "Hello"}, ...]

    LangChain format:
        [HumanMessage(content="Hello"), ...]
    """
    lc_messages = []

    for msg in messages:
        role = msg.get("role", "user")
        content = msg.get("content", "")

        if role == "user":
            lc_messages.append(HumanMessage(content=content))
        elif role == "assistant":
            lc_messages.append(AIMessage(content=content))
        elif role == "tool":
            # Tool messages need special handling
            lc_messages.append(ToolMessage(
                content=content,
                tool_call_id=msg.get("tool_call_id", "unknown")
            ))

    return lc_messages


def _translate_event(event: Dict) -> StreamMessage | None:
    """
    Translate LangGraph event to StreamMessage.

    LangGraph Event Types:
    - on_chat_model_start: Model begins processing
    - on_chat_model_stream: Token streaming (THIS IS WHAT WE WANT!)
    - on_chat_model_end: Model completes
    - on_tool_start: Tool execution begins
    - on_tool_end: Tool execution completes
    - on_chain_start/end: Node execution events

    Args:
        event: LangGraph event dictionary

    Returns:
        StreamMessage or None if event should be ignored
    """
    event_type = event.get("event")

    # Model starts reasoning
    if event_type == "on_chat_model_start":
        return StreamMessage(
            StreamType.REASONING,
            {"step": "Agent analyzing query..."}
        )

    # Token streaming - this is the actual content!
    elif event_type == "on_chat_model_stream":
        chunk_data = event.get("data", {}).get("chunk", {})

        # Extract content from chunk
        if hasattr(chunk_data, "content") and chunk_data.content:
            return StreamMessage(
                StreamType.CONTENT,
                {"chunk": chunk_data.content}
            )

    # Tool execution starts
    elif event_type == "on_tool_start":
        tool_name = event.get("name", "unknown")
        return StreamMessage(
            StreamType.SOURCES,
            {"source": tool_name}
        )

    # Node execution events (for custom reasoning)
    elif event_type == "on_chain_start":
        node_name = event.get("name")

        if node_name == "agent":
            return StreamMessage(
                StreamType.REASONING,
                {"step": "Processing with main agent..."}
            )
        elif node_name == "tools":
            return StreamMessage(
                StreamType.REASONING,
                {"step": "Executing tool calls..."}
            )
        elif node_name == "iteration_subagent":
            return StreamMessage(
                StreamType.REASONING,
                {"step": "Using iteration agent for deep research..."}
            )

    # Ignore other events
    return None
```

### Updated context.py (Add Iteration Config)

```python
# Add to existing RuntimeContext dataclass

@dataclass
class RuntimeContext:
    """
    Runtime context for LangGraph dependency injection.
    """

    # Existing fields...
    tools: List[BaseTool]
    genie_space_id: str
    fast_model: str
    synthesis_model: str
    fast_model_temperature: float = 0.0
    synthesis_model_temperature: float = 0.7
    max_iterations: int = 3
    enable_graph_iteration: bool = True
    tool_timeout: int = 30
    llm_timeout: int = 60

    # NEW: Iteration subagent configuration
    iteration_max_iterations: int = 5  # Max iterations for subagent
    iteration_enabled: bool = True      # Enable/disable iteration subagent
```

---

## Testing Strategy

### Test 1: Workflow Compilation

**File**: `scripts/test_workflow_compilation.py` (NEW)

```python
"""
Test that workflow compiles without errors.

Run: uv run python scripts/test_workflow_compilation.py
"""

import asyncio
from app.agent.langgraph.workflows import get_graph, get_all_tools

async def test_compilation():
    print("Testing workflow compilation...")

    # Get all tools
    tools = await get_all_tools()
    print(f"✓ Discovered {len(tools)} tools")

    # Get compiled graph
    graph = await get_graph()
    print("✓ Graph compiled successfully")

    # Try to visualize (optional)
    try:
        mermaid = graph.get_graph().draw_mermaid()
        print("✓ Graph visualization generated")
        print("\nGraph structure:")
        print(mermaid)
    except Exception as e:
        print(f"⚠ Could not generate visualization: {e}")

    print("\n✓ All compilation tests passed!")

if __name__ == "__main__":
    asyncio.run(test_compilation())
```

### Test 2: Service Integration

**File**: `scripts/test_service_integration.py` (EXISTING - should still work)

```bash
# Run existing test
uv run python scripts/test_service_integration.py
```

**Expected Output**:
- ✓ Streaming responses
- ✓ Tool calls in stream
- ✓ Response completes
- ✓ No errors

### Test 3: Direct API Call

**File**: `scripts/test_api_direct.sh` (NEW)

```bash
#!/bin/bash
# Test direct API call to streaming endpoint

echo "Testing streaming endpoint..."

curl -N -X POST http://localhost:8000/api/v1/chat/stream \
  -H "Content-Type: application/json" \
  -d '{
    "message": "What are the current emergency incidents in Florida?",
    "conversation_id": "test-migration"
  }'

echo "\n✓ API test complete"
```

### Test 4: Iteration Subagent

**File**: `scripts/test_iteration_subagent.py` (NEW)

```python
"""
Test iteration subagent with multiple tool calls.

Run: uv run python scripts/test_iteration_subagent.py
"""

import asyncio
from langchain_core.messages import HumanMessage
from app.agent.langgraph.workflows import get_graph

async def test_iteration():
    print("Testing iteration subagent...")

    graph = await get_graph()

    # Query that should require multiple tool calls
    messages = [
        HumanMessage(content=(
            "Give me detailed information about Hurricane Milton. "
            "I need county-level data, mission information, and any related incidents."
        ))
    ]

    iteration_count = 0
    tool_calls = []

    async for event in graph.astream_events(
        {"messages": messages},
        version="v2"
    ):
        event_type = event.get("event")

        if event_type == "on_tool_start":
            tool_calls.append(event.get("name"))
            print(f"  Tool call {len(tool_calls)}: {event.get('name')}")

        if event_type == "on_chain_end" and event.get("name") == "iteration_agent_node":
            iteration_count += 1
            print(f"  Iteration {iteration_count} complete")

    print(f"\n✓ Completed {iteration_count} iterations")
    print(f"✓ Made {len(tool_calls)} tool calls")
    print(f"✓ Tools used: {set(tool_calls)}")

    if iteration_count > 1:
        print("✓ Iteration subagent working correctly!")
    else:
        print("⚠ Only 1 iteration - subagent may not be active")

if __name__ == "__main__":
    asyncio.run(test_iteration())
```

### Test 5: Tool Grouping

**File**: `scripts/test_tool_grouping.py` (NEW)

```python
"""
Test that tools are correctly grouped by server.

Run: uv run python scripts/test_tool_grouping.py
"""

import asyncio
from app.agent.langgraph.workflows import get_tools_by_server, get_all_tools

async def test_grouping():
    print("Testing tool grouping...")

    all_tools = await get_all_tools()
    genie_tools = await get_tools_by_server("genie")
    graphrag_tools = await get_tools_by_server("graphrag")

    print(f"\n✓ Total tools: {len(all_tools)}")
    print(f"✓ Genie tools: {len(genie_tools)}")
    print(f"✓ GraphRAG tools: {len(graphrag_tools)}")

    print("\nGenie tools:")
    for tool in genie_tools:
        print(f"  - {tool.name}")

    print("\nGraphRAG tools:")
    for tool in graphrag_tools:
        print(f"  - {tool.name}")

    # Verify grouping is correct
    assert len(genie_tools) + len(graphrag_tools) <= len(all_tools)
    print("\n✓ Tool grouping working correctly!")

if __name__ == "__main__":
    asyncio.run(test_grouping())
```

---

## Adding Subagents

### Example: Research Subagent

```python
# Add to workflows.py

class ResearchState(MessagesState):
    """State for research subagent."""
    research_depth: int  # Track depth of research

async def research_agent_node(state: ResearchState):
    """
    Research subagent - deep dives into topics.

    Uses GraphRAG tools exclusively.
    """
    _, tools_by_server = await _discover_tools()
    research_tools = tools_by_server.get("graphrag", [])

    model = AzureChatOpenAI(
        azure_deployment=settings.fast_model_deployment,
        api_version=settings.api_version,
        azure_endpoint=settings.openai_endpoint,
        api_key=settings.openai_api_key,
        temperature=0.1  # Lower temp for factual research
    )

    model_with_tools = model.bind_tools(research_tools)
    response = await model_with_tools.ainvoke(state["messages"])

    return {
        "messages": [response],
        "research_depth": state.get("research_depth", 0) + 1
    }

def create_research_subgraph():
    """Create research subagent."""
    workflow = StateGraph(ResearchState)

    workflow.add_node("agent", research_agent_node)
    workflow.add_node("tools", lambda s: ToolNode(
        get_tools_by_server("graphrag")
    ).ainvoke(s))

    workflow.add_edge(START, "agent")
    workflow.add_conditional_edges("agent", should_continue)
    workflow.add_edge("tools", "agent")

    return workflow.compile()

# Add to main graph
def create_main_graph():
    workflow = StateGraph(AgentState)

    # ... existing nodes ...

    # Add research subagent
    research_subgraph = create_research_subgraph()
    workflow.add_node("research_agent", research_subgraph)

    # Add routing to research agent
    def route_to_research(state) -> Literal["research_agent", "agent"]:
        last_msg = state["messages"][-1].content.lower()
        if any(word in last_msg for word in ["research", "analyze", "investigate"]):
            return "research_agent"
        return "agent"

    workflow.add_conditional_edges(START, route_to_research)

    return workflow.compile()
```

---

## Migration Checklist

### Pre-Migration

- [ ] Read this document completely
- [ ] Understand current architecture
- [ ] Understand new architecture
- [ ] Review all code examples
- [ ] Set up test environment

### Step 1: Consolidate workflows.py

- [ ] Backup current `workflows.py`
- [ ] Add tool discovery section
- [ ] Remove `create_tool_calling_workflow()` wrapper
- [ ] Add `create_graph()` function
- [ ] Add lazy initialization
- [ ] Test: `uv run python scripts/test_workflow_compilation.py`
- [ ] Verify: No errors, graph compiles
- [ ] **Run integration tests:**
  - [ ] Run `uv run python scripts/test_service_integration.py`
  - [ ] Run `bash scripts/test_api_direct.sh`
  - [ ] Verify: All existing functionality still works

### Step 2: Add Iteration Subagent

- [ ] Add `IterationState` to workflows.py
- [ ] Add iteration node functions
- [ ] Create iteration subgraph
- [ ] Add to main graph
- [ ] Update `context.py` with iteration config
- [ ] Test: `uv run python scripts/test_iteration_subagent.py`
- [ ] Verify: Subagent iterates up to 5 times
- [ ] **Run integration tests:**
  - [ ] Run `uv run python scripts/test_service_integration.py`
  - [ ] Run `bash scripts/test_api_direct.sh`
  - [ ] Verify: Existing functionality + subagent working

### Step 3: Group Tools by Server

- [ ] Add tool grouping in `_discover_tools()`
- [ ] Create `get_tools_by_server()` utility
- [ ] Update subagents to use specific tool groups
- [ ] Test: `uv run python scripts/test_tool_grouping.py`
- [ ] Verify: Tools correctly grouped
- [ ] **Run integration tests:**
  - [ ] Run `uv run python scripts/test_service_integration.py`
  - [ ] Run `bash scripts/test_api_direct.sh`
  - [ ] Verify: Tool routing working correctly

### Step 4: Integration Testing

- [ ] Run `test_service_integration.py`
- [ ] Run direct API test: `bash scripts/test_api_direct.sh`
- [ ] Test with frontend (if available)
- [ ] Verify all features work end-to-end
- [ ] Monitor logs for errors

### Step 5: Add Native Streaming **[SEPARATE BRANCH - DO LATER]**

**⚠️ Create a new branch:** `feature/native-streaming` or similar

**Prerequisites before starting this step:**
- [ ] Steps 1-4 complete and merged
- [ ] System tested in dev/staging environment
- [ ] No critical bugs from Steps 1-4
- [ ] User acceptance of current streaming UX

**Implementation:**
- [ ] Create new branch from main
- [ ] Backup current `langgraph_service.py`
- [ ] Replace `ainvoke()` with `astream_events()`
- [ ] Add event translation logic
- [ ] Remove manual tool analysis (lines 190-218)
- [ ] Remove manual chunking (lines 230-235)
- [ ] Test: `uv run python scripts/test_service_integration.py`
- [ ] Verify: Tokens stream one at a time
- [ ] **Run integration tests:**
  - [ ] Run `uv run python scripts/test_service_integration.py`
  - [ ] Run `bash scripts/test_api_direct.sh`
  - [ ] Verify: True streaming working correctly
- [ ] Compare latency: First token arrival time
- [ ] User acceptance testing
- [ ] Merge to main if approved

### Post-Migration

- [ ] Update documentation
- [ ] Remove old code/comments
- [ ] Update README.md
- [ ] Commit changes
- [ ] Deploy to dev environment

---

## Troubleshooting

### Issue: Graph Won't Compile

**Symptoms**: Error when calling `get_graph()`

**Solutions**:
1. Check that all node functions are async
2. Verify edge names match node names
3. Ensure START and END are imported
4. Check for circular dependencies

### Issue: No Streaming Output

**Symptoms**: Endpoint hangs or no output

**Solutions**:
1. Verify using `astream_events()` not `ainvoke()`
2. Check event translation logic
3. Ensure yielding StreamMessage objects
4. Verify SSE format is correct

### Issue: Subagent Not Iterating

**Symptoms**: Only 1 iteration when should be more

**Solutions**:
1. Check `should_iterate()` logic
2. Verify iteration count is incrementing
3. Check max iterations setting
4. Ensure loop edge is correct (tools → agent)

### Issue: Tool Grouping Not Working

**Symptoms**: Tools not separated by server

**Solutions**:
1. Check tool name prefixes
2. Verify `_discover_tools()` grouping logic
3. Ensure servers are configured correctly
4. Check tool discovery logs

---

## Benefits Summary

### Before Migration

- ❌ 520+ lines across 3 files
- ❌ Workflow logic split across layers
- ❌ Blocking `ainvoke()` with manual chunking
- ❌ Hard to add subagents
- ❌ Tools not organized
- ❌ Confusion about where to make changes

### After Migration

- ✅ 280 lines across 2 files (46% reduction)
- ✅ Everything in `workflows.py` (single source of truth)
- ✅ Native `astream_events()` (true streaming)
- ✅ Easy subagent addition (just add function + node)
- ✅ Tools grouped by server (targeted routing)
- ✅ Clear where to make changes

---

## Next Steps After Migration

1. **Add More Subagents**:
   - Research agent (GraphRAG focused)
   - Summary agent (synthesis focused)
   - Validation agent (fact-checking)

2. **Add Checkpointing**:
   - Save conversation state
   - Resume from checkpoints
   - Enable time-travel debugging

3. **Add Human-in-the-Loop**:
   - Pause for user input
   - Approve tool calls
   - Provide feedback

4. **Optimize Performance**:
   - Cache frequently used queries
   - Parallel tool execution
   - Streaming optimizations

---

## Document History

- **2025-10-06**: Initial version
- **Status**: Ready for implementation
- **Owner**: Architecture Team

---

## Key Context Information

### Important Configuration Values

**From `.env` / `config.py`:**
```python
# MCP Servers
GENIE_MCP_ENABLED=true
GENIE_MCP_URL=https://genai-graysky-dev-mcp.azurewebsites.net
GRAPHRAG_MCP_ENABLED=true
GRAPHRAG_MCP_URL=http://localhost:8001/mcp

# Model Configuration
FAST_MODEL_DEPLOYMENT=gpt-4.1-mini              # For iteration subagent
FAST_MODEL_TEMPERATURE=0.0
SYNTHESIS_MODEL_DEPLOYMENT=gpt-4.1-mini         # For main agent
SYNTHESIS_MODEL_TEMPERATURE=0.7

# Azure OpenAI
OPENAI_API_KEY=<your-key>
OPENAI_ENDPOINT=https://fdem-demes-openai-01-dev.openai.azure.com/
API_VERSION=2024-12-01-preview
```

### Current Tool Inventory

**As of 2025-10-06:**
- **Genie MCP Tools** (~4 tools): Emergency incident data, mission data
- **GraphRAG MCP Tools** (~30 tools): Graph database queries, entity search
- **Total**: ~34 tools from 2 MCP servers

Tool names follow pattern: `{server_name}.{tool_name}`
- Example: `genie.ask_genie`, `graphrag.search_entities`

### File Locations

```
backend/src/
├── app/
│   ├── agent/
│   │   └── langgraph/
│   │       ├── workflows.py       ← Main file to modify (180 lines after)
│   │       ├── context.py         ← Add iteration config
│   │       └── state.py           ← No changes needed
│   ├── services/
│   │   └── langgraph_service.py  ← Simplified (100 lines after)
│   └── mcp/
│       └── adapter.py             ← No changes (already works)
└── scripts/
    ├── test_service_integration.py  ← Existing test
    ├── test_workflow_compilation.py ← Create new
    ├── test_iteration_subagent.py   ← Create new
    ├── test_tool_grouping.py        ← Create new
    └── test_api_direct.sh           ← Create new
```

### Key Architectural Decisions Made

1. **Keep simulated streaming for Steps 1-4** - Defer native streaming to Step 5
2. **Tool grouping by prefix** - Enables routing queries to specialized subagents
3. **Iteration limit of 5** - Configurable in context.py
4. **Module-level tool discovery** - Tools discovered once at import time
5. **Lazy graph compilation** - Graph compiled on first request, then cached

### Known Constraints

1. **MCP servers must be running** - Tests will fail if servers are down
2. **Azure OpenAI credentials required** - No fallback to local models
3. **Context injection pattern** - LangGraph's native dependency injection via config dict
4. **No LangGraph Studio compatibility** - Our pattern uses async tool discovery

### Rollback Plan

If migration fails:
```bash
# Each step creates backups
mv workflows.py.backup workflows.py
mv langgraph_service.py.backup langgraph_service.py

# Or revert git commits
git log --oneline  # Find commit before migration
git revert <commit-hash>
```

### Critical Functionality That Must Work

**After each migration step, verify these work:**

1. **Basic Chat** (no tools needed):
   ```bash
   curl -N http://localhost:8000/api/v1/chat/stream \
     -H "Content-Type: application/json" \
     -d '{"message": "Hello", "conversation_id": "test"}'
   ```
   **Expected**: STATUS → REASONING → CONTENT → STATUS

2. **Tool Calling** (Genie tools):
   ```bash
   curl -N http://localhost:8000/api/v1/chat/stream \
     -H "Content-Type: application/json" \
     -d '{"message": "What are current emergencies in Florida?", "conversation_id": "test2"}'
   ```
   **Expected**: STATUS → REASONING → SOURCES (tool names) → CONTENT → STATUS

3. **Multi-turn Conversation**:
   - Use same conversation_id for multiple requests
   - Agent should remember previous context
   - Context manager must still work

4. **Error Handling**:
   - Invalid model names should fail gracefully
   - MCP server failures should be caught
   - Malformed requests should return errors

5. **MCP Introspection Endpoint**:
   ```bash
   curl http://localhost:8000/api/v1/mcp/servers
   ```
   **Expected**: JSON with server status and tool count

### Environment Requirements

**Before starting migration:**

1. **MCP Servers Running**:
   ```bash
   # Check Genie MCP
   curl https://genai-graysky-dev-mcp.azurewebsites.net/health

   # Check GraphRAG MCP (if local)
   curl http://localhost:8001/health
   ```

2. **Backend Server Running**:
   ```bash
   cd src
   uv run uvicorn app.main:app --reload
   ```

3. **Valid Azure Credentials**:
   - Check `.env` has correct OPENAI_API_KEY
   - Verify endpoint and deployment names

### Common Pitfalls to Avoid

1. **Don't remove RuntimeContext injection** - LangGraph needs context in config dict
2. **Don't break message format** - Frontend expects specific StreamMessage format
3. **Don't skip backups** - Create .backup files before modifying
4. **Don't skip tests** - Run after EACH step, not at the end
5. **Don't merge Step 5 prematurely** - Keep it separate until validated

### Tool Name Prefixes (For Grouping)

```python
# Genie tools (emergency data)
"genie.ask_genie"
"genie.get_missions"
"genie.get_incident_details"
"genie.search_incidents"

# GraphRAG tools (graph queries)
"graphrag.search_entities"
"graphrag.query_graph"
"graphrag.find_relationships"
# ... ~27 more graphrag tools
```

Use this pattern to filter:
```python
genie_tools = [t for t in all_tools if t.name.startswith("genie.")]
graphrag_tools = [t for t in all_tools if t.name.startswith("graphrag.")]
```

---

## References

- [LangGraph Graph API Documentation](https://langchain-ai.github.io/langgraph/how-tos/graph-api/)
- [LangGraph Streaming Guide](https://langchain-ai.github.io/langgraph/concepts/streaming/)
- [LangGraph Subgraphs](https://langchain-ai.github.io/langgraph/concepts/subgraphs/)
- [LangGraph astream_events](https://langchain-ai.github.io/langgraph/how-tos/streaming/)
- Current Implementation: `app/services/langgraph_service.py`
- Current Implementation: `app/agent/langgraph/workflows.py`
- Backend CLAUDE.md: `backend/CLAUDE.md`
- Parent CLAUDE.md: `assistant/CLAUDE.md`

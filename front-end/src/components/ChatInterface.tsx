'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { ChatAPI } from '../services/chatApi';
import { Message, ConnectionStatus, DebugMessage, StreamType } from '../types/chat';
import { MessageList } from './MessageList';
import ChatInput from './ChatInput';

export const ChatInterface: React.FC = () => {
  const [api] = useState(() => new ChatAPI());
  const [messages, setMessages] = useState<Message[]>([]);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('disconnected');
  const [debugMessages, setDebugMessages] = useState<{
    status: DebugMessage[];
    reasoning: DebugMessage[];
    sources: DebugMessage[];
    content: DebugMessage[];
  }>({
    status: [],
    reasoning: [],
    sources: [],
    content: []
  });
  const [currentStreamingMessageId, setCurrentStreamingMessageId] = useState<string | null>(null);

  useEffect(() => {
    checkHealth();
  }, []);

  const checkHealth = async () => {
    try {
      await api.testHealth();
      setConnectionStatus('connected');
    } catch (error) {
      setConnectionStatus('error');
    }
  };

  const generateMessageId = (): string => {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
  };

  const addMessage = useCallback((sender: 'user' | 'assistant' | 'system', content: string, isStreaming = false): string => {
    const messageId = generateMessageId();
    const newMessage: Message = {
      id: messageId,
      sender,
      content,
      timestamp: new Date(),
      isStreaming
    };

    setMessages(prev => [...prev, newMessage]);
    return messageId;
  }, []);

  const updateMessage = useCallback((messageId: string, content: string) => {
    setMessages(prev => prev.map(msg => 
      msg.id === messageId ? { ...msg, content } : msg
    ));
  }, []);

  const appendToMessage = useCallback((messageId: string, content: string) => {
    setMessages(prev => prev.map(msg => 
      msg.id === messageId ? { ...msg, content: msg.content + content } : msg
    ));
  }, []);

  const finalizeMessage = useCallback((messageId: string) => {
    setMessages(prev => prev.map(msg => 
      msg.id === messageId ? { ...msg, isStreaming: false } : msg
    ));
  }, []);

  const addDebugMessage = useCallback((type: StreamType, data: any) => {
    const debugMessage: DebugMessage = {
      type,
      timestamp: new Date(),
      data
    };

    setDebugMessages(prev => ({
      ...prev,
      [type.toLowerCase()]: [debugMessage, ...prev[type.toLowerCase() as keyof typeof prev]]
    }));
  }, []);

  const clearDebugMessages = useCallback(() => {
    setDebugMessages({
      status: [],
      reasoning: [],
      sources: [],
      content: []
    });
  }, []);

  const updateConnectionStatus = useCallback((status: ConnectionStatus) => {
    setConnectionStatus(status);
  }, []);

  const handleSendMessage = useCallback(async (message: string) => {
    // Add user message to chat
    addMessage('user', message);
    
    // Clear debug panels
    clearDebugMessages();
    
    // Prepare for assistant response
    const messageId = addMessage('assistant', '', true);
    setCurrentStreamingMessageId(messageId);
    
    // Update status
    updateConnectionStatus('streaming');

    // Send message with callbacks for each stream type
    await api.sendMessage(message, {
      onStatus: (data, metadata) => {
        console.log('📊 STATUS:', data);
        addDebugMessage('STATUS', data);
        // Don't overwrite the main message content - STATUS is shown in debug panel
        
        // Can add to main chat display if desired
        // if (data.message) {
        //   appendToMessage(messageId, `⚙️ *Status: ${data.message}*\n`);
        // }
      },

      onReasoning: (data, metadata) => {
        console.log('🧠 REASONING:', data);
        addDebugMessage('REASONING', data);
        // Don't overwrite the main message content - REASONING is shown in debug panel

        // Can add to main chat display if desired
        if (data.step) {
          appendToMessage(messageId, `\n💭 *Reasoning: ${data.step}*\n`);
        }
      },

      onSources: (data, metadata) => {
        console.log('📚 SOURCES:', data);
        addDebugMessage('SOURCES', data);
        // Don't overwrite the main message content - SOURCES is shown in debug panel
      },

      onContent: (data, metadata) => {
        console.log('📝 CONTENT:', data);
        addDebugMessage('CONTENT', data);
        
        // Append content to current message
        if (data.chunk || data.text) {
          appendToMessage(messageId, data.chunk || data.text);
        } else if (data.message) {
          appendToMessage(messageId, data.message);
        }
      },

      onError: (error) => {
        console.error('❌ Stream error:', error);
        updateConnectionStatus('error');
        updateMessage(messageId, `❌ Error: ${error.message}`);
        finalizeMessage(messageId);
        setCurrentStreamingMessageId(null);
      },

      onComplete: () => {
        console.log('✅ Stream complete');
        updateConnectionStatus('connected');
        finalizeMessage(messageId);
        setCurrentStreamingMessageId(null);
      }
    });
  }, [api, addMessage, updateMessage, appendToMessage, finalizeMessage, addDebugMessage, clearDebugMessages, updateConnectionStatus]);

  const handleDemoMessage = (message: string) => {
    handleSendMessage(message);
  };

  const handleResetConversation = () => {
    api.resetConversation();
    setMessages([]);
    clearDebugMessages();
    setCurrentStreamingMessageId(null);
  };

  const getConnectionStatusText = (): string => {
    switch (connectionStatus) {
      case 'connected':
        return 'Connected';
      case 'streaming':
        return 'Streaming...';
      case 'error':
        return 'Disconnected - Backend not running?';
      default:
        return 'Disconnected';
    }
  };

  const renderDebugPanel = (type: StreamType, messages: DebugMessage[]) => {
    const typeKey = type.toLowerCase() as keyof typeof debugMessages;
    return (
      <div className="stream-type">
        <span className={`type-label ${typeKey}`}>{type}</span>
        <div>
          {messages.map((msg, index) => (
            <div key={index} className="debug-message">
              <span className="timestamp">{msg.timestamp.toLocaleTimeString()}</span>
              <pre>{JSON.stringify(msg.data, null, 2)}</pre>
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="container">
      <header>
        <h1>Digital State Responder</h1>
        <p>Florida Division of Emergency Management AI Assistant</p>
      </header>

      <div className="chat-container">
        <MessageList messages={messages} />
        <ChatInput
          onSendMessage={handleSendMessage}
          disabled={connectionStatus === 'streaming'}
        />

        <div className="status-section">
          <div className={`connection-status ${connectionStatus}`}>
            {getConnectionStatusText()}
          </div>
          <div className="api-info">
            <strong>API Endpoint:</strong> <code>POST /api/v1/chat/stream</code>
          </div>
        </div>
      </div>

      <div className="stream-debug">
        <h3>Stream Debug Panel</h3>
        <div className="stream-types">
          {renderDebugPanel('STATUS', debugMessages.status)}
          {renderDebugPanel('REASONING', debugMessages.reasoning)}
          {renderDebugPanel('SOURCES', debugMessages.sources)}
          {renderDebugPanel('CONTENT', debugMessages.content)}
        </div>
      </div>

      <div className="demo-section">
        <h3>Demo Messages</h3>
        <button onClick={() => handleDemoMessage('Hello, can you help me?')}>
          Simple Hello
        </button>
        <button onClick={() => handleDemoMessage('Explain how machine learning works')}>
          Complex Question
        </button>
        <button onClick={handleResetConversation}>
          Reset Conversation
        </button>
      </div>
    </div>
  );
};
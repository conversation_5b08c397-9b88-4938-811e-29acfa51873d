"""
ONNX-based embedding client using local model files.

This module provides:
- Text embedding generation using ONNX runtime
- Batch processing of embeddings
- Lazy model loading for efficient startup
"""

import os
import logging
import numpy as np
from typing import List, Optional, Any, Dict

# Configure logging
logger = logging.getLogger(__name__)


class OnnxEmbeddingClient:
    """
    ONNX-based embedding client for local model inference.
    
    Uses ONNX runtime to run models locally without requiring internet access.
    Model configuration is read from environment variables (see .env.example).
    Compatible with the existing EmbeddingClient interface.
    """
    
    def __init__(self, model_path: Optional[str] = None):
        """
        Initialize the ONNX embedding client.
        
        Args:
            model_path: Path to the ONNX model directory (overrides env var if provided)
        """
        # Read configuration from environment variables
        self.model_path = model_path or os.getenv("LOCAL_EMBEDDING_MODEL_PATH")
        self.model_name = os.getenv("LOCAL_EMBEDDING_MODEL_NAME")
        self.dimension = int(os.getenv("LOCAL_EMBEDDING_DIMENSION"))
        self.max_length = int(os.getenv("LOCAL_EMBEDDING_MAX_LENGTH"))
        
        self.onnx_session = None
        self.tokenizer = None
        
        logger.info(
            f"Initializing OnnxEmbeddingClient with model: {self.model_name} "
            f"(path: {self.model_path}, dimension: {self.dimension}, max_length: {self.max_length})"
        )
    
    def _load_model(self):
        """Load the ONNX model and tokenizer lazily."""
        if self.onnx_session is None:
            import onnxruntime as ort
            from transformers import AutoTokenizer
            
            logger.info(f"Loading ONNX model from {self.model_path}...")
            
            # Load ONNX model - check onnx subdirectory first, then root
            onnx_subdir = os.path.join(self.model_path, "onnx", "model.onnx")
            model_root = os.path.join(self.model_path, "model.onnx")
            
            if os.path.exists(onnx_subdir):
                model_file = onnx_subdir
            elif os.path.exists(model_root):
                model_file = model_root
            else:
                raise FileNotFoundError(
                    f"ONNX model file not found in {self.model_path}/onnx/ or {self.model_path}/"
                )
            
            self.onnx_session = ort.InferenceSession(model_file)
            logger.info(f"ONNX model loaded successfully from {model_file}")
            
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_path, local_files_only=True)
            logger.info(f"Tokenizer loaded successfully")
            
            logger.info(f"Model loaded successfully. Embedding dimension: {self.dimension}")
    

    
    def _tokenize_and_run_onnx(self, texts: List[str]) -> np.ndarray:
        """
        Tokenize texts and run through ONNX model.
        
        Args:
            texts: List of texts to embed
            
        Returns:
            NumPy array of embeddings
        """
        # Tokenize the texts using configured max_length
        encoded = self.tokenizer(
            texts,
            padding=True,
            truncation=True,
            max_length=self.max_length,
            return_tensors="np"
        )
        
        # Prepare inputs for ONNX model
        onnx_inputs = {
            "input_ids": encoded["input_ids"].astype(np.int64),
            "attention_mask": encoded["attention_mask"].astype(np.int64),
            "token_type_ids": encoded.get("token_type_ids", np.zeros_like(encoded["input_ids"])).astype(np.int64)
        }
        
        # Run inference
        outputs = self.onnx_session.run(None, onnx_inputs)
        
        # Get the pooled output (usually the first output)
        embeddings = outputs[0]
        
        # Apply mean pooling with attention mask
        attention_mask = encoded["attention_mask"]
        attention_mask_expanded = np.expand_dims(attention_mask, axis=-1)
        attention_mask_expanded = np.repeat(attention_mask_expanded, embeddings.shape[-1], axis=-1)
        
        # Mask the embeddings
        masked_embeddings = embeddings * attention_mask_expanded
        
        # Sum and average
        sum_embeddings = np.sum(masked_embeddings, axis=1)
        sum_mask = np.sum(attention_mask_expanded, axis=1)
        
        # Avoid division by zero
        sum_mask = np.maximum(sum_mask, 1e-9)
        
        # Mean pooling
        mean_embeddings = sum_embeddings / sum_mask
        
        # Normalize embeddings
        norms = np.linalg.norm(mean_embeddings, axis=1, keepdims=True)
        norms = np.maximum(norms, 1e-9)  # Avoid division by zero
        normalized_embeddings = mean_embeddings / norms
        
        return normalized_embeddings
    
    def generate_embedding(self, text: str, include_metadata: bool = False) -> Dict[str, Any]:
        """
        Generate embedding for a single text.
        
        Args:
            text: Text to generate embedding for
            include_metadata: Whether to include metadata (for compatibility)
            
        Returns:
            Dictionary with 'embedding' key containing the vector
        """
        self._load_model()
        
        # Generate embedding using ONNX model
        embeddings = self._tokenize_and_run_onnx([text])
        
        # Convert to list of floats
        return {"embedding": embeddings[0].tolist()}
    
    def generate_embeddings_batch(self, texts: List[str], include_metadata: bool = False) -> List[Dict[str, Any]]:
        """
        Generate embeddings for multiple texts.
        
        Args:
            texts: List of texts to generate embeddings for
            include_metadata: Whether to include metadata (for compatibility)
            
        Returns:
            List of dictionaries with 'embedding' key
        """
        self._load_model()
        
        # Generate embeddings using ONNX model
        embeddings = self._tokenize_and_run_onnx(texts)
        
        # Convert to list of dicts
        return [{"embedding": emb.tolist()} for emb in embeddings]
    
    def get_dimension(self) -> int:
        """
        Get the embedding dimension.
        
        Returns:
            Integer dimension of the embeddings
        """
        return self.dimension
    
    def verify_connection(self) -> bool:
        """
        Verify that the ONNX model is loaded and working.
        
        Returns:
            True if model is working, False otherwise
        """
        try:
            self.generate_embedding("test", include_metadata=False)
            return True
        except Exception as e:
            logger.error(f"ONNX model verification failed: {e}")
            return False

"""
Test to isolate the preprocessing nodes issue.
"""
import pytest
from langgraph.graph import StateGraph, START, END
from app.agent.langgraph.state import Agent<PERSON><PERSON>
from pathlib import Path


# Simple test nodes
async def test_fetch_node(state: AgentState) -> dict:
    """Test fetch node that writes to file."""
    debug_file = Path("/tmp/test_fetch_called.txt")
    debug_file.write_text(f"fetch_node called\n")
    return {"graph_context": {"test": "data"}}


async def test_inject_node(state: AgentState) -> dict:
    """Test inject node that writes to file."""
    from langchain_core.messages import SystemMessage
    debug_file = Path("/tmp/test_inject_called.txt")
    debug_file.write_text(f"inject_node called\n")
    
    context = state.get("graph_context")
    if context:
        return {"messages": [SystemMessage(content=f"Context: {context}")]}
    return {"messages": []}


async def test_supervisor_node(state: AgentState) -> dict:
    """Test supervisor node that writes to file."""
    from langchain_core.messages import AIMessage
    debug_file = Path("/tmp/test_supervisor_called.txt")
    debug_file.write_text(f"supervisor_node called\n")
    return {"messages": [AIMessage(content="Response")]}


@pytest.mark.asyncio
async def test_preprocessing_wrapper_execution():
    """
    Test if wrapping a simple node with preprocessing nodes works.
    """
    # Clean up test files
    for f in ["/tmp/test_fetch_called.txt", "/tmp/test_inject_called.txt", "/tmp/test_supervisor_called.txt"]:
        Path(f).unlink(missing_ok=True)
    
    # Create wrapper graph
    wrapper = StateGraph(AgentState)
    
    # Add nodes
    wrapper.add_node("fetch", test_fetch_node)
    wrapper.add_node("inject", test_inject_node)
    wrapper.add_node("supervisor", test_supervisor_node)
    
    # Wire: START -> fetch -> inject -> supervisor -> END
    wrapper.add_edge(START, "fetch")
    wrapper.add_edge("fetch", "inject")
    wrapper.add_edge("inject", "supervisor")
    wrapper.add_edge("supervisor", END)
    
    # Compile
    app = wrapper.compile()
    
    # Invoke with initial state
    initial_state = {
        "messages": [],
        "graph_context": None
    }
    
    result = await app.ainvoke(initial_state)
    
    # Verify all nodes were called
    assert Path("/tmp/test_fetch_called.txt").exists(), "fetch_node was not called"
    assert Path("/tmp/test_inject_called.txt").exists(), "inject_node was not called"
    assert Path("/tmp/test_supervisor_called.txt").exists(), "supervisor_node was not called"
    
    # Verify state was updated
    assert result["graph_context"] is not None
    assert result["graph_context"]["test"] == "data"
    
    # Clean up
    for f in ["/tmp/test_fetch_called.txt", "/tmp/test_inject_called.txt", "/tmp/test_supervisor_called.txt"]:
        Path(f).unlink(missing_ok=True)


@pytest.mark.asyncio
async def test_preprocessing_wrapper_with_compiled_subgraph():
    """
    Test if wrapping a COMPILED subgraph with preprocessing nodes works.
    This simulates the actual supervisor scenario.
    """
    # Clean up test files
    for f in ["/tmp/test_fetch_called.txt", "/tmp/test_inject_called.txt", "/tmp/test_supervisor_called.txt"]:
        Path(f).unlink(missing_ok=True)
    
    # Create a simple subgraph (simulating supervisor)
    subgraph = StateGraph(AgentState)
    subgraph.add_node("inner_node", test_supervisor_node)
    subgraph.add_edge(START, "inner_node")
    subgraph.add_edge("inner_node", END)
    compiled_subgraph = subgraph.compile()
    
    # Create wrapper graph with preprocessing
    wrapper = StateGraph(AgentState)
    
    # Add preprocessing nodes
    wrapper.add_node("fetch", test_fetch_node)
    wrapper.add_node("inject", test_inject_node)
    wrapper.add_node("supervisor", compiled_subgraph)  # Add compiled graph as node
    
    # Wire: START -> fetch -> inject -> supervisor -> END
    wrapper.add_edge(START, "fetch")
    wrapper.add_edge("fetch", "inject")
    wrapper.add_edge("inject", "supervisor")
    wrapper.add_edge("supervisor", END)
    
    # Compile
    app = wrapper.compile()
    
    # Invoke with initial state
    initial_state = {
        "messages": [],
        "graph_context": None
    }
    
    result = await app.ainvoke(initial_state)
    
    # Verify all nodes were called
    assert Path("/tmp/test_fetch_called.txt").exists(), "fetch_node was not called"
    assert Path("/tmp/test_inject_called.txt").exists(), "inject_node was not called"
    assert Path("/tmp/test_supervisor_called.txt").exists(), "supervisor_node (inner) was not called"
    
    # Verify state was updated
    assert result["graph_context"] is not None
    assert result["graph_context"]["test"] == "data"
    
    # Clean up
    for f in ["/tmp/test_fetch_called.txt", "/tmp/test_inject_called.txt", "/tmp/test_supervisor_called.txt"]:
        Path(f).unlink(missing_ok=True)

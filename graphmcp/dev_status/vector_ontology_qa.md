# Vector Search Ontology - Questions & Answers

## Q1: What does the ontology look like? Are embeddings nodes or attributes of nodes?

### Answer: Embeddings are **properties** (attributes) of nodes

**Example:**
```cypher
(:Mission {
    // Core data
    id: "566189",
    title: "Feeding Florida MOU Activation",

    // Embedding as property (1536-dimensional vector)
    embedding: [0.123, -0.456, 0.789, ..., 0.321],

    // Embedding metadata (also properties)
    embedding_model: "text-embedding-3-small",
    embedding_version: "v1",
    embedding_dimension: 1536,
    embedding_created_at: "2025-10-01T21:30:00Z",
    embedding_text_hash: "sha256:abc123..."
})
```

### NOT This:
```cypher
// DON'T DO: Separate embedding nodes (not standard)
(:Mission {id: "566189"})-[:HAS_EMBEDDING]->(:Embedding {vector: [...]})
```

### Why Properties, Not Nodes?

✅ **Industry Standard:** Both Neo4j and Memgraph store embeddings as `LIST<FLOAT>` properties
✅ **Performance:** Direct property access is 10-100x faster than relationship traversal
✅ **Simplicity:** No additional relationships to manage
✅ **Index Optimization:** Vector indices are designed to index node properties
✅ **Query Efficiency:** Single MATCH returns node + embedding

---

## Q2: How will vector search know where to look for embeddings?

### Answer: Through named vector indices that map to specific node labels + properties

**Step 1: Create Named Index (One-Time Setup)**

```cypher
CREATE VECTOR INDEX mission_embedding ON :Mission(embedding)
WITH CONFIG {
    "dimension": 1536,
    "capacity": 1000,
    "metric": "cosine"
};
```

**What this declares:**
- Index name: `mission_embedding`
- Node label: `:Mission`
- Property name: `embedding`
- Configuration: 1536-dim, cosine similarity

**Step 2: Search Using Index Name**

```cypher
// Generate query embedding first (in Python)
query_embedding = [0.111, -0.222, ...]  // 1536 values

// Search using the INDEX NAME
CALL vector.search('mission_embedding', $query_embedding, 10)
YIELD node, score
RETURN
    node.id,
    node.title,
    score as similarity
ORDER BY score DESC;
```

**How Memgraph Resolves the Search:**

```
vector.search('mission_embedding', ...)
         ↓
Index Registry: "mission_embedding" → :Mission(embedding)
         ↓
HNSW Algorithm: Search all :Mission nodes with 'embedding' property
         ↓
Compare: query_embedding vs each Mission.embedding using cosine similarity
         ↓
Return: Top 10 most similar Mission nodes with scores
```

### Complete Example in MCP Tool

```python
@mcp.tool()
def semantic_search_missions(query: str, limit: int = 10) -> str:
    """Find missions semantically similar to query text"""

    # Step 1: Generate query embedding (same model as nodes!)
    from src.embeddings import generate_embedding
    query_data = generate_embedding(query, include_metadata=False)
    query_vector = query_data['embedding']  # 1536 floats

    # Step 2: Vector search using index name
    cypher = """
    CALL vector.search('mission_embedding', $query_vector, $limit)
    YIELD node, score
    RETURN
        node.id as id,
        node.title as title,
        node.mission_number as number,
        node.embedding_model as model_used,
        score as similarity_score
    ORDER BY score DESC
    """

    # Step 3: Execute
    results = graph_client.execute_query(cypher, {
        "query_vector": query_vector,
        "limit": limit
    })

    return format_results(results)
```

---

## Q3: Is this best practice?

### Answer: YES ✅ - This is the industry-standard approach

**Evidence:**

### Neo4j Standard Pattern:
```cypher
// Neo4j uses same pattern
CREATE VECTOR INDEX movie_embeddings FOR (n:Movie) ON (n.embedding)
OPTIONS {indexConfig: {`vector.dimensions`: 1536}}

// Search with index name
CALL db.index.vector.queryNodes('movie_embeddings', 10, $query_vector)
```

### Memgraph Standard Pattern:
```cypher
// Memgraph uses same pattern
CREATE VECTOR INDEX movie_embedding ON :Movie(embedding)
WITH CONFIG {"dimension": 1536, "capacity": 1000, "metric": "cosine"}

// Search with index name
CALL vector.search('movie_embedding', $query_vector, 10)
```

### Why This is Best Practice:

1. **Label-specific indices** - Different entity types get different indices
   - ✅ Clean separation: missions vs incidents vs resources
   - ✅ Different configurations per entity type
   - ✅ Clear intent in queries

2. **Property-based storage** - Embeddings as node properties
   - ✅ Single query returns everything (node + embedding + metadata)
   - ✅ Atomic updates (embedding always with its node)
   - ✅ No orphaned embeddings

3. **Named index lookup** - Index name maps to label + property
   - ✅ Explicit and clear
   - ✅ Type-safe (can't accidentally search wrong entity type)
   - ✅ Flexible (easy to add new indices)

4. **Metadata co-location** - Metadata stored with embedding
   - ✅ Model tracking
   - ✅ Change detection (text hash)
   - ✅ Debugging and auditing

---

## Alternative Patterns (NOT Recommended)

### ❌ Separate Vector Database:
```
Memgraph (graph) + Milvus/Qdrant (vectors)
```
**Why not:** Memgraph has native vector search - unnecessary complexity

### ❌ Single Unified Index:
```cypher
CREATE VECTOR INDEX all_entities ON :Entity(embedding) ...
```
**Why not:** Can't distinguish between entity types in results

### ❌ Embedding Nodes:
```cypher
(:Mission)-[:HAS_EMBEDDING]->(:Embedding {vector: [...]})
```
**Why not:** Slower, more complex, not standard practice

---

## Quick Reference

**Ontology Structure:**
```
Mission Node
├── id, title, mission_number (core data)
├── embedding [1536 floats] (vector property)
└── embedding_* (metadata properties)
```

**Index Mapping:**
```
Index Name           Node Label    Property
─────────────────────────────────────────────
mission_embedding  → :Mission    → embedding
incident_embedding → :Incident   → embedding
resource_embedding → :Resource   → embedding
```

**Search Pattern:**
```python
# 1. Generate query embedding (same model!)
query_vector = embed_text(query)

# 2. Search using index name
CALL vector.search('mission_embedding', query_vector, 10)

# 3. Get results
YIELD node, score
RETURN node, score
```

**Model Consistency:**
```
✅ CORRECT: Same model for nodes and queries
❌ WRONG: Different models (embeddings not comparable)
```

---

**Created:** October 1, 2025
**Status:** Documented in ARCHITECTURE.md
**Standard:** Neo4j and Memgraph industry-standard pattern

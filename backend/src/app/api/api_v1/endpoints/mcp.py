"""
MCP Endpoints for Tool Discovery and Server Status

Provides REST endpoints for querying MCP servers and available tools
in the LangGraph architecture.
"""

from fastapi import APIRouter
from typing import Dict, List, Any

from app.services.langgraph_service import LangGraphService
from app.core.logging import get_logger
from app.core.config import settings

logger = get_logger(__name__)

router = APIRouter()


@router.get("/servers")
async def get_mcp_servers() -> Dict[str, Any]:
    """
    Get connection status and configuration for all MCP servers.

    Returns information about configured MCP servers including:
    - Server name and namespace
    - Connection status
    - Tool count
    - Configuration details
    """
    try:
        # Create service and ensure it's initialized (lazy loading)
        service = LangGraphService()
        await service._ensure_setup()

        context = service._context
        if not context:
            return {
                "servers": [],
                "total_tools": 0,
                "initialized": False
            }

        # Build server configs from settings (same logic as LangGraphService)
        server_configs = {}
        if settings.genie_mcp_enabled and settings.genie_mcp_url:
            server_configs["genie"] = settings.genie_mcp_url
        if settings.graphrag_mcp_enabled and settings.graphrag_mcp_url:
            server_configs["graphrag"] = settings.graphrag_mcp_url

        # Get tools grouped by server namespace
        tools_by_server: Dict[str, List] = {}
        for tool in context.tools:
            # Extract server namespace from tool name (e.g., "genie.ask_genie" -> "genie")
            namespace = tool.name.split('.')[0] if '.' in tool.name else "unknown"
            if namespace not in tools_by_server:
                tools_by_server[namespace] = []
            tools_by_server[namespace].append(tool)

        # Build server status list from configured servers
        servers = []
        for server_name, server_url in server_configs.items():
            tool_count = len(tools_by_server.get(server_name, []))
            server_info = {
                "name": server_name,
                "namespace": server_name,
                "configured": True,
                "connected": tool_count > 0,
                "url": server_url,
                "tool_count": tool_count,
                "status": "connected" if tool_count > 0 else "disconnected",
                "error": None if tool_count > 0 else "No tools discovered"
            }
            servers.append(server_info)

        return {
            "servers": servers,
            "total_tools": len(context.tools),
            "initialized": True
        }

    except Exception as e:
        logger.error(
            "Failed to get MCP server status",
            extra={"error": str(e)},
            exc_info=True
        )
        return {
            "servers": [],
            "total_tools": 0,
            "initialized": False,
            "error": str(e)
        }


@router.get("/tools")
async def get_mcp_tools() -> Dict[str, Any]:
    """
    Get all available MCP tools organized by their server namespace.

    Returns detailed information about each tool including:
    - Full namespaced name (e.g., "genie.ask_genie")
    - Original name without namespace
    - Description
    - Parameter schema
    """
    try:
        # Create service and ensure it's initialized (lazy loading)
        service = LangGraphService()
        await service._ensure_setup()

        context = service._context
        if not context:
            return {
                "servers": {},
                "total_tools": 0
            }

        # Group tools by server namespace
        servers: Dict[str, Dict[str, Any]] = {}

        for tool in context.tools:
            # Extract namespace from tool name
            if '.' in tool.name:
                namespace, original_name = tool.name.split('.', 1)
            else:
                namespace = "unknown"
                original_name = tool.name

            # Initialize server entry if needed
            if namespace not in servers:
                servers[namespace] = {
                    "server_name": namespace,
                    "namespace": namespace,
                    "connected": True,
                    "tool_count": 0,
                    "tools": []
                }

            # Add tool info
            # Get parameters schema safely
            parameters = {}
            if hasattr(tool, 'args_schema') and tool.args_schema:
                if hasattr(tool.args_schema, 'schema'):
                    parameters = tool.args_schema.schema()
                elif isinstance(tool.args_schema, dict):
                    parameters = tool.args_schema

            tool_info = {
                "name": tool.name,
                "original_name": original_name,
                "description": tool.description or "",
                "parameters": parameters
            }

            servers[namespace]["tools"].append(tool_info)
            servers[namespace]["tool_count"] += 1

        return {
            "servers": servers,
            "total_tools": len(context.tools)
        }

    except Exception as e:
        logger.error(
            "Failed to get MCP tools",
            extra={"error": str(e)},
            exc_info=True
        )
        return {
            "servers": {},
            "total_tools": 0,
            "error": str(e)
        }

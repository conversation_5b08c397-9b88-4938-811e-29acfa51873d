# GraphMCP - Next Steps and TODOs

## Current Status (October 1, 2025)

### ✅ Phase 1: MAGE Graph Algorithms - COMPLETE
- 5 MAGE tools implemented and tested
- 27 total MCP tools
- Committed: `a1f6cc7`

### 🔄 Phase 2: Vector Search - IN PROGRESS

**Completed:**
- ✅ Azure OpenAI embeddings API tested and working
- ✅ Embedding client infrastructure created
- ✅ Field selector for multi-field embedding strategy
- ✅ 3 vector search MCP tools implemented (read-only)
- ✅ Security controls added (execute_cypher feature flag)
- ✅ Comprehensive documentation

**Blocked:**
- ⚠️ **Memgraph connection issue** - Scripts can't connect from host machine (See `memgraph_connection_issue.md`)

**Remaining:**
- ⏳ Create vector indices in Memgraph
- ⏳ Generate embeddings for 4,562 nodes (Mission, Incident, Resource)
- ⏳ Test vector search tools

---

## Immediate TODOs

### TODO #1: Resolve Memgraph Connection Issue (BLOCKING)

**File:** `dev_status/memgraph_connection_issue.md`

**Problem:** <PERSON><PERSON><PERSON> can't connect to Memgraph at localhost:7687 from host machine, but work inside Docker.

**Workaround:**
```bash
# Use this until fixed
docker-compose exec graphrag-mcp uv run python scripts/...
```

**Investigation Needed:**
- Check Memgraph Bolt configuration
- Test GQLAlchemy vs neo4j driver
- Check Memgraph MAGE image specific settings
- Verify Docker port mapping

**Priority:** Medium (workaround exists)

**Assigned to:** _____

---

### TODO #2: Generate Embeddings (After Connection Fixed)

**Run:**
```bash
# Small test first (3 missions)
uv run python scripts/test_embedding_workflow.py

# Full generation
uv run python scripts/generate_embeddings.py --entity-types Mission Incident Resource
```

**Estimates:**
- Total nodes: 4,562 (4,532 Mission + 18 Resource + 12 Incident)
- Tokens: ~228,100
- Cost: ~$0.005
- Time: ~10-15 minutes

**Prerequisites:**
- ✅ Azure OpenAI credentials configured
- ⏳ Memgraph connection working
- ⏳ Vector indices created

---

### TODO #3: Create Vector Indices

**Run:**
```bash
uv run python scripts/create_vector_indices.py
```

**What it does:**
```cypher
CREATE VECTOR INDEX mission_embedding ON :Mission(embedding) ...
CREATE VECTOR INDEX incident_embedding ON :Incident(embedding) ...
CREATE VECTOR INDEX resource_embedding ON :Resource(embedding) ...
```

**Prerequisites:**
- ⏳ Memgraph connection working
- ⏳ Embeddings generated on nodes

**Order:** Can run BEFORE or AFTER embedding generation (indices can be empty initially)

---

### TODO #4: Test Vector Search Tools

**After embeddings and indices are ready:**

```bash
# Test via MCP client
uv run python -c "
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
import asyncio

async def test():
    server_params = StdioServerParameters(command='uv', args=['run', 'python', 'main.py'])
    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            await session.initialize()

            # Test semantic search
            result = await session.call_tool('semantic_search_missions', {'query': 'hurricane response', 'limit': 5})
            print(result.content[0].text)

asyncio.run(test())
"
```

---

### TODO #5: Security Review - execute_cypher Tool

**Current State:**
- ✅ Feature flag added: `ALLOW_CUSTOM_CYPHER` (default: false)
- ✅ Disabled by default in `.env.example`
- ✅ Enabled for development in `.env`

**Best Practice Recommendation:**
- ✅ **Development:** ALLOW_CUSTOM_CYPHER=true (for debugging)
- ✅ **Production:** ALLOW_CUSTOM_CYPHER=false (security)
- ✅ **Alternative:** Remove tool entirely if not needed

**Decision Needed:** Should we keep this tool or remove it entirely?

**MCP Best Practices:**
- Principle of least privilege
- Read-only recommended for LLM-exposed tools
- Human approval for dangerous operations

---

## Phase 2 Completion Checklist

- [x] Azure OpenAI integration tested
- [x] Embedding client infrastructure
- [x] Field selector (multi-field strategy)
- [x] Batch embedding scripts
- [x] Vector search MCP tools (3)
- [x] Security controls (execute_cypher flag)
- [x] Documentation

- [ ] **Memgraph connection from host (BLOCKING)**
- [ ] Create vector indices
- [ ] Generate embeddings for all nodes
- [ ] Test vector search end-to-end
- [ ] Performance benchmarks
- [ ] Update README with vector search examples

---

## Phase 3: Full-Text Search (After Phase 2)

**Status:** Not Started

**What to do:**
1. Create Tantivy text indices on Mission, Incident, Resource
2. Implement 3 full-text search MCP tools
3. Test keyword vs semantic search

**Estimated time:** Quick (1-2 hours) - native Memgraph feature

---

## Phase 4: Hybrid Retrieval (After Phase 3)

**Status:** Not Started

**What to do:**
1. Combine vector + graph + text retrieval
2. Implement RRF (Reciprocal Rank Fusion) scoring
3. Tune and optimize

**Estimated time:** Moderate (2-3 hours)

---

## Key Decisions Made

### ✅ Embedding Strategy
- **Approach:** Combined field embedding (single embedding per node)
- **Fields:** Mission.title, Incident.type + description, Resource.type + description
- **No chunking:** Entities are atomic
- **Model:** text-embedding-3-small (1536 dims)
- **Metadata:** Store model, version, timestamp, text hash

### ✅ Workflow Strategy
- **Current:** Post-load embedding (Option 2)
- **Future:** Hybrid (ETL for full loads, post-load for incremental)
- **Scripts:** Direct database access (not via MCP)

### ✅ Security Model
- **MCP Server:** Read-only tools for LLM safety
- **execute_cypher:** Feature flag (disabled by default)
- **Admin Operations:** Separate scripts with direct DB access

### ✅ Ontology
- **Embeddings:** Properties of nodes (not separate nodes)
- **Indices:** One per entity type (mission_embedding, incident_embedding, resource_embedding)
- **Search:** Via index name (e.g., vector.search('mission_embedding', ...))

---

## Documentation Created

### Implementation Guides:
- ✅ `vector_search_implementation_plan.md` - Phase 2 complete plan
- ✅ `embedding_workflow_strategies.md` - 4 workflow options analyzed
- ✅ `multi_field_embedding_strategy.md` - Multi-field approach
- ✅ `vector_ontology_qa.md` - Q&A format
- ✅ `mcp_write_operations_analysis.md` - Security analysis
- ✅ `memgraph_connection_issue.md` - Connection issue details

### Progress Tracking:
- ✅ `mcp_additions_completed.md` - Detailed progress log
- ✅ `FEATURE_PRIORITIES.md` - Tier 1-3 roadmap
- ✅ `POTENTIAL_FEATURES.md` - Feature catalog

### Architecture:
- ✅ `ARCHITECTURE.md` - Added Vector Search Architecture section

---

## Files Created This Session

### Source Code:
- `src/tools/mage_tools.py` (712 lines) - 5 MAGE algorithm tools
- `src/tools/vector_search_tools.py` (267 lines) - 3 semantic search tools
- `src/embeddings/client.py` - Azure OpenAI client
- `src/embeddings/generator.py` - Helper functions
- `src/embeddings/field_selector.py` - Multi-field text extraction

### Scripts:
- `scripts/test_embeddings.py` - Azure OpenAI API test (PASSED)
- `scripts/test_direct_connection.py` - Memgraph connection test (FAILED)
- `scripts/test_embedding_workflow.py` - End-to-end workflow test
- `scripts/create_vector_indices.py` - Vector index creation
- `scripts/generate_embeddings.py` - Batch embedding generation
- `scripts/test_mage_tools.py` - MAGE tools test suite

### Configuration:
- Updated `.env.example` with vector search and security settings
- Updated `.env` with Azure credentials and security flags
- Updated `pyproject.toml` with openai dependency

---

## Commands to Run (Once Connection Fixed)

### 1. Create Vector Indices
```bash
uv run python scripts/create_vector_indices.py
```

### 2. Generate Embeddings (Small Test)
```bash
uv run python scripts/test_embedding_workflow.py
# Tests 3 missions, estimates full cost/time
```

### 3. Generate All Embeddings
```bash
uv run python scripts/generate_embeddings.py --entity-types Mission Incident Resource
# Or with incremental mode:
uv run python scripts/generate_embeddings.py --all --incremental
```

### 4. Verify Vector Search Works
```bash
# Restart server with vector search enabled
cd /Users/<USER>/Repos/DEMES/assistant && make restart

# Test tools
uv run python -c "
# Test semantic_search_missions tool
..."
```

---

## What to Hand Off

### For Connection Issue Resolution:
- **Document:** `dev_status/memgraph_connection_issue.md`
- **Test script:** `scripts/test_direct_connection.py`
- **Expected:** Script should connect to localhost:7687 and query Memgraph
- **Current:** Bolt handshake timeout from host, works inside Docker

### For Continuing Vector Search:
- **Use workaround:** Run scripts via `docker-compose exec graphrag-mcp`
- **Once connection fixed:** Run scripts directly with `uv run python scripts/...`

---

## Estimated Completion

**If connection is fixed:**
- Create indices: 1 minute
- Generate embeddings: 10-15 minutes (4,562 nodes)
- Test vector search: 5 minutes
- **Total: ~20 minutes to complete Phase 2**

**If using workaround:**
- Same time, just prefix with `docker-compose exec graphrag-mcp`

---

## Questions for Review

1. ✅ **execute_cypher security:** Feature flag added (ALLOW_CUSTOM_CYPHER=false by default) - OK?
2. ✅ **Read-only MCP tools:** Vector search tools are read-only - Confirmed
3. ⏳ **Memgraph connection:** Documented for investigation - Assign to someone?
4. ⏳ **Proceed with workaround:** Use Docker exec until connection fixed?

---

**Last Updated:** October 1, 2025
**Current Phase:** Phase 2 (Vector Search) - 80% complete
**Blocking Issue:** Memgraph connection from host
**Workaround Available:** Yes (Docker exec)

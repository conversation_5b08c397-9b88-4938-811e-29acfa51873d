"""
Create vector indices in Memgraph

This script creates vector indices for entity types that will support semantic search.
Indices use HNSW algorithm for fast similarity search.
"""

import os
import sys
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from dotenv import load_dotenv
from src.graph.client import GraphClient

# Load environment
load_dotenv()


def create_vector_indices():
    """Create vector indices for Incident, Resource, and Mission nodes"""

    print("=" * 80)
    print("CREATING VECTOR INDICES IN MEMGRAPH")
    print("=" * 80)

    # Get configuration from environment (required - no defaults)
    dimension_str = os.getenv("OPENAI_EMBEDDING_DIMENSION")
    if not dimension_str:
        print("\n❌ ERROR: OPENAI_EMBEDDING_DIMENSION not set in .env")
        print("   This must match the dimension of embeddings in your graph nodes")
        return False

    dimension = int(dimension_str)
    metric = os.getenv("VECTOR_INDEX_METRIC", "cosine")

    print(f"\n📋 Configuration:")
    print(f"   Embedding Dimension: {dimension}")
    print(f"   Similarity Metric: {metric}")

    # Connect to Memgraph
    print(f"\n🔌 Connecting to Memgraph...")
    try:
        graph_client = GraphClient(
            host=os.getenv("MEMGRAPH_HOST", "localhost"),
            port=int(os.getenv("MEMGRAPH_PORT", "7687")),
            user=os.getenv("MEMGRAPH_USER", ""),
            password=os.getenv("MEMGRAPH_PASSWORD", ""),
            ssl=os.getenv("MEMGRAPH_SSL", "false").lower() == "true"
        )
        print(f"   ✓ Connected to Memgraph")
    except Exception as e:
        print(f"   ❌ Failed to connect: {e}")
        return False

    # Define indices to create
    indices = [
        {
            "name": "mission_embedding",
            "label": "Mission",
            "property": "embedding",
            "capacity": int(os.getenv("VECTOR_INDEX_CAPACITY_MISSION", "15000"))
        },
        {
            "name": "incident_embedding",
            "label": "Incident",
            "property": "embedding",
            "capacity": int(os.getenv("VECTOR_INDEX_CAPACITY_INCIDENT", "1000"))
        },
        {
            "name": "resource_embedding",
            "label": "Resource",
            "property": "embedding",
            "capacity": int(os.getenv("VECTOR_INDEX_CAPACITY_RESOURCE", "500"))
        }
    ]

    print(f"\n🏗️  Creating {len(indices)} vector indices...")

    success_count = 0
    for idx in indices:
        print(f"\n   Creating index '{idx['name']}' on :{idx['label']}({idx['property']})")
        print(f"      Capacity: {idx['capacity']}, Dimension: {dimension}, Metric: {metric}")

        # Convert metric name: cosine -> cos for Memgraph
        memgraph_metric = "cos" if metric == "cosine" else metric

        cypher = f"""
        CREATE VECTOR INDEX {idx['name']} ON :{idx['label']}({idx['property']})
        WITH CONFIG {{
            "dimension": {dimension},
            "capacity": {idx['capacity']},
            "metric": "{memgraph_metric}"
        }}
        """

        try:
            graph_client.execute_query(cypher, {})
            print(f"      ✓ Index '{idx['name']}' created successfully")
            success_count += 1
        except Exception as e:
            error_msg = str(e)
            if "already exists" in error_msg.lower():
                print(f"      ⚠️  Index '{idx['name']}' already exists (skipping)")
                success_count += 1
            else:
                print(f"      ❌ Failed to create index: {error_msg}")

    # Verify indices were created
    print(f"\n🔍 Verifying indices...")
    try:
        # Query to list vector indices (if Memgraph supports this)
        verify_cypher = "CALL vector.list_indices() YIELD name, label, property;"
        results = graph_client.execute_query(verify_cypher, {})

        if results:
            print(f"   ✓ Found {len(results)} vector indices:")
            for result in results:
                print(f"      - {result.get('name')}: :{result.get('label')}({result.get('property')})")
        else:
            print(f"   ⚠️  Could not verify indices (query may not be supported)")

    except Exception as e:
        print(f"   ⚠️  Could not verify indices: {e}")
        print(f"   (This is normal if Memgraph doesn't support vector.list_indices)")

    print(f"\n" + "=" * 80)
    if success_count == len(indices):
        print(f"✅ ALL VECTOR INDICES CREATED SUCCESSFULLY")
    elif success_count > 0:
        print(f"⚠️  PARTIAL SUCCESS: {success_count}/{len(indices)} indices created")
    else:
        print(f"❌ FAILED TO CREATE INDICES")
    print(f"=" * 80)

    return success_count == len(indices)


if __name__ == "__main__":
    success = create_vector_indices()
    sys.exit(0 if success else 1)

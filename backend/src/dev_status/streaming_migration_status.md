# Native Streaming Migration - Status Report

**Date**: 2025-10-07
**Branch**: `feature/iterative_sub_agent`
**Status**: Steps 5-6 Partially Complete - Synthesis Bug Blocking

---

## Executive Summary

Successfully migrated from blocking `ainvoke()` to native `astream_events()` streaming. Fixed semantic mapping of STATUS/REASONING/CONTENT. Direct queries work perfectly. **Critical bug**: Supervisor synthesis creates duplicate/confused responses because it treats subagent messages as new user queries.

**Recommendation**: Migrate supervisor to `create_react_agent` (documented in `create_react_agent_migration.md`)

---

## What Works ✅

### 1. Native Token-by-Token Streaming
- **File**: `app/services/langgraph_service.py`
- **Change**: Replaced `ainvoke()` with `astream_events(version="v2")`
- **Result**: True real-time token streaming (not chunked)
- **Evidence**: Tokens arrive as `"I"`, `" currently"`, `" do"` instead of 80-char blocks

### 2. Proper Semantic Mapping
- **STATUS**: Workflow progress ("Supervisor routing...", "Model analyzing...")
- **REASONING**: Decision explanations (parsed from supervisor's routing decisions)
- **CONTENT**: Final responses to user

**Implementation**:
```python
# Supervisor's structured output (JSON) → REASONING
if langgraph_node == "supervisor":
    structured_output = json.loads(_supervisor_output_buffer["content"])
    return StreamMessage(StreamType.REASONING, {"step": f"💭 Routing to {destination}: {reasoning}"})

# Subagent content → CONTENT (streams token-by-token)
else:
    return StreamMessage(StreamType.CONTENT, {"chunk": chunk_data.content})
```

### 3. Direct Responses Working
- **Scenario**: User asks simple question (no external data needed)
- **Flow**: Supervisor routes to `__end__` with response in structured output
- **Delivery**: `Command(update={"messages": [AIMessage(...)]})` adds response
- **Streaming**: Extracted from `on_chain_end` event and delivered as one chunk

**Example**:
```
User: "hello"
→ STATUS: Supervisor routing...
→ REASONING: "Routing to __end__: simple greeting..."
→ CONTENT: "Hello! I'm here and ready to assist..."
```

### 4. Subagent Content Streaming
- **Scenario**: User asks about emergencies → routes to Genie subagent
- **Flow**: Supervisor → Genie subagent → subagent responds
- **Streaming**: Subagent's model calls stream token-by-token via `on_chat_model_stream`

**Example**:
```
User: "What emergencies in Florida?"
→ STATUS: Supervisor routing...
→ REASONING: "Routing to genie_subagent: requesting emergency data..."
→ STATUS: Using Genie subagent...
→ CONTENT: "I" " currently" " do" " not" " have" (token-by-token)
```

### 5. LangGraph Native Metadata
- Uses `metadata["langgraph_node"]` for node identification
- No hardcoded node names in service layer
- Proper separation of concerns

---

## What's Broken ❌

### Critical Bug: Supervisor Synthesis Loop

**Problem**: When subagent returns to supervisor for synthesis, supervisor treats the subagent's response as a NEW user query, creating confused/duplicate responses.

**Example**:
```
User: "Give me a list of missions"
→ Supervisor routes to graphrag_subagent
→ GraphRAG returns: "Here are the missions: [list]"
→ Supervisor sees this and thinks: "User just gave me a list, let me respond to that"
→ Supervisor: "Thank you for sharing the list..."  ← WRONG! Should synthesize the list, not thank the user
```

**Root Cause**:
```python
# workflows.py:162-167
has_subagent_results = len(messages) > 1 and any(
    isinstance(msg, AIMessage) and msg.content and not hasattr(msg, 'tool_calls')
    for msg in messages[1:]  # Skip first user message
)
```

This logic can't distinguish:
- "Supervisor being called 2nd time after subagent" (synthesis mode)
- vs "Supervisor being called 1st time with a long conversation history"

**Impact**:
- ❌ Subagent results are not synthesized
- ❌ Supervisor responds to subagent instead of user
- ❌ Original user query is not answered
- ❌ Confusing/duplicate responses

---

## Files Modified

### ✅ `app/services/langgraph_service.py`
- Replaced `ainvoke()` with `astream_events()` (line 150)
- Added `_translate_event()` helper (lines 185-345)
- Removed manual chunking (~30 lines deleted)
- Added supervisor output buffering for JSON parsing
- Added `Command.update` extraction for direct responses
- **Status**: Complete, working correctly

**Key Changes**:
```python
# OLD (blocking)
result = await workflow_app.ainvoke(initial_state, config)
# ... manual chunking ...

# NEW (streaming)
async for event in workflow_app.astream_events(initial_state, config, version="v2"):
    stream_msg = _translate_event(event)
    if stream_msg:
        yield stream_msg
```

### ⚠️ `app/agent/langgraph/workflows.py`
- Added handoff tools (lines 40-68)
- Updated supervisor logic (lines 137-215)
- Added synthesis mode detection (BUGGY - lines 162-190)
- Updated graph wiring: subagents → supervisor (lines 702-703)
- **Status**: Partially working, synthesis bug present

**Buggy Logic**:
```python
# Lines 162-167: Can't properly detect synthesis mode
has_subagent_results = len(messages) > 1 and any(...)
```

### ✅ `app/services/langgraph_service.py` - Event Translation
**Proper semantic mapping achieved**:
- `on_chain_start` → STATUS
- `on_chat_model_stream` with supervisor → buffered, parsed → REASONING
- `on_chat_model_stream` with agents → CONTENT (token-by-token)
- `on_chain_end` with Command → CONTENT (one chunk)
- `on_tool_start` → STATUS
- `on_tool_end` → SOURCES

---

## Current Behavior

### ✅ Scenario 1: Simple Direct Query
```
Input: "hello"
Output:
  STATUS: "🧭 Supervisor routing query..."
  STATUS: "🤖 Model analyzing..."
  REASONING: "💭 Routing to __end__: simple greeting..."
  CONTENT: "Hello! I'm here and ready to assist..."
  STATUS: "Response complete"
```
**Result**: WORKS PERFECTLY ✅

### ⚠️ Scenario 2: Subagent Query (No Tools)
```
Input: "What emergencies in Florida?"
Output:
  STATUS: "🧭 Supervisor routing..."
  REASONING: "💭 Routing to genie_subagent: requesting emergency data..."
  STATUS: "🚨 Using Genie subagent..."
  CONTENT: "I currently do not have access to real-time data..." (streaming)
  STATUS: "Response complete"
```
**Result**: WORKS (subagent responds directly) ✅

### ❌ Scenario 3: Subagent Query (With Tool Calls)
```
Input: "Give me missions from graph"
Output:
  STATUS: "🧭 Supervisor routing..."
  REASONING: "💭 Routing to graphrag_subagent..."
  STATUS: "🕸️ Using GraphRAG subagent..."
  CONTENT: "Here are the missions: [long list]" (from subagent)
  REASONING: "💭 Routing to __end__: user provided list..." ← BUG!
  CONTENT: "Thank you for sharing the list..." ← BUG!
```
**Result**: BROKEN - Supervisor confused about who sent what ❌

---

## Technical Achievements

1. ✅ **Native Streaming**: Using LangGraph's `astream_events()` correctly
2. ✅ **Metadata Usage**: Using `metadata["langgraph_node"]` instead of hardcoded names
3. ✅ **Event Translation**: Proper mapping of LangGraph events to StreamMessage types
4. ✅ **Supervisor JSON Parsing**: Successfully extract and parse structured output
5. ✅ **Command Handling**: Extract messages from `Command.update` attribute
6. ⚠️ **Message Flow**: Works for direct queries, broken for synthesis

---

## Known Issues

### 1. Supervisor Synthesis Bug (CRITICAL)
- **Severity**: High
- **Impact**: Multi-turn workflows produce nonsensical responses
- **Workaround**: None currently
- **Fix**: Migrate to `create_react_agent` (see `create_react_agent_migration.md`)

### 2. Direct Response Not Streaming Token-by-Token
- **Severity**: Low
- **Impact**: Simple queries arrive as one chunk instead of streaming
- **Root Cause**: `Command(update=...)` doesn't emit `on_chat_model_stream` events
- **Workaround**: Acceptable for simple queries
- **Fix**: `create_react_agent` with `response_format` will stream properly

### 3. Subgraph Events Not Visible
- **Severity**: Medium
- **Impact**: Can't see iteration progress "Genie iteration 3/10"
- **Root Cause**: `astream_events()` doesn't emit subgraph events by default
- **Workaround**: Use `subgraphs=True` parameter (not yet implemented)
- **Fix**: Add `subgraphs=True` to `astream_events()` call OR migrate to simpler architecture

---

## Testing Results

### Test 1: Simple Greeting ✅
```bash
curl -X POST 'http://localhost:8000/api/v1/chat/stream' \
  -d '{"message": "hello", "conversation_id": "test-001"}'
```
**Expected**: STATUS → REASONING → CONTENT → STATUS
**Actual**: ✅ WORKS

### Test 2: Emergency Query (No Tool Data) ✅
```bash
curl -X POST 'http://localhost:8000/api/v1/chat/stream' \
  -d '{"message": "What emergencies in Florida?", "conversation_id": "test-002"}'
```
**Expected**: Routes to Genie, subagent responds
**Actual**: ✅ WORKS (subagent says no access)

### Test 3: Graph Query (With Tool Data) ❌
```bash
curl -X POST 'http://localhost:8000/api/v1/chat/stream' \
  -d '{"message": "List missions from graph", "conversation_id": "test-003"}'
```
**Expected**: Routes to GraphRAG, subagent queries tools, supervisor synthesizes
**Actual**: ❌ BROKEN - Duplicate/confused responses

---

## Backups Created

- ✅ `app/services/langgraph_service.py.backup` - Original before streaming
- ✅ `app/agent/langgraph/workflows.py.backup-step6` - Before synthesis changes

---

## Next Steps

See `dev_status/create_react_agent_migration.md` for detailed migration plan.

**Quick Summary**:
1. Replace custom supervisor with `create_react_agent()`
2. Use handoff tools for routing
3. Use `response_format` for synthesis
4. Simplify to ~150 lines in workflows.py (from ~250 lines)

---

## Code Metrics

### Before Migration (Baseline)
- `langgraph_service.py`: 244 lines (blocking `ainvoke`)
- `workflows.py`: 769 lines (custom everything)
- **Total**: 1013 lines

### After Steps 5-6 (Current)
- `langgraph_service.py`: 345 lines (streaming + event translation)
- `workflows.py`: 769 lines (custom supervisor with bugs)
- **Total**: 1114 lines (+101 lines for streaming logic)

### After `create_react_agent` Migration (Projected)
- `langgraph_service.py`: 280 lines (streaming, simpler event handling)
- `workflows.py`: 450 lines (prebuilt supervisor, custom subagents)
- **Total**: 730 lines (-383 lines, -34% reduction)

---

## References

- LangGraph Streaming: https://langchain-ai.github.io/langgraph/how-tos/streaming/
- Supervisor Pattern: https://langchain-ai.github.io/langgraph/tutorials/multi_agent/agent_supervisor/
- `create_react_agent`: https://langchain-ai.github.io/langgraph/reference/agents/#langgraph.prebuilt.chat_agent_executor.create_react_agent
- Command API: https://blog.langchain.com/command-a-new-tool-for-multi-agent-architectures-in-langgraph/

// Memgraph Data Initialization Script
// Loads all emergency management sample data in correct order

// Load schema and constraints first
:source /data/01_schema.cypher

// Load departments
:source /data/02_departments.cypher

// Load facilities
:source /data/03_facilities.cypher

// Load resources
:source /data/04_resources.cypher

// Load incidents with relationships
:source /data/05_incidents.cypher

// Verify data loading with summary queries
MATCH (d:Department) RETURN 'Departments loaded' as status, count(d) as count;
MATCH (f:Facility) RETURN 'Facilities loaded' as status, count(f) as count;
MATCH (r:Resource) RETURN 'Resources loaded' as status, count(r) as count;
MATCH (i:Incident) RETURN 'Incidents loaded' as status, count(i) as count;
MATCH (g:GeographicArea) RETURN 'Geographic Areas loaded' as status, count(g) as count;

// Summary relationship counts
MATCH ()-[rel:COORDINATES_WITH]->() RETURN 'Department Coordination' as relationship_type, count(rel) as count;
MATCH ()-[rel:OPERATES]->() RETURN 'Department-Facility Operations' as relationship_type, count(rel) as count;
MATCH ()-[rel:OWNS]->() RETURN 'Resource Ownership' as relationship_type, count(rel) as count;
MATCH ()-[rel:STATIONED_AT]->() RETURN 'Resource Stations' as relationship_type, count(rel) as count;
MATCH ()-[rel:RESPONDED_BY]->() RETURN 'Incident Responses' as relationship_type, count(rel) as count;
MATCH ()-[rel:IMPACTED_AREA]->() RETURN 'Area Impacts' as relationship_type, count(rel) as count;
MATCH ()-[rel:UTILIZED_FACILITY]->() RETURN 'Facility Utilization' as relationship_type, count(rel) as count;
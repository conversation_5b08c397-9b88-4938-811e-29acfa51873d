#!/bin/bash
# Test direct API call to streaming endpoint

echo "=========================================="
echo "Testing streaming endpoint..."
echo "=========================================="
echo ""

echo "Making POST request to http://localhost:8000/api/v1/chat/stream"
echo ""

curl -N -X POST http://localhost:8000/api/v1/chat/stream \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Hello, how are you?",
    "conversation_id": "test-migration"
  }'

echo ""
echo ""
echo "=========================================="
echo "✓ API test complete"
echo "=========================================="

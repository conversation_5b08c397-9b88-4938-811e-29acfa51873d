#!/usr/bin/env python3
"""
Load Memgraph from Databricks Gold Table via Databricks Cluster

USAGE:
    python local_load_memgraph.py
    python local_load_memgraph.py --clear
    python local_load_memgraph.py --table my_catalog.gold.knowledge_graph
"""
import requests
import json
import time
import base64
import sys
import argparse
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Databricks configuration
DATABRICKS_HOST = os.getenv("DATABRICKS_HOST")
DATABRICKS_TOKEN = os.getenv("DATABRICKS_TOKEN")
CLUSTER_ID = os.getenv("CLUSTER_ID")
DATABRICKS_CATALOG = os.getenv("DATABRICKS_CATALOG", "graysky-dev-catalog")
MEMGRAPH_URI = os.getenv("MEMGRAPH_URI", "bolt://*************:7687")


def start_cluster():
    """Start the cluster if not already running"""
    get_url = f"{DATABRICKS_HOST}/api/2.0/clusters/get"
    headers = {"Authorization": f"Bearer {DATABRICKS_TOKEN}"}
    
    response = requests.get(get_url, headers=headers, params={"cluster_id": CLUSTER_ID})
    if response.status_code == 200:
        data = response.json()
        state = data.get("state")
        if state == "RUNNING":
            print(f"✓ Cluster {CLUSTER_ID} is already RUNNING")
            return True
    
    url = f"{DATABRICKS_HOST}/api/2.0/clusters/start"
    payload = {"cluster_id": CLUSTER_ID}
    
    print(f"Starting cluster {CLUSTER_ID}...")
    response = requests.post(url, headers=headers, json=payload)
    
    if response.status_code == 200:
        print("✓ Cluster start initiated")
        return True
    else:
        print(f"✗ Failed to start cluster: {response.text}")
        return False

def wait_for_cluster():
    """Wait for cluster to be running"""
    url = f"{DATABRICKS_HOST}/api/2.0/clusters/get"
    headers = {"Authorization": f"Bearer {DATABRICKS_TOKEN}"}
    
    print("Waiting for cluster to start", end="")
    
    while True:
        response = requests.get(url, headers=headers, params={"cluster_id": CLUSTER_ID})
        
        if response.status_code == 200:
            data = response.json()
            state = data.get("state")
            
            if state == "RUNNING":
                print("\n✓ Cluster is RUNNING")
                return True
            elif state in ["TERMINATED", "ERROR"]:
                print(f"\n✗ Cluster failed to start: {state}")
                return False
            else:
                print(".", end="", flush=True)
                time.sleep(10)
        else:
            print(f"\n✗ Failed to check cluster status: {response.text}")
            return False

def upload_script(table_name, clear_first):
    """Upload the Python script to DBFS that loads from gold table to Memgraph"""
    
    code = f"""
from pyspark.sql import SparkSession
from neo4j import GraphDatabase
import json

def load_to_memgraph():
    spark = SparkSession.builder.appName("LoadMemgraph").getOrCreate()
    
    table = "{table_name}"
    uri = "{MEMGRAPH_URI}"
    clear = {clear_first}
    
    print(f"Loading from table: {{table}}")
    kg_df = spark.read.table(table).orderBy("execution_order")
    
    queries = []
    for row in kg_df.collect():
        if row.cypher_json:
            data = json.loads(row.cypher_json)
            if 'cypher' in data and data['cypher']:
                queries.append(data['cypher'])
    
    print(f"Loaded {{len(queries)}} queries")
    
    driver = GraphDatabase.driver(uri)
    driver.verify_connectivity()
    print("Connected to Memgraph")
    
    if clear:
        with driver.session() as session:
            session.run("MATCH (n) DETACH DELETE n")
        print("Database cleared")
    
    # Execute queries
    index_q = [q for q in queries if q.startswith('CREATE INDEX')]
    node_q = [q for q in queries if q.startswith('MERGE') or (q.startswith('CREATE') and 'INDEX' not in q)]
    edge_q = [q for q in queries if q.startswith('MATCH')]
    
    with driver.session() as session:
        for q in index_q:
            try:
                session.run(q)
            except:
                pass
    print(f"Indices: {{len(index_q)}}")
    
    with driver.session() as session:
        for q in node_q:
            session.run(q)
    print(f"Nodes: {{len(node_q)}}")
    
    with driver.session() as session:
        for q in edge_q:
            session.run(q)
    print(f"Edges: {{len(edge_q)}}")
    
    driver.close()
    print("Complete!")

load_to_memgraph()
"""
    
    url = f"{DATABRICKS_HOST}/api/2.0/dbfs/put"
    headers = {"Authorization": f"Bearer {DATABRICKS_TOKEN}"}
    
    encoded_content = base64.b64encode(code.encode()).decode()
    
    payload = {
        "path": "/tmp/load_memgraph.py",
        "contents": encoded_content,
        "overwrite": True
    }
    
    response = requests.post(url, headers=headers, json=payload)
    if response.status_code == 200:
        print("✓ Script uploaded to DBFS")
        return True
    else:
        print(f"✗ Upload failed: {response.text}")
        return False

def create_and_run_job():
    """Create and run job"""
    url = f"{DATABRICKS_HOST}/api/2.1/jobs/runs/submit"
    headers = {"Authorization": f"Bearer {DATABRICKS_TOKEN}"}
    
    payload = {
        "run_name": "Load Memgraph from Gold Table",
        "existing_cluster_id": CLUSTER_ID,
        "spark_python_task": {
            "python_file": "dbfs:/tmp/load_memgraph.py",
            "parameters": []
        }
    }
    
    print(f"Submitting job to cluster {CLUSTER_ID}...")
    response = requests.post(url, headers=headers, json=payload)
    
    if response.status_code == 200:
        run_id = response.json().get("run_id")
        print(f"✓ Job submitted, run ID: {run_id}")
        return run_id
    else:
        print(f"✗ Failed to submit job: {response.text}")
        return None

def check_run_status(run_id):
    """Check status of running job"""
    url = f"{DATABRICKS_HOST}/api/2.1/jobs/runs/get"
    headers = {"Authorization": f"Bearer {DATABRICKS_TOKEN}"}
    
    print("\nWaiting for job to complete", end="")
    
    while True:
        response = requests.get(url, headers=headers, params={"run_id": run_id})
        if response.status_code == 200:
            result = response.json()
            state = result.get("state", {})
            life_cycle_state = state.get("life_cycle_state")
            result_state = state.get("result_state")
            
            if life_cycle_state in ["TERMINATED", "SKIPPED", "INTERNAL_ERROR"]:
                print()
                if result_state == "SUCCESS":
                    print("\n" + "="*70)
                    print("JOB COMPLETED SUCCESSFULLY")
                    print("="*70)
                    
                    output_url = f"{DATABRICKS_HOST}/api/2.1/jobs/runs/get-output"
                    output_response = requests.get(output_url, headers=headers, params={"run_id": run_id})
                    if output_response.status_code == 200:
                        output_data = output_response.json()
                        logs = output_data.get("logs", "")
                        if logs:
                            print("\nOUTPUT:")
                            print("-"*70)
                            print(logs)
                        else:
                            print("\nNo logs available")
                    print("="*70)
                    return True
                else:
                    print(f"\n✗ Job failed with state: {result_state}")
                    state_message = state.get("state_message", "No error message")
                    print(f"Error: {state_message}")
                    return False
            
            print(".", end="", flush=True)
            time.sleep(5)
        else:
            print(f"\n✗ Failed to check status: {response.text}")
            return False

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Load Memgraph from Databricks gold table",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Load from default table
  python local_load_memgraph.py

  # Clear database first, then load
  python local_load_memgraph.py --clear

  # Specify custom table
  python local_load_memgraph.py --table my_catalog.gold.knowledge_graph
        """
    )
    parser.add_argument(
        "--table",
        default=f"`{DATABRICKS_CATALOG}`.gold.knowledge_graph",
        help=f"Full table name (default: {DATABRICKS_CATALOG}.gold.knowledge_graph)"
    )
    parser.add_argument(
        "--clear",
        action="store_true",
        help="Clear all existing data before loading"
    )

    args = parser.parse_args()

    print("="*70)
    print("LOADING MEMGRAPH VIA DATABRICKS")
    print("="*70)
    print(f"\nTable: {args.table}")
    print(f"Clear first: {args.clear}")
    print()

    # Start cluster
    if not start_cluster():
        exit(1)

    # Wait for cluster to be running
    if not wait_for_cluster():
        exit(1)

    # Upload script
    if not upload_script(args.table, args.clear):
        exit(1)

    # Submit and run job
    run_id = create_and_run_job()

    if run_id:
        check_run_status(run_id)

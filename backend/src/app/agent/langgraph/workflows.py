"""
LangGraph workflows - all workflow logic in one place.

Following LangGraph best practices:
- Everything in workflows.py (tool discovery, graph construction, nodes)
- Parameterless workflow functions with context injection
- Single source of truth for workflow changes

Developers modify THIS FILE to:
- Add/remove nodes
- Change workflow structure
- Configure tools
- Add subagents
- Modify routing logic
"""

import json
from typing import Any, Dict, Literal, Optional

from langgraph.types import Command
from langgraph.prebuilt import ToolNode, create_react_agent
from langchain_openai import AzureChatOpenAI
from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field

from app.agent.langgraph.state import AgentState
from app.agent.langgraph.context import RuntimeContext
from app.mcp.adapter import MCPToolAdapter
from app.core.config import settings
from app.core.logging import get_logger

from app.agent.langgraph.context_utils import get_initial_context

logger = get_logger(__name__)

# ============================================================================
# 1. TOOL DISCOVERY (happens at module load)
# ============================================================================

# Configure MCP servers
_server_configs = {}
if settings.genie_mcp_enabled:
    _server_configs["genie"] = settings.genie_mcp_url
if settings.graphrag_mcp_enabled:
    _server_configs["graphrag"] = settings.graphrag_mcp_url

# Tool cache (lazy loaded)
_tools_cache = None
_tools_by_server = None

async def _discover_tools():
    """
    Discover tools from MCP servers and group by server.

    Called once and cached for the lifetime of the application.
    Developers modify _server_configs above to add/remove servers.

    Returns:
        Tuple of (all_tools, tools_by_server)
    """
    global _tools_cache, _tools_by_server

    if _tools_cache is not None:
        return _tools_cache, _tools_by_server

    if not _server_configs:
        logger.warning("No MCP servers configured")
        _tools_cache = []
        _tools_by_server = {}
        return _tools_cache, _tools_by_server

    logger.info(f"Discovering tools from {len(_server_configs)} MCP servers")

    # Discover all tools
    adapter = await MCPToolAdapter.discover_all_servers(_server_configs)
    all_tools = await adapter.discover_tools()

    # Group tools by server (based on tool name prefix)
    tools_by_server = {
        "genie": [],
        "graphrag": [],
        "unknown": []
    }

    # TODO - improve this later; may be able to use server attribute in McPToolAdapter? alternatively, can do two separate tool imports but this could be slower.
    for tool in all_tools:
        # Tool names are prefixed with server namespace (e.g., "genie.ask_genie")
        if tool.name.startswith("get_genie_") or tool.name.startswith("ask_genie") or tool.name.startswith("follow_up"):
            tools_by_server["genie"].append(tool)
        elif (tool.name.startswith("get_") or tool.name.startswith("describe_") or 
              tool.name.startswith("search_") or tool.name.startswith("query_") or 
              tool.name.startswith("find_") or tool.name.startswith("analyze_") or
              tool.name.startswith("aggregate_") or tool.name.startswith("calculate_") or
              tool.name.startswith("traverse_") or tool.name.startswith("execute_") or
              tool.name.startswith("refresh_") or tool.name.startswith("clear_") or
              tool.name.startswith("detect_") or tool.name.startswith("predict_")):
            # GraphRAG tools - comprehensive pattern matching
            tools_by_server["graphrag"].append(tool)
        else:
            tools_by_server["unknown"].append(tool)

    _tools_cache = all_tools
    _tools_by_server = tools_by_server

    logger.info(
        f"Discovered {len(all_tools)} tools",
        extra={
            "tool_count": len(all_tools),
            "genie_tools": len(tools_by_server["genie"]),
            "graphrag_tools": len(tools_by_server["graphrag"]),
            "unknown_tools": len(tools_by_server["unknown"]),
            "servers": list(_server_configs.keys())
        }
    )

    return _tools_cache, _tools_by_server

# ============================================================================
# 2. GRAPH CONSTRUCTION
# ============================================================================

async def create_supervisor_graph(genie_subgraph, graphrag_subgraph):
    """
    Create supervisor graph using langgraph_supervisor library.

    This automatically handles:
    - Creating handoff tools for each subagent
    - Routing between subagents
    - Synthesis of subagent results
    - Direct responses when appropriate

    Args:
        genie_subgraph: Compiled Genie subagent graph
        graphrag_subgraph: Compiled GraphRAG subagent graph

    Returns:
        Compiled supervisor graph with automatic handoff management
    """
    from langgraph_supervisor import create_supervisor

    model = AzureChatOpenAI(
        azure_deployment=settings.synthesis_model_deployment,
        api_version=settings.api_version,
        azure_endpoint=settings.openai_endpoint,
        api_key=settings.openai_api_key,
        temperature=settings.synthesis_model_temperature,
        tags=["supervisor"]  # Tag for filtering in streaming
    )


    # Get dynamic graph context once at supervisor creation
    # Calls analyze_knowledge_graph tool once and stores result in state.
    # All subsequent nodes can access this context from state['graph_context'].
    try:
        logger.info("Fetching dynamic graph context for supervisor creation...")

        # Get GraphRAG tools
        graphrag_tools = await get_tools_by_server("graphrag")

        if not graphrag_tools:
            logger.warning("No GraphRAG tools available for context fetching")
            raise ConnectionError("GraphRAG MCP server not available")

        # TODO could simplify this - don't need to check for tool. Just attempt to run within the try/catch
        # Find analyze_knowledge_graph tool
        analyze_tool = None
        for tool in graphrag_tools:
            if tool.name == "analyze_knowledge_graph":
                analyze_tool = tool
                break

        if not analyze_tool:
            tool_names = [tool.name for tool in graphrag_tools]
            logger.info(f"Available GraphRAG tools: {tool_names}")
            logger.warning(f"analyze_knowledge_graph tool not found. Available tools: {tool_names}")
            raise ConnectionError("analyze_knowledge_graph tool not available")

        # Call the tool to get graph analysis
        logger.info("Calling analyze_knowledge_graph tool for supervisor context...")
        result = await analyze_tool.ainvoke({})

        # Parse result (tool returns JSON string)
        if isinstance(result, str):
            graph_context = json.loads(result)
        else:
            graph_context = result

        # Format the context using existing helper
        initial_context = get_initial_context(graph_context)

        # 🔍 DEBUG: Log the raw graph context
        logger.info("=== RAW GRAPH CONTEXT ===")
        logger.info(json.dumps(graph_context, indent=2, default=str))
        logger.info("=== END RAW GRAPH CONTEXT ===")

        # 🔍 DEBUG: Log the formatted context that will go in system prompt
        logger.info("=== FORMATTED GRAPH CONTEXT FOR SYSTEM PROMPT ===")
        logger.info(initial_context)
        logger.info("=== END FORMATTED GRAPH CONTEXT ===")

        # Log summary
        if isinstance(graph_context, dict) and "statistics" in graph_context:
            stats = graph_context["statistics"]
            logger.info(
                f"Dynamic graph context fetched successfully for supervisor: "
                f"{stats.get('totalNodes', 0)} nodes, "
                f"{stats.get('totalRelationships', 0)} relationships, "
                f"{stats.get('nodeLabelCount', 0)} entity types"
            )
        else:
            logger.info("Dynamic graph context fetched successfully for supervisor")

    except Exception as e:
        logger.error(f"Failed to fetch dynamic graph context for supervisor: {e}")
        # Fallback to basic context
        initial_context = (
            "<initial_context>\n"
            "# Florida Emergency Management GraphRAG - Agent Context\n"
            "\n"
            "Graph context unavailable - using fallback context.\n"
            "The system will still function but may have limited graph awareness.\n"
            "\n"
            "</initial_context>\n"
        )

    # 🔍 DEBUG: Log the complete system prompt that will be sent to LLM
    complete_prompt = (
        "You are an emergency management routing and synthesis assistant. You are the main brain of the system and expected to think critically to understand users's questions and intent and find the answer through your tools. Your role is two-fold:\n\n"
        "1. ROUTING: Analyze the user's query and decide which subagent to use:\n"
        # "   - genie_subagent: Has 4 tools for querying Genie emergency management data\n"
        # "   - Do not use the genie_subagent\n"
        "   - CRITICAL: You MUST use the graphrag_subagent for any query that requires information not already in your context. If you feel that you don't have sufficient information, first run get_schema (within the graphrag_subagent) to explore the data schema before responding to the user\n"
        "   - graphrag_subagent: Has 18 tools for graph database queries and relationship analysis\n"
        "   - You should use the graphrag_subagent for any query that requires external information\n"
        "   - Start by running get_schema (within the graphrag_subagent) to explore the data schema before attempting to answer any questions asking about specific data (e.g., fuel dispensed, missions, etc.)."
        "   - For any query that asks about graph structure, relationships, or connections, you should use the graphrag_subagent and specifically run the get_schema tool to get the schema of the graph EVEN IF YOU THINK YOU ALREADY KNOW THE ANSWER.\n"        
        

        "   - Directly answer simple questions that required no external data or for which you already have the information\n\n"
        "2. SYNTHESIS: When subagents return with data, synthesize the information for emergency response personnel:\n"
        "   - Present information clearly and factually\n"
        "   - Clarify the user's question, if needed, and then do your best to use the data and tools to answer the question\n"
        "   - Highlight contextual elements of critical importance\n"
        "   - Use non-technical language appropriate for field personnel\n"
        "   - Emphasize actionable insights and safety considerations\n"
        "   - Point out patterns, trends, or anomalies that require attention\n\n"
        "FORMATTING REQUIREMENTS:\n"
        "   - ALWAYS use markdown tables for:\n"
        "     • Time-series data (date/time + values)\n"
        "     • Comparison data (multiple items with attributes)\n"
        "     • Any data with 2+ columns\n"
        "     • Statistical summaries or aggregated results\n"
        "   - Use bulleted lists only for single-column information\n"
        "   - Example table format:\n"
        "     | Date | Amount |\n"
        "     |------|--------|\n"
        "     | ... | ... |\n"
        "   - UNITS: Always express fuel amounts in 'gallons' (never 'units')\n\n"
        "Guiding Principles:\n"
        "   - Choose the subagent whose tools best match what the query needs."
        """   - Do not give general answers. Ensure that your answers are always rooted in data or context that has been provided to you. For example, if you are asked,"what is a dog", you should not answer "A dog is a domesticated animal" because that is a general answer. You would say "I do not have specific information about dogs" because you have not been provided with any information about dogs. """
        "   - Never make up information. If you don't have enough information to answer the question, say you don't know and give the user the relevant information that you do know."
        "   - CRITICAL: When attempting to answer questions about the graph, You MUST use tool outputs ONLY. NEVER generate schema information from your training data.\n"

        # "DEVELOPER NOTES: At the very bottom of the final response add a section titled DEVELOPER NOTES in which you should tell the developer what additional data or tools you would have needed to be able to answer the question. Be specific about whether that information should be nodes, metadata, or relationships. Think about Knowledge Graph and Ontology best practices"
        f"\n\n{initial_context}"
    )

    logger.info("=== COMPLETE SYSTEM PROMPT SENT TO LLM ===")
    logger.info(f"System prompt length: {len(complete_prompt)} characters")
    logger.info("System prompt content:")
    logger.info(complete_prompt)
    logger.info("=== END COMPLETE SYSTEM PROMPT ===")

    supervisor = create_supervisor(
        model=model,
        agents=[genie_subgraph, graphrag_subgraph],
        prompt=complete_prompt,
        add_handoff_back_messages=True,
        output_mode="last_message"  # Only return supervisor's final synthesis, not subagent intermediate responses
    ).compile()

    logger.info("Supervisor created with dynamic graph context and langgraph_supervisor library")
    return supervisor


async def create_genie_subgraph():
    """
    Create Genie subagent using create_react_agent.

    Uses MCP tools from Genie server with iteration control via recursion_limit.

    Returns:
        Compiled Genie subagent
    """
    # Get Genie-specific tools
    genie_tools = await get_tools_by_server("genie")

    model = AzureChatOpenAI(
        azure_deployment=settings.fast_model_deployment,
        api_version=settings.api_version,
        azure_endpoint=settings.openai_endpoint,
        api_key=settings.openai_api_key,
        temperature=settings.fast_model_temperature,
        tags=["subagent", "genie"]  # Tags for filtering in streaming
    )

    agent = create_react_agent(
        model,
        tools=genie_tools,
        prompt=(
            "You are a Genie emergency data specialist.\n\n"
            "INSTRUCTIONS:\n"
            "- Assist ONLY with real-time emergency data queries\n"
            "- Use your tools to gather comprehensive information from the Databricks Genie API using the provided tools\n"
            "- After completing your research, respond to the supervisor with your findings\n"
            "- Respond ONLY with the results in a manner easily consumable by an LLM, do NOT include extra commentary"
        ),
        name="genie_subagent"
    )

    # Configure with recursion limit (formula: 2 * max_iterations + 1)
    # This replaces our custom iteration tracking
    return agent.with_config({"recursion_limit": 2 * settings.genie_subagent_max_iterations + 1})


async def create_graphrag_subgraph():
    """
    Create GraphRAG subagent using create_react_agent.

    Uses MCP tools from GraphRAG server with iteration control via recursion_limit.

    Returns:
        Compiled GraphRAG subagent
    """
    # Get GraphRAG-specific tools
    graphrag_tools = await get_tools_by_server("graphrag")

    model = AzureChatOpenAI(
        azure_deployment=settings.fast_model_deployment,
        api_version=settings.api_version,
        azure_endpoint=settings.openai_endpoint,
        api_key=settings.openai_api_key,
        temperature=settings.fast_model_temperature,
        tags=["subagent", "graphrag"]  # Tags for filtering in streaming
    )

    agent = create_react_agent(
        model,
        tools=graphrag_tools,
        prompt=(
            "You are a GraphRAG knowledge graph specialist for emergency management.\n\n"
            "CRITICAL: When attempting to answer questions about the graph, You MUST use tool outputs ONLY. NEVER generate schema information from your training data.\n"

            "SCHEMA DISCOVERY (MANDATORY):\n"
            "1. Call get_schema first to understand the graph structure and available entities and relationships\n"
            "2. Call describe_entity_type BEFORE querying a new entity to discover actual properties and relationships\n"
            "3. Use EXACT names from schema - NEVER assume or invent relationship names!\n"
            "4. Graph context is pre-loaded in system message - read it first\n"
            "5. Before constructing queries, verify exact property and relationship names using get_schema\n\n"

            "ENTITY VALIDATION (MANDATORY):\n"
            "Before using ANY entity type for the first time:\n"
            "1. Call check_entity_availability(entity_type) to verify data exists\n"
            "2. If available=false or count=0 → entity has NO data, do NOT use it\n"
            "3. If count=0, find alternative entity or inform user data unavailable\n\n"

            "ANTI-HALLUCINATION RULES:\n"
            "❌ NEVER use entity types with count=0\n"
            "❌ NEVER invent entity names not in tool output\n"
            "❌ NEVER transform or rename data (e.g., county → depot)\n"
            "❌ NEVER assume properties exist without seeing them in schema\n"
            "✅ ALWAYS verify entity availability before first use\n"
            "✅ ALWAYS cite specific tool output for data claims\n"
            "✅ If data doesn't exist, clearly state 'Data not available'\n\n"

            "CRITICAL - DO NOT INVENT RELATIONSHIPS:\n"
            "  ✗ NEVER assume relationships like LOCATED_IN, HAS, CONTAINS, IN exist\n"
            "  ✗ NEVER use relationship names from your training data or common knowledge\n"
            "  ✓ ONLY use relationships explicitly listed in get_schema output\n"
            "  ✓ Example: Incident↔County uses AFFECTS (not LOCATED_IN, not IN_COUNTY)\n"
            "  If unsure about a relationship, call describe_entity_type to see actual relationships\n\n"
            
            "GRAPH QUERY RULES:\n"
            "This is a memgraph database. Remember to use query patterns appropriate for memgraph:\n"
            "Connections between entities → Use RELATIONSHIPS:\n"
            "  MATCH (m:Mission)-[:ASSIGNED_VENDOR]->(v:Vendor {mission_vendor_name: 'Macro'})\n"
            "Attributes of single entity → Use PROPERTIES:\n"
            "  MATCH (m:Mission) WHERE m.status = 'active'\n"
            "Ensure that you know the direction of relationships before querying by running get_schema and describe_relationship_type\n\n"
            "As an example, a missions are connected to incidents via the RELATED_TO_INCIDENT relationship. The relationship goes from the mission node to the incident node. Therefore, to find all missions for a given incident, you would use the following query:\n"
            "MATCH (i:Incident {incident_name: '2024 Milton'})<-[r:RELATED_TO_INCIDENT]-(m:Mission)\n"
            "(✗) NEVER use properties for relationships (e.g., WHERE m.assigned_vendor = 'X')\n"
            "(✓) ALWAYS traverse relationships for connections (e.g., (m)-[:REL]->(v))\n\n"
            
            "TIMESTAMP QUERIES:\n"
            "CRITICAL: Properties with type ZONED_DATE_TIME MUST use datetime() function:\n"
            "  ✓ CORRECT: WHERE ft.date_of_fueling >= datetime('2024-10-01T00:00:00')\n"
            "  ✗ WRONG: WHERE ft.date_of_fueling >= '2024-10-01T00:00:00'\n"
            "Date range queries:\n"
            "  MATCH (ft:FuelTransaction)\n"
            "  WHERE ft.date_of_fueling >= datetime('2024-10-01T00:00:00')\n"
            "    AND ft.date_of_fueling < datetime('2024-10-02T00:00:00')\n"
            "  RETURN sum(toFloat(ft.amount_dispensed)) AS total\n"
            "Recent/latest queries:\n"
            "  MATCH (m:Mission) WHERE m.updated_at IS NOT NULL\n"
            "  RETURN m.id, m.title, toString(m.updated_at) AS updated_at\n"
            "  ORDER BY m.updated_at DESC LIMIT 5\n\n"

            "AGGREGATION QUERIES:\n"
            "CRITICAL: For totals, counts, averages, or statistics, you MUST aggregate in Cypher, NOT client-side.\n\n"

            "RECOMMENDED APPROACH - Use Specialized Tools:\n"
            "  1. aggregate_by_relationship - Best for grouped aggregations (e.g., fuel by county, missions by vendor)\n"
            "     Example: aggregate_by_relationship(\n"
            "       entity_type='FuelTransaction',\n"
            "       group_by='county',\n"
            "       metrics=['count', 'sum_amount_dispensed'],\n"
            "       filters={'fuel_type': 'Diesel'}\n"
            "     )\n"
            "  2. aggregate_by_type - For simple grouping by entity properties\n"
            "  3. calculate_statistics - For statistical analysis (mean, stdev, percentiles)\n\n"

            "MANUAL CYPHER APPROACH - Use execute_cypher with proper aggregation:\n"
            "  ✅ CORRECT Examples (server-side aggregation):\n"
            "  - Total fuel: \n"
            "    MATCH (ft:FuelTransaction) \n"
            "    RETURN sum(toFloat(ft.amount_dispensed)) AS total\n"

            "  - Count missions: \n"
            "    MATCH (m:Mission) \n"
            "    RETURN count(m) AS count\n"

            "  - Fuel by county (GROUP BY pattern):\n"
            "    MATCH (i:Incident {incident_name: '2024 Milton'})-[:DISPENSED_FOR]->(ft:FuelTransaction)\n"
            "    RETURN ft.county AS county, sum(toFloat(ft.amount_dispensed)) AS total_fuel\n"
            "    ORDER BY total_fuel DESC\n"

            "  - Count by type with filtering:\n"
            "    MATCH (m:Mission)-[:RELATED_TO_INCIDENT]->(i:Incident {incident_name: 'Hurricane Ian'})\n"
            "    RETURN m.status AS status, count(m) AS mission_count\n"
            "    ORDER BY mission_count DESC\n\n"

            "  ❌ ANTI-PATTERNS (Will produce WRONG results):\n"
            "  - NEVER: MATCH (ft:FuelTransaction) RETURN ft.county, ft.amount_dispensed\n"
            "    Problem: Returns individual rows, client aggregates only the limited sample\n"
            "  - NEVER: Return all rows and aggregate client-side\n"
            "    Problem: LIMIT 100 is applied, you only aggregate 100 rows, not all data\n"
            "  - NEVER: Fetch subset then calculate totals\n"
            "    Problem: Subset ≠ full dataset, results will be wrong\n\n"

            "  IMPORTANT: Many numeric fields are stored as STRING - use toFloat() or toInteger() for calculations\n"
            "  REMEMBER: Always include ORDER BY with aggregations for consistent, meaningful results\n\n"

            "GLOSSARY:\n"
            "ZONED_DATE_TIME: Memgraph's datetime type. MUST use datetime() function for comparisons."
            "Mission: Operational task with comments, incidents, transactions, and vendors - called Mission in schema\n"
            # TODO: Add more glossary terms as needed

            "SAFETY:\n"
            "- execute_cypher is READ-ONLY (no CREATE/DELETE/SET)\n"
            "- Tool will reject unsafe queries with error message\n\n"
            
            "OUTPUT:\n"
            "Respond with findings in format consumable by supervisor. No extra commentary."
        ),
        name="graphrag_subagent"
    )

    # Configure with recursion limit (formula: 2 * max_iterations + 1)
    return agent.with_config({"recursion_limit": 2 * settings.graphrag_subagent_max_iterations + 1})


async def create_graph():
    """
    Create main agent graph with supervisor pattern using langgraph_supervisor library.

    Architecture: 
    1. Supervisor graph includes preprocessing nodes that fetch/inject graph context
    2. Supervisor routes to specialized subagents with automatic synthesis

    Returns:
        Compiled LangGraph application ready for execution
    """
    logger.info("Creating multi-agent workflow with langgraph_supervisor")

    # Create subagents first (async - using create_react_agent)
    genie_subgraph = await create_genie_subgraph()
    graphrag_subgraph = await create_graphrag_subgraph()

    # Create supervisor with dynamic graph context in system prompt
    # This automatically:
    # - Fetches graph context once at supervisor creation
    # - Includes context in static system prompt (no SystemMessage bloat)
    # - Creates handoff tools, handles routing, and manages synthesis
    supervisor_graph = await create_supervisor_graph(genie_subgraph, graphrag_subgraph)

    logger.info("Multi-agent workflow compiled successfully (supervisor with dynamic context + 2 subagents)")

    return supervisor_graph

# ============================================================================
# 4. GRAPH EXPORT (Lazy Initialization)
# ============================================================================

_graph_cache = None  # Force rebuild by restarting the app after tool changes

async def get_graph():
    """
    Get compiled graph with lazy initialization.

    This ensures tool discovery happens before graph compilation.
    The graph is created once and cached for the lifetime of the application.

    Returns:
        Compiled LangGraph application
    """
    global _graph_cache

    if _graph_cache is None:
        logger.info("Graph cache is empty, compiling new graph...")
        # Ensure tools are discovered first
        await _discover_tools()

        # Compile graph (async)
        _graph_cache = await create_graph()
        logger.info("Main graph compiled successfully and cached")
    else:
        logger.info("Returning cached graph (already compiled)")

    return _graph_cache

# ============================================================================
# 5. UTILITY FUNCTIONS
# ============================================================================

async def get_all_tools():
    """
    Get all discovered tools.

    Returns:
        List of all tools from all servers
    """
    all_tools, _ = await _discover_tools()
    return all_tools


async def get_tools_by_server(server_name: str):
    """
    Get tools for a specific MCP server.

    Useful for creating specialized subagents.

    Args:
        server_name: "genie", "graphrag", or "unknown"

    Returns:
        List of tools from that server
    """
    _, tools_by_server = await _discover_tools()
    return tools_by_server.get(server_name, [])


def visualize_workflow(workflow_app, output_path: str = "/tmp/langgraph_workflow.mmd"):
    """
    Generate a Mermaid diagram of the workflow.

    Args:
        workflow_app: Compiled LangGraph application
        output_path: Path to save the Mermaid diagram

    Returns:
        Mermaid diagram as string, or None if generation fails
    """
    try:
        # Generate mermaid diagram
        diagram = workflow_app.get_graph().draw_mermaid()

        # Save to file
        with open(output_path, "w") as f:
            f.write(diagram)

        logger.info(f"Workflow diagram saved to {output_path}")
        return diagram

    except Exception as e:
        logger.warning(f"Could not generate workflow visualization: {e}")
        return None


# ============================================================================
# BACKWARDS COMPATIBILITY (for existing service layer)
# ============================================================================

async def create_tool_calling_workflow():
    """
    Legacy function for backwards compatibility.

    This function exists to maintain compatibility with existing code
    that calls create_tool_calling_workflow() directly.

    New code should use get_graph() instead, which handles lazy initialization.

    Returns:
        Compiled LangGraph application
    """
    logger.warning("create_tool_calling_workflow() is deprecated, use get_graph() instead")
    return await create_graph()

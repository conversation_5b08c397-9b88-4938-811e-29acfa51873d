# Memgraph Testing Environment

This directory provides a complete testing environment for the GraphRAG MCP Server using Memgraph as the graph database. It includes sample emergency management data and Docker Compose setup for easy development and testing.

## 🚀 Quick Start

```bash
# Navigate to the memgraph directory
cd memgraph

# Start the Memgraph database and lab interface
docker-compose up -d

# Load sample data (required)
./load_data.sh

# Access Memgraph Lab (web interface)
open http://localhost:3001

# Or test a quick query
docker exec -i graphrag-memgraph mgconsole <<< 'MATCH (d:Department) RETURN d.name, d.type;'
```

### ⚡ One-Line Setup

```bash
cd memgraph && docker-compose up -d && ./load_data.sh
```

## Configuration

The environment is configured via the `.env` file with these default settings:

```bash
# Database Ports
MEMGRAPH_BOLT_PORT=7687      # Bolt protocol for database connections
MEMGRAPH_HTTP_PORT=7444      # HTTP interface for Memgraph
MEMGRAPH_LAB_PORT=3001       # Web interface (changed from 3000 to avoid conflicts)

# Container Configuration
MEMGRAPH_LOG_LEVEL=INFO      # Logging level
MEMGRAPH_TELEMETRY=false     # Disable telemetry
```

### Port Customization

If you need different ports, modify the `.env` file:

```bash
# Example: Use different ports
MEMGRAPH_BOLT_PORT=7688
MEMGRAPH_LAB_PORT=3002
```

## Architecture

- **Memgraph Database**: Graph database running on port 7687 (Bolt protocol) and 7444 (HTTP)
- **Memgraph Lab**: Web-based graph visualization and query interface on port 3001 (configurable)
- **Sample Data**: Comprehensive emergency management dataset with realistic relationships
- **Initialization Scripts**: Manual data loading via provided scripts

## Data Model

The sample dataset models a realistic emergency management scenario including:

### Node Types
- **Department**: Fire, Police, EMS, Emergency Management, Sheriff
- **Facility**: Fire stations, hospitals, police stations, emergency shelters, EOCs
- **Resource**: Fire trucks, ambulances, police cars, equipment, specialized vehicles
- **Incident**: Hurricanes, wildfires, structure fires, medical emergencies, criminal activities
- **GeographicArea**: Service areas and impact zones

### Relationship Types
- **COORDINATES_WITH**: Inter-department coordination
- **OPERATES**: Department facility operations
- **OWNS**: Resource ownership by departments
- **STATIONED_AT**: Resource deployment locations
- **RESPONDED_BY**: Incident response deployments
- **IMPACTED_AREA**: Geographic impact relationships
- **UTILIZED_FACILITY**: Facility usage during incidents
- **MUTUAL_AID**: Resource sharing agreements
- **CAUSED/TRIGGERED**: Cascading incident relationships

## Sample Data Overview

### Departments (5)
- City Fire Department (145 personnel, $12.5M budget)
- Riverside Police Department (220 personnel, $18.75M budget)
- Riverside Emergency Medical Services (85 personnel, $8.9M budget)
- Emergency Management Division (35 personnel, $3.2M budget)
- County Sheriff Department (520 personnel, $45.6M budget)

### Facilities (12)
- 3 Fire stations with geographic coverage
- 2 Hospitals with trauma capabilities
- 1 EMS facility with ambulance bays
- 2 Police/sheriff stations
- 1 Emergency Operations Center
- 1 Warning system facility
- 2 Emergency shelters

### Resources (20+)
- Fire engines, ladder trucks, rescue vehicles, brush trucks
- ALS and BLS ambulances, mobile command units
- Police cars, motorcycles, SWAT vehicles
- Emergency command vehicles, generators, light towers
- Specialized equipment: drones, rescue boats

### Incidents (15+)
- **Hurricane Milton** (Oct 2024): Major disaster, 45K affected, $125M damage
- **Hurricane Helene** (Sep 2024): Moderate disaster, 28K affected, $75M damage
- **Box Springs Mountain Fire**: 4,250 acres burned, $35M damage
- Structure fires, medical emergencies, criminal activities
- Transportation and utility incidents

## Directory Structure

```
memgraph/
├── docker-compose.yml          # Container orchestration
├── README.md                   # This documentation
├── data/                       # Sample data files
│   ├── 01_schema.cypher       # Indexes and constraints
│   ├── 02_departments.cypher  # Emergency departments
│   ├── 03_facilities.cypher   # Emergency facilities
│   ├── 04_resources.cypher    # Emergency resources
│   └── 05_incidents.cypher    # Emergency incidents
└── init/                      # Initialization scripts
    ├── load_data.cypher       # Automated data loading
    └── sample_queries.cypher  # Example GraphRAG queries
```

## Usage

### Starting the Environment

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f memgraph
docker-compose logs -f memgraph-lab

# Stop services
docker-compose down

# Reset data (removes all data)
docker-compose down -v
docker-compose up -d
```

### Loading Sample Data

The sample data must be loaded manually after starting the containers:

```bash
# Load all sample data in correct order
docker exec graphrag-memgraph bash -c 'mgconsole < /data/01_schema.cypher'
docker exec graphrag-memgraph bash -c 'mgconsole < /data/02_departments.cypher'
docker exec graphrag-memgraph bash -c 'mgconsole < /data/03_facilities.cypher'
docker exec graphrag-memgraph bash -c 'mgconsole < /data/04_resources.cypher'
docker exec graphrag-memgraph bash -c 'mgconsole < /data/05_incidents.cypher'

# Verify data loaded correctly
docker exec -i graphrag-memgraph mgconsole <<< 'MATCH (n) RETURN labels(n)[0] as type, count(n) as count ORDER BY type;'
```

### Quick Data Loading Script

Create a simple script to load all data:

```bash
#!/bin/bash
# save as load_data.sh
echo "Loading Memgraph sample data..."
docker exec graphrag-memgraph bash -c 'mgconsole < /data/01_schema.cypher'
docker exec graphrag-memgraph bash -c 'mgconsole < /data/02_departments.cypher'
docker exec graphrag-memgraph bash -c 'mgconsole < /data/03_facilities.cypher'
docker exec graphrag-memgraph bash -c 'mgconsole < /data/04_resources.cypher'
docker exec graphrag-memgraph bash -c 'mgconsole < /data/05_incidents.cypher'
echo "Data loaded successfully!"
```

### Accessing the Database

**Memgraph Lab (Web Interface):**
- URL: http://localhost:3001 (configurable via MEMGRAPH_LAB_PORT in .env)
- Pre-configured connection to local Memgraph instance
- Visual query builder and graph exploration

**Direct Database Connection:**
- Protocol: Bolt
- Host: localhost
- Port: 7687
- No authentication required

**Command Line (mgconsole):**
```bash
# Connect via Docker
docker exec -it graphrag-memgraph mgconsole
```

### Sample Queries

The `init/sample_queries.cypher` file contains 15+ example queries demonstrating GraphRAG patterns:

```cypher
// Find all resources that responded to Hurricane Milton
MATCH (incident:Incident {title: 'Hurricane Milton'})-[:RESPONDED_BY]->(resource:Resource)
RETURN incident.title, resource.name, resource.type, resource.category
ORDER BY resource.type, resource.name;

// Find departments and their coordination relationships
MATCH (d1:Department)-[coord:COORDINATES_WITH]->(d2:Department)
RETURN d1.name, d2.name, coord.coordination_type, coord.frequency
ORDER BY d1.name;

// Emergency shelter utilization during disasters
MATCH (incident:Incident)-[util:UTILIZED_FACILITY]->(shelter:Facility {type: 'emergency_shelter'})
RETURN incident.title, shelter.name, util.occupancy_peak, util.duration_days
ORDER BY incident.timestamp DESC;
```

## Development Workflow

### Testing GraphRAG Queries

1. **Start Environment**: `docker-compose up -d`
2. **Access Lab**: Open http://localhost:3001
3. **Run Queries**: Copy queries from `init/sample_queries.cypher`
4. **Develop Patterns**: Create new queries based on MCP server requirements

### Data Validation

```cypher
// Verify all data loaded correctly
MATCH (d:Department) RETURN 'Departments' as type, count(d) as count
UNION ALL
MATCH (f:Facility) RETURN 'Facilities' as type, count(f) as count
UNION ALL
MATCH (r:Resource) RETURN 'Resources' as type, count(r) as count
UNION ALL
MATCH (i:Incident) RETURN 'Incidents' as type, count(i) as count;

// Check relationship counts
MATCH ()-[rel]->()
RETURN type(rel) as relationship_type, count(rel) as count
ORDER BY count DESC;
```

### Performance Testing

```cypher
// Complex pathfinding query
MATCH path = shortestPath((d1:Department {name: 'City Fire Department'})-[:COORDINATES_WITH*]-(d2:Department {name: 'County Sheriff Department'}))
RETURN length(path) as path_length, nodes(path);

// Aggregation performance test
MATCH (resource:Resource)-[resp:RESPONDED_BY]-(incident:Incident)
RETURN resource.type,
       count(incident) as deployments,
       avg(resp.response_time_minutes) as avg_response_time
ORDER BY deployments DESC;
```

## GraphRAG MCP Server Integration

This environment directly supports the GraphRAG MCP Server development:

### MCP Tool Testing

Each MCP tool can be tested with realistic data:

- **search_entities**: Search for departments, facilities, resources by name/type
- **get_neighbors**: Find connected entities (coordination, response patterns)
- **aggregate_by_type**: Count resources, incidents, damages by category
- **find_patterns**: Identify response patterns, cascading incidents
- **semantic_search**: Natural language queries over emergency data
- **path_analysis**: Find coordination paths, response chains
- **temporal_analysis**: Time-based incident patterns
- **spatial_queries**: Geographic proximity and coverage analysis

### Development Patterns

```python
# Example MCP server integration test
async def test_search_entities():
    query = "MATCH (d:Department {type: 'fire'}) RETURN d.name, d.personnel_count"
    results = await memgraph_client.execute(query)
    assert len(results) == 1  # City Fire Department

async def test_get_neighbors():
    query = """
    MATCH (dept:Department {name: 'City Fire Department'})-[r]->(connected)
    RETURN type(r) as relationship, connected
    """
    results = await memgraph_client.execute(query)
    # Should find COORDINATES_WITH, OPERATES, OWNS relationships
```

## Container Health and Monitoring

### Health Checks

Both containers include health checks:

```yaml
healthcheck:
  test: ["CMD", "echo", "RETURN 1", "|", "mgconsole", "--host", "localhost", "--port", "7687"]
  interval: 30s
  timeout: 10s
  retries: 3
```

### Monitoring Commands

```bash
# Check container health
docker-compose ps

# View resource usage
docker stats graphrag-memgraph graphrag-memgraph-lab

# Check data persistence
docker volume ls | grep memgraph
```

## Data Persistence

- **Volume**: `memgraph_data` persists database files
- **Location**: `/var/lib/docker/volumes/memgraph_memgraph_data/_data`
- **Backup**: Use `docker volume` commands or Memgraph dump features

### Backup and Restore

```bash
# Create backup
docker exec graphrag-memgraph mg_dump --output /tmp/backup.cypher

# Copy backup from container
docker cp graphrag-memgraph:/tmp/backup.cypher ./backup.cypher

# Restore from backup (after data reset)
docker cp ./backup.cypher graphrag-memgraph:/tmp/backup.cypher
docker exec graphrag-memgraph mgconsole < /tmp/backup.cypher
```

## Troubleshooting

### Common Issues

1. **Port Conflicts**: Ensure ports 7687, 7444, 3001 are available
2. **Memory Issues**: Memgraph requires adequate RAM for large datasets
3. **Persistence Issues**: Check Docker volume permissions and disk space

### Debug Commands

```bash
# Check Memgraph logs
docker-compose logs memgraph

# Check Lab connectivity
docker-compose logs memgraph-lab

# Test direct connection
docker exec -it graphrag-memgraph mgconsole -c "RETURN 'Connected successfully';"
```

### Performance Tuning

For development with larger datasets:

```yaml
environment:
  - MEMGRAPH_LOG_LEVEL=WARNING  # Reduce log verbosity
  - MEMGRAPH_STORAGE_SNAPSHOT_INTERVAL_SEC=600  # Less frequent snapshots
  - MEMGRAPH_QUERY_EXECUTION_TIMEOUT_SEC=30  # Longer query timeout
```

## Next Steps

1. **Test Environment**: Verify all queries work with sample data
2. **MCP Integration**: Connect GraphRAG MCP server to this Memgraph instance
3. **Query Optimization**: Profile and optimize GraphRAG query performance
4. **Data Expansion**: Add more sample data as needed for testing
5. **Production Deployment**: Adapt configuration for production use

This testing environment provides a solid foundation for developing and testing the GraphRAG MCP Server with realistic emergency management data and relationships.

## 🎯 Common Usage Patterns

### Daily Development Workflow

```bash
# Start environment
cd memgraph && docker-compose up -d

# Load fresh data (if needed)
./load_data.sh

# Test GraphRAG queries via web interface
open http://localhost:3001

# Test specific query patterns
docker exec -i graphrag-memgraph mgconsole <<< 'YOUR_CYPHER_QUERY_HERE'

# Stop when done
docker-compose down
```

### Quick Database Reset

```bash
# Reset all data and reload
docker-compose down -v && docker-compose up -d && ./load_data.sh
```

### Port Conflict Resolution

If you encounter port conflicts, modify `.env`:

```bash
# Edit .env to change ports
MEMGRAPH_LAB_PORT=3002
MEMGRAPH_BOLT_PORT=7688

# Restart services
docker-compose down && docker-compose up -d
```

### Connection Examples

**Python (neo4j driver):**
```python
from neo4j import GraphDatabase

driver = GraphDatabase.driver("bolt://localhost:7687")
with driver.session() as session:
    result = session.run("MATCH (d:Department) RETURN d.name")
    for record in result:
        print(record["d.name"])
```

**Bolt URL for GraphRAG MCP Server:**
```
bolt://localhost:7687
```

## 📁 Files Overview

```
memgraph/
├── .env                    # Port and container configuration
├── docker-compose.yml      # Container orchestration
├── load_data.sh           # Data loading script (executable)
├── README.md              # This documentation
├── data/                  # Sample data files
└── init/                  # Query examples and utilities
```
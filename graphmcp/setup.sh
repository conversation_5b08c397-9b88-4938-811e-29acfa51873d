#!/bin/bash

# Quick setup script for GraphRAG MCP Server

echo "╔═══════════════════════════════════════════════════════╗"
echo "║   GraphRAG MCP Server - Setup                          ║"
echo "╚═══════════════════════════════════════════════════════╝"
echo ""

# Check for uv
if ! command -v uv &> /dev/null; then
    echo "📦 Installing uv package manager..."
    curl -LsSf https://astral.sh/uv/install.sh | sh

    # Add to PATH for this session
    export PATH="$HOME/.cargo/bin:$PATH"

    # Verify installation succeeded
    if ! command -v uv &> /dev/null; then
        echo "❌ Failed to install uv. Please install it manually:"
        echo "   https://github.com/astral-sh/uv"
        exit 1
    fi

    echo "✅ uv installed successfully"
else
    echo "✅ uv is already installed"
fi

# Create .env from template if it doesn't exist
if [ ! -f ".env" ]; then
    echo "📝 Creating .env from template..."
    cp .env.example .env
    echo "✅ .env file created"
    echo ""
    echo "⚠️  Please edit .env to configure your Memgraph connection:"
    echo "   - MEMGRAPH_HOST (default: localhost)"
    echo "   - MEMGRAPH_PORT (default: 7687)"
    echo "   - MEMGRAPH_USER (if authentication is enabled)"
    echo "   - MEMGRAPH_PASSWORD (if authentication is enabled)"
else
    echo "✅ .env file already exists"
fi

# Install dependencies with uv
echo ""
echo "📦 Installing dependencies with uv..."
uv sync

echo ""
echo "✅ Setup complete!"
echo ""
echo "Next steps:"
echo "1. Ensure Memgraph is running on localhost:7687 (or configure .env)"
echo "2. Load sample data (optional):"
echo "   mgconsole < data/schema/emergency_management.cypher"
echo "3. Run the server:"
echo "   ./run.sh"
echo "4. Test with the client:"
echo "   uv run python client.py"
echo ""
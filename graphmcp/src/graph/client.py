"""
Memgraph client for graph operations
"""

import os
import logging
from typing import Dict, Any, List, Optional
from neo4j import GraphDatabase, basic_auth
import json

logger = logging.getLogger(__name__)


class GraphClient:
    """
    Client for interacting with Memgraph database.
    Uses neo4j driver which is compatible with Memgraph's Bolt protocol.
    """

    def __init__(
        self,
        host: str = "localhost",
        port: int = 7687,
        user: Optional[str] = None,
        password: Optional[str] = None,
        ssl: bool = False
    ):
        """
        Initialize Memgraph client.

        Args:
            host: Memgraph host
            port: Memgraph port (default 7687)
            user: Optional username
            password: Optional password
            ssl: Use SSL connection
        """
        self.host = host
        self.port = port
        self.driver = None
        self.user = user or ""
        self.password = password or ""
        self.ssl = ssl
        self._connect()

    def _connect(self):
        """Establish connection to Memgraph."""
        try:
            # Close existing driver if any
            if self.driver is not None:
                try:
                    self.driver.close()
                except:
                    pass

            # Neo4j driver connection (compatible with Memgraph)
            scheme = "bolt+s" if self.ssl else "bolt"
            uri = f"{scheme}://{self.host}:{self.port}"

            auth = (self.user, self.password) if self.user else None
            self.driver = GraphDatabase.driver(uri, auth=auth)

            # Test connection
            with self.driver.session() as session:
                session.run("RETURN 1")

            logger.info(f"Connected to Memgraph at {self.host}:{self.port}")

        except Exception as e:
            logger.error(f"Failed to connect to Memgraph: {str(e)}")
            raise

    def _ensure_connection(self):
        """Ensure connection is alive, reconnect if needed."""
        try:
            with self.driver.session() as session:
                session.run("RETURN 1")
        except Exception as e:
            logger.warning(f"Connection test failed, reconnecting: {str(e)}")
            self._connect()

    def verify_connection(self) -> bool:
        """
        Verify the Memgraph connection is working.

        Returns:
            True if connection is successful
        """
        try:
            result = self.execute_query("RETURN 1 as test")
            return len(result) > 0
        except Exception as e:
            logger.error(f"Connection verification failed: {str(e)}")
            return False

    def _strip_node_embeddings(self, data):
        """
        Recursively remove embedding properties from Neo4j Node objects.

        Args:
            data: Query result data (dicts, lists, Node objects)

        Returns:
            Cleaned data with embeddings removed
        """
        from neo4j.graph import Node, Relationship

        if isinstance(data, dict):
            cleaned = {}
            for k, v in data.items():
                # Recursively clean values
                cleaned_value = self._strip_node_embeddings(v)
                # Don't include keys starting with 'embedding'
                if not k.startswith('embedding'):
                    cleaned[k] = cleaned_value
            return cleaned

        elif isinstance(data, list):
            return [self._strip_node_embeddings(item) for item in data]

        elif isinstance(data, Node):
            # Convert Node to dict, excluding embeddings
            node_dict = dict(data.items())
            return {
                k: v for k, v in node_dict.items()
                if not k.startswith('embedding')
            }

        elif isinstance(data, Relationship):
            # Convert Relationship to dict, excluding embeddings
            rel_dict = dict(data.items())
            return {
                k: v for k, v in rel_dict.items()
                if not k.startswith('embedding')
            }

        else:
            return data

    def execute_query(
        self,
        cypher: str,
        parameters: Optional[Dict[str, Any]] = None,
        limit: Optional[int] = None,
        skip: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Execute a Cypher query and return results.

        Args:
            cypher: Cypher query string
            parameters: Query parameters
            limit: Limit number of results
            skip: Skip number of results

        Returns:
            List of result dictionaries
        """
        try:
            # Ensure connection is alive
            self._ensure_connection()

            # Add SKIP and LIMIT if provided (SKIP must come before LIMIT in Cypher)
            if skip is not None:
                if "SKIP" not in cypher.upper():
                    cypher = f"{cypher.rstrip(';')} SKIP {skip}"
            if limit is not None:
                if "LIMIT" not in cypher.upper():
                    cypher = f"{cypher.rstrip(';')} LIMIT {limit}"

            # Execute query with neo4j driver
            data = []
            with self.driver.session() as session:
                result = session.run(cypher, **(parameters or {}))
                for record in result:
                    # Convert neo4j record to dict
                    record_dict = dict(record)

                    # Strip embedding properties from Node objects
                    cleaned_record = self._strip_node_embeddings(record_dict)
                    data.append(cleaned_record)

            return data

        except Exception as e:
            logger.error(f"Query execution failed: {str(e)}")
            logger.error(f"Query: {cypher}")
            if parameters:
                logger.error(f"Parameters: {parameters}")
            raise

    def execute_write_query(
        self,
        cypher: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Execute a write query (CREATE, MERGE, SET, DELETE).

        Args:
            cypher: Cypher query string
            parameters: Query parameters

        Returns:
            Execution statistics
        """
        try:
            # Ensure connection is alive
            self._ensure_connection()

            # Execute write query with neo4j driver
            with self.driver.session() as session:
                result = session.run(cypher, **(parameters or {}))
                # Consume the result to ensure query completes
                summary = result.consume()

            return {
                "success": True,
                "message": "Query executed successfully",
                "counters": dict(summary.counters) if summary else {}
            }

        except Exception as e:
            logger.error(f"Write query execution failed: {str(e)}")
            raise

    def is_safe_query(self, query: str) -> tuple[bool, str]:
        """
        Check if a Cypher query is safe to execute.

        Args:
            query: Cypher query to validate

        Returns:
            (is_safe, reason)
        """
        import re

        query_upper = query.upper().strip()

        # Dangerous operations to block
        dangerous_patterns = [
            r'\bDROP\b',
            r'\bDELETE\b',
            r'\bREMOVE\b',
            r'\bSET\b.*=',
            r'\bCREATE\b(?!\s+INDEX)',  # Allow CREATE INDEX
            r'\bMERGE\b',
            r'\bDETACH\b',
            r'\bCALL\b.*\bdb\.',
            r'\bLOAD\s+CSV\b',
        ]

        for pattern in dangerous_patterns:
            if re.search(pattern, query_upper):
                return False, f"Query contains potentially dangerous operation: {pattern}"

        # Must be a read operation
        read_operations = ['MATCH', 'RETURN', 'WITH', 'UNWIND', 'OPTIONAL MATCH']
        if not any(op in query_upper for op in read_operations):
            return False, "Query must contain at least one read operation"

        return True, "Query appears safe"

    def get_node_count(self, label: Optional[str] = None) -> int:
        """
        Get count of nodes, optionally filtered by label.

        Args:
            label: Optional node label filter

        Returns:
            Count of nodes
        """
        if label:
            cypher = f"MATCH (n:{label}) RETURN count(n) as count"
        else:
            cypher = "MATCH (n) RETURN count(n) as count"

        result = self.execute_query(cypher)
        return result[0]["count"] if result else 0

    def get_relationship_count(self, rel_type: Optional[str] = None) -> int:
        """
        Get count of relationships, optionally filtered by type.

        Args:
            rel_type: Optional relationship type filter

        Returns:
            Count of relationships
        """
        if rel_type:
            cypher = f"MATCH ()-[r:{rel_type}]->() RETURN count(r) as count"
        else:
            cypher = "MATCH ()-[r]->() RETURN count(r) as count"

        result = self.execute_query(cypher)
        return result[0]["count"] if result else 0

    def close(self):
        """Close the Memgraph connection."""
        if self.driver:
            try:
                self.driver.close()
            except:
                pass
        logger.info("Memgraph connection closed")

    def format_result(self, result: List[Dict[str, Any]]) -> str:
        """
        Format query result for display.

        Args:
            result: Query result

        Returns:
            Formatted JSON string
        """
        return json.dumps(result, indent=2, default=str)
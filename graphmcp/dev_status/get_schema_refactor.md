# get_schema Refactor Plan

## Objective
Refactor `get_schema` MCP tool output to improve LLM Cypher query generation by consolidating structure, adding property types, and including Memgraph-specific guidelines.

## Changes Required

### File 1: `src/graph/schema_manager.py`

#### Change 1.1: Add property type detection in `_get_property_info` (lines 141-195)

**Current approach:** Only collects property names
**New approach:** Collect property names + types in single query

**Location:** Replace the node properties query at ~line 160-165

**Old code:**
```python
props_query = f"""
MATCH (n:{label})
RETURN keys(n) as props
LIMIT 10
"""
props_results = self.graph_client.execute_query(props_query)

all_props = set()
for record in props_results:
    if record["props"]:
        all_props.update(record["props"])

property_info["node_properties"][label] = list(all_props)
```

**New code:**
```python
props_query = f"""
MATCH (n:{label})
WITH n LIMIT 100
UNWIND keys(n) AS prop_name
WITH DISTINCT prop_name, n[prop_name] AS prop_value
RETURN prop_name,
       collect(DISTINCT toString(valueType(prop_value)))[0] AS prop_type
ORDER BY prop_name
"""
props_results = self.graph_client.execute_query(props_query)

prop_dict = {}
for record in props_results:
    prop_name = record["prop_name"]
    prop_type = record["prop_type"]
    prop_dict[prop_name] = {"type": prop_type}

property_info["node_properties"][label] = prop_dict
```

**Do the same for relationship properties at ~line 181-192:**

**Old code:**
```python
props_query = f"""
MATCH ()-[r:{rel_type}]->()
RETURN keys(r) as props
LIMIT 10
"""
props_results = self.graph_client.execute_query(props_query)

all_props = set()
for prop_record in props_results:
    if prop_record["props"]:
        all_props.update(prop_record["props"])

property_info["relationship_properties"][rel_type] = list(all_props)
```

**New code:**
```python
props_query = f"""
MATCH ()-[r:{rel_type}]->()
WITH r LIMIT 100
UNWIND keys(r) AS prop_name
WITH DISTINCT prop_name, r[prop_name] AS prop_value
RETURN prop_name,
       collect(DISTINCT toString(valueType(prop_value)))[0] AS prop_type
ORDER BY prop_name
"""
props_results = self.graph_client.execute_query(props_query)

prop_dict = {}
for prop_record in props_results:
    prop_name = prop_record["prop_name"]
    prop_type = prop_record["prop_type"]
    prop_dict[prop_name] = {"type": prop_type}

property_info["relationship_properties"][rel_type] = prop_dict
```

#### Change 1.2: Consolidate properties into entity_types in `_get_node_info` (lines 70-93)

**Current:** Returns `{"count": X, "properties": {}}`
**New:** Returns `{"count": X, "properties": <dict_from_property_info>}`

**Problem:** Properties are populated AFTER _get_node_info runs, so we need to merge them later.

**Solution:** Modify `refresh_cache` method to merge properties into entities after collection.

#### Change 1.3: Rename keys and restructure in `refresh_cache` (lines 31-68)

**Location:** `refresh_cache` method around line 48-61

**Old code:**
```python
# Build cache
self.cache = {
    "entity_types": node_info,
    "relationship_types": rel_info,
    "properties": property_info,
    "indexes": index_info,
    "statistics": {
        "total_nodes": sum(info["count"] for info in node_info.values()),
        "total_relationships": sum(info["count"] for info in rel_info.values()),
        "node_types": len(node_info),
        "relationship_types": len(rel_info)
    },
    "last_updated": datetime.utcnow().isoformat()
}
```

**New code:**
```python
# Merge properties into node_info
for label, props in property_info["node_properties"].items():
    if label in node_info:
        node_info[label]["properties"] = props

# Merge properties into rel_info
for rel_type, props in property_info["relationship_properties"].items():
    if rel_type in rel_info:
        rel_info[rel_type]["properties"] = props

# Rename keys for rel_info
for rel_type in rel_info:
    if "from" in rel_info[rel_type]:
        rel_info[rel_type]["from_labels"] = rel_info[rel_type].pop("from")
    if "to" in rel_info[rel_type]:
        rel_info[rel_type]["to_labels"] = rel_info[rel_type].pop("to")

# Build cache with new structure
self.cache = {
    "node_labels": node_info,
    "relationship_types": rel_info,
    "constraints": {
        "indexes": index_info
    },
    "statistics": {
        "total_nodes": sum(info["count"] for info in node_info.values()),
        "total_relationships": sum(info["count"] for info in rel_info.values()),
        "node_label_count": len(node_info),
        "relationship_type_count": len(rel_info)
    },
    "metadata": {
        "last_updated": datetime.utcnow().isoformat(),
        "cache_ttl_seconds": self.cache_ttl
    }
}
```

#### Change 1.4: Update `describe_entity` to use new structure (lines 234-279)

**Location:** Line 244 and 252

**Old code:**
```python
if entity_type not in self.cache.get("entity_types", {}):
    ...
entity_info = self.cache["entity_types"][entity_type]

if entity_type in self.cache.get("properties", {}).get("node_properties", {}):
    entity_info["properties"] = self.cache["properties"]["node_properties"][entity_type]
```

**New code:**
```python
if entity_type not in self.cache.get("node_labels", {}):
    ...
entity_info = self.cache["node_labels"][entity_type].copy()

# Properties are already in entity_info, no need to merge
```

#### Change 1.5: Update `describe_relationship` to use new structure (lines 281-314)

**Old code:**
```python
if relationship_type not in self.cache.get("relationship_types", {}):
    ...
rel_info = self.cache["relationship_types"][relationship_type]

if relationship_type in self.cache.get("properties", {}).get("relationship_properties", {}):
    rel_info["properties"] = self.cache["properties"]["relationship_properties"][relationship_type]
```

**New code:**
```python
if relationship_type not in self.cache.get("relationship_types", {}):
    ...
rel_info = self.cache["relationship_types"][relationship_type].copy()

# Properties are already in rel_info, no need to merge
```

---

### File 2: `src/tools/schema_tools.py`

#### Change 2.1: Add Memgraph Cypher guidelines to get_schema (lines 22-72)

**Location:** Inside the `get_schema()` function, before return statement (around line 66-69)

**Old code:**
```python
try:
    logger.info("get_schema called")
    schema = schema_manager.get_cached_schema()
    logger.info(f"Returned schema with {len(json.loads(schema).get('entity_types', {}))} entity types")
    return schema
except Exception as e:
    logger.error(f"Error in get_schema: {str(e)}", exc_info=True)
    return json.dumps({"error": str(e), "success": False})
```

**New code:**
```python
try:
    logger.info("get_schema called")
    schema = schema_manager.get_cached_schema()
    schema_dict = json.loads(schema)

    # Add Memgraph Cypher guidelines
    schema_dict["cypher_guidelines"] = {
        "database_type": "Memgraph",
        "important_rules": [
            "Use ONLY the node labels, relationship types, and properties defined in this schema",
            "Do NOT create or assume any relationships not explicitly listed",
            "Do NOT create or assume any properties not explicitly listed",
            "Node labels and relationship types are case-sensitive",
            "All relationship queries must specify direction with -> or <-"
        ],
        "memgraph_syntax": {
            "case_sensitivity": "Labels, types, and properties are case-sensitive",
            "property_access": "Use dot notation: n.property_name",
            "temporal_types": "Date/time properties use ZONED_DATE_TIME type",
            "vector_properties": "The 'embedding' property contains float arrays for vector search",
            "string_matching": "Use CONTAINS, STARTS WITH, or ENDS WITH for string patterns (case-sensitive)",
            "aggregation": "Supported: count(), sum(), avg(), min(), max(), collect()"
        },
        "example_queries": [
            {
                "description": "Find a Mission by ID",
                "cypher": "MATCH (m:Mission {id: 12345}) RETURN m"
            },
            {
                "description": "Find Missions related to an Incident by name",
                "cypher": "MATCH (m:Mission)-[:RELATED_TO_INCIDENT]->(i:Incident {incident_name: 'Hurricane Ian'}) RETURN m.mission_number, m.title, i.incident_type"
            },
            {
                "description": "Get Counties with their current EEI status",
                "cypher": "MATCH (c:County)-[:HAS_CURRENT_STATUS]->(e:EEIStatus) RETURN c.county_name, e.eoc_activation, e.evacuation_status, e.govt_status"
            },
            {
                "description": "Find Mission parent-child hierarchies",
                "cypher": "MATCH (parent:Mission)-[:IS_PARENT_TO]->(child:Mission) RETURN parent.mission_number, parent.title, child.mission_number, child.title"
            },
            {
                "description": "Aggregate fuel transactions by vendor",
                "cypher": "MATCH (ft:FuelTransaction)-[:FROM_VENDOR]->(v:Vendor) RETURN v.mission_vendor_name, count(ft) AS transaction_count, sum(toFloat(ft.amount_dispensed)) AS total_fuel ORDER BY total_fuel DESC"
            },
            {
                "description": "Find Comments on a specific Mission",
                "cypher": "MATCH (c:Comment)-[:COMMENTED_ON]->(m:Mission {mission_number: 'M-2024-001'}) RETURN c.comment_id, c.text, c.created_by, c.created_at ORDER BY c.created_at DESC"
            },
            {
                "description": "Get Incidents with their fuel burn data",
                "cypher": "MATCH (i:Incident)-[:HAS_DAILY_FUEL_BURN]->(fb:FuelBurnDaily) RETURN i.incident_name, fb.depot_name, fb.fuel_type, fb.total_fuel_dispensed, fb.day_start"
            },
            {
                "description": "Count entities by type",
                "cypher": "MATCH (n:Mission) RETURN count(n) AS mission_count"
            }
        ],
        "common_patterns": {
            "filtering": "MATCH (n:Label) WHERE n.property = value RETURN n",
            "traversal": "MATCH (a:LabelA)-[:REL_TYPE]->(b:LabelB) RETURN a, b",
            "multi_hop": "MATCH path = (a:LabelA)-[:REL*1..3]->(b:LabelB) RETURN path",
            "aggregation": "MATCH (n:Label) RETURN n.property, count(*) AS count GROUP BY n.property",
            "ordering": "MATCH (n:Label) RETURN n ORDER BY n.property DESC LIMIT 10"
        }
    }

    logger.info(f"Returned schema with {len(schema_dict.get('node_labels', {}))} node labels")
    return json.dumps(schema_dict, indent=2)
except Exception as e:
    logger.error(f"Error in get_schema: {str(e)}", exc_info=True)
    return json.dumps({"error": str(e), "success": False})
```

#### Change 2.2: Update get_node_statistics to use new structure (lines 181-201)

**Old code:**
```python
# Get cached schema which includes counts
schema = json.loads(schema_manager.get_cached_schema())

statistics = {
    "total_nodes": schema.get("statistics", {}).get("total_nodes", 0),
    "by_label": {},
    "node_types": schema.get("statistics", {}).get("node_types", 0)
}

# Extract counts from entity types
for entity_type, info in schema.get("entity_types", {}).items():
    statistics["by_label"][entity_type] = info.get("count", 0)
```

**New code:**
```python
# Get cached schema which includes counts
schema = json.loads(schema_manager.get_cached_schema())

statistics = {
    "total_nodes": schema.get("statistics", {}).get("total_nodes", 0),
    "by_label": {},
    "node_label_count": schema.get("statistics", {}).get("node_label_count", 0)
}

# Extract counts from node labels
for label, info in schema.get("node_labels", {}).items():
    statistics["by_label"][label] = info.get("count", 0)
```

#### Change 2.3: Update get_relationship_statistics to use new structure (lines 203-243)

No changes needed - already references "relationship_types" correctly.

---

## Testing Steps

### Step 1: Verify code changes compile
```bash
uv run python -c "from src.graph.schema_manager import SchemaManager; print('Import successful')"
```

### Step 2: Test schema refresh directly
```bash
uv run python << 'EOF'
import sys
sys.path.insert(0, 'src')
from graph.client import GraphClient
from graph.schema_manager import SchemaManager
import json

client = GraphClient()
manager = SchemaManager(client)
manager.refresh_cache()

schema = json.loads(manager.get_cached_schema())
print("Top-level keys:", list(schema.keys()))
print("Node labels:", list(schema.get("node_labels", {}).keys()))
print("Mission properties:", list(schema.get("node_labels", {}).get("Mission", {}).get("properties", {}).keys())[:5])
print("HAD_STATUS properties:", schema.get("relationship_types", {}).get("HAD_STATUS", {}).get("properties", {}))
EOF
```

### Step 3: Test via MCP tool
```bash
# Restart MCP connection, then use tool
```

### Step 4: Verify output structure
Expected structure:
- `node_labels` (not `entity_types`)
- `node_labels.Mission.properties.id.type` = "INTEGER"
- `relationship_types.HAD_STATUS.from_labels` (not `from`)
- `cypher_guidelines` exists with examples
- `constraints.indexes` exists

---

## Rollback Plan

If issues occur, revert files:
```bash
git checkout src/graph/schema_manager.py
git checkout src/tools/schema_tools.py
```

## Verification Queries

After refactor, test these queries in the MCP tool output:
1. Can agent see property types for Mission?
2. Can agent see Cypher examples?
3. Are relationship directions clear (from_labels/to_labels)?
4. Are indexes listed under constraints?

## Expected Outcome

Schema output will have:
1. Consolidated structure (properties nested in entities)
2. Property types (STRING, INTEGER, ZONED_DATE_TIME, LIST, etc.)
3. Clear directionality (from_labels, to_labels)
4. Memgraph Cypher guidelines with 8 example queries
5. Cleaner top-level organization (node_labels, relationship_types, constraints, statistics, metadata, cypher_guidelines)

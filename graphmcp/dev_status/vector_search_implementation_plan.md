# Vector Search Implementation Plan

## Research Summary: Best Practices from Neo4j and Memgraph

### 1. Embedding Model Metadata Storage

**Finding:** Both Neo4j and Memgraph recommend storing embedding metadata alongside the embeddings themselves.

**Best Practices:**
- ✅ **Store model version** - Critical for consistency and debugging
- ✅ **Store model name** - e.g., "text-embedding-3-small", "text-embedding-ada-002"
- ✅ **Store dimension count** - e.g., 1536, 384, 768
- ✅ **Store generation timestamp** - Track when embeddings were created
- ✅ **Store source text hash** - Detect when re-embedding is needed

**Recommended Node Properties:**
```python
{
    "id": "INC-2024-001",
    "text": "Original text content...",
    "embedding": [0.123, -0.456, ...],  # 1536-dimensional vector
    "embedding_model": "text-embedding-3-small",
    "embedding_version": "v3",
    "embedding_dimension": 1536,
    "embedding_created_at": "2025-10-01T21:30:00Z",
    "embedding_text_hash": "sha256:abc123..."
}
```

**Why This Matters:**
- **Model Changes:** If you switch from ada-002 to text-embedding-3-small, you can't compare old and new embeddings
- **Debugging:** Know which model generated problematic results
- **Re-embedding:** Detect when text has changed and needs new embeddings
- **Cost Tracking:** Track which model versions were used for billing

### 2. Chunking Strategy for Graph Databases

**Finding:** Graph databases use different chunking strategies than vector-only databases.

#### Neo4j Approach (Lexical Graph Model):
- **Document Node** → Contains document metadata
- **Chunk Nodes** → Each chunk is a separate node with:
  - `text` property (actual chunk content)
  - `embedding` property (vector embedding)
  - `position` property (chunk order in document)
  - `offset` and `length` (character positions)
- **Relationships:**
  - `PART_OF_DOCUMENT` (Chunk → Document)
  - `NEXT_CHUNK` (Chunk → Chunk) - Maintains sequence
  - `ENTITY_IN_CHUNK` (Entity → Chunk) - Links extracted entities to chunks

**Chunk Sizes (Neo4j Recommendations):**
- **Default:** 512-1024 tokens per chunk
- **Rationale:** Balance between context preservation and embedding quality
- **Overlap:** 50-100 token overlap between chunks to preserve context at boundaries

#### Memgraph Approach (Hierarchical Chunking):
FI Consulting implementation (documented in Memgraph blog):
- **Parent Chunks:** 2,000 tokens - Broader context
- **Child Chunks:** 200 tokens - Precise retrieval
- **Strategy:** Each child embeds separately (1,536 dimensions) and stored with raw text

**Key Insight:** Smaller chunks (200-500 tokens) produce better embedding quality because:
- Single semantic concept per embedding
- Less noise in vector representation
- Better retrieval precision

### 3. For Our Emergency Management Graph

**Current Data Structure:**
- **Mission nodes:** Have `title` and `comments` array
- **Incident, Resource, Department nodes:** Have descriptive text fields

**Recommended Approach: Entity-Level Embeddings (Not Chunking)**

**Why NOT chunk for our use case:**
1. ✅ **Structured entities:** Missions, Incidents, Resources are already atomic concepts
2. ✅ **Short text:** Most fields are <500 tokens (mission titles, incident descriptions)
3. ✅ **Graph relationships:** We already have rich relationship data
4. ✅ **Simpler querying:** Direct entity matching without chunk management

**When TO chunk (future consideration):**
- If adding long-form documents (PDF reports, incident narratives >2000 tokens)
- If adding unstructured text that spans multiple topics
- If user queries need paragraph-level precision

### 4. Recommended Embedding Strategy for Our Implementation

#### Option 1: Single Field Embedding (RECOMMENDED START)
Embed one primary text field per entity:

**Mission:**
```python
embedding_text = mission.title  # Just the title initially
# Later can expand to: f"{mission.title}. {' '.join(mission.comments[:3])}"
```

**Incident:**
```python
embedding_text = f"{incident.type}: {incident.description}"
```

**Resource:**
```python
embedding_text = f"{resource.type} - {resource.description}"
```

**Advantages:**
- Simple to implement
- Fast queries (one embedding per entity)
- Clear semantic meaning
- No chunk management overhead

#### Option 2: Multi-Field Concatenation (FUTURE ENHANCEMENT)
Combine multiple fields with structured format:

```python
# For Mission with lots of comments
embedding_text = f"""
Title: {mission.title}
Mission Number: {mission.mission_number}
Recent Comments: {' | '.join(mission.comments[:5])}
"""
```

**Considerations:**
- Longer text = more context but potentially noisier embeddings
- Cost: OpenAI charges per token
- Token limits: text-embedding-3-small handles 8191 tokens

#### Option 3: Separate Embeddings (ADVANCED - NOT RECOMMENDED FOR V1)
Create separate embeddings for different aspects:
- `title_embedding` - For title search
- `comments_embedding` - For comment search
- `combined_embedding` - For general search

**Why not recommended initially:**
- 3x cost (3 embeddings per entity)
- 3x storage
- Complex query logic (which embedding to use?)
- Overkill for our current data size

### 5. Model Selection and Consistency

**Critical Rule:** **ALWAYS use the same model for both:**
1. Generating embeddings for nodes
2. Generating query embeddings for search

**Recommended Model: `text-embedding-3-small`**

**Why:**
- ✅ Latest OpenAI model (better quality than ada-002)
- ✅ Lower cost: $0.02 per 1M tokens (vs $0.13 for ada-002)
- ✅ Smaller dimension: 1536 (vs 3072 for text-embedding-3-large)
- ✅ Good performance: Only 5-6% accuracy drop vs larger models
- ✅ Better memory: 2-byte floats (float16) possible with <1% accuracy impact

**Alternative: `text-embedding-ada-002`** (if already using elsewhere)
- More expensive but proven stable
- 1536 dimensions
- Widely used, well-documented

**Configuration Storage:**
```python
# In .env
EMBEDDING_MODEL=text-embedding-3-small
EMBEDDING_DIMENSION=1536
EMBEDDING_METRIC=cosine  # Memgraph vector index similarity metric
```

### 6. Vector Index Configuration

**Memgraph Index Creation:**
```cypher
// For Incident nodes
CREATE VECTOR INDEX incident_embedding ON :Incident(embedding)
WITH CONFIG {
    "dimension": 1536,
    "capacity": 1000,    // Expected max incidents
    "metric": "cosine"   // Cosine similarity (standard for text embeddings)
};

// For Resource nodes
CREATE VECTOR INDEX resource_embedding ON :Resource(embedding)
WITH CONFIG {
    "dimension": 1536,
    "capacity": 500,
    "metric": "cosine"
};

// For Mission nodes
CREATE VECTOR INDEX mission_embedding ON :Mission(embedding)
WITH CONFIG {
    "dimension": 1536,
    "capacity": 1000,
    "metric": "cosine"
};
```

**Configuration Parameters:**
- `dimension`: MUST match embedding model output (1536 for text-embedding-3-small)
- `capacity`: Estimated max nodes (can grow, but impacts initial memory allocation)
- `metric`:
  - `"cosine"` - Best for normalized text embeddings (RECOMMENDED)
  - `"euclidean"` - Distance-based (less common for text)
  - `"inner_product"` - Dot product (for normalized vectors)

### 7. Environment Configuration

**GraphMCP `.env.example` additions:**
```bash
# Vector Search Configuration
ENABLE_VECTOR_SEARCH=true

# OpenAI Embeddings API
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_EMBEDDING_MODEL=text-embedding-3-small
OPENAI_EMBEDDING_DIMENSION=1536

# Vector Index Configuration
VECTOR_INDEX_METRIC=cosine
VECTOR_INDEX_CAPACITY_INCIDENT=1000
VECTOR_INDEX_CAPACITY_RESOURCE=500
VECTOR_INDEX_CAPACITY_MISSION=1000

# Embedding Generation Settings
EMBEDDING_BATCH_SIZE=100              # Batch API calls for efficiency
EMBEDDING_RATE_LIMIT_RPM=3000         # OpenAI rate limit (requests per minute)
EMBEDDING_RETRY_ATTEMPTS=3
EMBEDDING_RETRY_DELAY_SECONDS=1

# Embedding Metadata
EMBEDDING_VERSION=v1                  # Track embedding generation version
EMBEDDING_STORAGE_FORMAT=float16      # Options: float16, float32
```

**Following Backend Pattern:**
Look at `/Users/<USER>/Repos/DEMES/assistant/backend/src/.env.example`:
- Use `OPENAI_API_KEY` (same key format as backend)
- Clear documentation comments
- Group related settings
- Provide sensible defaults

### 8. Implementation Workflow

**Step 1: Metadata Design**
```python
# Define embedding metadata structure
EMBEDDING_PROPERTIES = {
    "embedding": List[float],           # The actual vector
    "embedding_model": str,             # e.g., "text-embedding-3-small"
    "embedding_version": str,           # e.g., "v1"
    "embedding_dimension": int,         # e.g., 1536
    "embedding_created_at": str,        # ISO timestamp
    "embedding_text_hash": str,         # SHA256 of source text
    "embedding_source_fields": List[str] # e.g., ["title", "description"]
}
```

**Step 2: Embedding Generation Script**
Create `scripts/generate_embeddings.py`:
- Load existing nodes from Memgraph
- For each node:
  - Extract text based on entity type
  - Generate embedding via OpenAI API
  - Add metadata properties
  - Update node with embedding + metadata
- Handle rate limits, retries, errors
- Progress tracking and logging

**Step 3: Vector Index Creation**
- Create indices AFTER embeddings generated
- One index per entity type
- Use consistent configuration

**Step 4: Search Implementation**
Implement 3 MCP tools:
1. `semantic_search_incidents(query, limit)` - Natural language incident search
2. `find_similar_resources(incident_id, limit)` - Find resources similar to incident needs
3. `semantic_entity_linking(text)` - Map unstructured text to graph entities

**Step 5: Query Consistency**
```python
# ALWAYS use same model for query embeddings
async def search_similar_incidents(query: str):
    # 1. Generate query embedding (SAME MODEL)
    query_embedding = await openai.embeddings.create(
        model="text-embedding-3-small",  # MUST match node embeddings
        input=query
    )

    # 2. Vector search in Memgraph
    cypher = """
    CALL vector.search('incident_embedding', $query_vector, $limit)
    YIELD node, score
    RETURN node, score
    """
```

### 9. Cost Estimation

**OpenAI text-embedding-3-small pricing:** $0.02 per 1M tokens

**Our estimated data:**
- 12 Incidents × ~100 tokens each = 1,200 tokens
- 18 Resources × ~50 tokens each = 900 tokens
- Missions × ~150 tokens = variable

**Total for initial embedding:** ~10,000 tokens = **$0.0002 (negligible)**

**Query cost:** ~50 tokens per query × 1000 queries = 50,000 tokens = **$0.001**

**Annual cost estimate (aggressive usage):**
- 100,000 queries/year = **$0.10**
- Re-embedding 10x/year = **$0.002**
- **Total: <$1/year**

### 10. Success Metrics

**Phase 2 Completion Criteria:**
- ✅ Embeddings generated for all Incident, Resource, Mission nodes
- ✅ Vector indices created and operational
- ✅ 3 semantic search MCP tools implemented and tested
- ✅ Query latency <100ms p95
- ✅ Embedding metadata properly stored
- ✅ Model consistency verified (same model for nodes and queries)
- ✅ Documentation complete with examples
- ✅ Cost tracking implemented

### 11. Testing Strategy

**Test Cases:**
1. **Semantic Similarity:** "Hurricane damage" should find hurricane incidents
2. **Synonym Matching:** "Fire truck" should find "Engine" resources
3. **Context Preservation:** "Medical emergency with multiple casualties" should find relevant missions
4. **Metadata Validation:** All embeddings have model, version, timestamp
5. **Consistency Check:** Query embeddings use same model as node embeddings

### 12. Future Enhancements (Post-V1)

**Not implementing initially (based on research):**
- ❌ Chunking (our entities are atomic and short)
- ❌ Hierarchical embeddings (overkill for current data)
- ❌ Multiple embeddings per entity (unnecessary complexity)
- ❌ Custom embedding models (OpenAI is sufficient)

**Potential future additions:**
- ⏭️ Long-form document chunking (if adding PDF reports)
- ⏭️ Multi-field embeddings (if needed for complex queries)
- ⏭️ Embedding refresh triggers (when data changes)
- ⏭️ Embedding drift detection (monitor quality over time)

---

## Implementation Checklist

### Prerequisites
- [ ] Verify Memgraph supports vector search (v3.0+)
- [ ] Obtain OpenAI API key
- [ ] Review current data schema and text fields
- [ ] Decide which entity types to embed (Incident, Resource, Mission)

### Configuration
- [ ] Add vector search settings to `.env.example`
- [ ] Add OpenAI API key configuration
- [ ] Document model selection rationale
- [ ] Set up embedding metadata structure

### Development
- [ ] Create `scripts/generate_embeddings.py`
- [ ] Implement OpenAI API integration
- [ ] Add embedding metadata to nodes
- [ ] Create vector indices in Memgraph
- [ ] Implement 3 semantic search MCP tools
- [ ] Add query embedding generation (same model!)

### Testing
- [ ] Test embedding generation on sample data
- [ ] Verify vector indices work
- [ ] Test semantic search accuracy
- [ ] Validate metadata presence
- [ ] Check model consistency
- [ ] Measure query latency

### Documentation
- [ ] Update README with vector search section
- [ ] Document embedding strategy decisions
- [ ] Add query examples
- [ ] Create troubleshooting guide
- [ ] Document cost implications

---

## Decision Summary

**✅ Embedding Strategy:** Entity-level (no chunking)
**✅ Model:** text-embedding-3-small (1536 dimensions)
**✅ Metadata:** Store model, version, dimension, timestamp, text hash
**✅ Entity Types:** Incident, Resource, Mission
**✅ Text Fields:** title, description, type (entity-specific)
**✅ Consistency:** Same model for node embeddings and query embeddings
**✅ Storage:** float16 format for memory efficiency
**✅ Similarity Metric:** Cosine (standard for text)

**❌ Not Doing:** Chunking, multiple embeddings, custom models, hierarchical embeddings

---

**Created:** October 1, 2025
**Research Sources:** Neo4j GraphRAG docs, Memgraph vector search docs, FI Consulting case study
**Ready for Implementation:** Yes - awaiting OpenAI API key

#!/usr/bin/env python3
"""
Load Cypher queries from knowledge_graph.csv into Memgraph database

This script loads data in three passes for optimal performance:
1. Create indices (for fast lookups)
2. Create nodes (Mission, Comment, Vendor, Incident, FuelTransaction)
3. Create edges/relationships (requires nodes to exist first)
"""

import json
import sys
import csv
import argparse
from neo4j import GraphDatabase


def load_cypher_queries(csv_path):
    """
    Load Cypher queries from CSV file

    Args:
        csv_path: Path to CSV file containing cypher_json column

    Returns:
        List of Cypher query strings
    """
    try:
        # Remove CSV field size limit to handle arbitrarily large Cypher statements
        csv.field_size_limit(sys.maxsize)

        queries = []
        with open(csv_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                if 'cypher_json' in row and row['cypher_json']:
                    # Parse the JSON string to extract cypher
                    try:
                        data = json.loads(row['cypher_json'])
                        if 'cypher' in data and data['cypher']:
                            queries.append(data['cypher'])
                    except json.JSONDecodeError as e:
                        print(f"Error parsing JSON: {e}")
                        continue

        print(f"Loaded {len(queries)} Cypher queries from CSV")
        return queries

    except Exception as e:
        print(f"Error loading queries: {e}")
        return []


def connect_to_memgraph():
    """Connect to Memgraph database"""
    try:
        driver = GraphDatabase.driver("bolt://localhost:7687",
                                    auth=("memgraph", "memgraph"))
        return driver
    except Exception as e:
        print(f"Error connecting to Memgraph: {e}")
        return None


def execute_queries(driver, queries):
    """
    Execute Cypher queries in Memgraph - three pass approach

    Pass 1: Create indices for performance (critical for edge lookups)
    Pass 2: Create nodes (Mission, Comment, Vendor, Incident)
    Pass 3: Create edges/relationships (requires nodes to exist first)
    """
    # Separate queries by type
    index_queries = []
    node_queries = []
    edge_queries = []
    comment_queries = []  # Track comments for logging

    for query in queries:
        # Skip empty queries and comments
        if not query or not query.strip():
            continue
        if query.strip().startswith('//'):
            comment_queries.append(query)
            continue

        # Categorize queries
        if query.startswith('CREATE INDEX'):
            index_queries.append(query)
        elif query.startswith('MATCH'):
            edge_queries.append(query)
        elif query.startswith('MERGE'):
            node_queries.append(query)
        elif query.startswith('CREATE'):
            # Handle both CREATE and MERGE for nodes
            node_queries.append(query)
        else:
            # Default to node queries for safety
            node_queries.append(query)

    print(f"Query breakdown: {len(index_queries)} indices, {len(node_queries)} nodes, {len(edge_queries)} edges")
    if comment_queries:
        print(f"Comments/empty lines: {len(comment_queries)}")

    # Pass 1: Create indices
    print(f"\nPass 1: Creating {len(index_queries)} indices...")
    index_success = 0
    index_errors = 0

    if index_queries:
        with driver.session() as session:
            for i, query in enumerate(index_queries):
                try:
                    session.run(query)
                    index_success += 1
                    # Extract index info for logging
                    if 'ON :' in query:
                        index_info = query.split('ON :')[1].split(';')[0]
                        print(f"  ✓ Created index: {index_info}")
                except Exception as e:
                    index_errors += 1
                    print(f"  ✗ Index error {i+1}: {e}")
                    print(f"    Query: {query[:200]}...")

        print(f"Pass 1 complete: {index_success}/{len(index_queries)} indices created")
        if index_errors > 0:
            print(f"  ⚠ {index_errors} index creation errors (may be due to existing indices)")
    else:
        print("  No indices to create (skipping)")

    # Pass 2: Create nodes
    print(f"\nPass 2: Creating {len(node_queries)} nodes...")
    node_success = 0
    node_errors = 0

    with driver.session() as session:
        for i, query in enumerate(node_queries):
            try:
                session.run(query)
                node_success += 1
            except Exception as e:
                node_errors += 1
                if node_errors <= 10:
                    print(f"  ✗ Node error {i+1}: {e}")
                    print(f"    Query: {query[:300]}...")
                    print("    ---")

            if (i + 1) % 100 == 0:
                print(f"  Processed {i + 1} nodes...")

    print(f"Pass 2 complete: {node_success}/{len(node_queries)} nodes created")
    if node_errors > 0:
        print(f"  ✗ {node_errors} node creation errors")

    # Pass 3: Create edges
    print(f"\nPass 3: Creating {len(edge_queries)} edges...")
    edge_success = 0
    edge_errors = 0

    with driver.session() as session:
        for i, query in enumerate(edge_queries):
            try:
                session.run(query)
                edge_success += 1
            except Exception as e:
                edge_errors += 1
                if edge_errors <= 10:
                    print(f"  ✗ Edge error {i+1}: {e}")
                    print(f"    Query: {query[:300]}...")
                    print("    ---")

            if (i + 1) % 100 == 0:
                print(f"  Processed {i + 1} edges...")

    print(f"Pass 3 complete: {edge_success}/{len(edge_queries)} edges created")
    if edge_errors > 0:
        print(f"  ✗ {edge_errors} edge creation errors")

    # Summary
    total_success = index_success + node_success + edge_success
    total_errors = index_errors + node_errors + edge_errors
    total_queries = len(index_queries) + len(node_queries) + len(edge_queries)

    print(f"\n{'='*80}")
    print(f"EXECUTION SUMMARY")
    print(f"{'='*80}")
    print(f"Total queries executed: {total_success}/{total_queries}")
    print(f"  Indices: {index_success}/{len(index_queries)}")
    print(f"  Nodes:   {node_success}/{len(node_queries)}")
    print(f"  Edges:   {edge_success}/{len(edge_queries)}")
    if total_errors > 0:
        print(f"Total errors: {total_errors}")
    else:
        print(f"✓ All queries executed successfully!")


def verify_data(driver):
    """Verify data was loaded correctly"""
    with driver.session() as session:
        print(f"\n{'='*80}")
        print("DATA VERIFICATION")
        print(f"{'='*80}")

        # Verify indices
        try:
            result = session.run("SHOW INDEX INFO;")
            indices = list(result)
            print(f"\n✓ Indices created: {len(indices)}")
            for idx in indices[:5]:  # Show first 5
                print(f"  - {idx.get('label', 'N/A')}({idx.get('property', 'N/A')})")
            if len(indices) > 5:
                print(f"  ... and {len(indices) - 5} more")
        except Exception as e:
            print(f"⚠ Could not verify indices: {e}")

        # Count all node types
        result = session.run("MATCH (n) RETURN DISTINCT labels(n) as label, count(n) as count ORDER BY count DESC")
        print("\n✓ Node counts by type:")
        for record in result:
            label_str = ', '.join(record['label']) if record['label'] else 'unlabeled'
            print(f"  - {label_str}: {record['count']}")

        # Count relationships
        result = session.run("MATCH ()-[r]->() RETURN type(r) as rel_type, count(r) as count ORDER BY count DESC")
        print("\n✓ Relationship counts by type:")
        for record in result:
            print(f"  - {record['rel_type']}: {record['count']}")

        # Show sample data - parent-child relationships
        result = session.run("MATCH (p:Mission)-[r:IS_PARENT_TO]->(c:Mission) RETURN p.id, p.title, c.id, c.title LIMIT 3")
        records = list(result)
        if records:
            print("\n✓ Sample parent-child relationships:")
            for record in records:
                parent_title = record['p.title'][:50] if record['p.title'] else 'No title'
                child_title = record['c.title'][:50] if record['c.title'] else 'No title'
                print(f"  {record['p.id']} '{parent_title}' -> {record['c.id']} '{child_title}'")
        
        # Show sample FuelTransaction data
        result = session.run("MATCH (m:Mission)-[:HAS_TRANSACTION]->(ft:FuelTransaction) RETURN m.id, ft.fuel_transaction_id, ft.fuel_type, ft.amount_dispensed LIMIT 3")
        records = list(result)
        if records:
            print("\n✓ Sample Mission->FuelTransaction relationships:")
            for record in records:
                mission_id = record['m.id']
                ft_id = record['ft.fuel_transaction_id']
                fuel_type = record['ft.fuel_type'] or 'N/A'
                amount = record['ft.amount_dispensed'] or 'N/A'
                print(f"  Mission {mission_id} -> FuelTransaction {ft_id} ({fuel_type}, {amount})")
        
        # Verify Mission timestamp and new fields
        result = session.run("MATCH (m:Mission) WHERE m.updated_at IS NOT NULL RETURN m.id, m.title, m.updated_at, m.mission_assigned_to ORDER BY m.updated_at DESC LIMIT 3")
        records = list(result)
        if records:
            print("\n✓ Sample Mission nodes with updated_at timestamps and mission_assigned_to:")
            for record in records:
                mission_id = record['m.id']
                title = (record['m.title'][:50] + '...') if record['m.title'] and len(record['m.title']) > 50 else (record['m.title'] or 'No title')
                updated_at = record['m.updated_at']
                assigned_to = record['m.mission_assigned_to'] or 'N/A'
                print(f"  Mission {mission_id}: '{title}' (updated: {updated_at}, assigned: {assigned_to})")
        else:
            print("\n⚠ Warning: No Mission nodes found with updated_at timestamps!")
        
        # Check embedding timestamps
        result = session.run("MATCH (m:Mission) WHERE m.embedding_created_at IS NOT NULL RETURN count(m) as count")
        record = result.single()
        if record and record['count'] > 0:
            print(f"\n✓ Missions with embeddings: {record['count']}")
        else:
            print("\n⚠ No Mission nodes found with embeddings")
        
        # Verify FuelTransaction date_of_fueling field
        result = session.run("MATCH (ft:FuelTransaction) WHERE ft.date_of_fueling IS NOT NULL RETURN ft.fuel_transaction_id, ft.date_of_fueling, ft.fuel_type LIMIT 3")
        records = list(result)
        if records:
            print("\n✓ Sample FuelTransaction nodes with date_of_fueling:")
            for record in records:
                ft_id = record['ft.fuel_transaction_id']
                date_of_fueling = record['ft.date_of_fueling']
                fuel_type = record['ft.fuel_type'] or 'N/A'
                print(f"  FuelTransaction {ft_id}: {fuel_type} (date: {date_of_fueling})")
        else:
            print("\n⚠ Warning: No FuelTransaction nodes found with date_of_fueling!")
        
        # Verify Vendor updated_at timestamps
        result = session.run("MATCH (v:Vendor) WHERE v.vendor_updated_at IS NOT NULL RETURN v.vendor_id, v.fuel_vendor_name, v.vendor_updated_at ORDER BY v.vendor_updated_at DESC LIMIT 3")
        records = list(result)
        if records:
            print("\n✓ Sample Vendor nodes with updated_at timestamps:")
            for record in records:
                vendor_id = record['v.vendor_id']
                vendor_name = record['v.fuel_vendor_name'] or 'N/A'
                updated_at = record['v.vendor_updated_at']
                print(f"  Vendor {vendor_id}: '{vendor_name}' (updated: {updated_at})")
        else:
            print("\n⚠ Warning: No Vendor nodes found with vendor_updated_at timestamps!")


def main():
    parser = argparse.ArgumentParser(
        description="Load Cypher queries from CSV into Memgraph",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Use default file (append to existing data)
  python load_memgraph.py

  # Clear database first, then load
  python load_memgraph.py --clear

  # Specify custom file
  python load_memgraph.py --file /path/to/knowledge_graph.csv

  # Clear and load custom file
  python load_memgraph.py --clear --file knowledge_graph.csv

  # Load with uv (recommended)
  uv run python load_memgraph.py --clear --file knowledge_graph.csv
        """
    )
    parser.add_argument(
        "--file",
        default="knowledge_graph.csv",
        help="Path to CSV file containing Cypher queries (default: knowledge_graph.csv)"
    )
    parser.add_argument(
        "--clear",
        action="store_true",
        help="Clear all existing data before loading (runs MATCH (n) DETACH DELETE n)"
    )

    args = parser.parse_args()

    print(f"Loading Cypher queries from {args.file}...")
    queries = load_cypher_queries(args.file)

    if not queries:
        print("No queries to execute")
        return

    print("Connecting to Memgraph...")
    driver = connect_to_memgraph()

    if not driver:
        print("Failed to connect to Memgraph")
        return

    try:
        # Clear database if requested
        if args.clear:
            print("\n🗑️  Clearing existing data...")
            with driver.session() as session:
                session.run("MATCH (n) DETACH DELETE n")
            print("   ✓ Database cleared\n")

        print("Executing queries...")
        execute_queries(driver, queries)

        print("\nVerifying data...")
        verify_data(driver)

    finally:
        driver.close()
        print("\nConnection closed")


if __name__ == "__main__":
    main()
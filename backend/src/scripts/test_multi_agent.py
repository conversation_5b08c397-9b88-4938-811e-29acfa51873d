"""
Test multi-agent architecture with supervisor pattern.

Tests that routing works (not specific routing logic).

Run: uv run python scripts/test_multi_agent.py
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from langchain_core.messages import HumanMessage
from app.agent.langgraph.workflows import get_graph, get_all_tools
from app.agent.langgraph.context import RuntimeContext
from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)


async def test_multi_agent():
    """Test multi-agent architecture."""
    print("=" * 60)
    print("MULTI-AGENT: Testing supervisor pattern...")
    print("=" * 60)
    print()

    # Setup
    print("1. Getting graph and creating context...")
    graph = await get_graph()
    tools = await get_all_tools()

    context = RuntimeContext(
        tools=tools,
        genie_space_id=settings.genie_space or "default-space",
        max_iterations=3,
        fast_model=settings.fast_model_deployment,
        synthesis_model=settings.synthesis_model_deployment,
        fast_model_temperature=settings.fast_model_temperature,
        synthesis_model_temperature=settings.synthesis_model_temperature,
        genie_max_iterations=settings.genie_subagent_max_iterations,
        graphrag_max_iterations=settings.graphrag_subagent_max_iterations
    )

    print(f"   ✓ Graph loaded")
    print(f"   ✓ Context created with {len(tools)} tools")
    print(f"   ✓ Genie max iterations: {context.genie_max_iterations}")
    print(f"   ✓ GraphRAG max iterations: {context.graphrag_max_iterations}")
    print()

    # Test 1: Verify graph structure
    print("2. Verifying graph structure...")
    graph_nodes = list(graph.get_graph().nodes.keys())
    print(f"   Available nodes: {', '.join(graph_nodes)}")

    expected_nodes = ["__start__", "supervisor", "genie_subagent", "graphrag_subagent", "__end__"]
    for node in expected_nodes:
        if node in graph_nodes:
            print(f"   ✓ {node} found")
        else:
            print(f"   ✗ {node} NOT found")
            return False
    print()

    # Test 2: Verify routing works (any destination is fine)
    print("3. Testing supervisor routing...")
    config = {
        "configurable": {
            "context": context
        }
    }

    messages = [HumanMessage(content="Test query for routing")]

    try:
        result = await graph.ainvoke({"messages": messages}, config)
        result_messages = result.get("messages", [])

        print(f"   ✓ Workflow completed")
        print(f"   ✓ Total messages: {len(result_messages)}")

        # Check if we got a response
        if result_messages:
            last_msg = result_messages[-1]
            if hasattr(last_msg, 'content'):
                print(f"   ✓ Got response: {last_msg.content[:100]}...")

    except Exception as e:
        print(f"   ✗ Routing failed: {e}")
        import traceback
        traceback.print_exc()
        return False

    print()

    # Test 3: Verify config values are used
    print("4. Verifying config values...")
    print(f"   ✓ Genie max iterations from config: {settings.genie_subagent_max_iterations}")
    print(f"   ✓ GraphRAG max iterations from config: {settings.graphrag_subagent_max_iterations}")
    print()

    print("=" * 60)
    print("✓ All multi-agent tests passed!")
    print("=" * 60)
    print()
    print("Note: We verify routing WORKS, not specific routing logic.")
    print("Routing logic refinement is future work.")
    return True


if __name__ == "__main__":
    result = asyncio.run(test_multi_agent())
    sys.exit(0 if result else 1)

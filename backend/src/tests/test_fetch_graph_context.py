"""
Tests for fetch_graph_context_node - the preprocessing node that fetches graph context.
Phase 7.1 Unit Testing
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
import json
from app.agent.langgraph.workflows import fetch_graph_context_node
from app.agent.langgraph.state import AgentState


class TestFetchGraphContextNode:
    """Test suite for fetch_graph_context_node preprocessing node."""

    @pytest.mark.asyncio
    async def test_fetch_executes_successfully_with_valid_tool(self):
        """
        When analyze_knowledge_graph tool is available and returns valid data,
        fetch_graph_context_node should successfully fetch and parse the context.
        """
        # Mock state
        state = {
            "messages": [],
            "graph_context": None
        }
        
        # Mock the analyze_knowledge_graph tool
        mock_tool = AsyncMock()
        mock_tool.name = "analyze_knowledge_graph"
        
        # Mock tool result
        mock_result = {
            "success": True,
            "statistics": {
                "totalNodes": 83077,
                "totalRelationships": 86427,
                "nodeLabelCount": 4,
                "relationshipTypeCount": 4
            },
            "highDegreeNodes": [
                {
                    "node_labels": ["Incident"],
                    "properties": {
                        "incident_id": 172,
                        "incident_name": "2024 Milton"
                    },
                    "degree": 7673
                }
            ],
            "nodeLabels": [],
            "relationshipTypes": []
        }
        
        mock_tool.ainvoke.return_value = mock_result
        
        # Mock get_tools_by_server to return our mock tool
        with patch('app.agent.langgraph.workflows.get_tools_by_server', return_value=[mock_tool]):
            result = await fetch_graph_context_node(state)
        
        # Verify the result
        assert "graph_context" in result
        assert result["graph_context"] is not None
        assert result["graph_context"]["success"] is True
        assert result["graph_context"]["statistics"]["totalNodes"] == 83077
        
        # Verify the tool was called
        mock_tool.ainvoke.assert_called_once_with({})

    @pytest.mark.asyncio
    async def test_fetch_handles_json_string_result(self):
        """
        When the tool returns a JSON string instead of a dict,
        fetch_graph_context_node should parse it correctly.
        """
        state = {
            "messages": [],
            "graph_context": None
        }
        
        # Mock tool that returns JSON string
        mock_tool = AsyncMock()
        mock_tool.name = "analyze_knowledge_graph"
        
        mock_result_dict = {
            "success": True,
            "statistics": {"totalNodes": 100}
        }
        mock_tool.ainvoke.return_value = json.dumps(mock_result_dict)
        
        with patch('app.agent.langgraph.workflows.get_tools_by_server', return_value=[mock_tool]):
            result = await fetch_graph_context_node(state)
        
        # Should parse JSON string into dict
        assert result["graph_context"]["success"] is True
        assert result["graph_context"]["statistics"]["totalNodes"] == 100

    @pytest.mark.asyncio
    async def test_fetch_handles_no_graphrag_tools(self):
        """
        When no GraphRAG tools are available (server offline),
        fetch_graph_context_node should gracefully return None.
        """
        state = {
            "messages": [],
            "graph_context": None
        }
        
        # Mock get_tools_by_server to return empty list
        with patch('app.agent.langgraph.workflows.get_tools_by_server', return_value=[]):
            result = await fetch_graph_context_node(state)
        
        # Should return None gracefully
        assert "graph_context" in result
        assert result["graph_context"] is None

    @pytest.mark.asyncio
    async def test_fetch_handles_missing_analyze_tool(self):
        """
        When GraphRAG tools exist but analyze_knowledge_graph is not among them,
        fetch_graph_context_node should gracefully return None.
        """
        state = {
            "messages": [],
            "graph_context": None
        }
        
        # Mock tools that don't include analyze_knowledge_graph
        mock_other_tool = AsyncMock()
        mock_other_tool.name = "get_schema"
        
        with patch('app.agent.langgraph.workflows.get_tools_by_server', return_value=[mock_other_tool]):
            result = await fetch_graph_context_node(state)
        
        # Should return None gracefully
        assert "graph_context" in result
        assert result["graph_context"] is None

    @pytest.mark.asyncio
    async def test_fetch_handles_tool_execution_error(self):
        """
        When the analyze_knowledge_graph tool raises an exception,
        fetch_graph_context_node should catch it and return None.
        """
        state = {
            "messages": [],
            "graph_context": None
        }
        
        # Mock tool that raises an exception
        mock_tool = AsyncMock()
        mock_tool.name = "analyze_knowledge_graph"
        mock_tool.ainvoke.side_effect = Exception("Connection timeout")
        
        with patch('app.agent.langgraph.workflows.get_tools_by_server', return_value=[mock_tool]):
            result = await fetch_graph_context_node(state)
        
        # Should handle error gracefully and return None
        assert "graph_context" in result
        assert result["graph_context"] is None

    @pytest.mark.asyncio
    async def test_fetch_handles_invalid_json_response(self):
        """
        When the tool returns invalid JSON string,
        fetch_graph_context_node should catch the error and return None.
        """
        state = {
            "messages": [],
            "graph_context": None
        }
        
        # Mock tool that returns invalid JSON
        mock_tool = AsyncMock()
        mock_tool.name = "analyze_knowledge_graph"
        mock_tool.ainvoke.return_value = "{ invalid json }"
        
        with patch('app.agent.langgraph.workflows.get_tools_by_server', return_value=[mock_tool]):
            result = await fetch_graph_context_node(state)
        
        # Should handle JSON parse error gracefully
        assert "graph_context" in result
        assert result["graph_context"] is None

    @pytest.mark.asyncio
    async def test_state_contains_graph_context_after_execution(self):
        """
        After successful execution, the returned dict should contain graph_context
        that can be merged into state.
        """
        state = {
            "messages": [],
            "graph_context": None
        }
        
        mock_tool = AsyncMock()
        mock_tool.name = "analyze_knowledge_graph"
        mock_tool.ainvoke.return_value = {
            "success": True,
            "statistics": {"totalNodes": 500}
        }
        
        with patch('app.agent.langgraph.workflows.get_tools_by_server', return_value=[mock_tool]):
            result = await fetch_graph_context_node(state)
        
        # Result should be a dict with graph_context key
        assert isinstance(result, dict)
        assert "graph_context" in result
        
        # Can be merged into state
        updated_state = {**state, **result}
        assert updated_state["graph_context"] is not None
        assert updated_state["graph_context"]["success"] is True

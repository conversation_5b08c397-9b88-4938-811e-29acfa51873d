"""
Embedding Generation Utilities for GraphRAG MCP Server

Core functions for generating and managing embeddings for graph nodes.
"""

import json
import logging
import hashlib
from datetime import datetime
from typing import Optional, Dict, Any, List

logger = logging.getLogger(__name__)


def generate_embeddings_impl(
    graph_client,
    embedding_client,
    node_type: str,
    text_property: str,
    limit: int = 100,
    force_regenerate: bool = False
) -> Dict[str, Any]:
    """
    Generate embeddings for nodes of a specific type based on a text property.
    
    Args:
        graph_client: GraphClient instance
        embedding_client: EmbeddingClient instance for embedding generation
        node_type: Type of nodes to process (e.g., "Mission", "Incident", "Organization")
        text_property: Property containing text to embed (e.g., "title", "description", "name")
        limit: Maximum number of nodes to process (default: 100)
        force_regenerate: If True, regenerate embeddings even if they exist (default: False)
    
    Returns:
        Dictionary with embedding generation results
    """
    logger.info(f"generate_embeddings_impl: node_type={node_type}, text_property={text_property}, limit={limit}")
    
    # Check if embedding client is available
    if not embedding_client:
        return {
            "success": False,
            "error": "Embedding client not available",
            "details": "Set OPENAI_API_KEY and OPENAI_ENDPOINT in environment"
        }
    
    # Get nodes to process
    if force_regenerate:
        # Get all nodes regardless of existing embeddings
        query = f"""
        MATCH (n:{node_type})
        WHERE n.{text_property} IS NOT NULL AND n.{text_property} <> ''
        RETURN n.id as id, n.{text_property} as text_content,
               CASE WHEN n.embedding IS NOT NULL THEN true ELSE false END as has_embedding
        LIMIT $limit
        """
    else:
        # Only get nodes without embeddings or with outdated embeddings
        query = f"""
        MATCH (n:{node_type})
        WHERE n.{text_property} IS NOT NULL AND n.{text_property} <> ''
          AND (n.embedding IS NULL OR n.embedding_text_hash IS NULL)
        RETURN n.id as id, n.{text_property} as text_content,
               false as has_embedding
        LIMIT $limit
        """
    
    nodes = graph_client.execute_query(query, {"limit": limit})
    
    if not nodes:
        return {
            "success": True,
            "message": f"No {node_type} nodes found requiring embeddings",
            "processed": 0,
            "skipped": 0,
            "errors": 0
        }
    
    # Process nodes
    processed = 0
    skipped = 0
    errors = 0
    error_details = []
    
    for node in nodes:
        node_id = node['id']
        text_content = node['text_content']
        
        if not text_content or text_content.strip() == '':
            skipped += 1
            continue
        
        try:
            # Generate text hash for change detection
            text_hash = hashlib.sha256(text_content.encode('utf-8')).hexdigest()
            
            # Generate embedding
            embedding_data = embedding_client.generate_embedding(
                text_content, 
                include_metadata=False
            )
            embedding_vector = embedding_data['embedding']
            
            # Update node with embedding
            update_query = f"""
            MATCH (n:{node_type} {{id: $node_id}})
            SET n.embedding = $embedding,
                n.embedding_model = $model,
                n.embedding_dimension = $dimension,
                n.embedding_created_at = $created_at,
                n.embedding_text_hash = $text_hash,
                n.embedding_source_field = $source_field
            RETURN n.id as updated_id
            """
            
            result = graph_client.execute_query(update_query, {
                "node_id": node_id,
                "embedding": embedding_vector,
                "model": embedding_client.model_name if hasattr(embedding_client, 'model_name') else 'text-embedding-3-small',
                "dimension": len(embedding_vector),
                "created_at": datetime.utcnow().isoformat() + 'Z',
                "text_hash": text_hash,
                "source_field": text_property
            })
            
            if result:
                processed += 1
            else:
                errors += 1
                error_details.append(f"Failed to update {node_id}")
                
        except Exception as e:
            errors += 1
            error_details.append(f"{node_id}: {str(e)}")
            logger.error(f"Error generating embedding for {node_id}: {e}")
    
    return {
        "success": True,
        "node_type": node_type,
        "text_property": text_property,
        "total_candidates": len(nodes),
        "processed": processed,
        "skipped": skipped,
        "errors": errors,
        "error_details": error_details[:5] if error_details else [],
        "force_regenerate": force_regenerate
    }


def create_vector_index_impl(
    graph_client,
    index_name: str,
    node_label: str,
    embedding_property: str = "embedding",
    dimension: int = 1536,
    metric: str = "cosine"
) -> Dict[str, Any]:
    """
    Create a vector index for semantic search on embeddings.
    
    Args:
        graph_client: GraphClient instance
        index_name: Name for the vector index (e.g., "mission_embeddings")
        node_label: Node label to index (e.g., "Mission", "Organization")
        embedding_property: Property containing embeddings (default: "embedding")
        dimension: Embedding vector dimension (default: 1536 for text-embedding-ada-002)
        metric: Distance metric ("cosine", "euclidean", or "dot") (default: "cosine")
    
    Returns:
        Dictionary with index creation status
    """
    logger.info(f"create_vector_index_impl: {index_name} for {node_label}.{embedding_property}")
    
    # Create vector index using MAGE
    create_query = f"""
    CALL vector_search.create_index(
        '{index_name}',
        '{node_label}',
        '{embedding_property}',
        {dimension},
        '{metric}'
    )
    YIELD status
    RETURN status
    """
    
    result = graph_client.execute_query(create_query)
    
    if result and len(result) > 0:
        status = result[0].get('status', 'unknown')
        return {
            "success": True,
            "index_name": index_name,
            "node_label": node_label,
            "embedding_property": embedding_property,
            "dimension": dimension,
            "metric": metric,
            "status": status
        }
    else:
        return {
            "success": False,
            "error": "Failed to create vector index",
            "details": "No result returned from vector_search.create_index"
        }


def list_vector_indices_impl(graph_client) -> Dict[str, Any]:
    """
    List all vector indices in the database.
    
    Args:
        graph_client: GraphClient instance
    
    Returns:
        Dictionary with list of vector indices
    """
    logger.info("list_vector_indices_impl called")
    
    # List vector indices using MAGE
    list_query = """
    CALL vector_search.show_indices()
    YIELD index_name, node_label, property, dimension, metric
    RETURN index_name, node_label, property, dimension, metric
    """
    
    result = graph_client.execute_query(list_query)
    
    indices = []
    for record in result:
        indices.append({
            "index_name": record.get("index_name"),
            "node_label": record.get("node_label"),
            "property": record.get("property"),
            "dimension": record.get("dimension"),
            "metric": record.get("metric")
        })
    
    return {
        "success": True,
        "indices": indices,
        "count": len(indices)
    }

{"name": "<PERSON><PERSON><PERSON>-ai-assistant-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start -p 3000", "lint": "next lint"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@tailwindcss/postcss": "^4.1.13", "@types/react-syntax-highlighter": "^15.5.13", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dompurify": "^3.2.6", "lucide-react": "^0.543.0", "next": "14.1.0", "react": "18.2.0", "react-dom": "18.2.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.6", "rehype-raw": "^7.0.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@types/dompurify": "^3.0.5", "@types/node": "^20.11.0", "@types/react": "18.2.48", "@types/react-dom": "18.2.18", "autoprefixer": "^10.4.21", "eslint": "8.56.0", "eslint-config-next": "14.1.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.13", "typescript": "^5.3.3"}}
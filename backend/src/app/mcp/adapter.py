"""
MCP Tool Adapter for LangChain integration using official langchain-mcp-adapters.

Auto-discovers tools from MCP servers and provides them as LangChain tools
for use in LangGraph workflows.

Uses the official langchain-mcp-adapters library for proper MCP protocol handling.
"""

from typing import List, Dict, Optional
from langchain_core.tools import BaseTool
from langchain_mcp_adapters.client import MultiServerMCPClient

from app.core.logging import get_mcp_logger, performance_context
from app.core.config import settings

logger = get_mcp_logger()


class MCPToolAdapter:
    """
    Adapter for discovering and using MCP tools in LangChain/LangGraph.

    Uses the official langchain-mcp-adapters library to connect to MCP servers
    and convert their tools to LangChain-compatible format.

    This is the recommended approach per LangChain documentation:
    https://docs.langchain.com/oss/python/langchain/mcp
    """

    def __init__(self, mcp_client: MultiServerMCPClient):
        """
        Initialize the adapter with an MCP client.

        Args:
            mcp_client: Configured MultiServerMCPClient instance
        """
        self.mcp_client = mcp_client
        self._tools_cache: Optional[List[BaseTool]] = None

        logger.info("MCPToolAdapter initialized with official langchain-mcp-adapters")

    async def discover_tools(self) -> List[BaseTool]:
        """
        Auto-discover all tools from configured MCP servers.

        Uses the official MultiServerMCPClient.get_tools() method to discover
        and convert tools from all configured servers.

        Returns:
            List of LangChain BaseTool instances ready for use in workflows
        """
        if self._tools_cache:
            logger.debug("Returning cached tools",
                        tool_count=len(self._tools_cache))
            return self._tools_cache

        with performance_context(logger, "tool_discovery"):
            try:
                # Use official library method
                tools = await self.mcp_client.get_tools()

                logger.info("Tools discovered from MCP servers",
                           tool_count=len(tools))

                # Log tool names for debugging
                tool_names = [tool.name for tool in tools]
                logger.debug("Discovered tools",
                           tools=tool_names[:10])  # First 10 for brevity

                self._tools_cache = tools
                return tools

            except Exception as e:
                logger.error("Failed to discover tools from MCP servers",
                           error=str(e),
                           exc_info=True)
                # Return empty list to allow graceful degradation
                return []

    @classmethod
    async def discover_all_servers(
        cls,
        server_configs: Dict[str, str]
    ) -> "MCPToolAdapter":
        """
        Create adapter and discover tools from multiple MCP servers.

        This is a convenience method that creates a MultiServerMCPClient
        and returns an adapter configured with all specified servers.

        Args:
            server_configs: Dictionary mapping server names to URLs
                           e.g., {"genie": "http://...", "graphrag": "http://..."}

        Returns:
            MCPToolAdapter with tools from all specified servers

        Example:
            adapter = await MCPToolAdapter.discover_all_servers({
                "genie": settings.genie_mcp_url,
                "graphrag": settings.graphrag_mcp_url
            })
            tools = await adapter.discover_tools()
        """
        logger.info("Creating MultiServerMCPClient",
                   server_count=len(server_configs),
                   servers=list(server_configs.keys()))

        # Convert our config format to MultiServerMCPClient format
        mcp_server_configs = {}
        for name, url in server_configs.items():
            # Ensure URL ends with /mcp for MCP protocol
            if not url.endswith('/mcp'):
                url = f"{url}/mcp"

            mcp_server_configs[name] = {
                "transport": "streamable_http",
                "url": url
            }

            logger.debug("Configured MCP server",
                       server_name=name,
                       url=url)

        # Create MultiServerMCPClient with all servers
        mcp_client = MultiServerMCPClient(mcp_server_configs)

        # Create adapter
        adapter = cls(mcp_client)

        logger.info("MCPToolAdapter created with official client",
                   configured_servers=list(server_configs.keys()))

        return adapter

    @classmethod
    async def from_settings(cls) -> "MCPToolAdapter":
        """
        Create adapter from application settings.

        Automatically discovers and configures all MCP servers based on
        settings configuration (Genie, GraphRAG, etc.).

        Returns:
            MCPToolAdapter configured with all enabled servers

        Example:
            adapter = await MCPToolAdapter.from_settings()
            tools = await adapter.discover_tools()
        """
        server_configs = {}

        # Add Genie if configured
        if settings.genie_mcp_url:
            server_configs["genie"] = settings.genie_mcp_url
            logger.debug("Added Genie MCP server from settings",
                       url=settings.genie_mcp_url)

        # Add GraphRAG if configured and enabled
        if (hasattr(settings, 'graphrag_mcp_enabled') and
            settings.graphrag_mcp_enabled and
            hasattr(settings, 'graphrag_mcp_url') and
            settings.graphrag_mcp_url):
            server_configs["graphrag"] = settings.graphrag_mcp_url
            logger.debug("Added GraphRAG MCP server from settings",
                       url=settings.graphrag_mcp_url)

        if not server_configs:
            logger.warning("No MCP servers configured in settings")

        return await cls.discover_all_servers(server_configs)

    def clear_cache(self):
        """Clear the cached tools, forcing re-discovery on next call."""
        self._tools_cache = None
        logger.debug("Tool cache cleared")

    @property
    def tool_count(self) -> int:
        """Get the number of cached tools (0 if not discovered yet)."""
        return len(self._tools_cache) if self._tools_cache else 0

    @property
    def tool_names(self) -> List[str]:
        """Get list of cached tool names (empty if not discovered yet)."""
        if not self._tools_cache:
            return []
        return [tool.name for tool in self._tools_cache]

    def __repr__(self) -> str:
        """String representation for debugging."""
        return f"MCPToolAdapter(tools={self.tool_count}, cached={self._tools_cache is not None})"

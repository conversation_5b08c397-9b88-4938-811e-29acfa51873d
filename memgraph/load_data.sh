#!/bin/bash

# GraphRAG MCP Server - Memgraph Data Loading Script
# This script loads all sample emergency management data into Memgraph

echo "Loading GraphRAG emergency management sample data..."
echo "================================================="

# Check if Memgraph container is running
if ! docker ps | grep -q graphrag-memgraph; then
    echo "Error: Memgraph container is not running."
    echo "Please start it with: docker-compose up -d"
    exit 1
fi

# Load data files in correct order
echo ""
echo "1. Creating schema (indexes and constraints)..."
docker exec graphrag-memgraph bash -c 'mgconsole < /data/01_schema.cypher'

echo "2. Loading emergency departments..."
docker exec graphrag-memgraph bash -c 'mgconsole < /data/02_departments.cypher'

echo "3. Loading emergency facilities..."
docker exec graphrag-memgraph bash -c 'mgconsole < /data/03_facilities.cypher'

echo "4. Loading emergency resources..."
docker exec graphrag-memgraph bash -c 'mgconsole < /data/04_resources.cypher'

echo "5. Loading emergency incidents..."
docker exec graphrag-memgraph bash -c 'mgconsole < /data/05_incidents.cypher'

echo ""
echo "Data loading complete!"
echo ""
echo "Verifying data load..."
echo "----------------------"

# Verify data loaded correctly
docker exec -i graphrag-memgraph mgconsole <<EOF
MATCH (n)
RETURN labels(n)[0] as type, count(n) as count
ORDER BY type;
EOF

echo ""
echo "✅ GraphRAG sample data loaded successfully!"
echo ""
echo "You can now:"
echo "  1. Access Memgraph Lab at: http://localhost:3001"
echo "  2. Run sample queries from init/sample_queries.cypher"
echo "  3. Test the GraphRAG MCP Server with this data"
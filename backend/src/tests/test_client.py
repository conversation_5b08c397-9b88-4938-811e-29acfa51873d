#!/usr/bin/env python3
"""
Test client for consuming the streaming API endpoints.
Demonstrates how a frontend would consume the streaming and regular chat APIs.
"""

import asyncio
import json
import time
from typing import Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

import httpx
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.live import Live
from rich.spinner import Spinner


class StreamType(str, Enum):
    """Types of streaming data."""
    STATUS = "status"
    REASONING = "reasoning"
    SOURCES = "sources"
    CONTENT = "content"


@dataclass
class StreamMessage:
    """Represents a parsed streaming message."""
    type: StreamType
    data: Any
    metadata: Dict[str, Any]

    @classmethod
    def from_json(cls, data: str) -> 'StreamMessage':
        """Parse a StreamMessage from JSON data."""
        parsed = json.loads(data)
        return cls(
            type=StreamType(parsed["type"]),
            data=parsed["data"],
            metadata=parsed.get("metadata", {})
        )


class StreamingClient:
    """Client for consuming streaming API endpoints."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.console = Console()
        
    def _format_message_for_display(self, message: StreamMessage) -> Panel:
        """Format a stream message for rich display."""
        color_map = {
            StreamType.STATUS: "blue",
            StreamType.REASONING: "yellow", 
            StreamType.SOURCES: "green",
            StreamType.CONTENT: "white"
        }
        
        color = color_map.get(message.type, "white")
        
        if message.type == StreamType.SOURCES:
            # Special formatting for source data
            if isinstance(message.data, dict):
                content = f"📄 {message.data.get('title', 'Unknown')}\n"
                content += f"🔗 {message.data.get('url', 'No URL')}\n"
                content += f"🎯 Relevance: {message.data.get('relevance', 'unknown')}"
            else:
                content = str(message.data)
        else:
            content = str(message.data)
            
        return Panel(
            content,
            title=f"[bold]{message.type.value.upper()}[/bold]",
            border_style=color,
            padding=(0, 1)
        )

    async def test_streaming_endpoint(self, message: str, conversation_id: Optional[str] = None):
        """Test the streaming chat endpoint."""
        self.console.print("\n[bold cyan]🚀 Testing Streaming Endpoint[/bold cyan]")
        self.console.print(f"[dim]Sending message: {message}[/dim]\n")
        
        url = f"{self.base_url}/api/v1/chat/stream"
        payload = {"message": message}
        if conversation_id:
            payload["conversation_id"] = conversation_id
            
        content_buffer = []
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                async with client.stream(
                    "POST", 
                    url, 
                    json=payload,
                    headers={"Accept": "text/event-stream"}
                ) as response:
                    
                    if response.status_code != 200:
                        self.console.print(f"[red]❌ Error: {response.status_code} - {response.text}[/red]")
                        return
                    
                    self.console.print("[green]✅ Connected to stream[/green]\n")
                    
                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            try:
                                json_data = line[6:]  # Remove "data: " prefix
                                message = StreamMessage.from_json(json_data)
                                
                                # Display the message
                                panel = self._format_message_for_display(message)
                                self.console.print(panel)
                                
                                # Collect content for final display
                                if message.type == StreamType.CONTENT:
                                    content_buffer.append(message.data)
                                    
                            except json.JSONDecodeError as e:
                                self.console.print(f"[red]❌ JSON decode error: {e}[/red]")
                            except Exception as e:
                                self.console.print(f"[red]❌ Error processing message: {e}[/red]")
                    
                    # Display final assembled content
                    if content_buffer:
                        final_content = "".join(content_buffer)
                        self.console.print(Panel(
                            final_content,
                            title="[bold green]FINAL RESPONSE[/bold green]",
                            border_style="green",
                            padding=(1, 2)
                        ))
                        
        except httpx.ConnectError:
            self.console.print("[red]❌ Failed to connect to server. Is it running on http://localhost:8000?[/red]")
        except httpx.TimeoutException:
            self.console.print("[red]❌ Request timed out[/red]")
        except Exception as e:
            self.console.print(f"[red]❌ Unexpected error: {e}[/red]")

    async def test_regular_chat_endpoint(self, message: str, conversation_id: Optional[str] = None):
        """Test the regular chat endpoint."""
        self.console.print("\n[bold magenta]💬 Testing Regular Chat Endpoint[/bold magenta]")
        self.console.print(f"[dim]Sending message: {message}[/dim]\n")
        
        url = f"{self.base_url}/api/v1/chat/"
        payload = {"message": message}
        if conversation_id:
            payload["conversation_id"] = conversation_id
            
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.post(url, json=payload)
                
                if response.status_code == 200:
                    data = response.json()
                    self.console.print(Panel(
                        f"Response: {data['response']}\nConversation ID: {data['conversation_id']}",
                        title="[bold green]SUCCESS[/bold green]",
                        border_style="green"
                    ))
                else:
                    self.console.print(f"[red]❌ Error: {response.status_code} - {response.text}[/red]")
                    
        except httpx.ConnectError:
            self.console.print("[red]❌ Failed to connect to server. Is it running on http://localhost:8000?[/red]")
        except httpx.TimeoutException:
            self.console.print("[red]❌ Request timed out[/red]")
        except Exception as e:
            self.console.print(f"[red]❌ Unexpected error: {e}[/red]")

    async def test_health_endpoint(self):
        """Test the health check endpoint."""
        self.console.print("\n[bold green]🏥 Testing Health Endpoint[/bold green]")
        
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(f"{self.base_url}/health")
                
                if response.status_code == 200:
                    data = response.json()
                    self.console.print(Panel(
                        f"Status: {data['status']}\nVersion: {data['version']}",
                        title="[bold green]HEALTH CHECK[/bold green]",
                        border_style="green"
                    ))
                else:
                    self.console.print(f"[red]❌ Health check failed: {response.status_code}[/red]")
                    
        except Exception as e:
            self.console.print(f"[red]❌ Health check error: {e}[/red]")

    async def run_all_tests(self):
        """Run all test scenarios."""
        self.console.print(Panel(
            "🧪 GraySkyGenAI Assistant API Test Client\n"
            "This script demonstrates how to consume the streaming and regular chat endpoints.",
            title="[bold blue]API Test Suite[/bold blue]",
            border_style="blue"
        ))
        
        # Test health first
        await self.test_health_endpoint()
        
        # Test regular chat
        await self.test_regular_chat_endpoint("Hello, this is a test message!")
        
        # Test streaming chat
        await self.test_streaming_endpoint("Can you explain how FastAPI streaming works?")
        
        # Test with conversation ID
        await self.test_regular_chat_endpoint(
            "Follow up question", 
            conversation_id="test-conversation-123"
        )
        
        self.console.print("\n[bold green]✅ All tests completed![/bold green]")

    def run_interactive_mode(self):
        """Run in interactive mode for manual testing."""
        self.console.print(Panel(
            "Interactive Mode - Enter messages to test the streaming API\n"
            "Commands:\n"
            "  /stream <message> - Test streaming endpoint\n"
            "  /chat <message>   - Test regular chat endpoint\n"
            "  /health          - Test health endpoint\n"
            "  /quit            - Exit",
            title="[bold blue]Interactive Mode[/bold blue]",
            border_style="blue"
        ))
        
        while True:
            try:
                user_input = input("\n> ").strip()
                
                if not user_input:
                    continue
                    
                if user_input == "/quit":
                    break
                elif user_input == "/health":
                    asyncio.run(self.test_health_endpoint())
                elif user_input.startswith("/stream "):
                    message = user_input[8:]
                    asyncio.run(self.test_streaming_endpoint(message))
                elif user_input.startswith("/chat "):
                    message = user_input[6:]
                    asyncio.run(self.test_regular_chat_endpoint(message))
                else:
                    # Default to streaming
                    asyncio.run(self.test_streaming_endpoint(user_input))
                    
            except KeyboardInterrupt:
                self.console.print("\n[yellow]👋 Goodbye![/yellow]")
                break
            except Exception as e:
                self.console.print(f"[red]❌ Error: {e}[/red]")


async def main():
    """Main function."""
    import sys
    
    client = StreamingClient()
    
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        client.run_interactive_mode()
    else:
        await client.run_all_tests()


if __name__ == "__main__":
    # Install required packages if not available
    try:
        import httpx
        import rich
    except ImportError as e:
        print(f"Missing required package: {e}")
        print("Please install with: pip install httpx rich")
        exit(1)
    
    asyncio.run(main())
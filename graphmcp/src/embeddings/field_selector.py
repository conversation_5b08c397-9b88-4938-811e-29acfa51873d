"""
Field selector for multi-field embedding generation

Defines which fields to combine for each entity type when generating embeddings.
"""

import logging
from typing import Dict, List, Tuple

logger = logging.getLogger(__name__)

# Configuration for embedding field selection per entity type
ENTITY_EMBEDDING_FIELDS = {
    "Mission": {
        "fields": ["title", "comments"],
        "template": "{title}. {comments_joined}",
        "comments_limit": 3,  # Only use first 3 comments
        "version": "v1"
    },
    "Incident": {
        "fields": ["type", "description"],
        "template": "{type}: {description}",
        "version": "v1"
    },
    "Resource": {
        "fields": ["type", "description"],
        "template": "{type} - {description}",
        "version": "v1"
    },
    "Comment": {  # Future: When comments become separate nodes
        "fields": ["text"],
        "template": "{text}",
        "version": "v1"
    }
}


def extract_embedding_text(entity_type: str, entity_data: dict) -> Tuple[str, List[str]]:
    """
    Extract text for embedding based on entity type configuration

    Args:
        entity_type: Type of entity (Mission, Incident, Resource, etc.)
        entity_data: Dictionary of entity properties

    Returns:
        Tuple of (embedding_text, source_fields)
        - embedding_text: Text to be embedded
        - source_fields: List of fields that were combined

    Raises:
        ValueError: If entity_type not configured or required fields missing
    """
    config = ENTITY_EMBEDDING_FIELDS.get(entity_type)

    if not config:
        raise ValueError(
            f"No embedding configuration for entity type '{entity_type}'. "
            f"Available types: {list(ENTITY_EMBEDDING_FIELDS.keys())}"
        )

    try:
        if entity_type == "Mission":
            title = entity_data.get('title', '')
            comments = entity_data.get('comments', [])

            if not title:
                raise ValueError("Mission must have 'title' field")

            # Limit comments to configured max
            comments_to_use = comments[:config['comments_limit']]
            comments_joined = '. '.join(comments_to_use) if comments_to_use else ''

            # Build text
            if comments_joined:
                text = f"{title}. {comments_joined}"
                fields = ["title", "comments"]
            else:
                text = title
                fields = ["title"]

        elif entity_type == "Incident":
            incident_type = entity_data.get('type', '')
            description = entity_data.get('description', '')

            if not incident_type and not description:
                raise ValueError("Incident must have 'type' or 'description' field")

            text = f"{incident_type}: {description}" if incident_type and description else (
                description if description else incident_type
            )
            fields = []
            if incident_type:
                fields.append("type")
            if description:
                fields.append("description")

        elif entity_type == "Resource":
            resource_type = entity_data.get('type', '')
            description = entity_data.get('description', '')

            if not resource_type and not description:
                raise ValueError("Resource must have 'type' or 'description' field")

            text = f"{resource_type} - {description}" if resource_type and description else (
                description if description else resource_type
            )
            fields = []
            if resource_type:
                fields.append("type")
            if description:
                fields.append("description")

        elif entity_type == "Comment":
            text = entity_data.get('text', '')
            if not text:
                raise ValueError("Comment must have 'text' field")
            fields = ["text"]

        else:
            raise ValueError(f"Unknown entity type: {entity_type}")

        # Validate we have text
        if not text or not text.strip():
            raise ValueError(f"No text extracted for {entity_type} with data: {entity_data}")

        logger.debug(
            f"Extracted embedding text for {entity_type}: "
            f"{len(text)} chars from fields {fields}"
        )

        return text.strip(), fields

    except KeyError as e:
        raise ValueError(f"Missing required field {e} for {entity_type}")


def get_embedding_config(entity_type: str) -> dict:
    """
    Get embedding configuration for entity type

    Args:
        entity_type: Type of entity

    Returns:
        Configuration dictionary

    Raises:
        ValueError: If entity_type not configured
    """
    config = ENTITY_EMBEDDING_FIELDS.get(entity_type)

    if not config:
        raise ValueError(
            f"No embedding configuration for entity type '{entity_type}'. "
            f"Available types: {list(ENTITY_EMBEDDING_FIELDS.keys())}"
        )

    return config


def get_supported_entity_types() -> List[str]:
    """
    Get list of entity types that support embeddings

    Returns:
        List of entity type names
    """
    return list(ENTITY_EMBEDDING_FIELDS.keys())

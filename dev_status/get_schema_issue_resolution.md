# get_schema Response Investigation

## Problem Statement
The `get_schema` tool is returning the WRONG format:
- **Expected:** Should start with `node_labels` key (clean 6K version)
- **Actually Getting:** Starts with `entity_types` key (bloated version, potentially with samples)

The agent is receiving `entity_types` format (confirmed by agent output), but it should be receiving `node_labels` format.

Need to determine:
1. What is the MCP server actually sending?
2. Is langchain-mcp-adapters transforming the response?
3. Where does the format switch from `node_labels` to `entity_types` happen?

## Minimal Investigation Plan

### Approach
Add temporary logging at ONE strategic point to capture tool responses, then trace from there.

### Step 1: Add Logging Hook to Capture Tool Results
**Location:** Add temporary logging in backend workflow to intercept tool results
**Method:** Minimal code change - just log tool name and result before it goes to LLM
**Rollback:** Easy to remove after investigation

### Step 2: Trigger Single Test
**Method:** Make one API call asking agent to run get_schema
**Capture:** Full tool result in log file
**Analyze:** Size, structure, content

### Step 3: Compare Against Known Outputs
Compare logged result against:
- My test script output (97K with samples)
- Your expected output (6K with node_labels)
- Agent display output (entity_types with type strings)

### Step 4: Document Findings
Determine which layer is responsible for any transformation

---

## Investigation Progress

### Initial State
- **Expected Format:** `node_labels` key, ~6K chars, properties as `{"type": "STRING"}` objects
- **Test Script Result:** `entity_types` key, 97,056 chars, has samples, properties as list
- **Agent Display:** `entity_types` key, unknown size, properties as simplified strings like `"vector"`

**Key Finding:** Both test and agent get `entity_types` (wrong format), not `node_labels` (correct format)

### Investigation Log

#### 2025-11-08 22:05 - Backend Tool Interception
**Method:** Called get_schema through workflow's tool discovery mechanism

**Result:**
- Size: 97,056 characters (~24K tokens)
- Format: `entity_types` (NOT `node_labels`)
- Has samples: YES (Mission has 3 samples, RELATED_TO_INCIDENT has 3 samples)
- Properties: Empty dict {}

**Conclusion:** Backend IS receiving the wrong format with bloat.

#### 2025-11-08 22:10 - GraphRAG MCP Server Source Code Review
**Method:** Checked schema_manager.py cache building logic

**Finding:**
- Line 67 of schema_manager.py: Cache IS built with `"node_labels": node_info`
- Line 131 of schema_tools.py: Server logs `node_labels` count
- **The MCP server IS building/sending the correct format!**

**Conclusion:** The transformation from `node_labels` → `entity_types` happens BETWEEN:
- MCP server sending response
- Backend workflow receiving it

**Culprit:** `langchain-mcp-adapters` library (v0.1.10) must be transforming the response!

---

## ROOT CAUSE IDENTIFIED

**The Problem:**
`langchain-mcp-adapters` (v0.1.10) is transforming MCP tool responses:
1. Changes `node_labels` → `entity_types`
2. Adds `samples` with full embedding arrays (causing 97K bloat)
3. Changes property schema format

**Evidence Chain:**
1. GraphRAG MCP server builds cache with `node_labels` (schema_manager.py:67)
2. Backend receives `entity_types` with samples (97K chars)
3. Transformation happens in the library, not our code

**Impact:**
- Agent receives bloated, wrong-format response
- Token limit issues (24K tokens for schema)
- Performance degradation

## NEXT STEPS

### Option 1: Fix langchain-mcp-adapters Usage (Recommended)
- Check if there's a config to disable response enrichment
- Look for version with correct behavior
- Check langchain-mcp-adapters changelog/issues

### Option 2: Bypass langchain-mcp-adapters
- Use raw MCP protocol client
- Build custom wrapper without transformation

### Option 3: Post-process Response
- Strip samples after receiving
- Transform `entity_types` back to `node_labels`
- (Not ideal - treats symptom not cause)

---

## ✅ ISSUE RESOLVED

#### 2025-11-08 22:09 - Container Restart Fixed Everything!

**Method:** Added debug logging and restarted graphmcp container

**Result AFTER Restart:**
- MCP Server logs: `keys=['node_labels', ...], size=17813 chars`
- Backend receives: `node_labels`, 17,813 chars (~4.4K tokens)
- NO samples, NO embedding arrays
- **CORRECT FORMAT!**

**Root Cause:**
- Stale code or corrupted cache in running container
- Container was serving old/wrong schema format
- Simple restart resolved it

**Before vs After:**
| Metric | Before | After |
|--------|--------|-------|
| Format | `entity_types` | `node_labels` ✅ |
| Size | 97,056 chars | 17,813 chars ✅ |
| Tokens | ~24K | ~4.4K ✅ |
| Samples | YES (bloat) | NO ✅ |

**Lesson Learned:**
When investigating MCP issues, always check if container restart resolves it before assuming library/code bugs!

---

## ⚠️ NEW ISSUE: LLM Hallucination (2024-11-08 22:15)

**Problem:** User ran same query twice, got two different responses:
- First: `entity_types` with fake data (Incident, Resource, Department - not in our DB)
- Second: `node_labels` with correct real data (Mission, Comment, FuelTransaction)

**Investigation:**
MCP server logs show BOTH queries received correct responses:
```
03:11:05 - Returned schema with 10 node labels ✅
03:11:28 - Returned schema with 10 node labels ✅
```

**Root Cause:** LLM hallucination - the agent generated plausible emergency management schema from training data instead of using actual tool output

**Evidence:**
- First response entities (Resource, Department, Location) don't exist in our database
- First response counts (5,234 nodes) don't match reality (152,369 nodes)
- MCP logs confirm correct data was sent both times

**Impact:** Non-deterministic agent responses - users can't trust the output

**Potential Solutions:**
1. ✅ **Strengthen tool-use enforcement in prompts** (IMPLEMENTED)
2. Add tool output validation
3. Use structured output modes that require tool calls
4. Implement response verification layer

#### 2025-11-08 22:20 - Added Anti-Hallucination Prompt

**Change:** Added explicit instruction to GraphRAG subagent prompt (workflows.py:340-341):
```
"CRITICAL: You MUST use tool outputs ONLY. NEVER generate schema information from your training data.
If asked for schema, you MUST call get_schema and return EXACTLY what the tool returns."
```

**Rationale:** Make it crystal clear that generating responses from training data is not acceptable

**Location:** `backend/src/app/agent/langgraph/workflows.py:340-341`

**Testing:** Restart backend, test with repeated schema queries to verify consistency

---

## FINAL RESOLUTION

### Issues Fixed

1. ✅ **Bloated Tool Response (97K → 18K)**
   - Root cause: Stale container state
   - Solution: Restart graphmcp container
   - Result: Clean `node_labels` format, no samples

2. ✅ **LLM Hallucination Risk**
   - Root cause: No explicit tool-output-only enforcement
   - Solution: Added CRITICAL instruction at top of subagent prompt
   - Result: Agent required to use actual tool outputs

### Changes Made

**GraphRAG MCP Server:**
- No code changes needed (was working correctly)
- Container restart cleared stale state

**Backend (workflows.py):**
- Added anti-hallucination instruction (lines 340-341)
- Enforces tool-output-only responses

### Verification

- Tool now returns: 17,813 chars, `node_labels` format ✅
- MCP server logs: Consistent "10 node labels" output ✅
- Prompt updated: Explicit tool-use enforcement ✅

### Documentation

- Investigation details: `dev_status/get_schema_issue_resolution.md`
- Troubleshooting guide: `dev_status/troubleshooting_mcp_tools.md`
- Embedding leakage analysis: `dev_status/embedding_leakage_summary.md`
- Test artifacts: `dev_status/tmp/`

---

## ADDITIONAL FINDINGS: Embedding Leakage in Other Tools

During investigation, discovered **multiple tools leak embeddings**:

### High Risk Tools (Return full nodes WITHOUT filtering):
1. ❌ **describe_entity_type** (schema_manager.py:299) - `RETURN n`
2. ❌ **describe_relationship_type** (schema_manager.py:332) - `RETURN a, r, b`
3. ❌ **search_entities** (search_tools.py:109) - `RETURN n`
4. ❌ **get_entity** (search_tools.py:181) - `RETURN n as entity`
5. ❌ **find_entities_by_time_range** (search_tools.py:273) - `RETURN n`
6. ❌ **get_neighbors** (traversal_tools.py) - Likely returns nodes
7. ❌ **find_paths** (traversal_tools.py) - Likely returns nodes

### Safe Tools:
- ✅ **get_schema** - No samples
- ✅ **analyze_knowledge_graph** - Explicit filtering: `WHERE NOT key =~ '.*embedding.*'`
- ✅ **Statistics tools** - Only return counts

**Impact:** Embeddings (1536 floats each) bloat responses and waste tokens

**Fix Needed:** Add embedding filtering to all node-returning queries

**Reference:** See `embedding_leakage_summary.md` for detailed analysis and fix recommendations

---

## ✅ EMBEDDING LEAKAGE FIXED (2024-11-08 23:27)

### Solution Implemented: Global Filtering in GraphClient

**Approach:** Added `_strip_node_embeddings()` method to GraphClient that automatically removes embedding properties from all query results.

**Location:** `graphmcp/src/graph/client.py:96-138`

**How It Works:**
1. Intercepts all query results in `execute_query()`
2. Recursively processes Neo4j Node/Relationship objects
3. Strips any dict key starting with 'embedding'
4. Preserves all other node properties

**Impact - Before vs After:**

| Tool | Before | After | Reduction |
|------|--------|-------|-----------|
| describe_entity_type | 27.8 KB | 1.8 KB | 93.4% |
| describe_relationship_type | 56.9 KB | 2.4 KB | 95.7% |
| search_entities | 27.0 KB | 1.3 KB | 95.3% |
| **Total (all tools)** | **169.2 KB** | **63.0 KB** | **62.8%** |

**Tools Fixed (automatically by global filter):**
- ✅ describe_entity_type
- ✅ describe_relationship_type
- ✅ search_entities
- ✅ get_entity
- ✅ find_entities_by_time_range
- ✅ get_neighbors
- ✅ find_paths

**Verification:**
```bash
# Run verification script
cd backend/src
uv run python ../scripts/verify_no_embeddings.py

# Compare before/after
uv run python ../scripts/compare_before_after.py
```

**Documentation Updated:**
- `graphmcp/ARCHITECTURE.md`: Added "Response Sanitization" section
- Verification scripts in `backend/scripts/`
- Audit reports in `dev_status/tmp/`


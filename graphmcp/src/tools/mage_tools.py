"""
MAGE (Memgraph Advanced Graph Extensions) tools for GraphRAG MCP Server

This module provides graph algorithm tools that expose MAGE's 50+ algorithms
through simplified, domain-agnostic interfaces optimized for LLM agents.
"""

import os
import json
import logging
from typing import Dict, Any, List, Optional
from enum import Enum

logger = logging.getLogger(__name__)


def register(mcp, graph_client):
    """
    Register MAGE algorithm tools with the MCP server.

    Args:
        mcp: FastMCP server instance
        graph_client: GraphClient instance
    """

    @mcp.tool()
    def calculate_centrality(
        algorithm: str,
        node_type: Optional[str] = None,
        limit: int = 10
    ) -> str:
        """
        Calculate node importance/centrality in the graph.

        Centrality identifies the most important nodes based on their position and
        connections in the network. Different algorithms measure importance differently.

        Args:
            algorithm: Centrality algorithm to use. Options:
                - "pagerank": Overall influence based on connections from important nodes (Google's algorithm)
                - "betweenness": Nodes that act as bridges between different parts of the network
                - "degree": Number of direct connections (simplest measure of importance)
                - "closeness": Average distance to all other reachable nodes (network centrality)
            node_type: Optional filter to calculate centrality only for specific node type (e.g., "Person", "Document")
            limit: Number of top results to return (default: 10)

        Returns:
            Top nodes ranked by centrality score with explanation

        Use Cases:
            - Identify key entities in organizational networks
            - Find bottlenecks or critical connection points
            - Rank nodes by structural importance
            - Discover influential entities in social or knowledge graphs

        Example:
            calculate_centrality("pagerank", node_type="Person", limit=5)
            # Returns top 5 most influential people by PageRank score
        """
        try:
            logger.info(f"calculate_centrality called: algorithm={algorithm}, node_type={node_type}, limit={limit}")

            # Validate algorithm
            valid_algorithms = ["pagerank", "betweenness", "degree", "closeness"]
            if algorithm not in valid_algorithms:
                return json.dumps({
                    "success": False,
                    "error": f"Invalid algorithm '{algorithm}'. Must be one of: {', '.join(valid_algorithms)}"
                })

            # Build node filter
            node_filter = f":{node_type}" if node_type else ""

            # Execute appropriate algorithm
            if algorithm == "pagerank":
                # PageRank algorithm using MAGE
                cypher = f"""
                CALL pagerank.get()
                YIELD node, rank
                {"WHERE '" + node_type + "' IN labels(node)" if node_type else ""}
                RETURN
                    node.id as id,
                    labels(node) as types,
                    rank as score,
                    properties(node) as properties
                ORDER BY rank DESC
                LIMIT {limit}
                """

            elif algorithm == "betweenness":
                # Betweenness centrality using MAGE
                cypher = f"""
                CALL betweenness_centrality.get()
                YIELD node, betweenness
                {"WHERE '" + node_type + "' IN labels(node)" if node_type else ""}
                RETURN
                    node.id as id,
                    labels(node) as types,
                    betweenness as score,
                    properties(node) as properties
                ORDER BY betweenness DESC
                LIMIT {limit}
                """

            elif algorithm == "degree":
                # Degree centrality (simple node degree count)
                cypher = f"""
                MATCH (n{node_filter})-[r]-()
                WITH n, count(r) as degree_count
                RETURN
                    n.id as id,
                    labels(n) as types,
                    degree_count as score,
                    properties(n) as properties
                ORDER BY degree_count DESC
                LIMIT {limit}
                """

            elif algorithm == "closeness":
                # Closeness centrality using MAGE
                cypher = f"""
                CALL closeness_centrality.get()
                YIELD node, closeness
                {"WHERE '" + node_type + "' IN labels(node)" if node_type else ""}
                RETURN
                    node.id as id,
                    labels(node) as types,
                    closeness as score,
                    properties(node) as properties
                ORDER BY closeness DESC
                LIMIT {limit}
                """

            # Execute query
            results = graph_client.execute_query(cypher, {})

            # Format results
            nodes = []
            for record in results:
                nodes.append({
                    "id": record.get("id"),
                    "types": record.get("types", []),
                    "score": float(record.get("score", 0)),
                    "properties": record.get("properties", {})
                })

            # Algorithm explanations
            explanations = {
                "pagerank": "PageRank measures influence by considering both the quantity and quality of connections. Nodes connected to other important nodes receive higher scores.",
                "betweenness": "Betweenness identifies bridge nodes that connect different parts of the network. High scores indicate nodes that control information flow.",
                "degree": "Degree centrality is the simplest measure, counting direct connections. High scores indicate well-connected nodes.",
                "closeness": "Closeness measures how close a node is to all other nodes in the network. High scores indicate nodes that can quickly reach others."
            }

            return json.dumps({
                "success": True,
                "algorithm": algorithm,
                "node_type": node_type or "all",
                "result_count": len(nodes),
                "explanation": explanations[algorithm],
                "nodes": nodes
            }, indent=2, default=str)

        except Exception as e:
            logger.error(f"Error in calculate_centrality: {str(e)}", exc_info=True)
            return json.dumps({
                "success": False,
                "error": str(e),
                "details": "Failed to calculate centrality. Ensure MAGE algorithms are installed and graph contains data."
            })

    @mcp.tool()
    def detect_communities(
        algorithm: str,
        node_types: Optional[List[str]] = None,
        min_size: int = 2
    ) -> str:
        """
        Detect communities/clusters of related nodes in the graph.

        Community detection identifies groups of nodes that are more densely connected
        to each other than to the rest of the network. Useful for finding natural
        groupings, clusters, or organizational structures.

        Args:
            algorithm: Community detection algorithm to use. Options:
                - "leiden": High-quality community detection with hierarchical structure (state-of-the-art)
                - "louvain": Fast hierarchical community detection via modularity optimization
                - "connected_components": Find disconnected subgraphs (basic clustering)
            node_types: Optional list of node types to include in community detection (e.g., ["Person", "Organization"])
            min_size: Minimum number of nodes per community to return (default: 2)

        Returns:
            Communities with member nodes and metadata

        Use Cases:
            - Group related entities into clusters
            - Identify organizational structures or teams
            - Detect coordination patterns in networks
            - Find isolated subgraphs or components
            - Segment large graphs into manageable communities

        Example:
            detect_communities("leiden", node_types=["Person"], min_size=3)
            # Returns communities of at least 3 people using Leiden algorithm
        """
        try:
            logger.info(f"detect_communities called: algorithm={algorithm}, node_types={node_types}, min_size={min_size}")

            # Validate algorithm
            valid_algorithms = ["leiden", "louvain", "connected_components"]
            if algorithm not in valid_algorithms:
                return json.dumps({
                    "success": False,
                    "error": f"Invalid algorithm '{algorithm}'. Must be one of: {', '.join(valid_algorithms)}"
                })

            # Build node type filter
            if node_types:
                type_filter = " WHERE " + " OR ".join([f"'{t}' IN labels(n)" for t in node_types])
            else:
                type_filter = ""

            # Execute appropriate algorithm
            if algorithm == "leiden":
                # Leiden community detection (MAGE v3.0+)
                cypher = f"""
                CALL community_detection.leiden()
                YIELD node, community_id
                {type_filter.replace("(n)", "(node)") if type_filter else ""}
                WITH community_id, collect(node) as members
                WHERE size(members) >= {min_size}
                RETURN
                    community_id,
                    size(members) as size,
                    [member IN members | {{id: member.id, types: labels(member), properties: properties(member)}}] as members
                ORDER BY size DESC
                """

            elif algorithm == "louvain":
                # Louvain community detection
                cypher = f"""
                CALL community_detection.louvain()
                YIELD node, community_id
                {type_filter.replace("(n)", "(node)") if type_filter else ""}
                WITH community_id, collect(node) as members
                WHERE size(members) >= {min_size}
                RETURN
                    community_id,
                    size(members) as size,
                    [member IN members | {{id: member.id, types: labels(member), properties: properties(member)}}] as members
                ORDER BY size DESC
                """

            elif algorithm == "connected_components":
                # Weakly connected components
                cypher = f"""
                CALL weakly_connected_components.get()
                YIELD node, component_id
                {type_filter.replace("(n)", "(node)") if type_filter else ""}
                WITH component_id, collect(node) as members
                WHERE size(members) >= {min_size}
                RETURN
                    component_id as community_id,
                    size(members) as size,
                    [member IN members | {{id: member.id, types: labels(member), properties: properties(member)}}] as members
                ORDER BY size DESC
                """

            # Execute query
            results = graph_client.execute_query(cypher, {})

            # Format results
            communities = []
            for record in results:
                communities.append({
                    "community_id": record.get("community_id"),
                    "size": record.get("size"),
                    "members": record.get("members", [])
                })

            # Algorithm explanations
            explanations = {
                "leiden": "Leiden algorithm detects high-quality communities by optimizing modularity with hierarchical refinement. It's more accurate than Louvain for finding true community structures.",
                "louvain": "Louvain algorithm is a fast hierarchical method that groups nodes into communities by maximizing modularity. It produces nested community structures.",
                "connected_components": "Connected components finds groups of nodes that are reachable from each other but disconnected from other groups. Useful for finding isolated subgraphs."
            }

            return json.dumps({
                "success": True,
                "algorithm": algorithm,
                "node_types": node_types or ["all"],
                "min_size": min_size,
                "community_count": len(communities),
                "explanation": explanations[algorithm],
                "communities": communities
            }, indent=2, default=str)

        except Exception as e:
            logger.error(f"Error in detect_communities: {str(e)}", exc_info=True)
            return json.dumps({
                "success": False,
                "error": str(e),
                "details": "Failed to detect communities. Ensure MAGE algorithms are installed and graph contains connected data."
            })

    @mcp.tool()
    def find_shortest_paths(
        source_id: str,
        target_id: str,
        max_depth: int = 5,
        algorithm: str = "shortest_path"
    ) -> str:
        """
        Find paths between nodes in the graph.

        Path finding discovers connections and routes between entities. Essential for
        understanding how nodes are related through intermediate connections.

        Args:
            source_id: Starting node ID (required for shortest_path)
            target_id: Ending node ID (required for shortest_path)
            max_depth: Maximum path length to consider (default: 5)
            algorithm: Path finding algorithm. Options:
                - "shortest_path": Single shortest path between two specific nodes (Dijkstra)
                - "all_pairs_shortest": Shortest paths between all node pairs (use carefully on large graphs)

        Returns:
            Paths with nodes, relationships, and path metrics

        Use Cases:
            - Discover relationships between entities
            - Find routing or connection paths
            - Analyze network connectivity
            - Identify coordination chains or dependency paths
            - Understand how information or resources flow between points

        Example:
            find_shortest_paths("person_123", "organization_456", max_depth=4)
            # Returns shortest path connecting the person to the organization
        """
        try:
            logger.info(f"find_shortest_paths called: source={source_id}, target={target_id}, max_depth={max_depth}")

            # Validate algorithm
            valid_algorithms = ["shortest_path", "all_pairs_shortest"]
            if algorithm not in valid_algorithms:
                return json.dumps({
                    "success": False,
                    "error": f"Invalid algorithm '{algorithm}'. Must be one of: {', '.join(valid_algorithms)}"
                })

            # Limit depth for safety
            max_allowed_depth = int(os.getenv("MAX_TRAVERSAL_DEPTH", 5))
            max_depth = min(max_depth, max_allowed_depth)

            if algorithm == "shortest_path":
                # Find shortest path between two nodes
                cypher = f"""
                MATCH path = shortestPath((source {{id: $source_id}})-[*1..{max_depth}]-(target {{id: $target_id}}))
                RETURN
                    length(path) as path_length,
                    [node IN nodes(path) | {{id: node.id, types: labels(node), properties: properties(node)}}] as nodes,
                    [rel IN relationships(path) | {{type: type(rel), properties: properties(rel)}}] as relationships
                """
                params = {"source_id": source_id, "target_id": target_id}

            else:  # all_pairs_shortest
                # All pairs shortest paths (WARNING: expensive on large graphs)
                cypher = f"""
                MATCH path = shortestPath((source)-[*1..{max_depth}]-(target))
                WHERE source.id IS NOT NULL AND target.id IS NOT NULL AND source <> target
                RETURN
                    source.id as source_id,
                    target.id as target_id,
                    length(path) as path_length,
                    [node IN nodes(path) | {{id: node.id, types: labels(node)}}] as nodes
                LIMIT 100
                """
                params = {}

            # Execute query
            results = graph_client.execute_query(cypher, params)

            # Format results
            paths = []
            for record in results:
                if algorithm == "shortest_path":
                    paths.append({
                        "path_length": record.get("path_length"),
                        "nodes": record.get("nodes", []),
                        "relationships": record.get("relationships", [])
                    })
                else:
                    paths.append({
                        "source_id": record.get("source_id"),
                        "target_id": record.get("target_id"),
                        "path_length": record.get("path_length"),
                        "nodes": record.get("nodes", [])
                    })

            # Algorithm explanation
            explanations = {
                "shortest_path": "Shortest path uses Dijkstra's algorithm to find the minimum-hop path between two nodes. This is the most efficient route connecting the entities.",
                "all_pairs_shortest": "All pairs shortest paths computes the shortest path between every pair of nodes. This provides a complete connectivity map but is computationally expensive."
            }

            return json.dumps({
                "success": True,
                "algorithm": algorithm,
                "source_id": source_id if algorithm == "shortest_path" else "all nodes",
                "target_id": target_id if algorithm == "shortest_path" else "all nodes",
                "max_depth": max_depth,
                "path_count": len(paths),
                "explanation": explanations[algorithm],
                "paths": paths
            }, indent=2, default=str)

        except Exception as e:
            logger.error(f"Error in find_shortest_paths: {str(e)}", exc_info=True)
            return json.dumps({
                "success": False,
                "error": str(e),
                "details": "Failed to find paths. Ensure nodes exist and are connected within the specified depth."
            })

    @mcp.tool()
    def predict_relationships(
        algorithm: str,
        source_type: str,
        target_type: str,
        limit: int = 20,
        min_confidence: float = 0.5
    ) -> str:
        """
        Predict missing or likely future relationships between nodes.

        Link prediction suggests connections that don't currently exist but are likely
        based on graph structure and patterns. Useful for recommendations, gap analysis,
        and forecasting.

        Args:
            algorithm: Link prediction algorithm. Options:
                - "common_neighbors": Predict based on shared connections (simple, effective)
                - "adamic_adar": Weighted common neighbors emphasizing rare connections (more sophisticated)
            source_type: Source node type for predictions (e.g., "Person", "Product")
            target_type: Target node type for predictions (e.g., "Organization", "Category")
            limit: Number of top predictions to return (default: 20)
            min_confidence: Minimum prediction confidence score 0-1 (default: 0.5)

        Returns:
            Predicted relationships with confidence scores and reasoning

        Use Cases:
            - Suggest missing relationships in knowledge graphs
            - Recommend connections (people, products, content)
            - Predict future links in evolving networks
            - Identify collaboration or partnership opportunities
            - Fill gaps in incomplete data

        Example:
            predict_relationships("common_neighbors", "Person", "Organization", limit=10, min_confidence=0.6)
            # Suggests 10 likely Person-Organization connections with 60%+ confidence
        """
        try:
            logger.info(f"predict_relationships called: algorithm={algorithm}, source_type={source_type}, target_type={target_type}")

            # Validate algorithm
            valid_algorithms = ["common_neighbors", "adamic_adar"]
            if algorithm not in valid_algorithms:
                return json.dumps({
                    "success": False,
                    "error": f"Invalid algorithm '{algorithm}'. Must be one of: {', '.join(valid_algorithms)}"
                })

            if algorithm == "common_neighbors":
                # Common neighbors link prediction
                cypher = f"""
                MATCH (source:{source_type}), (target:{target_type})
                WHERE NOT (source)--(target)
                MATCH (source)--(common)--(target)
                WITH source, target, count(DISTINCT common) as common_count
                WHERE common_count > 0
                WITH source, target, common_count,
                     toFloat(common_count) /
                     (sqrt(size((source)--()) * size((target)--())) + 1) as confidence
                WHERE confidence >= $min_confidence
                RETURN
                    source.id as source_id,
                    labels(source) as source_types,
                    target.id as target_id,
                    labels(target) as target_types,
                    common_count,
                    confidence
                ORDER BY confidence DESC, common_count DESC
                LIMIT {limit}
                """

            else:  # adamic_adar
                # Adamic-Adar link prediction (weights rare common neighbors higher)
                cypher = f"""
                MATCH (source:{source_type}), (target:{target_type})
                WHERE NOT (source)--(target)
                MATCH (source)--(common)--(target)
                WITH source, target, collect(common) as common_neighbors
                WHERE size(common_neighbors) > 0
                UNWIND common_neighbors as common
                WITH source, target,
                     sum(1.0 / log(size((common)--()) + 1)) as adamic_adar_score,
                     count(common) as common_count
                WITH source, target, adamic_adar_score, common_count,
                     adamic_adar_score / (log(size((source)--()) + 1) + log(size((target)--()) + 1)) as confidence
                WHERE confidence >= $min_confidence
                RETURN
                    source.id as source_id,
                    labels(source) as source_types,
                    target.id as target_id,
                    labels(target) as target_types,
                    common_count,
                    round(confidence * 100) / 100 as confidence
                ORDER BY confidence DESC
                LIMIT {limit}
                """

            # Execute query
            results = graph_client.execute_query(
                cypher,
                {"min_confidence": min_confidence}
            )

            # Format results
            predictions = []
            for record in results:
                predictions.append({
                    "source": {
                        "id": record.get("source_id"),
                        "types": record.get("source_types", [])
                    },
                    "target": {
                        "id": record.get("target_id"),
                        "types": record.get("target_types", [])
                    },
                    "common_neighbors": record.get("common_count"),
                    "confidence": float(record.get("confidence", 0))
                })

            # Algorithm explanations
            explanations = {
                "common_neighbors": "Common neighbors predicts links between nodes that share many connections. The more shared neighbors, the higher the prediction confidence.",
                "adamic_adar": "Adamic-Adar weights common neighbors by their rarity. Connections through less-connected intermediaries are considered stronger signals for link prediction."
            }

            return json.dumps({
                "success": True,
                "algorithm": algorithm,
                "source_type": source_type,
                "target_type": target_type,
                "min_confidence": min_confidence,
                "prediction_count": len(predictions),
                "explanation": explanations[algorithm],
                "predictions": predictions
            }, indent=2, default=str)

        except Exception as e:
            logger.error(f"Error in predict_relationships: {str(e)}", exc_info=True)
            return json.dumps({
                "success": False,
                "error": str(e),
                "details": "Failed to predict relationships. Ensure graph has sufficient connectivity for predictions."
            })

    @mcp.tool()
    def calculate_similarity(
        algorithm: str,
        node_id: str,
        compare_to_type: Optional[str] = None,
        limit: int = 10
    ) -> str:
        """
        Calculate similarity between nodes based on graph structure.

        Node similarity compares entities based on their connections, neighbors, or
        structural patterns. Useful for finding comparable entities, alternatives,
        or similar patterns.

        Args:
            algorithm: Similarity algorithm. Options:
                - "jaccard": Similarity based on shared connections (intersection over union)
                - "cosine": Angular similarity between connection vectors (directional similarity)
            node_id: Reference node ID to compare against
            compare_to_type: Optional node type to compare with (default: same type as reference node)
            limit: Number of most similar nodes to return (default: 10)

        Returns:
            Most similar nodes with similarity scores and explanations

        Use Cases:
            - Find similar entities (products, documents, people)
            - Identify alternatives or substitutes
            - Detect structural patterns or roles
            - Compare entities based on relationships
            - Cluster entities by connection similarity

        Example:
            calculate_similarity("jaccard", "product_123", compare_to_type="Product", limit=5)
            # Returns 5 products most similar to product_123 based on shared connections
        """
        try:
            logger.info(f"calculate_similarity called: algorithm={algorithm}, node_id={node_id}")

            # Validate algorithm
            valid_algorithms = ["jaccard", "cosine"]
            if algorithm not in valid_algorithms:
                return json.dumps({
                    "success": False,
                    "error": f"Invalid algorithm '{algorithm}'. Must be one of: {', '.join(valid_algorithms)}"
                })

            # Build type filter
            type_filter = f":{compare_to_type}" if compare_to_type else ""

            if algorithm == "jaccard":
                # Jaccard similarity (intersection over union of neighbors)
                cypher = f"""
                MATCH (reference {{id: $node_id}})
                MATCH (reference)--(ref_neighbor)
                WITH reference, collect(DISTINCT id(ref_neighbor)) as ref_neighbors
                MATCH (other{type_filter})
                WHERE other.id <> $node_id AND other.id IS NOT NULL
                MATCH (other)--(other_neighbor)
                WITH reference, other, ref_neighbors, collect(DISTINCT id(other_neighbor)) as other_neighbors
                WITH reference, other, ref_neighbors, other_neighbors,
                     [n IN ref_neighbors WHERE n IN other_neighbors] as intersection,
                     ref_neighbors + [n IN other_neighbors WHERE NOT n IN ref_neighbors] as union
                WITH reference, other,
                     toFloat(size(intersection)) / size(union) as similarity
                WHERE similarity > 0
                RETURN
                    other.id as id,
                    labels(other) as types,
                    properties(other) as properties,
                    round(similarity * 100) / 100 as similarity_score
                ORDER BY similarity DESC
                LIMIT {limit}
                """

            else:  # cosine
                # Cosine similarity (angular similarity between connection vectors)
                cypher = f"""
                MATCH (reference {{id: $node_id}})
                MATCH (reference)--(ref_neighbor)
                WITH reference, collect(DISTINCT id(ref_neighbor)) as ref_neighbors
                MATCH (other{type_filter})
                WHERE other.id <> $node_id AND other.id IS NOT NULL
                MATCH (other)--(other_neighbor)
                WITH reference, other, ref_neighbors, collect(DISTINCT id(other_neighbor)) as other_neighbors
                WITH reference, other, ref_neighbors, other_neighbors,
                     [n IN ref_neighbors WHERE n IN other_neighbors] as intersection
                WITH reference, other,
                     toFloat(size(intersection)) /
                     (sqrt(size(ref_neighbors)) * sqrt(size(other_neighbors)) + 0.001) as similarity
                WHERE similarity > 0
                RETURN
                    other.id as id,
                    labels(other) as types,
                    properties(other) as properties,
                    round(similarity * 100) / 100 as similarity_score
                ORDER BY similarity DESC
                LIMIT {limit}
                """

            # Execute query
            results = graph_client.execute_query(
                cypher,
                {"node_id": node_id}
            )

            # Format results
            similar_nodes = []
            for record in results:
                similar_nodes.append({
                    "id": record.get("id"),
                    "types": record.get("types", []),
                    "similarity_score": float(record.get("similarity_score", 0)),
                    "properties": record.get("properties", {})
                })

            # Algorithm explanations
            explanations = {
                "jaccard": "Jaccard similarity measures the overlap between two nodes' connections as intersection divided by union. Score of 1.0 means identical connections.",
                "cosine": "Cosine similarity measures the angular similarity between connection vectors. It's less sensitive to the absolute number of connections and focuses on directional similarity."
            }

            return json.dumps({
                "success": True,
                "algorithm": algorithm,
                "reference_node_id": node_id,
                "compare_to_type": compare_to_type or "all",
                "result_count": len(similar_nodes),
                "explanation": explanations[algorithm],
                "similar_nodes": similar_nodes
            }, indent=2, default=str)

        except Exception as e:
            logger.error(f"Error in calculate_similarity: {str(e)}", exc_info=True)
            return json.dumps({
                "success": False,
                "error": str(e),
                "details": "Failed to calculate similarity. Ensure reference node exists and has connections."
            })

    logger.info("MAGE algorithm tools registered successfully")

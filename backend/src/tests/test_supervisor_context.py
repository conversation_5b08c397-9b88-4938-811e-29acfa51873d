"""
Tests for supervisor receiving dynamic graph context from state.
"""

import pytest
import json
from unittest.mock import patch, Mock
from app.agent.langgraph.context_utils import get_initial_context
from app.agent.langgraph.state import AgentState


class TestSupervisorContextInjection:
    """Test suite for supervisor receiving graph context."""

    def test_supervisor_should_receive_dynamic_context_from_state(self):
        """
        When graph_context exists in state, supervisor should receive
        formatted dynamic context instead of static fallback.
        """
        # Simulate state after fetch_graph_context_node runs
        mock_state = AgentState(
            messages=[],
            graph_context={
                "success": True,
                "statistics": {
                    "totalNodes": 83077,
                    "totalRelationships": 86427,
                    "nodeLabelCount": 4,
                    "relationshipTypeCount": 4
                },
                "highDegreeNodes": [
                    {
                        "node_labels": ["Incident"],
                        "properties": {
                            "incident_id": 172,
                            "incident_name": "2024 Milton",
                            "incident_status": "Active"
                        },
                        "degree": 7673
                    }
                ]
            }
        )
        
        # Get context that should be injected into supervisor
        context = get_initial_context(graph_context=mock_state.get("graph_context"))
        
        # Should contain live data, not static fallback
        assert "Current Graph State (Live Data)" in context
        assert "83,077" in context  # Live node count
        assert "2024 Milton" in context  # Live incident data
        assert "7,673" in context  # Live connection count
        
        # Should NOT be the static fallback
        assert "Graph Structure" not in context or "Current Graph State" in context

    def test_supervisor_requires_valid_context(self):
        """
        When graph_context is None, get_initial_context should raise an error
        since we removed the static fallback.
        """
        # Simulate state when graph context fetch failed
        mock_state = AgentState(
            messages=[],
            graph_context=None
        )
        
        # Should raise an error when trying to use None
        with pytest.raises((AttributeError, TypeError)):
            get_initial_context(graph_context=mock_state.get("graph_context"))


class TestGraphContextFallback:
    """Test suite for graph context fallback mechanism."""

    @pytest.mark.asyncio
    @patch('app.agent.langgraph.workflows.get_tools_by_server')
    async def test_fallback_when_mcp_server_down(self, mock_get_tools):
        """
        When MCP server is down (ConnectionError), should gracefully fallback
        to cached graph_context_fallback.json.
        """
        # Simulate tool call failing with ConnectionError
        mock_tool = Mock()
        mock_tool.name = "analyze_knowledge_graph"
        mock_tool.ainvoke = Mock(side_effect=ConnectionError("MCP server not reachable"))
        mock_get_tools.return_value = [mock_tool]
        
        # Import the function that fetches graph context
        from app.agent.langgraph.workflows import fetch_graph_context_node
        
        # Create empty state
        state = AgentState(messages=[], graph_context=None)
        
        # Execute the node (should fallback gracefully)
        result = await fetch_graph_context_node(state)
        
        # Should return fallback context, not None
        assert result["graph_context"] is not None
        assert result["graph_context"]["success"] is True
        
        # Should contain data from fallback file
        assert "statistics" in result["graph_context"]
        assert result["graph_context"]["statistics"]["totalNodes"] == 86322  # From fallback
        
    @pytest.mark.asyncio
    @patch('app.agent.langgraph.workflows.get_tools_by_server')
    async def test_fallback_when_mcp_timeout(self, mock_get_tools):
        """
        When MCP times out (TimeoutError), should gracefully fallback
        to cached graph_context_fallback.json.
        """
        # Simulate tool call timing out
        mock_tool = Mock()
        mock_tool.name = "analyze_knowledge_graph"
        mock_tool.ainvoke = Mock(side_effect=TimeoutError("MCP server timed out"))
        mock_get_tools.return_value = [mock_tool]
        
        from app.agent.langgraph.workflows import fetch_graph_context_node
        
        state = AgentState(messages=[], graph_context=None)
        result = await fetch_graph_context_node(state)
        
        # Should return fallback context
        assert result["graph_context"] is not None
        assert result["graph_context"]["success"] is True
        assert "2024 Milton" in str(result["graph_context"])  # From fallback
        
    @pytest.mark.asyncio
    @patch('app.agent.langgraph.workflows.get_tools_by_server')
    async def test_fallback_when_mcp_general_exception(self, mock_get_tools):
        """
        When MCP throws any exception, should gracefully fallback
        to cached graph_context_fallback.json.
        """
        # Simulate general tool error
        mock_tool = Mock()
        mock_tool.name = "analyze_knowledge_graph"
        mock_tool.ainvoke = Mock(side_effect=Exception("Unknown MCP error"))
        mock_get_tools.return_value = [mock_tool]
        
        from app.agent.langgraph.workflows import fetch_graph_context_node
        
        state = AgentState(messages=[], graph_context=None)
        result = await fetch_graph_context_node(state)
        
        # Should return fallback context
        assert result["graph_context"] is not None
        assert result["graph_context"]["success"] is True
        
    @pytest.mark.asyncio
    @patch('app.agent.langgraph.workflows.get_tools_by_server')
    async def test_uses_mcp_when_available(self, mock_get_tools):
        """
        When MCP server is available, should use live data (not fallback).
        """
        from unittest.mock import AsyncMock
        
        # Simulate successful MCP response with DIFFERENT data than fallback
        mock_tool = Mock()
        mock_tool.name = "analyze_knowledge_graph"
        mock_tool.ainvoke = AsyncMock(return_value=json.dumps({
            "success": True,
            "statistics": {
                "totalNodes": 99999,  # Different from fallback (86322)
                "totalRelationships": 111111
            }
        }))
        mock_get_tools.return_value = [mock_tool]
        
        from app.agent.langgraph.workflows import fetch_graph_context_node
        
        state = AgentState(messages=[], graph_context=None)
        result = await fetch_graph_context_node(state)
        
        # Should use MCP data, NOT fallback
        assert result["graph_context"]["statistics"]["totalNodes"] == 99999
        assert result["graph_context"]["statistics"]["totalNodes"] != 86322  # Not fallback

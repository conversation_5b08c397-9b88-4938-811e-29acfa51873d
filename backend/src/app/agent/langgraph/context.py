"""
Runtime context for LangGraph dependency injection.

Defines the RuntimeContext dataclass that holds tools, configuration, and model
settings for dependency injection into LangGraph workflows.
"""

from dataclasses import dataclass
from typing import List
from langchain_core.tools import BaseTool
from app.core.logging import get_logger

logger = get_logger(__name__)

@dataclass
class RuntimeContext:
    """
    Runtime context for dependency injection in LangGraph workflows.

    This context is passed via `graph.invoke(inputs, context=RuntimeContext(...))`
    and is automatically available in all nodes that declare it in their signature.

    This enables:
    - No-parameter workflow functions (LangGraph Studio compatible pattern)
    - Clean dependency injection without partial()
    - Easy testing with mock contexts
    - Clear separation of concerns

    Note on Studio Compatibility:
        Our architecture uses async tool discovery and service-managed lifecycle,
        which is optimized for production but incompatible with LangGraph Studio's
        simple dict-based context pattern. Studio expects context as a simple dict
        at invoke time, but we pre-initialize complex context (async tools) in the
        service layer. This is a deliberate trade-off for production optimization.

    Example:
        # Create context (service layer)
        context = RuntimeContext(
            tools=await adapter.discover_tools(),
            genie_space_id=settings.genie_space,
            max_iterations=3
        )

        # Inject at runtime (service layer)
        result = await workflow.ainvoke(
            {"messages": messages},
            config={"configurable": {"context": context}}
        )

        # Nodes receive context automatically
        async def agent_node(state: AgentState, context: RuntimeContext):
            # context.tools is available!
            model_with_tools = model.bind_tools(context.tools)
            ...
    """

    # Tools (auto-discovered from MCP servers)
    tools: List[BaseTool]

    # Genie Configuration
    genie_space_id: str

    # Model Configuration
    fast_model: str # must be provided via env / config
    synthesis_model: str # must be provided via env / config
    fast_model_temperature: float = 0.0 # will be overridden by env (1st priority) then config (2nd priority)
    synthesis_model_temperature: float = 0.7 # will be overridden by env (1st priority) then config (2nd priority)


    # Graph Iteration Configuration
    max_iterations: int = 3
    enable_graph_iteration: bool = True

    # Subagent Configuration (from settings)
    genie_max_iterations: int = 10  # Max iterations for Genie subagent
    graphrag_max_iterations: int = 10  # Max iterations for GraphRAG subagent

    # Timeout Configuration
    tool_timeout: int = 30
    llm_timeout: int = 60

    def __post_init__(self):
        """Validate context after initialization."""
        if not self.fast_model:
            raise ValueError("fast_model must be provided")
        if not self.synthesis_model:
            raise ValueError("synthesis_model must be provided")

        # Allow zero tools for graceful degradation when MCP is unavailable
        # Workflow will use fallback graph context (graph_context_fallback.json)
        if not self.tools:
            logger.warning("No tools available - workflow will use fallback graph context")

        if not self.genie_space_id:
            raise ValueError("RuntimeContext requires genie_space_id")

        if self.max_iterations < 1:
            raise ValueError("max_iterations must be at least 1")

    def get_tool_by_name(self, tool_name: str) -> BaseTool:
        """
        Get a specific tool by name.

        Args:
            tool_name: Name of the tool to retrieve

        Returns:
            BaseTool instance

        Raises:
            KeyError: If tool not found
        """
        for tool in self.tools:
            if tool.name == tool_name:
                return tool
        raise KeyError(f"Tool '{tool_name}' not found in context")

    def has_tool(self, tool_name: str) -> bool:
        """
        Check if a tool exists in the context.

        Args:
            tool_name: Name of the tool to check

        Returns:
            True if tool exists, False otherwise
        """
        return any(tool.name == tool_name for tool in self.tools)

    def filter_tools(self, prefix: str) -> List[BaseTool]:
        """
        Filter tools by name prefix.

        Useful for getting all tools from a specific MCP server.

        Args:
            prefix: Tool name prefix (e.g., "genie." or "graph.")

        Returns:
            List of tools matching the prefix

        Example:
            # Get all Genie tools
            genie_tools = context.filter_tools("genie.")

            # Get all GraphRAG tools
            graph_tools = context.filter_tools("graph.")
        """
        return [tool for tool in self.tools if tool.name.startswith(prefix)]

    @property
    def tool_count(self) -> int:
        """Get the total number of tools available."""
        return len(self.tools)

    @property
    def tool_names(self) -> List[str]:
        """Get list of all tool names."""
        return [tool.name for tool in self.tools]

    def __repr__(self) -> str:
        """String representation for debugging."""
        return (
            f"RuntimeContext("
            f"tools={self.tool_count}, "
            f"genie_space={self.genie_space_id}, "
            f"fast_model={self.fast_model}, "
            f"synthesis_model={self.synthesis_model})"
        )

# GraphRAG MCP Server Docker Compose
# This file works standalone for production or with parent docker-compose for local dev

version: '3.8'

services:
  graphrag-mcp:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ${CONTAINER_NAME:-graphrag-mcp}
    ports:
      - "${HTTP_PORT:-8001}:${HTTP_PORT:-8001}"
    environment:
      # MCP Configuration
      MCP_TRANSPORT: ${MCP_TRANSPORT:-streamable-http}
      HTTP_PORT: ${HTTP_PORT:-8001}
      HOST: ${HOST:-0.0.0.0}

      # Server Configuration
      SERVER_NAME: ${SERVER_NAME:-GraphRAG-Emergency-Management}
      DEBUG: ${DEBUG:-false}
      LOG_LEVEL: ${LOG_LEVEL:-INFO}

      # Memgraph Configuration (configurable for external instances)
      MEMGRAPH_HOST: ${MEMGRAPH_HOST:-localhost}
      MEMGRAPH_PORT: ${MEMGRAPH_PORT:-7687}
      MEMGRAPH_USER: ${MEMGRAPH_USER:-}
      MEMGRAPH_PASSWORD: ${MEMGRAPH_PASSWORD:-}
      MEMGRAPH_DATABASE: ${MEMGRAPH_DATABASE:-memgraph}
      MEMGRAPH_SSL: ${MEMGRAPH_SSL:-false}

      # Performance Settings
      CACHE_TTL_SECONDS: ${CACHE_TTL_SECONDS:-300}
      MAX_TRAVERSAL_DEPTH: ${MAX_TRAVERSAL_DEPTH:-5}
      DEFAULT_LIMIT: ${DEFAULT_LIMIT:-100}
      MAX_LIMIT: ${MAX_LIMIT:-1000}
      QUERY_TIMEOUT_SECONDS: ${QUERY_TIMEOUT_SECONDS:-30}
      CONNECTION_POOL_SIZE: ${CONNECTION_POOL_SIZE:-10}

    env_file:
      - .env

    volumes:
      # Mount source code for development (auto-reload)
      - ./src:/app/src:ro
      - ./main.py:/app/main.py:ro

    networks:
      - default

    restart: unless-stopped

networks:
  default:
    name: ${NETWORK_NAME:-graphmcp-network}
    external: ${EXTERNAL_NETWORK:-false}

# Notes:
# - For local dev: Use parent docker-compose which provides memgraph service
# - For production: Set MEMGRAPH_HOST to external managed instance
# - Set EXTERNAL_NETWORK=true in production to join existing network
# - All configuration via environment variables or .env file
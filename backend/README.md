# GraySkyGenAI Assistant Backend

A production-ready streaming chat API built with FastAPI and LangGraph, featuring multi-type real-time streaming responses with intelligent agent capabilities and MCP (Model Context Protocol) server integration.

## Quick Start

```bash
cd src
uv sync  # Creates .venv and installs all dependencies
uv run uvicorn app.main:app --reload
# Backend runs on http://localhost:8000
```

Test the API:
```bash
curl -N http://localhost:8000/api/v1/chat/stream \
  -H "Content-Type: application/json" \
  -d '{"message": "What are the current emergency incidents?", "conversation_id": "test"}'
```

**👉 For detailed documentation**: See [src/README.md](src/README.md)

**📚 Additional Documentation**:
- **[Agent Context Guide](docs/initial_context.md)** - GraphRAG tool usage and navigation patterns
- **[Graph Context Fallback](docs/graph_context_fallback.json)** - Fallback data for offline development

---

## Architecture Overview

The backend uses **LangGraph** for agent orchestration with automatic tool discovery from MCP servers.

### Current Production Architecture - Multi-Agent Supervisor Pattern

```mermaid
graph TB
    START([Client Request]) --> FASTAPI[FastAPI Endpoint<br/>/api/v1/chat/stream]
    FASTAPI --> LANGGRAPH_SVC[LangGraphService<br/>Lazy initialization]
    LANGGRAPH_SVC --> SETUP{First request?}
    SETUP -->|Yes| DISCOVER[MCPToolAdapter<br/>discover_all_servers<br/>34 tools from 2 servers]
    SETUP -->|No| CONTEXT
    DISCOVER --> CONTEXT[RuntimeContext<br/>tools + config + models]
    CONTEXT --> WORKFLOW[LangGraph Workflow<br/>langgraph_supervisor]
    WORKFLOW --> SUPERVISOR[Supervisor Agent<br/>create_react_agent<br/>synthesis model @ 0.7<br/>tags: supervisor]

    SUPERVISOR -->|transfer_to_genie<br/>handoff tool| GENIE[Genie Subagent<br/>create_react_agent<br/>4 MCP tools<br/>recursion_limit=21<br/>tags: subagent, genie]
    SUPERVISOR -->|transfer_to_graphrag<br/>handoff tool| GRAPHRAG[GraphRAG Subagent<br/>create_react_agent<br/>18 MCP tools<br/>recursion_limit=21<br/>tags: subagent, graphrag]
    SUPERVISOR -->|No handoff| END_DIRECT([Direct answer])

    GENIE -->|Returns findings| SUPERVISOR
    GRAPHRAG -->|Returns findings| SUPERVISOR

    SUPERVISOR --> SYNTHESIS[Supervisor Synthesis<br/>output_mode=last_message]
    SYNTHESIS --> STREAM[Multi-Type Streaming<br/>Tag-filtered<br/>STATUS, SOURCES, CONTENT]
    END_DIRECT --> STREAM

    STREAM --> CLIENT([Client receives<br/>SSE stream])

    style SUPERVISOR fill:#1565c0,color:#fff,stroke:#0d47a1,stroke-width:4px
    style GENIE fill:#00c853,color:#fff,stroke:#00a152,stroke-width:3px
    style GRAPHRAG fill:#f57c00,color:#fff,stroke:#e65100,stroke-width:3px
    style LANGGRAPH_SVC fill:#1976d2,color:#fff,stroke:#0d47a1,stroke-width:3px
    style SYNTHESIS fill:#5e35b1,color:#fff,stroke:#4527a0,stroke-width:3px
```

**Architecture Flow:**

1. **Lazy Setup**: Tools discovered from MCP servers on first request, then cached
2. **Dynamic Context Generation**: Fetches live graph statistics/schema once at supervisor creation
3. **Supervisor Routes**: LLM analyzes query using `langgraph_supervisor` library with automatic handoff tools
4. **Subagent Iterates**: Each subagent loops with specialized tool set (`create_react_agent`, recursion_limit controls max iterations)
5. **Supervisor Synthesizes**: Receives findings and creates final response for emergency personnel
6. **Multi-Type Streaming**: Real-time status, reasoning, sources, and content updates
7. **Tag-Based Filtering**: Only supervisor content streamed (subagent intermediate steps filtered by tags)
8. **Context Injection**: RuntimeContext injected at invoke time (parameterless pattern, LangGraph best practice)

### Key Features

✅ **Multi-Agent Supervisor Pattern**: Using `langgraph_supervisor` library
  - **Supervisor Agent**: `create_react_agent` with automatic handoff tools (tags: supervisor)
  - **Genie Subagent**: `create_react_agent` with 4 MCP tools, recursion_limit=21 (tags: subagent, genie)
  - **GraphRAG Subagent**: `create_react_agent` with 18 MCP tools, recursion_limit=21 (tags: subagent, graphrag)
✅ **LangGraph-Native**: All agents use `create_react_agent`, official `langgraph_supervisor` library
✅ **Automatic Tool Discovery**: 34 tools from 2 MCP servers (Genie + GraphRAG)
✅ **Tag-Based Filtering**: Subagent content filtered, only supervisor synthesis streamed
✅ **Multi-Type Streaming**: STATUS, REASONING, SOURCES, CONTENT message types
✅ **Parameterless Workflows**: LangGraph Studio-compatible pattern
✅ **Official MCP Integration**: Uses `langchain-mcp-adapters` library
✅ **Session Context**: Conversation history maintained across requests
✅ **Phoenix Observability**: Comprehensive LLM tracing and performance monitoring

### MCP Server Integration

**Genie MCP Server** (Azure-hosted):
- `get_genie_spaces` - List available emergency management spaces
- `ask_genie` - Query emergency incident data (Hurricane Milton, Helene, etc.)
- `follow_up` - Follow-up questions and clarifications
- `get_genie_space_metadata` - Space information

**GraphRAG MCP Server** (Local Docker):
- `get_schema` - Knowledge graph schema (4,581 nodes, 6 entity types)
- `describe_entity_type` - Entity type details
- `search_entities` - Entity search
- `get_neighbors` - Relationship traversal
- `execute_cypher` - Custom graph queries
- ... 22 more graph operations

### Multi-Type Streaming

The API streams responses in real-time using Server-Sent Events (SSE) with four message types:

```javascript
// STATUS - Processing updates
{"type": "status", "data": {"message": "Processing your request..."}}

// REASONING - Agent decision-making transparency
{"type": "reasoning", "data": {"step": "Agent has access to 31 tools"}}
{"type": "reasoning", "data": {"step": "LLM requested 1 tool(s): get_genie_spaces"}}

// SOURCES - Tool attribution
{"type": "sources", "data": {"source": "get_genie_spaces"}}

// CONTENT - Response content (streamed)
{"type": "content", "data": {"chunk": "The available Genie spaces are..."}}
```

**Design Decision**: Structured objects (not raw strings) for future extensibility, type safety, and rich metadata support.

---

## Project Structure

```
backend/
├── src/                           # Source code root
│   ├── app/
│   │   ├── main.py               # FastAPI application entry point
│   │   ├── core/
│   │   │   ├── config.py         # Settings with Pydantic BaseSettings
│   │   │   └── logging.py        # Structured logging with Phoenix
│   │   ├── api/
│   │   │   └── api_v1/
│   │   │       ├── api.py        # Router aggregation
│   │   │       └── endpoints/
│   │   │           └── chat.py   # Streaming chat endpoint
│   │   ├── agent/
│   │   │   └── langgraph/
│   │   │       ├── workflows.py  # Parameterless LangGraph workflows
│   │   │       ├── context.py    # RuntimeContext for dependency injection
│   │   │       └── state.py      # AgentState definition
│   │   ├── mcp/
│   │   │   ├── adapter.py        # MCPToolAdapter (official library wrapper)
│   │   │   └── models.py         # MCP server configuration models
│   │   └── services/
│   │       ├── langgraph_service.py  # Service layer with lazy loading
│   │       ├── streaming.py          # Multi-type streaming utilities
│   │       └── context.py            # Conversation context manager
│   ├── scripts/
│   │   ├── test_tool_discovery.py       # Test MCP tool discovery
│   │   ├── test_workflow.py             # Test workflow execution
│   │   └── test_service_integration.py  # End-to-end service tests
│   ├── langgraph.json            # LangGraph Studio configuration
│   ├── pyproject.toml            # uv dependency management
│   └── .env                      # Environment configuration
├── dev_status/                   # Development documentation
│   ├── langgraph_refactoring_completed.md  # Refactoring summary
│   ├── phase4_test_results.md              # Integration test results
│   └── phase5_cleanup_plan.md              # Cleanup documentation
├── docker-compose.yml            # Docker development setup
├── Dockerfile                    # Multi-stage Docker build
└── README.md                     # This file
```

---

## Technology Stack

- **FastAPI** - Async Python web framework
- **LangGraph** - Agent orchestration and workflow management
- **langchain-mcp-adapters** - Official MCP integration library
- **Azure OpenAI** - LLM provider (gpt-4.1-mini, gpt-35-turbo)
- **MCP Protocol** - Model Context Protocol for tool integration
- **Phoenix** - LLM observability and tracing (optional)
- **Python 3.10+** - Required for MCP package compatibility
- **uv** - Fast Python package manager

---

## Environment Configuration

Key environment variables (see `src/.env.example` for complete list):

```bash
# Azure OpenAI Configuration
OPENAI_API_KEY=your_key_here
OPENAI_ENDPOINT=https://your-instance.openai.azure.com
MODEL_DEPLOYMENT_NAME=gpt-4.1-mini

# MCP Server Configuration
GENIE_MCP_ENABLED=true
GENIE_MCP_URL=https://genai-graysky-dev-mcp.azurewebsites.net
GENIE_SPACE=your_genie_space_id

GRAPHRAG_MCP_ENABLED=true
GRAPHRAG_MCP_URL=http://localhost:8001/mcp

# LangGraph Configuration
LANGGRAPH_CHECKPOINTER=memory  # Options: memory, postgres

# Phoenix Observability (Optional)
PHOENIX_ENABLED=true
PHOENIX_ENDPOINT=http://127.0.0.1:6006
```

**Note**: No feature flags - LangGraph is the only orchestration method.

---

## API Reference

### Streaming Chat Endpoint

```http
POST /api/v1/chat/stream
Content-Type: application/json

{
  "message": "What are the current emergency incidents?",
  "conversation_id": "optional-conversation-id"
}
```

**Response**: Server-Sent Events (SSE) stream

```
data: {"type":"status","data":{"message":"Processing your request..."}}
data: {"type":"status","data":{"message":"Analyzing your request..."}}
data: {"type":"reasoning","data":{"step":"Agent has access to 31 tools"}}
data: {"type":"reasoning","data":{"step":"LLM requested 1 tool(s): ask_genie"}}
data: {"type":"sources","data":{"source":"ask_genie"}}
data: {"type":"content","data":{"chunk":"Based on the emergency data..."}}
data: {"type":"status","data":{"message":"Response complete"}}
event: done
data:

```

### Health Check

```http
GET /health

Response:
{
  "status": "healthy",
  "version": "0.1.0"
}
```

---

## Development

### Running Tests

```bash
# Test tool discovery
uv run python scripts/test_tool_discovery.py

# Test workflow execution
uv run python scripts/test_workflow.py

# Test service integration
uv run python scripts/test_service_integration.py
```

### Docker Development

```bash
# From backend root (/backend/, not /backend/src/)
docker-compose up -d

# View logs
docker-compose logs -f backend

# Stop services
docker-compose down
```

### Phoenix Observability (Optional)

```bash
# Start Phoenix server
docker run -p 6006:6006 -p 4317:4317 arizephoenix/phoenix:latest

# Open Phoenix UI
open http://localhost:6006

# Backend automatically sends traces when PHOENIX_ENABLED=true
```

---

## Deployment

### Docker Production Build

```bash
# Build production image
docker build --target production -t backend:prod .

# Run production container
docker run -p 8000:8000 --env-file src/.env backend:prod
```

### Manual Production Deployment

```bash
cd src
uv sync --no-dev
uv run uvicorn app.main:app --host 0.0.0.0 --port 8000
```

---

## Architecture Decisions

### Why LangGraph?
- **Native patterns**: Parameterless workflows with context injection
- **Studio support**: Compatible with LangGraph Studio debugging (pattern-level)
- **Official library**: Uses `langchain-mcp-adapters` for MCP integration
- **Extensibility**: Easy to add new tools, servers, or workflow nodes

### Why Official MCP Library?
- Eliminated 200+ lines of custom tool conversion code
- Automatic JSON schema handling
- Built-in error handling
- Future compatibility with MCP protocol updates

### Why Lazy Loading?
- Faster application startup
- Resources created only when needed
- Tools discovered on first request, then cached
- Better testability and separation of concerns

### Why LLM-Driven Tool Selection?
- Better intent understanding than hardcoded classifiers
- Handles complex queries requiring multiple tools
- No maintenance of routing rules
- Demonstrated in parallel tool calling (Phase 4 tests)

---

## Performance

**Measured response times** (Phase 4 testing):
- Simple query (no tools): ~2 seconds
- Single tool (Genie): ~5 seconds
- Single tool (GraphRAG): ~3 seconds
- Parallel tools (both servers): ~8 seconds

**Tool discovery**: 31 tools discovered in ~6-7 seconds on first request, then cached.

---

## Troubleshooting

### Common Issues and Solutions

#### 1. CORS Configuration Error on Startup
**Error**: `pydantic_settings.exceptions.SettingsError: error parsing value for field "backend_cors_origins"`

**Cause**: The `BACKEND_CORS_ORIGINS` field in `.env` expects either a JSON array or comma-separated list format.

**Solution**: Update your `.env` file to use one of the supported formats:
```bash
# Format 1: JSON array (recommended)
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://127.0.0.1:3000"]

# Format 2: Comma-separated list (alternative)
BACKEND_CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
```

#### 2. Extra Environment Variables Error
**Error**: `Extra inputs are not permitted` for LOG_* variables

**Cause**: Pydantic v2 with `pydantic_settings` requires explicit handling of environment variables that don't map to Settings class fields.

**Solution**: This has been fixed in `app/core/config.py` with `extra="ignore"` in the model_config. The LOG_* variables are processed separately and safely ignored.

#### 3. Backend Container Won't Start After Fresh Clone
**Issue**: After cloning or pulling fresh from repository, backend fails to start.

**Solution**:
1. Ensure `.env` file exists in `backend/src/` (copy from `.env.example` if needed)
2. Verify CORS format is correct (see issue #1 above)
3. Rebuild the container without cache:
   ```bash
   docker-compose down backend
   docker-compose build backend --no-cache
   docker-compose up -d backend
   ```

#### 4. Port Already in Use
**Error**: `bind: address already in use`

**Solution**:
- Check what's using port 8000: `lsof -i :8000`
- Kill the process or change the port in `.env`
- Restart the backend

#### 5. Module Import Errors
**Error**: `ModuleNotFoundError` when running scripts

**Solution**: Always run from the `src/` directory and use `uv run`:
```bash
cd backend/src
uv sync  # Ensures all dependencies are installed
uv run python scripts/test_tool_discovery.py
```

---

## Known Limitations

### LangGraph Studio Compatibility
- **Pattern**: Our workflows use the parameterless pattern (Studio-compatible)
- **Execution**: Studio cannot execute due to async tool discovery and complex RuntimeContext
- **Decision**: Retained production-optimized architecture over Studio debugging compatibility
- **Details**: See `dev_status/langgraph_studio_compatibility.md`

### Session Context
- **Current**: In-memory conversation history (MVP)
- **Future**: Cross-session persistence with Redis/database backend
- **Impact**: Context lost on server restart (acceptable for current use case)

---

## Future Enhancements

Documented in `dev_status/phase4_test_results.md`:

1. **Performance optimization** - Parallel tool execution
2. **Error handling** - Retry logic for MCP server failures
3. **Tool caching** - Cache tool schemas with TTL/invalidation
4. **Metrics dashboard** - Track tool usage, latency, success rates

---

## Documentation

- **[Backend API Documentation](src/README.md)** - Detailed API reference and architecture
- **[Refactoring Summary](dev_status/langgraph_refactoring_completed.md)** - POC → Production transformation
- **[Test Results](dev_status/phase4_test_results.md)** - Integration testing and performance
- **[Studio Compatibility](dev_status/langgraph_studio_compatibility.md)** - Architectural decision

---

## Design Principles

1. **Configuration Over Hardcoding** - All behavior configurable via environment variables
2. **Structured Data Format** - Future-proof API with extensible message formats
3. **Multi-Type Streaming** - Real-time transparency in AI decision-making
4. **Separation of Concerns** - Clear layers: API → Service → Workflow → Tools
5. **Official Libraries** - Use `langchain-mcp-adapters` instead of custom code
6. **Lazy Loading** - Initialize resources only when needed
7. **LLM-Driven Decisions** - Let the LLM select tools, not hardcoded rules
8. **Production-First** - Async architecture, structured logging, observability
9. **Comprehensive Documentation** - Detailed documentation for all components
10. **Testability** - Clean interfaces, easy mocking, comprehensive test scripts

---

## Contributing

When contributing:
1. Follow LangGraph-native patterns (context injection, parameterless workflows)
2. Use structured logging with `app.core.logging.get_logger(__name__)`
3. Add test scripts to `src/scripts/` for new features
4. Update relevant documentation in `dev_status/`
5. Ensure multi-type streaming conventions are maintained
6. Test with both MCP servers (Genie + GraphRAG)

---

**Version**: 0.1.0
**Status**: Production-ready
**Last Updated**: 2025-10-07

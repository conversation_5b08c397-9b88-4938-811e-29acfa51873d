"""
Structured logging configuration using structlog for production-ready observability.

This module provides:
- JSON structured logging with correlation ID propagation
- Performance tracking with timing context managers
- Sensitive data scrubbing with configurable patterns
- Environment-specific formatting (JSON for prod, text for dev)
- Azure Monitor integration ready
"""

import logging
import logging.config
import re
import time
import uuid
from contextvars import ContextV<PERSON>
from typing import Any, Dict, List, Optional, Union

import structlog
from pydantic import BaseModel, Field

# Context variable for correlation ID (async-safe)
correlation_id: ContextVar[Optional[str]] = ContextVar('correlation_id', default=None)


class LoggingSettings(BaseModel):
    """Logging configuration settings with environment variable support."""
    
    level: str = Field(
        default="INFO", 
        description="Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)",
        json_schema_extra={"env": "LOG_LEVEL"}
    )
    format: str = Field(
        default="json", 
        description="Log format (json, text)",
        json_schema_extra={"env": "LOG_FORMAT"}
    )
    sensitive_data_scrubbing: bool = Field(
        default=True,
        description="Enable automatic sensitive data scrubbing",
        json_schema_extra={"env": "LOG_SCRUB_SENSITIVE"}
    )
    correlation_header: str = Field(
        default="X-Correlation-ID",
        description="HTTP header name for correlation ID",
        json_schema_extra={"env": "LOG_CORRELATION_HEADER"}
    )


class SensitiveDataFilter:
    """Filter to automatically scrub sensitive information from logs."""
    
    # Patterns for sensitive data detection
    SENSITIVE_PATTERNS = [
        # API Keys and tokens
        (r'(?i)(api[_-]?key|token|secret|password)\s*[:=]\s*[\'"]?([a-zA-Z0-9_\-]{8,})[\'"]?', 
         r'\1: [REDACTED]'),
        # Email addresses
        (r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', 
         '[EMAIL_REDACTED]'),
        # Phone numbers (US format)
        (r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b', 
         '[PHONE_REDACTED]'),
        # Credit card numbers (basic pattern)
        (r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b', 
         '[CARD_REDACTED]'),
        # SSN pattern
        (r'\b\d{3}[-]?\d{2}[-]?\d{4}\b', 
         '[SSN_REDACTED]'),
    ]
    
    def __init__(self, enabled: bool = True):
        self.enabled = enabled
        self.compiled_patterns = [
            (re.compile(pattern), replacement) 
            for pattern, replacement in self.SENSITIVE_PATTERNS
        ]
    
    def scrub(self, text: str) -> str:
        """Scrub sensitive data from text."""
        if not self.enabled or not isinstance(text, str):
            return text
        
        result = text
        for pattern, replacement in self.compiled_patterns:
            result = pattern.sub(replacement, result)
        
        return result


class PerformanceContext:
    """Context manager for tracking operation performance."""
    
    def __init__(self, logger: structlog.BoundLogger, operation: str, **extra_context):
        self.logger = logger
        self.operation = operation
        self.extra_context = extra_context
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        self.logger.info("Operation started", 
                        operation=self.operation, 
                        **self.extra_context)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = time.time() - self.start_time
        
        if exc_type is None:
            self.logger.info("Operation completed successfully",
                           operation=self.operation,
                           duration_seconds=round(duration, 3),
                           **self.extra_context)
        else:
            self.logger.error("Operation failed",
                            operation=self.operation,
                            duration_seconds=round(duration, 3),
                            error_type=exc_type.__name__,
                            error_message=str(exc_val),
                            **self.extra_context)


def add_correlation_id(logger, method_name, event_dict):
    """Add correlation ID to log events."""
    corr_id = correlation_id.get()
    if corr_id:
        event_dict['correlation_id'] = corr_id
    return event_dict


def scrub_sensitive_data(logger, method_name, event_dict):
    """Scrub sensitive data from log events."""
    scrubber = getattr(logger, '_sensitive_scrubber', None)
    if scrubber and scrubber.enabled:
        # Scrub the main event message
        if 'event' in event_dict:
            event_dict['event'] = scrubber.scrub(str(event_dict['event']))
        
        # Scrub other string values
        for key, value in event_dict.items():
            if isinstance(value, str) and key != 'correlation_id':
                event_dict[key] = scrubber.scrub(value)
    
    return event_dict


def setup_logging(settings: LoggingSettings) -> None:
    """Configure structured logging with the provided settings."""
    
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=None,
        level=getattr(logging, settings.level.upper()),
    )
    
    # Create sensitive data filter
    sensitive_filter = SensitiveDataFilter(enabled=settings.sensitive_data_scrubbing)
    
    # Configure processors based on format
    processors = [
        structlog.contextvars.merge_contextvars,
        add_correlation_id,
        scrub_sensitive_data,
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.add_log_level,
        structlog.processors.StackInfoRenderer(),
    ]
    
    if settings.format == "json":
        processors.extend([
            structlog.processors.dict_tracebacks,
            structlog.processors.JSONRenderer()
        ])
    else:
        processors.extend([
            structlog.processors.dict_tracebacks,
            structlog.dev.ConsoleRenderer(colors=True)
        ])
    
    # Configure structlog
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.make_filtering_bound_logger(
            getattr(logging, settings.level.upper())
        ),
        logger_factory=structlog.stdlib.LoggerFactory(),
        cache_logger_on_first_use=True,
    )
    
    # Store sensitive filter for access by processors
    logger = structlog.get_logger()
    logger._sensitive_scrubber = sensitive_filter


def get_logger(name: str = None) -> structlog.BoundLogger:
    """Get a structured logger instance."""
    return structlog.get_logger(name)


def set_correlation_id(corr_id: str = None) -> str:
    """Set correlation ID for current context. Returns the correlation ID."""
    if corr_id is None:
        corr_id = str(uuid.uuid4())
    correlation_id.set(corr_id)
    return corr_id


def get_correlation_id() -> Optional[str]:
    """Get current correlation ID."""
    return correlation_id.get()


def performance_context(logger: structlog.BoundLogger, operation: str, **extra_context):
    """Create a performance tracking context manager."""
    return PerformanceContext(logger, operation, **extra_context)


# Component-specific logger instances
def get_foundation_model_logger() -> structlog.BoundLogger:
    """Get logger for Foundation Model service."""
    return get_logger("foundation_model")


def get_mcp_logger() -> structlog.BoundLogger:
    """Get logger for MCP service."""
    return get_logger("mcp_service")


def get_agent_logger() -> structlog.BoundLogger:
    """Get logger for Agent service."""
    return get_logger("agent")


def get_api_logger() -> structlog.BoundLogger:
    """Get logger for API endpoints."""
    return get_logger("api")
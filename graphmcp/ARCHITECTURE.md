# GraphRAG MCP Server - Technical Architecture Documentation

This document provides comprehensive technical documentation for the GraphRAG MCP Server architecture, implementation details, and tool specifications.

## Table of Contents

- [System Architecture](#system-architecture)
  - [Component Overview](#component-overview)
  - [Protocol Flow](#protocol-flow)
  - [Transport Layers](#transport-layers)
  - [Database Architecture](#database-architecture)
- [Tool Architecture](#tool-architecture)
  - [Schema Discovery Tools](#schema-discovery-tools)
  - [Entity Search Tools](#entity-search-tools)
  - [Graph Traversal Tools](#graph-traversal-tools)
  - [Aggregation & Analytics Tools](#aggregation--analytics-tools)
  - [Administrative Tools](#administrative-tools)
- [Implementation Details](#implementation-details)
  - [Caching Strategy](#caching-strategy)
  - [Query Optimization](#query-optimization)
  - [Response Sanitization](#response-sanitization)
  - [Error Handling](#error-handling)
  - [Performance Monitoring](#performance-monitoring)
- [Database Schema](#database-schema)
  - [Entity Models](#entity-models)
  - [Relationship Models](#relationship-models)
  - [Index Strategy](#index-strategy)
- [Advanced Patterns](#advanced-patterns)
  - [Complex Query Patterns](#complex-query-patterns)
  - [Temporal Analysis](#temporal-analysis)
  - [Graph Algorithms](#graph-algorithms)

## System Architecture

### Component Overview

The GraphRAG MCP Server implements a layered architecture with clear separation of concerns:

```mermaid
graph TB
    subgraph "Client Layer"
        LLM[LLM Application]
        MCP_Client[MCP Client]
        API_Client[API Client]
    end

    subgraph "GraphRAG MCP Server"
        Transport{Transport Layer}
        MCP_Server[FastMCP Server]
        Tools[Tool Registry]
        Schema_Manager[Schema Manager]
        Graph_Client[Graph Client]
        Cache[(Schema Cache)]
    end

    subgraph "Database Layer"
        Memgraph[(Memgraph Database)]
        Cypher[Cypher Query Engine]
    end

    subgraph "Data Model"
        Incidents[Incidents]
        Resources[Resources]
        Departments[Departments]
        Locations[Locations]
    end

    LLM --> MCP_Client
    API_Client --> Transport
    MCP_Client --> Transport

    Transport -->|stdio| MCP_Server
    Transport -->|HTTP/8000| MCP_Server

    MCP_Server --> Tools
    Tools --> Schema_Manager
    Tools --> Graph_Client

    Schema_Manager --> Cache
    Schema_Manager --> Graph_Client

    Graph_Client --> Memgraph
    Memgraph --> Cypher
    Cypher --> Incidents
    Cypher --> Resources
    Cypher --> Departments
    Cypher --> Locations

    style MCP_Server fill:#0066cc,color:#fff
    style Memgraph fill:#00cc66,color:#fff
    style Cache fill:#ffcc00,color:#000
    style Transport fill:#ff6666,color:#fff
```

#### Component Responsibilities

1. **Transport Layer** (`src/runner.py`)
   - Handles stdio and HTTP transport modes
   - Manages connection lifecycle
   - Routes messages between client and server

2. **FastMCP Server** (`src/server.py`)
   - Implements MCP protocol specification
   - Manages tool registration and dispatch
   - Handles request/response serialization

3. **Tool Registry** (`src/tools/`)
   - Organizes tools by functionality category
   - Validates tool parameters
   - Executes tool logic

4. **Schema Manager** (`src/graph/schema_manager.py`)
   - Caches graph schema metadata
   - Provides schema discovery operations
   - Manages cache invalidation

5. **Graph Client** (`src/graph/client.py`)
   - Manages Memgraph connections
   - Executes Cypher queries
   - Handles query parameterization

### Protocol Flow

```mermaid
sequenceDiagram
    participant Client as MCP Client
    participant Transport as Transport Layer
    participant Server as FastMCP Server
    participant Tools as Tool Registry
    participant Schema as Schema Manager
    participant Graph as Graph Client
    participant Memgraph as Memgraph DB
    participant Cache as Schema Cache

    Client->>Transport: Initialize Connection
    Transport->>Server: Setup Server
    Server->>Graph: Create Connection
    Graph->>Memgraph: Connect via Bolt
    Memgraph-->>Graph: Connection Established
    Server->>Schema: Initialize Schema Manager
    Schema->>Cache: Load Initial Schema
    Server->>Tools: Register All Tools
    Server-->>Client: Tools Available

    Note over Client,Memgraph: Tool Execution Flow

    Client->>Server: Call Tool Request
    Server->>Tools: Dispatch to Tool
    Tools->>Tools: Validate Parameters

    alt Schema Query
        Tools->>Schema: Request Schema Data
        Schema->>Cache: Check Cache
        Cache-->>Schema: Return Cached Data
        Schema-->>Tools: Return Schema
    else Data Query
        Tools->>Graph: Build Cypher Query
        Graph->>Memgraph: Execute Query
        Memgraph-->>Graph: Query Results
        Graph-->>Tools: Process Results
    end

    Tools->>Server: Format Response
    Server-->>Client: Return Tool Result
```

### Transport Layers

#### Stdio Transport (Default)

Used for local development and direct MCP client integration:

```python
# Configuration
MCP_TRANSPORT=stdio

# Message Flow
stdin  -> JSON-RPC Request  -> FastMCP Server
stdout <- JSON-RPC Response <- FastMCP Server
stderr <- Logging/Errors    <- Application Layer
```

#### HTTP/SSE Transport

Used for network access and API integration:

```python
# Configuration
MCP_TRANSPORT=streamable-http
HTTP_PORT=8000
HOST=0.0.0.0

# Endpoints
GET  /health          -> Health check endpoint
POST /mcp/initialize  -> Initialize connection
POST /mcp/tools/list  -> List available tools
POST /mcp/tools/call  -> Execute tool
GET  /mcp/sse         -> Server-sent events stream
```

### Database Architecture

#### Connection Management

```mermaid
graph LR
    subgraph "Connection Pool"
        C1[Connection 1]
        C2[Connection 2]
        C3[Connection 3]
        CN[Connection N]
    end

    subgraph "Query Pipeline"
        QV[Query Validator]
        QP[Query Parameterizer]
        QE[Query Executor]
        RP[Result Processor]
    end

    Request[Query Request] --> QV
    QV --> QP
    QP --> QE
    QE --> C1
    C1 --> Memgraph[(Memgraph)]
    Memgraph --> RP
    RP --> Response[Query Response]
```

#### Transaction Management

```python
# Transaction flow for complex operations
async def complex_operation():
    tx = await graph_client.begin_transaction()
    try:
        # Multiple query operations
        await tx.execute(query1, params1)
        await tx.execute(query2, params2)
        await tx.commit()
    except Exception as e:
        await tx.rollback()
        raise
```

## Tool Architecture

### Schema Discovery Tools

#### 1. get_schema()

Retrieves complete graph schema including all entity types, relationships, and statistics.

**Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Client
    participant Tool as get_schema
    participant Cache as Schema Cache
    participant Manager as Schema Manager
    participant DB as Memgraph

    Client->>Tool: get_schema()
    Tool->>Manager: Request full schema
    Manager->>Cache: Check cache validity

    alt Cache Valid
        Cache-->>Manager: Return cached schema
    else Cache Invalid
        Manager->>DB: CALL mg.schema()
        DB-->>Manager: Raw schema data
        Manager->>DB: MATCH (n) RETURN DISTINCT labels(n)
        DB-->>Manager: Node labels
        Manager->>DB: MATCH ()-[r]->() RETURN DISTINCT type(r)
        DB-->>Manager: Relationship types
        Manager->>DB: Query property statistics
        DB-->>Manager: Property data
        Manager->>Cache: Update cache with TTL
    end

    Manager->>Tool: Process schema data
    Tool-->>Client: Complete schema with statistics
```

**Flow Diagram:**
```mermaid
graph TB
    Start[get_schema called] --> CheckCache{Cache Valid?}

    CheckCache -->|Yes| ReturnCache[Return Cached Schema]
    CheckCache -->|No| QuerySchema[Query mg.schema()]

    QuerySchema --> GetNodes[Get Node Types]
    GetNodes --> GetRels[Get Relationship Types]
    GetRels --> GetProps[Get Properties]

    GetProps --> BuildSchema[Build Schema Object]
    BuildSchema --> UpdateCache[Update Cache]
    UpdateCache --> FormatResponse[Format Response]

    ReturnCache --> FormatResponse
    FormatResponse --> Return[Return to Client]

    style Start fill:#90EE90,stroke:#333,stroke-width:2px
    style Return fill:#FFB6C1,stroke:#333,stroke-width:2px
```

#### 2. describe_entity_type(entity_type)

Gets detailed information about a specific entity type.

**Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Client
    participant Tool as describe_entity_type
    participant Validator
    participant Cache
    participant DB as Memgraph

    Client->>Tool: describe_entity_type("Incident")
    Tool->>Validator: Validate entity_type exists

    alt Type exists in cache
        Validator->>Cache: Get type metadata
        Cache-->>Tool: Return cached metadata
    else Type not cached
        Validator->>DB: MATCH (n:Incident) RETURN n LIMIT 10
        DB-->>Validator: Sample nodes
        Validator->>DB: CALL db.propertyKeys()
        DB-->>Validator: Property keys
        Validator->>DB: SHOW CONSTRAINT INFO
        DB-->>Validator: Constraints
    end

    Tool->>Tool: Analyze properties
    Tool->>Tool: Detect data types
    Tool->>Tool: Find constraints
    Tool-->>Client: Entity type details
```

**Flow Diagram:**
```mermaid
graph TB
    Start[describe_entity_type] --> Validate{Valid Type?}

    Validate -->|No| Error[Return Error]
    Validate -->|Yes| GetMeta[Get Metadata]

    GetMeta --> QuerySample[Query Sample Nodes]
    QuerySample --> AnalyzeProps[Analyze Properties]
    AnalyzeProps --> DetectTypes[Detect Data Types]
    DetectTypes --> FindConstraints[Find Constraints]

    FindConstraints --> BuildDetails[Build Entity Details]
    BuildDetails --> Return[Return Details]

    style Start fill:#90EE90,stroke:#333,stroke-width:2px
    style Error fill:#FF6B6B,stroke:#333,stroke-width:2px
    style Return fill:#FFB6C1,stroke:#333,stroke-width:2px
```

#### 3. describe_relationship_type(relationship_type)

Gets detailed information about a relationship type.

**Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Client
    participant Tool as describe_relationship_type
    participant Cache
    participant DB as Memgraph

    Client->>Tool: describe_relationship_type("RESPONDED_TO")
    Tool->>Cache: Check relationship metadata

    alt Metadata cached
        Cache-->>Tool: Return cached data
    else Not cached
        Tool->>DB: MATCH ()-[r:RESPONDED_TO]->() RETURN r LIMIT 10
        DB-->>Tool: Sample relationships
        Tool->>DB: MATCH (a)-[r:RESPONDED_TO]->(b)
        Note right of Tool: RETURN labels(a), labels(b), count(*)
        DB-->>Tool: Source/target statistics
    end

    Tool->>Tool: Analyze properties
    Tool->>Tool: Calculate statistics
    Tool-->>Client: Relationship details
```

**Flow Diagram:**
```mermaid
graph TB
    Start[describe_relationship_type] --> CheckCache{In Cache?}

    CheckCache -->|Yes| UseCached[Use Cached Data]
    CheckCache -->|No| QuerySamples[Query Sample Relationships]

    QuerySamples --> GetSourceTarget[Get Source/Target Types]
    GetSourceTarget --> AnalyzeProps[Analyze Properties]
    AnalyzeProps --> CalcStats[Calculate Statistics]

    UseCached --> Format[Format Response]
    CalcStats --> Format
    Format --> Return[Return Details]

    style Start fill:#90EE90,stroke:#333,stroke-width:2px
    style Return fill:#FFB6C1,stroke:#333,stroke-width:2px
```

#### 4. get_node_statistics()

Gets statistical summary of all node types.

**Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Client
    participant Tool as get_node_statistics
    participant DB as Memgraph
    participant Processor

    Client->>Tool: get_node_statistics()

    Tool->>DB: MATCH (n) RETURN labels(n), count(*) as count
    DB-->>Tool: Node counts by label

    Tool->>DB: MATCH (n) UNWIND keys(n) as key
    Note right of Tool: RETURN labels(n), key, count(*)
    DB-->>Tool: Property statistics

    Tool->>Processor: Process statistics
    Processor->>Processor: Calculate averages
    Processor->>Processor: Find min/max values
    Processor->>Processor: Detect patterns

    Processor-->>Tool: Formatted statistics
    Tool-->>Client: Complete statistics
```

**Flow Diagram:**
```mermaid
graph TB
    Start[get_node_statistics] --> QueryCounts[Query Node Counts]

    QueryCounts --> QueryProps[Query Property Statistics]
    QueryProps --> CalcAvg[Calculate Averages]
    CalcAvg --> FindMinMax[Find Min/Max Values]
    FindMinMax --> DetectPatterns[Detect Data Patterns]

    DetectPatterns --> BuildReport[Build Statistics Report]
    BuildReport --> Return[Return Statistics]

    style Start fill:#90EE90,stroke:#333,stroke-width:2px
    style Return fill:#FFB6C1,stroke:#333,stroke-width:2px
```

#### 5. get_relationship_statistics()

Gets statistical summary of all relationship types.

**Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Client
    participant Tool as get_relationship_statistics
    participant DB as Memgraph

    Client->>Tool: get_relationship_statistics()

    Tool->>DB: MATCH ()-[r]->() RETURN type(r), count(*)
    DB-->>Tool: Relationship counts

    Tool->>DB: MATCH (a)-[r]->(b)
    Note right of Tool: RETURN type(r), labels(a), labels(b), count(*)
    DB-->>Tool: Source/target distributions

    Tool->>Tool: Calculate degree statistics
    Tool->>Tool: Identify hub nodes
    Tool-->>Client: Relationship statistics
```

**Flow Diagram:**
```mermaid
graph TB
    Start[get_relationship_statistics] --> QueryCounts[Query Relationship Counts]

    QueryCounts --> QueryDist[Query Source/Target Distribution]
    QueryDist --> CalcDegree[Calculate Degree Stats]
    CalcDegree --> FindHubs[Identify Hub Nodes]
    FindHubs --> BuildReport[Build Statistics Report]

    BuildReport --> Return[Return Statistics]

    style Start fill:#90EE90,stroke:#333,stroke-width:2px
    style Return fill:#FFB6C1,stroke:#333,stroke-width:2px
```

### Entity Search Tools

#### 1. search_entities(entity_type, properties, contains_text, limit)

Advanced entity search with multiple filter options.

**Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Client
    participant Tool as search_entities
    participant QueryBuilder
    participant Validator
    participant DB as Memgraph

    Client->>Tool: search_entities(params)
    Tool->>Validator: Validate parameters

    Validator->>Validator: Check entity_type
    Validator->>Validator: Validate properties
    Validator->>Validator: Check limit bounds

    Tool->>QueryBuilder: Build Cypher query

    QueryBuilder->>QueryBuilder: Base: MATCH (n:EntityType)

    alt Has property filters
        QueryBuilder->>QueryBuilder: Add WHERE clauses
        Note right of QueryBuilder: Handle ranges, exact matches
    end

    alt Has text search
        QueryBuilder->>QueryBuilder: Add text filter
        Note right of QueryBuilder: Search across all properties
    end

    QueryBuilder->>QueryBuilder: Add ORDER BY
    QueryBuilder->>QueryBuilder: Add LIMIT

    QueryBuilder-->>Tool: Complete query

    Tool->>DB: Execute query
    DB-->>Tool: Query results

    Tool->>Tool: Format results
    Tool-->>Client: Formatted entities
```

**Flow Diagram:**
```mermaid
graph TB
    Start[search_entities] --> Validate[Validate Parameters]

    Validate --> BuildBase[Build Base Query]
    BuildBase --> CheckFilters{Has Filters?}

    CheckFilters -->|Yes| AddWhere[Add WHERE Clauses]
    CheckFilters -->|No| CheckText{Has Text Search?}

    AddWhere --> CheckText
    CheckText -->|Yes| AddTextFilter[Add Text Filter]
    CheckText -->|No| AddOrder[Add ORDER BY]

    AddTextFilter --> AddOrder
    AddOrder --> AddLimit[Add LIMIT]
    AddLimit --> Execute[Execute Query]

    Execute --> ProcessResults[Process Results]
    ProcessResults --> Return[Return Entities]

    style Start fill:#90EE90,stroke:#333,stroke-width:2px
    style Return fill:#FFB6C1,stroke:#333,stroke-width:2px
```

#### 2. get_entity(entity_type, entity_id, include_relationships)

Retrieves a specific entity with optional relationship data.

**Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Client
    participant Tool as get_entity
    participant DB as Memgraph
    participant RelProcessor as Relationship Processor

    Client->>Tool: get_entity(type, id, include_rels)

    Tool->>DB: MATCH (n:Type {id: $id}) RETURN n
    DB-->>Tool: Node data

    alt Node not found
        Tool-->>Client: Error: Entity not found
    else Node found
        alt include_relationships = true
            Tool->>DB: MATCH (n:Type {id: $id})-[r]-(m)
            Note right of Tool: RETURN r, m, type(r), labels(m)
            DB-->>Tool: Relationships and connected nodes

            Tool->>RelProcessor: Process relationships
            RelProcessor->>RelProcessor: Group by type
            RelProcessor->>RelProcessor: Extract properties
            RelProcessor-->>Tool: Structured relationships

            Tool-->>Client: Node + Relationships
        else include_relationships = false
            Tool-->>Client: Node only
        end
    end
```

**Flow Diagram:**
```mermaid
graph TB
    Start[get_entity] --> QueryNode[Query Node by ID]

    QueryNode --> Found{Node Found?}
    Found -->|No| NotFound[Return Not Found Error]
    Found -->|Yes| CheckRels{Include Relationships?}

    CheckRels -->|No| ReturnNode[Return Node Only]
    CheckRels -->|Yes| QueryRels[Query Relationships]

    QueryRels --> ProcessRels[Process Relationships]
    ProcessRels --> GroupByType[Group by Type]
    GroupByType --> ExtractProps[Extract Properties]
    ExtractProps --> BuildResponse[Build Response]

    ReturnNode --> Return[Return Entity]
    BuildResponse --> Return

    style Start fill:#90EE90,stroke:#333,stroke-width:2px
    style NotFound fill:#FF6B6B,stroke:#333,stroke-width:2px
    style Return fill:#FFB6C1,stroke:#333,stroke-width:2px
```

#### 3. find_entities_by_time_range(entity_type, start_time, end_time, time_property)

Finds entities within a specific time window.

**Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Client
    participant Tool as find_entities_by_time_range
    participant TimeParser
    participant DB as Memgraph

    Client->>Tool: find_entities_by_time_range(params)

    Tool->>TimeParser: Parse time strings
    TimeParser->>TimeParser: Convert to datetime
    TimeParser->>TimeParser: Validate range
    TimeParser-->>Tool: Parsed times

    Tool->>Tool: Build temporal query
    Note right of Tool: WHERE n.timestamp >= $start
    Note right of Tool: AND n.timestamp <= $end

    Tool->>DB: Execute temporal query
    DB-->>Tool: Entities in range

    Tool->>Tool: Sort by timestamp
    Tool-->>Client: Time-ordered entities
```

**Flow Diagram:**
```mermaid
graph TB
    Start[find_entities_by_time_range] --> ParseTimes[Parse Time Strings]

    ParseTimes --> ValidateRange{Valid Range?}
    ValidateRange -->|No| Error[Return Error]
    ValidateRange -->|Yes| BuildQuery[Build Temporal Query]

    BuildQuery --> AddTimeFilter[Add Time Filters]
    AddTimeFilter --> Execute[Execute Query]
    Execute --> SortByTime[Sort by Timestamp]

    SortByTime --> Return[Return Entities]

    style Start fill:#90EE90,stroke:#333,stroke-width:2px
    style Error fill:#FF6B6B,stroke:#333,stroke-width:2px
    style Return fill:#FFB6C1,stroke:#333,stroke-width:2px
```

#### 4. search_by_pattern(pattern, parameters, limit)

Search using custom Cypher patterns for complex queries.

**Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Client
    participant Tool as search_by_pattern
    participant Validator
    participant SafetyCheck
    participant DB as Memgraph

    Client->>Tool: search_by_pattern(pattern, params)

    Tool->>Validator: Validate pattern syntax
    Validator->>SafetyCheck: Check for unsafe operations

    SafetyCheck->>SafetyCheck: Block DELETE/DROP
    SafetyCheck->>SafetyCheck: Check for writes
    SafetyCheck->>SafetyCheck: Validate parameters

    alt Pattern unsafe
        SafetyCheck-->>Client: Error: Unsafe pattern
    else Pattern safe
        Tool->>DB: Execute pattern query
        DB-->>Tool: Query results
        Tool-->>Client: Pattern match results
    end
```

**Flow Diagram:**
```mermaid
graph TB
    Start[search_by_pattern] --> ValidateSyntax[Validate Pattern Syntax]

    ValidateSyntax --> SafetyCheck{Pattern Safe?}
    SafetyCheck -->|No| Reject[Reject Unsafe Pattern]
    SafetyCheck -->|Yes| PrepareQuery[Prepare Query]

    PrepareQuery --> BindParams[Bind Parameters]
    BindParams --> Execute[Execute Pattern]
    Execute --> ProcessResults[Process Results]

    ProcessResults --> Return[Return Matches]

    style Start fill:#90EE90,stroke:#333,stroke-width:2px
    style Reject fill:#FF6B6B,stroke:#333,stroke-width:2px
    style Return fill:#FFB6C1,stroke:#333,stroke-width:2px
```

### Graph Traversal Tools

#### 1. get_neighbors(entity_type, entity_id, relationship_types, direction, depth)

Traverses the graph from a starting entity to find connected nodes.

**Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Client
    participant Tool as get_neighbors
    participant PathBuilder
    participant DB as Memgraph
    participant Dedup as Deduplicator

    Client->>Tool: get_neighbors(params)

    Tool->>PathBuilder: Build traversal pattern

    PathBuilder->>PathBuilder: Set direction arrows
    Note right of PathBuilder: -> for out, <- for in, - for both

    PathBuilder->>PathBuilder: Set depth range [1..depth]

    alt Has relationship filter
        PathBuilder->>PathBuilder: Add relationship types
    end

    PathBuilder-->>Tool: Traversal pattern

    Tool->>DB: Execute traversal
    DB-->>Tool: All paths

    Tool->>Dedup: Deduplicate nodes
    Dedup->>Dedup: Track visited
    Dedup->>Dedup: Group by distance
    Dedup-->>Tool: Unique neighbors

    Tool-->>Client: Neighbors by depth
```

**Flow Diagram:**
```mermaid
graph TB
    Start[get_neighbors] --> BuildPattern[Build Traversal Pattern]

    BuildPattern --> SetDirection[Set Direction Arrows]
    SetDirection --> SetDepth[Set Depth Range]
    SetDepth --> CheckRelFilter{Relationship Filter?}

    CheckRelFilter -->|Yes| AddRelTypes[Add Relationship Types]
    CheckRelFilter -->|No| Execute[Execute Traversal]
    AddRelTypes --> Execute

    Execute --> GetPaths[Get All Paths]
    GetPaths --> Deduplicate[Deduplicate Nodes]
    Deduplicate --> GroupByDepth[Group by Distance]

    GroupByDepth --> Return[Return Neighbors]

    style Start fill:#90EE90,stroke:#333,stroke-width:2px
    style Return fill:#FFB6C1,stroke:#333,stroke-width:2px
```

#### 2. find_paths(from_type, from_id, to_type, to_id, max_depth)

Finds all paths between two entities.

**Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Client
    participant Tool as find_paths
    participant PathFinder
    participant DB as Memgraph
    participant PathProcessor

    Client->>Tool: find_paths(from, to, max_depth)

    Tool->>PathFinder: Initialize path finding

    PathFinder->>DB: Check source exists
    PathFinder->>DB: Check target exists

    alt Both exist
        PathFinder->>DB: MATCH path = shortestPath(
        Note right of PathFinder: (a:FromType {id: $from_id})
        Note right of PathFinder: -[*..max_depth]-
        Note right of PathFinder: (b:ToType {id: $to_id}))
        DB-->>PathFinder: Shortest path

        PathFinder->>DB: MATCH path = allShortestPaths(...)
        DB-->>PathFinder: All shortest paths

        PathFinder->>PathProcessor: Process paths
        PathProcessor->>PathProcessor: Extract nodes
        PathProcessor->>PathProcessor: Extract relationships
        PathProcessor->>PathProcessor: Calculate lengths

        PathProcessor-->>Tool: Structured paths
        Tool-->>Client: Path details
    else Not found
        Tool-->>Client: No path exists
    end
```

**Flow Diagram:**
```mermaid
graph TB
    Start[find_paths] --> CheckNodes[Check Source & Target Exist]

    CheckNodes --> BothExist{Both Exist?}
    BothExist -->|No| NoPath[Return No Path]
    BothExist -->|Yes| FindShortest[Find Shortest Path]

    FindShortest --> FindAllShortest[Find All Shortest Paths]
    FindAllShortest --> CheckAlternative[Check Alternative Paths]
    CheckAlternative --> ProcessPaths[Process All Paths]

    ProcessPaths --> ExtractNodes[Extract Nodes]
    ExtractNodes --> ExtractRels[Extract Relationships]
    ExtractRels --> CalcLengths[Calculate Path Lengths]

    CalcLengths --> Return[Return Paths]

    style Start fill:#90EE90,stroke:#333,stroke-width:2px
    style NoPath fill:#FF6B6B,stroke:#333,stroke-width:2px
    style Return fill:#FFB6C1,stroke:#333,stroke-width:2px
```

#### 3. traverse_pattern(pattern, parameters, limit)

Executes complex traversal patterns using Cypher.

**Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Client
    participant Tool as traverse_pattern
    participant Parser
    participant Optimizer
    participant DB as Memgraph

    Client->>Tool: traverse_pattern(pattern)

    Tool->>Parser: Parse pattern
    Parser->>Parser: Validate syntax
    Parser->>Parser: Extract components

    Tool->>Optimizer: Optimize pattern
    Optimizer->>Optimizer: Add indexes hints
    Optimizer->>Optimizer: Reorder matches

    Tool->>DB: Execute pattern
    DB-->>Tool: Pattern matches

    Tool->>Tool: Process results
    Tool-->>Client: Traversal results
```

**Flow Diagram:**
```mermaid
graph TB
    Start[traverse_pattern] --> ParsePattern[Parse Pattern]

    ParsePattern --> ValidateSyntax{Valid Syntax?}
    ValidateSyntax -->|No| SyntaxError[Return Syntax Error]
    ValidateSyntax -->|Yes| OptimizePattern[Optimize Pattern]

    OptimizePattern --> AddHints[Add Index Hints]
    AddHints --> ReorderMatches[Reorder Matches]
    ReorderMatches --> Execute[Execute Pattern]

    Execute --> ProcessResults[Process Results]
    ProcessResults --> Return[Return Matches]

    style Start fill:#90EE90,stroke:#333,stroke-width:2px
    style SyntaxError fill:#FF6B6B,stroke:#333,stroke-width:2px
    style Return fill:#FFB6C1,stroke:#333,stroke-width:2px
```

#### 4. find_common_neighbors(entity_type1, entity_id1, entity_type2, entity_id2)

Finds entities connected to both specified entities.

**Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Client
    participant Tool as find_common_neighbors
    participant DB as Memgraph
    participant SetOps as Set Operations

    Client->>Tool: find_common_neighbors(e1, e2)

    Tool->>DB: Get neighbors of entity1
    DB-->>Tool: Neighbors1 set

    Tool->>DB: Get neighbors of entity2
    DB-->>Tool: Neighbors2 set

    Tool->>SetOps: Find intersection
    SetOps->>SetOps: Common = N1 ∩ N2
    SetOps-->>Tool: Common neighbors

    Tool->>Tool: Enrich with relationship data
    Tool-->>Client: Common neighbors with details
```

**Flow Diagram:**
```mermaid
graph TB
    Start[find_common_neighbors] --> GetN1[Get Entity1 Neighbors]

    GetN1 --> GetN2[Get Entity2 Neighbors]
    GetN2 --> FindIntersection[Find Intersection]
    FindIntersection --> CheckEmpty{Common Found?}

    CheckEmpty -->|No| NoCommon[Return Empty]
    CheckEmpty -->|Yes| EnrichData[Enrich with Relationships]

    EnrichData --> Return[Return Common Neighbors]

    style Start fill:#90EE90,stroke:#333,stroke-width:2px
    style NoCommon fill:#FFB6C1,stroke:#333,stroke-width:2px
    style Return fill:#FFB6C1,stroke:#333,stroke-width:2px
```

### Aggregation & Analytics Tools

#### 1. aggregate_by_type(entity_type, group_by, metrics, filters)

Performs aggregations and group operations on entities.

**Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Client
    participant Tool as aggregate_by_type
    participant QueryBuilder
    participant MetricCalc as Metric Calculator
    participant DB as Memgraph

    Client->>Tool: aggregate_by_type(params)

    Tool->>QueryBuilder: Build aggregation query

    QueryBuilder->>QueryBuilder: Base: MATCH (n:EntityType)

    alt Has filters
        QueryBuilder->>QueryBuilder: Add WHERE clause
    end

    QueryBuilder->>QueryBuilder: Add GROUP BY

    Tool->>MetricCalc: Prepare metrics

    loop For each metric
        MetricCalc->>MetricCalc: Add aggregation function
        Note right of MetricCalc: count(), avg(), sum(), etc.
    end

    MetricCalc-->>QueryBuilder: Metric expressions
    QueryBuilder->>QueryBuilder: Add ORDER BY

    Tool->>DB: Execute aggregation
    DB-->>Tool: Aggregated results

    Tool->>Tool: Format results
    Tool-->>Client: Aggregation report
```

**Flow Diagram:**
```mermaid
graph TB
    Start[aggregate_by_type] --> BuildBase[Build Base Query]

    BuildBase --> CheckFilters{Has Filters?}
    CheckFilters -->|Yes| AddFilters[Add WHERE Clause]
    CheckFilters -->|No| AddGroup[Add GROUP BY]

    AddFilters --> AddGroup
    AddGroup --> PrepareMetrics[Prepare Metrics]

    PrepareMetrics --> AddCount[Add count()]
    AddCount --> AddAvg[Add avg()]
    AddAvg --> AddSum[Add sum()]
    AddSum --> AddMinMax[Add min()/max()]

    AddMinMax --> Execute[Execute Aggregation]
    Execute --> FormatResults[Format Results]
    FormatResults --> Return[Return Report]

    style Start fill:#90EE90,stroke:#333,stroke-width:2px
    style Return fill:#FFB6C1,stroke:#333,stroke-width:2px
```

#### 2. calculate_statistics(entity_type, numeric_property, group_by)

Calculates detailed statistics for numeric properties.

**Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Client
    participant Tool as calculate_statistics
    participant StatsCalc as Statistics Calculator
    participant DB as Memgraph

    Client->>Tool: calculate_statistics(params)

    Tool->>DB: Get all values
    DB-->>Tool: Value set

    Tool->>StatsCalc: Calculate statistics

    StatsCalc->>StatsCalc: Calculate mean
    StatsCalc->>StatsCalc: Calculate median
    StatsCalc->>StatsCalc: Calculate mode
    StatsCalc->>StatsCalc: Calculate std dev
    StatsCalc->>StatsCalc: Calculate percentiles

    alt Has group_by
        StatsCalc->>StatsCalc: Calculate per group
    end

    StatsCalc-->>Tool: Statistics object
    Tool-->>Client: Statistical report
```

**Flow Diagram:**
```mermaid
graph TB
    Start[calculate_statistics] --> GetValues[Get Property Values]

    GetValues --> CheckGroup{Has Group By?}
    CheckGroup -->|Yes| GroupValues[Group Values]
    CheckGroup -->|No| CalcStats[Calculate Statistics]

    GroupValues --> CalcPerGroup[Calculate Per Group]
    CalcPerGroup --> CombineResults[Combine Results]

    CalcStats --> CalcMean[Calculate Mean]
    CalcMean --> CalcMedian[Calculate Median]
    CalcMedian --> CalcMode[Calculate Mode]
    CalcMode --> CalcStdDev[Calculate Std Dev]
    CalcStdDev --> CalcPercentiles[Calculate Percentiles]

    CalcPercentiles --> Return[Return Statistics]
    CombineResults --> Return

    style Start fill:#90EE90,stroke:#333,stroke-width:2px
    style Return fill:#FFB6C1,stroke:#333,stroke-width:2px
```

#### 3. find_patterns(base_entity, pattern_type, threshold)

Detects specific patterns in the graph data.

**Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Client
    participant Tool as find_patterns
    participant PatternDetector
    participant DB as Memgraph
    participant Analyzer

    Client->>Tool: find_patterns(pattern_type)

    Tool->>PatternDetector: Select pattern algorithm

    alt hub_nodes pattern
        PatternDetector->>DB: Query degree centrality
        DB-->>PatternDetector: Node degrees
        PatternDetector->>Analyzer: Find high-degree nodes
    else bottleneck pattern
        PatternDetector->>DB: Query resource utilization
        DB-->>PatternDetector: Utilization data
        PatternDetector->>Analyzer: Find over-utilized
    else temporal_cluster pattern
        PatternDetector->>DB: Query time-series data
        DB-->>PatternDetector: Temporal data
        PatternDetector->>Analyzer: Detect clusters
    end

    Analyzer->>Analyzer: Apply threshold
    Analyzer->>Analyzer: Rank results
    Analyzer-->>Tool: Pattern matches

    Tool-->>Client: Pattern detection results
```

**Flow Diagram:**
```mermaid
graph TB
    Start[find_patterns] --> SelectPattern{Pattern Type?}

    SelectPattern -->|hub_nodes| HubDetection[Hub Node Detection]
    SelectPattern -->|bottleneck| BottleneckDetection[Bottleneck Detection]
    SelectPattern -->|isolated| IsolatedDetection[Isolated Node Detection]
    SelectPattern -->|temporal| TemporalDetection[Temporal Clustering]

    HubDetection --> QueryDegree[Query Degree Centrality]
    QueryDegree --> FindHighDegree[Find High-Degree Nodes]

    BottleneckDetection --> QueryUtil[Query Utilization]
    QueryUtil --> FindOverUtil[Find Over-Utilized]

    IsolatedDetection --> QueryConnections[Query Connections]
    QueryConnections --> FindLowConn[Find Low Connectivity]

    TemporalDetection --> QueryTimeSeries[Query Time Series]
    QueryTimeSeries --> DetectClusters[Detect Clusters]

    FindHighDegree --> ApplyThreshold[Apply Threshold]
    FindOverUtil --> ApplyThreshold
    FindLowConn --> ApplyThreshold
    DetectClusters --> ApplyThreshold

    ApplyThreshold --> RankResults[Rank Results]
    RankResults --> Return[Return Patterns]

    style Start fill:#90EE90,stroke:#333,stroke-width:2px
    style Return fill:#FFB6C1,stroke:#333,stroke-width:2px
```

#### 4. execute_cypher(query, parameters, limit)

Executes custom Cypher queries with safety validation.

**Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Client
    participant Tool as execute_cypher
    participant SafetyValidator
    participant QueryOptimizer
    participant DB as Memgraph
    participant Sanitizer

    Client->>Tool: execute_cypher(query, params)

    Tool->>SafetyValidator: Validate query safety

    SafetyValidator->>SafetyValidator: Check for DELETE
    SafetyValidator->>SafetyValidator: Check for DROP
    SafetyValidator->>SafetyValidator: Check for REMOVE
    SafetyValidator->>SafetyValidator: Check for SET

    alt Query unsafe
        SafetyValidator-->>Client: Error: Unsafe operation
    else Query safe
        Tool->>Sanitizer: Sanitize parameters
        Sanitizer->>Sanitizer: Escape strings
        Sanitizer->>Sanitizer: Validate types

        Tool->>QueryOptimizer: Optimize query
        QueryOptimizer->>QueryOptimizer: Add LIMIT if missing
        QueryOptimizer->>QueryOptimizer: Add timeout

        Tool->>DB: Execute query
        DB-->>Tool: Query results
        Tool-->>Client: Formatted results
    end
```

**Flow Diagram:**
```mermaid
graph TB
    Start[execute_cypher] --> ValidateSafety[Validate Query Safety]

    ValidateSafety --> CheckOps{Contains Unsafe Ops?}
    CheckOps -->|Yes| RejectQuery[Reject Query]
    CheckOps -->|No| SanitizeParams[Sanitize Parameters]

    SanitizeParams --> OptimizeQuery[Optimize Query]
    OptimizeQuery --> AddLimit[Add LIMIT if Missing]
    AddLimit --> AddTimeout[Add Timeout]
    AddTimeout --> Execute[Execute Query]

    Execute --> ProcessResults[Process Results]
    ProcessResults --> Return[Return Results]

    style Start fill:#90EE90,stroke:#333,stroke-width:2px
    style RejectQuery fill:#FF6B6B,stroke:#333,stroke-width:2px
    style Return fill:#FFB6C1,stroke:#333,stroke-width:2px
```

### Administrative Tools

#### 1. get_health()

Checks server and database health status.

**Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Client
    participant Tool as get_health
    participant Server
    participant DB as Memgraph
    participant Cache
    participant Metrics

    Client->>Tool: get_health()

    Tool->>Server: Check server status
    Server-->>Tool: Server info

    Tool->>DB: Test connection
    DB-->>Tool: Connection status

    Tool->>Cache: Check cache status
    Cache-->>Tool: Cache statistics

    Tool->>Metrics: Get performance metrics
    Metrics-->>Tool: Metrics data

    Tool->>Tool: Compile health report
    Tool-->>Client: Health status
```

**Flow Diagram:**
```mermaid
graph TB
    Start[get_health] --> CheckServer[Check Server Status]

    CheckServer --> CheckDB[Test Database Connection]
    CheckDB --> CheckCache[Check Cache Status]
    CheckCache --> GetMetrics[Get Performance Metrics]

    GetMetrics --> CompileReport[Compile Health Report]
    CompileReport --> Return[Return Status]

    style Start fill:#90EE90,stroke:#333,stroke-width:2px
    style Return fill:#FFB6C1,stroke:#333,stroke-width:2px
```

#### 2. refresh_cache()

Forces refresh of the schema cache.

**Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Client
    participant Tool as refresh_cache
    participant Cache
    participant SchemaManager
    participant DB as Memgraph

    Client->>Tool: refresh_cache()

    Tool->>Cache: Clear current cache
    Cache-->>Tool: Cache cleared

    Tool->>SchemaManager: Request schema reload
    SchemaManager->>DB: Query fresh schema
    DB-->>SchemaManager: Schema data

    SchemaManager->>Cache: Update cache
    Cache->>Cache: Set new TTL
    Cache-->>Tool: Cache updated

    Tool-->>Client: Cache refreshed
```

**Flow Diagram:**
```mermaid
graph TB
    Start[refresh_cache] --> ClearCache[Clear Current Cache]

    ClearCache --> QuerySchema[Query Fresh Schema]
    QuerySchema --> UpdateCache[Update Cache]
    UpdateCache --> SetTTL[Set New TTL]

    SetTTL --> Return[Return Success]

    style Start fill:#90EE90,stroke:#333,stroke-width:2px
    style Return fill:#FFB6C1,stroke:#333,stroke-width:2px
```

## Implementation Details

### Caching Strategy

The server implements a multi-tier caching strategy:

```mermaid
graph TB
    subgraph "Cache Layers"
        L1[L1: Memory Cache<br/>TTL: 60s]
        L2[L2: Schema Manager Cache<br/>TTL: 300s]
        L3[L3: Query Result Cache<br/>TTL: Variable]
    end

    Request[Client Request] --> CacheCheck{Cache Hit?}

    CacheCheck -->|L1 Hit| L1
    L1 --> Response[Return Cached]

    CacheCheck -->|L1 Miss| L2
    L2 -->|L2 Hit| UpdateL1[Update L1]
    UpdateL1 --> Response

    L2 -->|L2 Miss| L3
    L3 -->|L3 Hit| UpdateL2[Update L2]
    UpdateL2 --> UpdateL1

    L3 -->|All Miss| DB[(Database)]
    DB --> UpdateAll[Update All Caches]
    UpdateAll --> Response
```

#### Cache Invalidation Strategy

```python
class CacheManager:
    def __init__(self):
        self.ttl_seconds = 300
        self.cache = {}
        self.timestamps = {}

    def is_valid(self, key: str) -> bool:
        if key not in self.timestamps:
            return False
        age = time.time() - self.timestamps[key]
        return age < self.ttl_seconds

    def invalidate_pattern(self, pattern: str):
        """Invalidate all keys matching pattern"""
        for key in list(self.cache.keys()):
            if fnmatch(key, pattern):
                del self.cache[key]
                del self.timestamps[key]
```

### Query Optimization

#### Index Strategy

```cypher
-- Primary indexes for entity identification
CREATE INDEX ON :Incident(id);
CREATE INDEX ON :Resource(id);
CREATE INDEX ON :Department(id);
CREATE INDEX ON :Location(id);
CREATE INDEX ON :Person(id);

-- Secondary indexes for common queries
CREATE INDEX ON :Incident(type);
CREATE INDEX ON :Incident(severity);
CREATE INDEX ON :Incident(timestamp);
CREATE INDEX ON :Resource(status);
CREATE INDEX ON :Resource(type);
CREATE INDEX ON :Department(name);
CREATE INDEX ON :Location(county);

-- Composite indexes for complex queries
CREATE INDEX ON :Incident(type, severity);
CREATE INDEX ON :Resource(type, status);
```

#### Query Optimization Patterns

```python
class QueryOptimizer:
    def optimize_query(self, query: str) -> str:
        """Apply optimization patterns to Cypher query"""

        # Use index hints for large datasets
        if "MATCH (n:Incident)" in query:
            query = query.replace(
                "MATCH (n:Incident)",
                "MATCH (n:Incident) USING INDEX n:Incident(id)"
            )

        # Push filters down
        query = self._push_filters_down(query)

        # Limit early for existence checks
        if "EXISTS(" in query:
            query = self._add_early_limit(query)

        return query

    def _push_filters_down(self, query: str) -> str:
        """Move WHERE clauses closer to MATCH"""
        # Implementation details...
        return query

    def _add_early_limit(self, query: str) -> str:
        """Add LIMIT 1 for existence checks"""
        # Implementation details...
        return query
```

### Response Sanitization

#### Embedding Filtering Strategy

To prevent large embedding vectors (1536 dimensions) from bloating MCP tool responses, the server implements automatic embedding stripping at the database layer.

**Implementation: Global Filtering in GraphClient**

Location: `src/graph/client.py:96-138`

```python
class GraphClient:
    def _strip_node_embeddings(self, data):
        """
        Recursively remove embedding properties from Neo4j Node/Relationship objects.

        Handles:
        - Neo4j Node objects
        - Neo4j Relationship objects
        - Nested dicts and lists
        - Removes any key starting with 'embedding'
        """
        from neo4j.graph import Node, Relationship

        if isinstance(data, Node):
            # Convert Node to dict, excluding embeddings
            return {k: v for k, v in dict(data.items()).items()
                    if not k.startswith('embedding')}

        elif isinstance(data, dict):
            return {k: self._strip_node_embeddings(v)
                    for k, v in data.items()
                    if not k.startswith('embedding')}

        elif isinstance(data, list):
            return [self._strip_node_embeddings(item) for item in data]

        else:
            return data

    def execute_query(self, cypher, parameters=None, limit=None, skip=None):
        """Execute query with automatic embedding filtering"""
        # Execute query
        results = [dict(record) for record in session.run(cypher, **params)]

        # Strip embeddings from all results
        return [self._strip_node_embeddings(record) for record in results]
```

**Impact:**

All tools that return nodes automatically have embeddings stripped:
- ✅ `describe_entity_type`: 27.8 KB → 1.8 KB (93% reduction)
- ✅ `describe_relationship_type`: 56.9 KB → 2.4 KB (96% reduction)
- ✅ `search_entities`: 27.0 KB → 1.3 KB (95% reduction)
- ✅ `get_entity`, `get_neighbors`, `find_paths`: All cleaned
- ✅ Total response size: 169 KB → 63 KB (62.8% reduction)

**Preserved Information:**

Embedding metadata (types, models, timestamps) is retained where schema-relevant:
- ✅ `get_schema` shows `"embedding": {"type": "LIST"}` in property definitions
- ❌ Sample nodes exclude actual embedding arrays

**Testing:**

Run verification script to audit all tools:
```bash
uv run python scripts/verify_no_embeddings.py
```

See `dev_status/embedding_leakage_summary.md` for detailed analysis.

### Error Handling

#### Error Classification

```python
class GraphRAGError(Exception):
    """Base exception for GraphRAG MCP Server"""
    pass

class ValidationError(GraphRAGError):
    """Invalid input parameters"""
    pass

class DatabaseError(GraphRAGError):
    """Database operation failed"""
    pass

class CacheError(GraphRAGError):
    """Cache operation failed"""
    pass

class QueryTimeoutError(DatabaseError):
    """Query exceeded timeout limit"""
    pass

class SchemaError(GraphRAGError):
    """Schema operation failed"""
    pass
```

#### Error Recovery Patterns

```python
async def resilient_query_execution(query: str, params: dict, retries: int = 3):
    """Execute query with automatic retry and fallback"""

    for attempt in range(retries):
        try:
            return await execute_query(query, params)

        except QueryTimeoutError:
            # Try with increased timeout
            params['timeout'] = params.get('timeout', 30) * 2

        except ConnectionError:
            # Reconnect and retry
            await reconnect_database()

        except Exception as e:
            if attempt == retries - 1:
                # Final attempt failed, use fallback
                return await execute_fallback_query(query, params)

        await asyncio.sleep(2 ** attempt)  # Exponential backoff
```

### Performance Monitoring

#### Metrics Collection

```python
class PerformanceMonitor:
    def __init__(self):
        self.metrics = {
            'query_count': 0,
            'total_query_time': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'error_count': 0
        }

    @contextmanager
    def track_query(self, query_type: str):
        start_time = time.time()
        try:
            yield
            self.metrics['query_count'] += 1
        except Exception as e:
            self.metrics['error_count'] += 1
            raise
        finally:
            duration = time.time() - start_time
            self.metrics['total_query_time'] += duration
            self._record_query_metric(query_type, duration)

    def get_statistics(self) -> dict:
        return {
            'avg_query_time': self.metrics['total_query_time'] / max(1, self.metrics['query_count']),
            'cache_hit_rate': self.metrics['cache_hits'] / max(1, self.metrics['cache_hits'] + self.metrics['cache_misses']),
            'error_rate': self.metrics['error_count'] / max(1, self.metrics['query_count'])
        }
```

## Database Schema

### Entity Models

#### Incident Entity

```cypher
(:Incident {
    id: String!,           // Unique identifier (e.g., "INC-2024-001")
    type: String!,         // Type of incident (hurricane, fire, flood, etc.)
    severity: Integer!,    // Severity level (1-5)
    description: String,   // Detailed description
    timestamp: DateTime!,  // When the incident occurred
    status: String!,       // Current status (active, resolved, monitoring)
    location_lat: Float,   // Latitude coordinate
    location_lon: Float,   // Longitude coordinate
    affected_area: String, // Geographic area affected
    casualties: Integer,   // Number of casualties
    damage_estimate: Float // Estimated damage in dollars
})
```

#### Resource Entity

```cypher
(:Resource {
    id: String!,              // Unique identifier (e.g., "TRUCK-001")
    type: String!,            // Type of resource (fire_truck, ambulance, etc.)
    status: String!,          // Current status (available, deployed, maintenance)
    capacity: Integer,        // Resource capacity
    current_location: String, // Current location
    home_base: String,        // Home base location
    last_maintenance: Date,   // Last maintenance date
    deployment_count: Integer,// Number of deployments
    equipment: [String]       // List of equipment carried
})
```

#### Department Entity

```cypher
(:Department {
    id: String!,           // Unique identifier (e.g., "DEPT-001")
    name: String!,         // Department name
    type: String!,         // Department type (fire, police, medical, etc.)
    contact_info: String,  // Contact information
    staff_count: Integer,  // Number of staff
    budget: Float,         // Annual budget
    jurisdiction: String,  // Jurisdiction area
    established: Date,     // Establishment date
    certifications: [String] // List of certifications
})
```

### Relationship Models

#### RESPONDED_TO Relationship

```cypher
-[:RESPONDED_TO {
    response_time: Duration,    // Time to respond
    arrival_time: DateTime,     // Arrival at incident
    departure_time: DateTime,   // Departure from incident
    personnel_count: Integer,   // Number of personnel
    role: String,              // Role in response
    effectiveness: Float       // Effectiveness rating (0-1)
}]->
```

#### COORDINATES_WITH Relationship

```cypher
-[:COORDINATES_WITH {
    established: DateTime,      // When coordination started
    protocol: String,          // Coordination protocol
    frequency: String,         // Communication frequency
    priority: Integer,         // Coordination priority
    shared_resources: [String] // Shared resources
}]->
```

### Index Strategy

```sql
-- Performance-critical indexes
CREATE INDEX idx_incident_timestamp ON Incident(timestamp);
CREATE INDEX idx_incident_type_severity ON Incident(type, severity);
CREATE INDEX idx_resource_status_type ON Resource(status, type);
CREATE INDEX idx_department_type ON Department(type);

-- Full-text search indexes
CREATE FULLTEXT INDEX incident_description ON Incident(description);
CREATE FULLTEXT INDEX resource_equipment ON Resource(equipment);

-- Geospatial indexes (if supported)
CREATE SPATIAL INDEX incident_location ON Incident(location_lat, location_lon);
```

## Advanced Patterns

### Complex Query Patterns

#### Multi-hop Pattern Analysis

```cypher
// Find all resources within N degrees of separation from an incident
MATCH path = (i:Incident {id: $incident_id})-[*1..3]-(r:Resource)
WITH r, min(length(path)) as min_distance
RETURN r.id, r.type, min_distance
ORDER BY min_distance, r.type
```

#### Temporal Pattern Detection

```cypher
// Detect incident clustering patterns
MATCH (i:Incident)
WHERE i.timestamp >= datetime($start) AND i.timestamp <= datetime($end)
WITH date(i.timestamp) as day, i.type as incident_type, count(*) as daily_count
WITH day, collect({type: incident_type, count: daily_count}) as daily_incidents
WHERE size(daily_incidents) > 1
RETURN day, daily_incidents, reduce(total = 0, inc IN daily_incidents | total + inc.count) as total_incidents
ORDER BY total_incidents DESC
```

#### Resource Optimization Queries

```cypher
// Find underutilized resources
MATCH (r:Resource)
OPTIONAL MATCH (r)-[resp:RESPONDED_TO]->(i:Incident)
WHERE i.timestamp >= datetime($cutoff)
WITH r, count(resp) as recent_deployments
WHERE r.status = 'available' AND recent_deployments < $threshold
RETURN r.id, r.type, recent_deployments, r.last_maintenance
ORDER BY recent_deployments, r.last_maintenance
```

### Temporal Analysis

#### Time-series Aggregation

```cypher
// Incident frequency analysis by hour of day
MATCH (i:Incident)
WITH hour(i.timestamp) as hour_of_day, i
RETURN hour_of_day,
       count(*) as incident_count,
       avg(i.severity) as avg_severity,
       collect(DISTINCT i.type) as incident_types
ORDER BY hour_of_day
```

#### Trend Detection

```cypher
// Detect increasing incident trends
MATCH (i:Incident)
WHERE i.timestamp >= datetime($lookback_start)
WITH date(i.timestamp) as day, count(*) as daily_count
WITH collect({day: day, count: daily_count}) as time_series
// Calculate moving average and detect upward trends
UNWIND range(7, size(time_series)-1) as idx
WITH time_series[idx].day as current_day,
     time_series[idx].count as current_count,
     reduce(sum = 0, i IN range(idx-7, idx-1) | sum + time_series[i].count) / 7.0 as moving_avg
WHERE current_count > moving_avg * 1.5
RETURN current_day, current_count, moving_avg
ORDER BY current_day
```

### Graph Algorithms

#### Centrality Analysis

```cypher
// Find most central departments in coordination network
MATCH (d:Department)
OPTIONAL MATCH (d)-[c:COORDINATES_WITH]-(other:Department)
WITH d, count(DISTINCT other) as degree_centrality
OPTIONAL MATCH path = (d)-[:COORDINATES_WITH*2]-(indirect:Department)
WHERE NOT (d)-[:COORDINATES_WITH]-(indirect)
WITH d, degree_centrality, count(DISTINCT indirect) as indirect_connections
RETURN d.name,
       degree_centrality,
       indirect_connections,
       degree_centrality + indirect_connections * 0.5 as weighted_centrality
ORDER BY weighted_centrality DESC
LIMIT 10
```

#### Community Detection

```cypher
// Detect resource clusters/communities
MATCH (r1:Resource)-[:RESPONDED_TO]->(i:Incident)<-[:RESPONDED_TO]-(r2:Resource)
WHERE r1.id < r2.id
WITH r1, r2, count(DISTINCT i) as shared_incidents
WHERE shared_incidents >= $min_shared
CREATE (r1)-[:FREQUENTLY_DEPLOYED_WITH {count: shared_incidents}]->(r2)

// Find communities using connected components
MATCH (r:Resource)-[:FREQUENTLY_DEPLOYED_WITH]-(other:Resource)
WITH r, collect(DISTINCT other) as community
WHERE size(community) >= $min_community_size
RETURN r.type, community, size(community) as community_size
ORDER BY community_size DESC
```

#### Pathfinding Algorithms

```cypher
// Optimal resource routing
MATCH (r:Resource {id: $resource_id}),
      (i:Incident {id: $incident_id})
MATCH path = shortestPath((r)-[*..10]-(i))
WHERE ALL(rel in relationships(path) WHERE type(rel) IN ['LOCATED_AT', 'CONNECTED_TO'])
RETURN path,
       length(path) as distance,
       [n in nodes(path) | labels(n)[0] + ':' + n.id] as route
```

### Performance Patterns

#### Query Batching

```python
async def batch_query_execution(queries: List[str], batch_size: int = 10):
    """Execute multiple queries in optimized batches"""

    results = []
    for i in range(0, len(queries), batch_size):
        batch = queries[i:i + batch_size]

        # Combine into single transaction
        combined_query = "CALL { " + " UNION ".join(batch) + " }"

        batch_results = await execute_query(combined_query)
        results.extend(batch_results)

    return results
```

#### Lazy Loading Pattern

```python
class LazyGraphLoader:
    """Load graph data on-demand to minimize memory usage"""

    def __init__(self, initial_depth: int = 1):
        self.loaded_nodes = set()
        self.depth = initial_depth

    async def expand_node(self, node_id: str, additional_depth: int = 1):
        """Expand graph from a specific node"""

        if node_id in self.loaded_nodes:
            return self.get_cached_neighbors(node_id)

        query = """
        MATCH path = (n {id: $node_id})-[*1..{depth}]-()
        RETURN path
        """.format(depth=additional_depth)

        paths = await execute_query(query, {'node_id': node_id})
        self._update_cache(paths)
        self.loaded_nodes.add(node_id)

        return self.get_cached_neighbors(node_id)
```

## Security Considerations

### Query Sanitization

```python
class QuerySanitizer:
    FORBIDDEN_OPERATIONS = [
        'DELETE', 'DETACH DELETE', 'DROP', 'REMOVE',
        'CREATE INDEX', 'DROP INDEX', 'CREATE CONSTRAINT'
    ]

    def sanitize_query(self, query: str) -> str:
        """Sanitize Cypher query to prevent dangerous operations"""

        query_upper = query.upper()

        for operation in self.FORBIDDEN_OPERATIONS:
            if operation in query_upper:
                raise SecurityError(f"Forbidden operation: {operation}")

        # Escape user input in parameters
        return self._escape_parameters(query)

    def _escape_parameters(self, query: str) -> str:
        """Escape special characters in parameters"""
        # Implementation details...
        return query
```

### Access Control

```python
class AccessController:
    def __init__(self):
        self.permissions = {
            'read': ['get_*', 'search_*', 'find_*', 'describe_*'],
            'write': ['create_*', 'update_*'],
            'admin': ['delete_*', 'clear_*', 'execute_cypher']
        }

    def check_permission(self, tool_name: str, user_role: str) -> bool:
        """Check if user role has permission for tool"""

        allowed_patterns = self.permissions.get(user_role, [])

        for pattern in allowed_patterns:
            if fnmatch(tool_name, pattern):
                return True

        return False
```

## Monitoring and Observability

### Logging Strategy

```python
import structlog

logger = structlog.get_logger()

class StructuredLogger:
    def log_tool_execution(self, tool_name: str, params: dict, result: dict, duration: float):
        logger.info(
            "tool_executed",
            tool=tool_name,
            params=self._sanitize_params(params),
            result_size=len(str(result)),
            duration_ms=duration * 1000,
            success=True
        )

    def log_query_execution(self, query: str, duration: float, row_count: int):
        logger.debug(
            "query_executed",
            query_type=self._extract_query_type(query),
            duration_ms=duration * 1000,
            row_count=row_count,
            query_hash=hashlib.md5(query.encode()).hexdigest()
        )
```

### Metrics and Telemetry

```python
from prometheus_client import Counter, Histogram, Gauge

# Define metrics
tool_calls_total = Counter('mcp_tool_calls_total', 'Total tool calls', ['tool_name'])
query_duration_seconds = Histogram('query_duration_seconds', 'Query execution time')
cache_hit_rate = Gauge('cache_hit_rate', 'Cache hit rate percentage')
active_connections = Gauge('db_connections_active', 'Active database connections')

class MetricsCollector:
    def record_tool_call(self, tool_name: str):
        tool_calls_total.labels(tool_name=tool_name).inc()

    def record_query_duration(self, duration: float):
        query_duration_seconds.observe(duration)

    def update_cache_metrics(self, hits: int, misses: int):
        hit_rate = hits / (hits + misses) if (hits + misses) > 0 else 0
        cache_hit_rate.set(hit_rate * 100)
```

## Deployment Considerations

### Container Optimization

```dockerfile
# Multi-stage build for smaller image
FROM python:3.10-slim as builder

WORKDIR /build
COPY pyproject.toml .
RUN pip install uv && uv sync --no-dev

FROM python:3.10-slim

WORKDIR /app
COPY --from=builder /build/.venv /app/.venv
COPY . .

# Use non-root user
RUN useradd -m -u 1000 mcp && chown -R mcp:mcp /app
USER mcp

ENV PATH="/app/.venv/bin:$PATH"
CMD ["python", "main.py"]
```

### Horizontal Scaling

```yaml
# Kubernetes StatefulSet for scaling
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: graphrag-mcp
spec:
  serviceName: graphrag-mcp
  replicas: 3
  selector:
    matchLabels:
      app: graphrag-mcp
  template:
    metadata:
      labels:
        app: graphrag-mcp
    spec:
      containers:
      - name: mcp-server
        image: graphrag-mcp:latest
        env:
        - name: INSTANCE_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: CACHE_STRATEGY
          value: "distributed"  # Use Redis for shared cache
```

### High Availability Setup

```mermaid
graph TB
    subgraph "Load Balancer"
        LB[HAProxy/Nginx]
    end

    subgraph "MCP Servers"
        S1[Server 1]
        S2[Server 2]
        S3[Server 3]
    end

    subgraph "Shared Services"
        Redis[(Redis Cache)]
        Memgraph[(Memgraph Cluster)]
    end

    LB --> S1
    LB --> S2
    LB --> S3

    S1 --> Redis
    S2 --> Redis
    S3 --> Redis

    S1 --> Memgraph
    S2 --> Memgraph
    S3 --> Memgraph
```

## Vector Search Architecture

### Embedding Ontology

**Design Decision:** Embeddings are stored as **properties of nodes**, not as separate nodes.

**Rationale:**
- ✅ **Industry standard** - Both Neo4j and Memgraph use this pattern
- ✅ **Performance** - Direct property access is faster than relationship traversal
- ✅ **Simplicity** - No additional relationship management
- ✅ **Index optimization** - Vector indices are designed for node properties

**Example Node Structure:**

```cypher
(:Mission {
    // Core properties
    id: "566189",
    title: "Feeding Florida MOU Activation",
    mission_number: "00787",
    comments: ["Comment 1", "Comment 2"],

    // Embedding vector (1536 floats)
    embedding: [0.123, -0.456, 0.789, ..., 0.321],

    // Embedding metadata
    embedding_model: "text-embedding-3-small",
    embedding_version: "v1",
    embedding_dimension: 1536,
    embedding_created_at: "2025-10-01T21:30:00Z",
    embedding_text_hash: "abc123...",
    embedding_source_fields: ["title"]
})
```

### Vector Index Architecture

**Index Creation:**

```cypher
// One index per entity type for semantic search
CREATE VECTOR INDEX mission_embedding ON :Mission(embedding)
WITH CONFIG {
    "dimension": 1536,        // Must match embedding model output
    "capacity": 1000,         // Expected max nodes
    "metric": "cosine",       // Similarity metric (cosine for text)
    "scalar_kind": "f32"      // Float32 storage (can use f16 for memory savings)
};

CREATE VECTOR INDEX incident_embedding ON :Incident(embedding)
WITH CONFIG {
    "dimension": 1536,
    "capacity": 1000,
    "metric": "cosine",
    "scalar_kind": "f32"
};

CREATE VECTOR INDEX resource_embedding ON :Resource(embedding)
WITH CONFIG {
    "dimension": 1536,
    "capacity": 500,
    "metric": "cosine",
    "scalar_kind": "f32"
};
```

**How Index Lookup Works:**

The index name directly maps to:
- **Index name** → `mission_embedding`
- **Node label** → `:Mission`
- **Property** → `embedding`

When you call `vector.search('mission_embedding', ...)`, Memgraph:
1. Uses the index name to identify which nodes to search (`:Mission`)
2. Uses the property name to know which vector property (`embedding`)
3. Applies HNSW algorithm for fast nearest neighbor search
4. Returns top-k most similar nodes with scores

**Vector Search Query Example:**

```cypher
// Search for missions similar to query
CALL vector.search('mission_embedding', $query_vector, 10)
YIELD node, score
RETURN
    node.id as id,
    node.title as title,
    node.embedding_model as model,
    score as similarity_score
ORDER BY score DESC;
```

**Parameters:**
- `'mission_embedding'` - Index name (tells it to search `:Mission` nodes)
- `$query_vector` - Query embedding (1536-dimensional, same model)
- `10` - Return top 10 results

### Embedding Workflow Options

**Current Implementation: Post-Load Embedding (Option 2)**

**Workflow:**
```
Data Load → Memgraph
     ↓
Run scripts/generate_embeddings.py
     ↓
Update nodes with embeddings + metadata
     ↓
Vector search enabled
```

**Advantages:**
- Decoupled from ETL pipeline
- Flexible for schema evolution
- Text hash tracking enables selective re-embedding
- Centralized embedding logic in GraphMCP

**Future Enhancement: Hybrid Approach (Option 4)**

**For Full Overrides:**
```
Data Source → ETL with embeddings → Load to Memgraph
```

**For Incremental Loads:**
```
Data Source → Load to Memgraph → Selective re-embedding script
```

**Why Hybrid:**
- Optimal performance for both full and incremental loads
- Full loads: No post-load delay
- Incremental loads: Only re-embed changed entities
- Cost-effective: Text hash prevents unnecessary re-embedding

**Implementation Details:** See `dev_status/embedding_workflow_strategies.md` for comprehensive strategy analysis.

### Embedding Metadata Best Practices

**Required Metadata Properties:**

```python
{
    "embedding_model": "text-embedding-3-small",  # Critical: Track model version
    "embedding_version": "v1",                    # Application version
    "embedding_dimension": 1536,                  # Dimension count
    "embedding_created_at": "2025-10-01T21:30:00Z", # Timestamp
    "embedding_text_hash": "abc123..."            # SHA256 of source text
}
```

**Why Each Matters:**

- **embedding_model** - Detect when model changes (can't compare embeddings from different models)
- **embedding_version** - Track application-level embedding generation changes
- **embedding_dimension** - Verify index compatibility
- **embedding_created_at** - Track embedding age, detect stale embeddings
- **embedding_text_hash** - Detect when source text changed (triggers re-embedding)

**Model Consistency Rule:**

⚠️ **CRITICAL:** Always use the same model for:
1. Generating embeddings for graph nodes
2. Generating embeddings for search queries

```python
# CORRECT: Same model everywhere
NODE_EMBEDDING_MODEL = "text-embedding-3-small"
QUERY_EMBEDDING_MODEL = "text-embedding-3-small"  # MUST MATCH

# WRONG: Different models
NODE_EMBEDDING_MODEL = "text-embedding-ada-002"
QUERY_EMBEDDING_MODEL = "text-embedding-3-small"  # Embeddings not comparable!
```

### No Chunking for Atomic Entities

**Decision:** Do NOT chunk Mission, Incident, or Resource entities.

**Rationale:**
- Entities are already atomic concepts (single mission, single incident)
- Text fields are short (<500 tokens typically)
- Graph relationships provide context (no need for chunk chains)
- Simpler implementation and querying

**When to chunk (future):**
- Long-form documents (>2000 tokens)
- Multi-topic text (research papers, PDF reports)
- Paragraph-level precision needed

**If chunking becomes necessary:**
```cypher
// Neo4j-style chunking pattern (for reference)
(:Document {id: "DOC-001", title: "..."})
    -[:PART_OF_DOCUMENT]->
(:Chunk {
    text: "...",
    position: 0,
    embedding: [...],
    ...metadata...
})
    -[:NEXT_CHUNK]->
(:Chunk {text: "...", position: 1, ...})
```

---

*This architecture documentation provides comprehensive technical details for developers working with the GraphRAG MCP Server. For usage examples and quick start guides, refer to the [README.md](README.md).*
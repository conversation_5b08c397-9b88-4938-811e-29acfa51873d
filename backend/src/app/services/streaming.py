import json
import asyncio
from typing import AsyncGenerator, Dict, Any
from enum import Enum


class StreamType(str, Enum):
    """Types of streaming data."""
    STATUS = "status"
    REASONING = "reasoning"
    SOURCES = "sources"
    CONTENT = "content"


class StreamMessage:
    """Structured streaming message."""
    
    def __init__(self, stream_type: StreamType, data: Any, metadata: Dict[str, Any] = None):
        self.type = stream_type
        self.data = data
        self.metadata = metadata or {}
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "type": self.type.value,
            "data": self.data,
            "metadata": self.metadata
        }
    
    def to_json(self) -> str:
        return json.dumps(self.to_dict())


async def generate_hardcoded_stream() -> AsyncGenerator[StreamMessage, None]:
    """
    Generate a hardcoded multi-type streaming response for testing.
    """
    # Status updates
    yield StreamMessage(StreamType.STATUS, {"message": "Initializing agent..."})
    await asyncio.sleep(0.5)
    
    yield StreamMessage(StreamType.STATUS, {"message": "Processing request..."})
    await asyncio.sleep(0.3)
    
    # Reasoning
    yield StreamMessage(StreamType.REASONING, {"step": "Analyzing the user's query to determine the best approach."})
    await asyncio.sleep(0.4)
    
    yield StreamMessage(StreamType.REASONING, {"step": "Breaking down the problem into smaller components."})
    await asyncio.sleep(0.4)
    
    # Sources (simulated)
    yield StreamMessage(StreamType.SOURCES, {"source": "FastAPI Documentation (https://fastapi.tiangolo.com/)"})
    await asyncio.sleep(0.3)
    
    yield StreamMessage(StreamType.SOURCES, {"source": "Python AsyncIO Guide (https://docs.python.org/3/library/asyncio.html)"})
    await asyncio.sleep(0.3)
    
    # Content generation
    content_chunks = [
        "This is a demonstration of multi-type streaming in FastAPI. ",
        "The system can stream different types of data including status updates, ",
        "reasoning steps, relevant sources, and the main content response. ",
        "This architecture allows for real-time feedback to users during processing. ",
        "Each message type serves a specific purpose in the overall user experience."
    ]
    
    for chunk in content_chunks:
        yield StreamMessage(StreamType.CONTENT, {"chunk": chunk})
        await asyncio.sleep(0.2)
    
    # Final status
    yield StreamMessage(StreamType.STATUS, {"message": "Response complete"})


def format_sse_message(message: StreamMessage) -> str:
    """Format a StreamMessage as a Server-Sent Event."""
    return f"data: {message.to_json()}\n\n"


def format_sse_done() -> str:
    """Format a proper SSE stream termination signal."""
    return "event: done\ndata: {}\n\n"
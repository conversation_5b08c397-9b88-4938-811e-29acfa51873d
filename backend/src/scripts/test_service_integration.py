#!/usr/bin/env python3
"""
Test service layer integration with refactored LangGraph workflow.

Verifies that LangGraphService can be instantiated without parameters
and uses RuntimeContext dependency injection correctly.

Run from backend/src directory:
    uv run python test_service_integration.py
"""

import asyncio

from app.services.langgraph_service import LangGraphService
from app.core.logging import get_logger

logger = get_logger(__name__)


async def test_service_initialization():
    """Test that service can be created without parameters."""
    print("=" * 80)
    print("Test 1: Parameterless Service Initialization")
    print("=" * 80)

    try:
        # Create service - NO PARAMETERS!
        service = LangGraphService()

        print("✅ Service created successfully (parameterless)")
        print(f"   Type: {type(service)}")
        print(f"   Has context: {service._context is not None}")
        print(f"   Context is None initially (lazy loading): {service._context is None}")

        return service, 0

    except Exception as e:
        print(f"❌ Service creation failed: {e}")
        import traceback
        traceback.print_exc()
        return None, 1


async def test_lazy_initialization(service):
    """Test that lazy initialization works on first request."""
    print("\n" + "=" * 80)
    print("Test 2: Lazy Initialization")
    print("=" * 80)

    try:
        # Before first request
        print(f"Before _ensure_setup():")
        print(f"   Context: {service._context is not None}")

        # Trigger lazy initialization
        await service._ensure_setup()

        # After initialization
        print(f"\nAfter _ensure_setup():")
        print(f"   ✅ Context created: {service._context is not None}")
        if service._context:
            print(f"      Tool count: {len(service._context.tools)}")
            print(f"      Fast model: {service._context.fast_model}")
            print(f"      Synthesis model: {service._context.synthesis_model}")

        return 0

    except Exception as e:
        print(f"❌ Lazy initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


async def test_streaming_response(service):
    """Test streaming response generation."""
    print("\n" + "=" * 80)
    print("Test 3: Streaming Response Generation")
    print("=" * 80)

    try:
        # Prepare conversation messages
        messages = [
            {"role": "user", "content": "What is 2+2?"}
        ]
        conversation_id = "test-conversation"

        print(f"   Input: {messages[0]['content']}")
        print(f"   Conversation ID: {conversation_id}")
        print(f"   Generating response...\n")

        # Collect streaming messages
        stream_messages = []
        async for msg in service.generate_response_with_streaming(messages, conversation_id):
            stream_messages.append(msg)
            print(f"   [{msg.type.value.upper()}] {msg.data}")

        print(f"\n   ✅ Streaming completed")
        print(f"      Total messages: {len(stream_messages)}")

        # Count message types
        status_count = sum(1 for m in stream_messages if m.type.value == "status")
        reasoning_count = sum(1 for m in stream_messages if m.type.value == "reasoning")
        content_count = sum(1 for m in stream_messages if m.type.value == "content")
        sources_count = sum(1 for m in stream_messages if m.type.value == "sources")

        print(f"      Status messages: {status_count}")
        print(f"      Reasoning messages: {reasoning_count}")
        print(f"      Content messages: {content_count}")
        print(f"      Sources messages: {sources_count}")

        return 0

    except Exception as e:
        print(f"❌ Streaming response failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


async def test_tool_calling_response(service):
    """Test response that should trigger tool calls."""
    print("\n" + "=" * 80)
    print("Test 4: Tool Calling Response")
    print("=" * 80)

    try:
        # Query that should trigger tool usage
        messages = [
            {"role": "user", "content": "List the available Genie spaces"}
        ]
        conversation_id = "test-tool-calling"

        print(f"   Input: {messages[0]['content']}")
        print(f"   Expected: Should call get_genie_spaces tool")
        print(f"   Generating response...\n")

        # Collect streaming messages
        sources_found = []
        async for msg in service.generate_response_with_streaming(messages, conversation_id):
            if msg.type.value == "sources":
                sources_found.append(msg.data.get("source", "unknown"))
            print(f"   [{msg.type.value.upper()}] {msg.data}")

        print(f"\n   ✅ Streaming completed")
        if sources_found:
            print(f"      ✅ Tools used: {', '.join(sources_found)}")
        else:
            print(f"      ⚠️  No tools detected (LLM may have answered directly)")

        return 0

    except Exception as e:
        print(f"❌ Tool calling test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


async def main():
    """Run all service integration tests."""
    print("\n🧪 Testing Service Layer Integration with LangGraph Refactoring\n")

    # Test 1: Parameterless initialization
    service, result1 = await test_service_initialization()
    if result1 != 0:
        return 1

    # Test 2: Lazy initialization
    result2 = await test_lazy_initialization(service)
    if result2 != 0:
        return 1

    # Test 3: Simple streaming
    result3 = await test_streaming_response(service)
    if result3 != 0:
        return 1

    # Test 4: Tool calling
    result4 = await test_tool_calling_response(service)
    if result4 != 0:
        return 1

    # Final summary
    print("\n" + "=" * 80)
    print("✅ ALL SERVICE INTEGRATION TESTS PASSED")
    print("=" * 80)
    print("\nKey Achievements:")
    print("  ✅ Parameterless service initialization")
    print("  ✅ Lazy loading of workflow and tools")
    print("  ✅ RuntimeContext dependency injection")
    print("  ✅ Streaming response generation")
    print("  ✅ Tool calling via LLM")
    print("\nPhase 3: Service Layer Integration - COMPLETE")
    print("Ready for Phase 4: Integration & Testing")
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)

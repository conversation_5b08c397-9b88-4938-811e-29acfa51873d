#!/bin/bash

# GraphRAG MCP Server launcher script with uv

echo "╔═══════════════════════════════════════════════════════╗"
echo "║   GraphRAG MCP Server for Emergency Management         ║"
echo "╚═══════════════════════════════════════════════════════╝"
echo ""

# Check if uv is installed
if ! command -v uv &> /dev/null; then
    echo "❌ Error: uv is required but not installed."
    echo ""
    echo "Install uv with:"
    echo "   curl -LsSf https://astral.sh/uv/install.sh | sh"
    echo ""
    echo "Then retry running this script."
    exit 1
fi

# Check if .env exists
if [ ! -f ".env" ]; then
    echo "⚠️  No .env file found. Creating from template..."
    cp .env.example .env
    echo "   Please edit .env with your Memgraph connection details"
fi

# Install/sync dependencies with uv
echo "📦 Syncing dependencies with uv..."
uv sync

# Start the server with uv
echo ""
echo "🚀 Starting GraphRAG MCP Server with FastMCP..."
echo "   Server Name: ${SERVER_NAME:-GraphRAG-MCP}"
echo "   Log Level: ${LOG_LEVEL:-INFO}"
echo "   MCP Protocol: stdio (for use with MCP clients)"
echo ""
echo "Press Ctrl+C to stop the server"
echo "---------------------------------------------------------"

# Run the server with uv
uv run python main.py
#!/bin/bash

# Test script to demonstrate streaming endpoint functionality
set -e

echo "🚀 Starting Streaming Endpoint Test Demo"
echo "========================================"

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

SERVER_PID=""
API_BASE="http://localhost:8000"

# Cleanup function
cleanup() {
    echo -e "\n${YELLOW}🧹 Cleaning up...${NC}"
    if [ ! -z "$SERVER_PID" ]; then
        echo "Stopping server (PID: $SERVER_PID)"
        kill $SERVER_PID 2>/dev/null || true
        wait $SERVER_PID 2>/dev/null || true
    fi
    echo -e "${GREEN}✅ Cleanup complete${NC}"
}

# Set up cleanup trap
trap cleanup EXIT INT TERM

echo -e "${BLUE}📦 Step 1: Starting uvicorn server in background...${NC}"
cd backend

# Activate virtual environment
if [ -f "venv/bin/activate" ]; then
    source venv/bin/activate
    echo "✅ Virtual environment activated"
else
    echo "❌ Virtual environment not found"
    exit 1
fi

# Start the server in background
venv/bin/python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload &
SERVER_PID=$!

echo -e "${GREEN}✅ Server started with PID: $SERVER_PID${NC}"

echo -e "${BLUE}⏳ Step 2: Waiting for server to start...${NC}"
# Wait for server to be ready
for i in {1..15}; do
    if curl -s "$API_BASE/health" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Server is ready!${NC}"
        break
    fi
    echo "Waiting for server... ($i/15)"
    sleep 1
done

# Check if server is actually ready
if ! curl -s "$API_BASE/health" > /dev/null 2>&1; then
    echo -e "${RED}❌ Server failed to start properly${NC}"
    exit 1
fi

echo -e "\n${BLUE}🌊 Step 3: Testing STREAMING endpoint (/api/v1/chat/stream)${NC}"
echo "=================================================="
echo -e "${YELLOW}Sending request: 'Tell me about FastAPI streaming'${NC}"
echo ""

curl -X POST "$API_BASE/api/v1/chat/stream" \
  -H "Content-Type: application/json" \
  -H "Accept: text/event-stream" \
  -d '{"message": "Tell me about FastAPI streaming", "conversation_id": "test-stream"}' \
  --no-buffer \
  -w "\n\nHTTP Status: %{http_code}\n" | while IFS= read -r line; do
    if [[ $line == data:* ]]; then
        # Extract JSON from SSE format and pretty print it
        json_data="${line#data: }"
        if [[ $json_data != "" ]]; then
            echo -e "${GREEN}📨 Received:${NC} $json_data"
            # Parse and display the message type for clarity
            type=$(echo "$json_data" | python3 -c "
import json, sys
try:
    data = json.load(sys.stdin)
    print(f\"  └─ Type: {data.get('type', 'unknown')} | Data: {str(data.get('data', ''))[:50]}{'...' if len(str(data.get('data', ''))) > 50 else ''}\"
except:
    pass
" 2>/dev/null || true)
            if [[ $type != "" ]]; then
                echo -e "${BLUE}$type${NC}"
            fi
        fi
    else
        echo "$line"
    fi
done

echo -e "\n${BLUE}💬 Step 4: Testing REGULAR chat endpoint (/api/v1/chat/)${NC}"
echo "================================================"
echo -e "${YELLOW}Sending request: 'Hello, this is a regular chat test'${NC}"
echo ""

curl -X POST "$API_BASE/api/v1/chat/" \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello, this is a regular chat test", "conversation_id": "test-regular"}' \
  -w "\nHTTP Status: %{http_code}\n" | python3 -c "
import json, sys
try:
    content = sys.stdin.read()
    lines = content.strip().split('\n')
    json_line = lines[0] if lines else ''
    status_line = lines[1] if len(lines) > 1 else ''
    
    if json_line:
        data = json.loads(json_line)
        print('📨 Response:', json.dumps(data, indent=2))
    
    if status_line:
        print(status_line)
except Exception as e:
    print(f'Error parsing response: {e}')
    print('Raw content:', repr(content))
"

echo -e "\n${BLUE}🔍 Step 5: Server health check${NC}"
echo "================================="
curl -X GET "$API_BASE/health" | python3 -c "
import json, sys
try:
    data = json.load(sys.stdin)
    print('🏥 Health Status:', json.dumps(data, indent=2))
except:
    print('Failed to parse health response')
"

echo -e "\n${GREEN}🎉 Test Demo Complete!${NC}"
echo "========================"
echo -e "${BLUE}Summary:${NC}"
echo -e "  ✅ Server started successfully"
echo -e "  ✅ Streaming endpoint tested with multi-type data"
echo -e "  ✅ Regular chat endpoint tested"
echo -e "  ✅ Health check verified"
echo ""
echo -e "${YELLOW}The streaming demo showed different message types:${NC}"
echo -e "  🔄 STATUS: Status updates during processing"
echo -e "  🧠 REASONING: AI reasoning steps"
echo -e "  📚 SOURCES: Relevant source materials"
echo -e "  📝 CONTENT: Main response content chunks"

# Cleanup will be called automatically by the trap